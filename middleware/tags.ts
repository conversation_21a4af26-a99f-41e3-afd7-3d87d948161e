import { Context } from "@nuxt/types";
import { config } from "~/config/global.config";
import { MenuItem, TagNode } from "~/model/common";
import { PageInfo } from "~/store/tags";
import menuUtils from '~/utils/menuUtils';

export function getMenuByPath(path: string, items: MenuItem[]): MenuItem | null {
  for (let i in items) {
    let item = items[i];
    if (item.url && item.url.split('?')[0] == path) {
      return item;
    }
    if (item.subs) {
      let m = getMenuByPath(path, item.subs);
      if (m) {
        return m;
      }
    }
  }
  return null;
}

export default async function (ctx: Context) {
  const { route, store, redirect } = ctx;

  let routePath = route.path.endsWith('/') ? route.path.substring(0, route.path.length - 1) : route.path;
  {
    let url2pageInfo: Map<string, PageInfo> = store.state.tags.url2pageInfo;
    let nPageRole = store.state.auth.pageRole;
    let info = url2pageInfo.get(routePath);
    if (info) {
      if (info.pageRole != nPageRole) {
        await store.dispatch('auth/putPageRole', info.pageRole);
      }
    } else {
      let m = menuUtils.findMenuReg(routePath);
      if (m && m.toPageRole) {
        nPageRole = m.toPageRole;
        await store.dispatch('auth/putPageRole', nPageRole);
      }
      if (nPageRole) {
        let info: PageInfo = {};
        info.path = routePath;
        info.name = route.name!;
        info.pageRole = nPageRole;
        store.commit('tags/putUrl2pageInfo', { key: routePath, value: info });
      }
    }
  }
  {
    let tagsList: TagNode[] = store.state.tags.tagsList;
    let tags = tagsList.filter(x => x.path == routePath);
    if (tags.length > 0) {
      let tag = { ...(tags[0]) }
      if (route.fullPath != tag.fullPath) {
        tag.fullPath = route.fullPath;
        store.commit('tags/setTagMsg', { path: tag.path, data: tag });
      }
    } else {
      let menus: MenuItem[] = config.menu.items;
      let menu = getMenuByPath(routePath!, menus);
      if (menu) {
        if (menu.show) {
          if (tagsList.length >= 15) {
            store.commit("tags/delTagsItem", { index: 0 });
          }
          let tag: TagNode = {};
          tag.name = menu.name;
          tag.fullPath = route.fullPath;
          tag.routeName = route.name ?? undefined;
          tag.path = routePath;
          tag.keepalive = menu.keepalive;

          store.commit("tags/putTagsItem", tag);
        }
      }
    }
  }
}

