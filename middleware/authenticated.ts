import { Context } from "@nuxt/types";

export default async function (ctx: Context) {
  const { route, store, redirect } = ctx;
  let link = route.path;
  let fullPath = route.fullPath;
  let token = await store.dispatch('auth/getToken');
  if (!token) {
    let links = null;
    if (link) {
      let p = link.startsWith('/') ? link.substring(1) : link;
      links = p.split('/');
    }
    if (!links || links.length < 1 || links[1] != 'common') {
      console.log("进入到页面：", link);
      return redirect('/def/common/login?return_url=' + encodeURIComponent(fullPath))
    }
  }
  console.log("进入到页面：", link);
  return link;
}

