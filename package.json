{"name": "sys-web", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=dev nuxt-ts", "build": "nuxt-ts build", "test": "cross-env NODE_ENV=test BASE_PATH= nuxt-ts build", "prod": "cross-env NODE_ENV=prod BASE_PATH= nuxt-ts build", "start": "nuxt-ts start", "generate": "nuxt-ts generate", "swagger2ts": "node ./_tools/swagger2ts/index.js"}, "dependencies": {"@nuxt/typescript-runtime": "2.0.0", "@nuxtjs/axios": "5.13.1", "@nuxtjs/dotenv": "1.4.1", "@tinymce/tinymce-vue": "3.2.8", "ali-oss": "^6.18.0", "autoprefixer": "^9.8.6", "cookieparser": "^0.1.0", "core-js": "^3.6.5", "decimal.js": "^10.4.3", "echarts": "5.1.1", "echarts-gl": "2.0.9", "element-ui": "^2.15.14", "fingerprintjs2": "^2.1.4", "js-cookie": "^2.2.0", "moment": "^2.30.1", "nuxt": "^2.14.6", "nuxt-i18n": "^6.25.0", "nuxt-property-decorator": "^2.9.1", "postcss": "^7.0.35", "sm-crypto": "^0.3.13", "sortablejs": "^1.15.2", "spark-md5": "^3.0.2", "tinymce": "^5.5.0", "vue-cropperjs": "^5.0.0", "vue-i18n": "^8.24.1", "vue-schart": "^2.0.0", "vuex-module-decorators": "^1.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nuxt/types": "^2.14.6", "@nuxt/typescript-build": "^2.0.3", "babel-plugin-component": "^1.1.1", "fibers": "^5.0.0", "postcss-px2rem": "^0.3.0", "pug": "^3.0.2", "pug-plain-loader": "^1.1.0", "sass": "^1.32.8", "sass-loader": "^10.1.1", "cross-env": "^7.0.3"}}