/* 改变主题色变量 */
@import '_var';

* {
  margin: 0;
  padding: 0;
}

html,
body,
#app,
.wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

body {
  font-family: 'PingFang SC', "Helvetica Neue", Helvetica, "microsoft yahei", arial, STHeiTi, sans-serif;
}

a {
  text-decoration: none
}


/* 布局 start */
.content-box {
  position: absolute;
  left: 250px;
  right: 0;
  top: 70px;
  bottom: 0;
  // padding-bottom: 30px;
  -webkit-transition: left .3s ease-in-out;
  transition: left .3s ease-in-out;
  background: #f0f0f0;
}

.layout-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.layout-content {
  width: auto;
  // height: 100%;
  padding: 10px;
  overflow-y: scroll;
  box-sizing: border-box;

  flex: 1;
  // overflow-y: auto;
}

.content-collapse {
  left: 65px;
}

.crumbs {
  margin: 10px 0;
}

.container {
  padding: 30px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
}

/* 布局 end */

/* 导航菜单 start */
// $--background-color: #324157;
$--background-color: mix($--color-white, $--color-main-background, 5%);
// $--text-color: $--color-main-text;
$--text-color: mix($--color-black, $--color-main-text, 5%);

.sidebar,
.el-menu--vertical {
  .el-menu {
    color: $--text-color;
    background-color: $--background-color;
  }

  .el-submenu__title {
    color: $--text-color;

    &:hover {
      background-color: mix($--color-white, $--background-color, 10%);
    }
  }

  .el-menu-item {
    color: $--text-color;
    background-color: $--background-color;

    &:hover {
      background-color: mix($--color-white, $--background-color, 10%);
    }
  }

  .is-active.el-menu-item {
    color: $--color-primary;
    background-color: mix($--color-primary, $--background-color, 5%);
  }
}

/* 导航菜单 end */

/* 表格页面 start */
.y-page-table {
  .y-header {
    margin-bottom: 20px;
  }

  .y-body {
    margin: 0;
  }

  .y-footer {
    margin-top: 20px;
  }
}

/* 表格页面 end */

/* 表单页面 start */
.y-page-form {}

/* 表单页面 end */

/* 排序拖拽 start */
.y-sorthandle {
  cursor: grab;
}

/* 表单页面 end */

.el-table th {
  background-color: #f5f7fa !important;
}

.pagination {
  margin: 20px 0;
  text-align: right;
}

.plugins-tips {
  padding: 20px 10px;
  margin-bottom: 20px;
}

.el-button+.el-tooltip {
  margin-left: 10px;
}

.el-table tr:hover {
  background: #f6faff;
}

.mgt10 {
  margin-top: 10px;
}

.mgl10 {
  margin-left: 10px;
}

.mgr10 {
  margin-right: 10px;
}

.mgb10 {
  margin-bottom: 10px;
}

.mgb20 {
  margin-bottom: 20px;
}

.move-enter-active,
.move-leave-active {
  transition: opacity .1s ease;
}

.move-enter-from,
.move-leave-to {
  opacity: 0;
}

/*BaseForm*/
.form-box .line {
  text-align: center;
}

.el-time-panel__content::after,
.el-time-panel__content::before {
  margin-top: -7px;
}

.el-time-spinner__wrapper .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
  padding-bottom: 0;
}


/*VueEditor*/

.ql-container {
  min-height: 400px;
}

.ql-snow .ql-tooltip {
  transform: translateX(117.5px) translateY(10px) !important;
}

.editor-btn {
  margin-top: 20px;
}

/*markdown*/

/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "element-ui/packages/theme-chalk/src/index";
// @import './icon.scss';

@font-face {
  font-family: "iconfont"; /* Project id 4978247 */
  src: url('//at.alicdn.com/t/c/font_4978247_fmoetd1oohj.woff2?t=1753347355297') format('woff2'),
       url('//at.alicdn.com/t/c/font_4978247_fmoetd1oohj.woff?t=1753347355297') format('woff'),
       url('//at.alicdn.com/t/c/font_4978247_fmoetd1oohj.ttf?t=1753347355297') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-biaoqian:before {
  content: "\e62c";
}

.icon-download:before {
  content: "\e7ef";
}

.icon-left-1:before {
  content: "\e603";
}

.icon-right-1-copy:before {
  content: "\e61f";
}

.icon-gougou:before {
  content: "\e63f";
}

.icon-gou:before {
  content: "\e643";
}

