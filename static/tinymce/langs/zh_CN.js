tinymce.addI18n('zh_CN', {
  'Redo': '\u91cd\u590d',
  'Undo': '\u64a4\u6d88',
  'Cut': '\u526a\u5207',
  'Copy': '\u590d\u5236',
  'Paste': '\u7c98\u8d34',
  'Select all': '\u5168\u9009',
  'New document': '\u65b0\u6587\u6863',
  'Ok': '\u786e\u5b9a',
  'Cancel': '\u53d6\u6d88',
  'Visual aids': '\u7f51\u683c\u7ebf',
  'Bold': '\u7c97\u4f53',
  'Italic': '\u659c\u4f53',
  'Underline': '\u4e0b\u5212\u7ebf',
  'Strikethrough': '\u5220\u9664\u7ebf',
  'Superscript': '\u4e0a\u6807',
  'Subscript': '\u4e0b\u6807',
  'Clear formatting': '\u6e05\u9664\u683c\u5f0f',
  'Align left': '\u5de6\u5bf9\u9f50',
  'Align center': '\u5c45\u4e2d',
  'Align right': '\u53f3\u5bf9\u9f50',
  'Justify': '\u4e24\u7aef\u5bf9\u9f50',
  'Bullet list': '\u9879\u76ee\u7b26\u53f7',
  'Numbered list': '\u7f16\u53f7\u5217\u8868',
  'Decrease indent': '\u51cf\u5c11\u7f29\u8fdb',
  'Increase indent': '\u589e\u52a0\u7f29\u8fdb',
  'Close': '\u5173\u95ed',
  'Formats': '\u683c\u5f0f',
  "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": '\u4f60\u7684\u6d4f\u89c8\u5668\u4e0d\u652f\u6301\u5bf9\u526a\u8d34\u677f\u7684\u8bbf\u95ee\uff0c\u8bf7\u4f7f\u7528Ctrl+X\/C\/V\u952e\u8fdb\u884c\u590d\u5236\u7c98\u8d34\u3002',
  'Headers': '\u6807\u9898',
  'Header 1': '\u6807\u98981',
  'Header 2': '\u6807\u98982',
  'Header 3': '\u6807\u98983',
  'Header 4': '\u6807\u98984',
  'Header 5': '\u6807\u98985',
  'Header 6': '\u6807\u98986',
  'Headings': '\u6807\u9898',
  'Heading 1': '\u6807\u98981',
  'Heading 2': '\u6807\u98982',
  'Heading 3': '\u6807\u98983',
  'Heading 4': '\u6807\u98984',
  'Heading 5': '\u6807\u98985',
  'Heading 6': '\u6807\u98986',
  'Preformatted': '\u9884\u683c\u5f0f\u5316',
  'Div': 'Div\u533a\u5757',
  'Pre': '\u9884\u683c\u5f0f\u6587\u672c',
  'Code': '\u4ee3\u7801',
  'Paragraph': '\u6bb5\u843d',
  'Blockquote': '\u5f15\u7528',
  'Inline': '\u6587\u672c',
  'Blocks': '\u533a\u5757',
  'Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.': '\u5f53\u524d\u4e3a\u7eaf\u6587\u672c\u7c98\u8d34\u6a21\u5f0f\uff0c\u518d\u6b21\u70b9\u51fb\u53ef\u4ee5\u56de\u5230\u666e\u901a\u7c98\u8d34\u6a21\u5f0f\u3002',
  'Font Family': '\u5b57\u4f53',
  'Font Sizes': '\u5b57\u53f7',
  'Class': 'Class',
  'Browse for an image': '\u6d4f\u89c8\u56fe\u50cf',
  'OR': '\u6216',
  'Drop an image here': '\u62d6\u653e\u4e00\u5f20\u56fe\u50cf\u81f3\u6b64',
  'Upload': '\u4e0a\u4f20',
  'Block': '\u5757',
  'Align': '\u5bf9\u9f50',
  'Default': '\u9ed8\u8ba4',
  'Circle': '\u7a7a\u5fc3\u5706',
  'Disc': '\u5b9e\u5fc3\u5706',
  'Square': '\u65b9\u5757',
  'Lower Alpha': '\u5c0f\u5199\u82f1\u6587\u5b57\u6bcd',
  'Lower Greek': '\u5c0f\u5199\u5e0c\u814a\u5b57\u6bcd',
  'Lower Roman': '\u5c0f\u5199\u7f57\u9a6c\u5b57\u6bcd',
  'Upper Alpha': '\u5927\u5199\u82f1\u6587\u5b57\u6bcd',
  'Upper Roman': '\u5927\u5199\u7f57\u9a6c\u5b57\u6bcd',
  'Anchor': '\u951a\u70b9',
  'Name': '\u540d\u79f0',
  'Id': '\u6807\u8bc6\u7b26',
  'Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.': '\u6807\u8bc6\u7b26\u5e94\u8be5\u4ee5\u5b57\u6bcd\u5f00\u5934\uff0c\u540e\u8ddf\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u7834\u6298\u53f7\u3001\u70b9\u3001\u5192\u53f7\u6216\u4e0b\u5212\u7ebf\u3002',
  'You have unsaved changes are you sure you want to navigate away?': '\u4f60\u8fd8\u6709\u6587\u6863\u5c1a\u672a\u4fdd\u5b58\uff0c\u786e\u5b9a\u8981\u79bb\u5f00\uff1f',
  'Restore last draft': '\u6062\u590d\u4e0a\u6b21\u7684\u8349\u7a3f',
  'Special character': '\u7279\u6b8a\u7b26\u53f7',
  'Source code': '\u6e90\u4ee3\u7801',
  'Insert\/Edit code sample': '\u63d2\u5165\/\u7f16\u8f91\u4ee3\u7801\u793a\u4f8b',
  'Language': '\u8bed\u8a00',
  'Code sample': '\u4ee3\u7801\u793a\u4f8b',
  'Color': '\u989c\u8272',
  'R': 'R',
  'G': 'G',
  'B': 'B',
  'Left to right': '\u4ece\u5de6\u5230\u53f3',
  'Right to left': '\u4ece\u53f3\u5230\u5de6',
  'Emoticons': '\u8868\u60c5',
  'Document properties': '\u6587\u6863\u5c5e\u6027',
  'Title': '\u6807\u9898',
  'Keywords': '\u5173\u952e\u8bcd',
  'Description': '\u63cf\u8ff0',
  'Robots': '\u673a\u5668\u4eba',
  'Author': '\u4f5c\u8005',
  'Encoding': '\u7f16\u7801',
  'Fullscreen': '\u5168\u5c4f',
  'Action': '\u64cd\u4f5c',
  'Shortcut': '\u5feb\u6377\u952e',
  'Help': '\u5e2e\u52a9',
  'Address': '\u5730\u5740',
  'Focus to menubar': '\u79fb\u52a8\u7126\u70b9\u5230\u83dc\u5355\u680f',
  'Focus to toolbar': '\u79fb\u52a8\u7126\u70b9\u5230\u5de5\u5177\u680f',
  'Focus to element path': '\u79fb\u52a8\u7126\u70b9\u5230\u5143\u7d20\u8def\u5f84',
  'Focus to contextual toolbar': '\u79fb\u52a8\u7126\u70b9\u5230\u4e0a\u4e0b\u6587\u83dc\u5355',
  'Insert link (if link plugin activated)': '\u63d2\u5165\u94fe\u63a5 (\u5982\u679c\u94fe\u63a5\u63d2\u4ef6\u5df2\u6fc0\u6d3b)',
  'Save (if save plugin activated)': '\u4fdd\u5b58(\u5982\u679c\u4fdd\u5b58\u63d2\u4ef6\u5df2\u6fc0\u6d3b)',
  'Find (if searchreplace plugin activated)': '\u67e5\u627e(\u5982\u679c\u67e5\u627e\u66ff\u6362\u63d2\u4ef6\u5df2\u6fc0\u6d3b)',
  'Plugins installed ({0}):': '\u5df2\u5b89\u88c5\u63d2\u4ef6 ({0}):',
  'Premium plugins:': '\u4f18\u79c0\u63d2\u4ef6\uff1a',
  'Learn more...': '\u4e86\u89e3\u66f4\u591a...',
  'You are using {0}': '\u4f60\u6b63\u5728\u4f7f\u7528 {0}',
  'Plugins': '\u63d2\u4ef6',
  'Handy Shortcuts': '\u5feb\u6377\u952e',
  'Horizontal line': '\u6c34\u5e73\u5206\u5272\u7ebf',
  'Insert\/edit image': '\u63d2\u5165\/\u7f16\u8f91\u56fe\u7247',
  'Image description': '\u56fe\u7247\u63cf\u8ff0',
  'Source': '\u5730\u5740',
  'Dimensions': '\u5927\u5c0f',
  'Constrain proportions': '\u4fdd\u6301\u7eb5\u6a2a\u6bd4',
  'General': '\u666e\u901a',
  'Advanced': '\u9ad8\u7ea7',
  'Style': '\u6837\u5f0f',
  'Vertical space': '\u5782\u76f4\u8fb9\u8ddd',
  'Horizontal space': '\u6c34\u5e73\u8fb9\u8ddd',
  'Border': '\u8fb9\u6846',
  'Insert image': '\u63d2\u5165\u56fe\u7247',
  'Image': '\u56fe\u7247',
  'Image list': '\u56fe\u7247\u5217\u8868',
  'Rotate counterclockwise': '\u9006\u65f6\u9488\u65cb\u8f6c',
  'Rotate clockwise': '\u987a\u65f6\u9488\u65cb\u8f6c',
  'Flip vertically': '\u5782\u76f4\u7ffb\u8f6c',
  'Flip horizontally': '\u6c34\u5e73\u7ffb\u8f6c',
  'Edit image': '\u7f16\u8f91\u56fe\u7247',
  'Image options': '\u56fe\u7247\u9009\u9879',
  'Zoom in': '\u653e\u5927',
  'Zoom out': '\u7f29\u5c0f',
  'Crop': '\u88c1\u526a',
  'Resize': '\u8c03\u6574\u5927\u5c0f',
  'Orientation': '\u65b9\u5411',
  'Brightness': '\u4eae\u5ea6',
  'Sharpen': '\u9510\u5316',
  'Contrast': '\u5bf9\u6bd4\u5ea6',
  'Color levels': '\u989c\u8272\u5c42\u6b21',
  'Gamma': '\u4f3d\u9a6c\u503c',
  'Invert': '\u53cd\u8f6c',
  'Apply': '\u5e94\u7528',
  'Back': '\u540e\u9000',
  'Insert date\/time': '\u63d2\u5165\u65e5\u671f\/\u65f6\u95f4',
  'Date\/time': '\u65e5\u671f\/\u65f6\u95f4',
  'Insert link': '\u63d2\u5165\u94fe\u63a5',
  'Insert\/edit link': '\u63d2\u5165\/\u7f16\u8f91\u94fe\u63a5',
  'Text to display': '\u663e\u793a\u6587\u5b57',
  'Url': '\u5730\u5740',
  'Target': '\u6253\u5f00\u65b9\u5f0f',
  'None': '\u65e0',
  'New window': '\u5728\u65b0\u7a97\u53e3\u6253\u5f00',
  'Remove link': '\u5220\u9664\u94fe\u63a5',
  'Anchors': '\u951a\u70b9',
  'Link': '\u94fe\u63a5',
  'Paste or type a link': '\u7c98\u8d34\u6216\u8f93\u5165\u94fe\u63a5',
  'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?': '\u4f60\u6240\u586b\u5199\u7684URL\u5730\u5740\u4e3a\u90ae\u4ef6\u5730\u5740\uff0c\u9700\u8981\u52a0\u4e0amailto:\u524d\u7f00\u5417\uff1f',
  'The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?': '\u4f60\u6240\u586b\u5199\u7684URL\u5730\u5740\u5c5e\u4e8e\u5916\u90e8\u94fe\u63a5\uff0c\u9700\u8981\u52a0\u4e0ahttp:\/\/:\u524d\u7f00\u5417\uff1f',
  'Link list': '\u94fe\u63a5\u5217\u8868',
  'Insert video': '\u63d2\u5165\u89c6\u9891',
  'Insert\/edit video': '\u63d2\u5165\/\u7f16\u8f91\u89c6\u9891',
  'Insert\/edit media': '\u63d2\u5165\/\u7f16\u8f91\u5a92\u4f53',
  'Alternative source': '\u955c\u50cf',
  'Poster': '\u5c01\u9762',
  'Paste your embed code below:': '\u5c06\u5185\u5d4c\u4ee3\u7801\u7c98\u8d34\u5728\u4e0b\u9762:',
  'Embed': '\u5185\u5d4c',
  'Media': '\u5a92\u4f53',
  'Nonbreaking space': '\u4e0d\u95f4\u65ad\u7a7a\u683c',
  'Page break': '\u5206\u9875\u7b26',
  'Paste as text': '\u7c98\u8d34\u4e3a\u6587\u672c',
  'Preview': '\u9884\u89c8',
  'Print': '\u6253\u5370',
  'Save': '\u4fdd\u5b58',
  'Find': '\u67e5\u627e',
  'Replace with': '\u66ff\u6362\u4e3a',
  'Replace': '\u66ff\u6362',
  'Replace all': '\u5168\u90e8\u66ff\u6362',
  'Prev': '\u4e0a\u4e00\u4e2a',
  'Next': '\u4e0b\u4e00\u4e2a',
  'Find and replace': '\u67e5\u627e\u548c\u66ff\u6362',
  'Could not find the specified string.': '\u672a\u627e\u5230\u641c\u7d22\u5185\u5bb9.',
  'Match case': '\u533a\u5206\u5927\u5c0f\u5199',
  'Whole words': '\u5168\u5b57\u5339\u914d',
  'Spellcheck': '\u62fc\u5199\u68c0\u67e5',
  'Ignore': '\u5ffd\u7565',
  'Ignore all': '\u5168\u90e8\u5ffd\u7565',
  'Finish': '\u5b8c\u6210',
  'Add to Dictionary': '\u6dfb\u52a0\u5230\u5b57\u5178',
  'Insert table': '\u63d2\u5165\u8868\u683c',
  'Table properties': '\u8868\u683c\u5c5e\u6027',
  'Delete table': '\u5220\u9664\u8868\u683c',
  'Cell': '\u5355\u5143\u683c',
  'Row': '\u884c',
  'Column': '\u5217',
  'Cell properties': '\u5355\u5143\u683c\u5c5e\u6027',
  'Merge cells': '\u5408\u5e76\u5355\u5143\u683c',
  'Split cell': '\u62c6\u5206\u5355\u5143\u683c',
  'Insert row before': '\u5728\u4e0a\u65b9\u63d2\u5165',
  'Insert row after': '\u5728\u4e0b\u65b9\u63d2\u5165',
  'Delete row': '\u5220\u9664\u884c',
  'Row properties': '\u884c\u5c5e\u6027',
  'Cut row': '\u526a\u5207\u884c',
  'Copy row': '\u590d\u5236\u884c',
  'Paste row before': '\u7c98\u8d34\u5230\u4e0a\u65b9',
  'Paste row after': '\u7c98\u8d34\u5230\u4e0b\u65b9',
  'Insert column before': '\u5728\u5de6\u4fa7\u63d2\u5165',
  'Insert column after': '\u5728\u53f3\u4fa7\u63d2\u5165',
  'Delete column': '\u5220\u9664\u5217',
  'Cols': '\u5217',
  'Rows': '\u884c',
  'Width': '\u5bbd',
  'Height': '\u9ad8',
  'Cell spacing': '\u5355\u5143\u683c\u5916\u95f4\u8ddd',
  'Cell padding': '\u5355\u5143\u683c\u5185\u8fb9\u8ddd',
  'Caption': '\u6807\u9898',
  'Left': '\u5de6\u5bf9\u9f50',
  'Center': '\u5c45\u4e2d',
  'Right': '\u53f3\u5bf9\u9f50',
  'Cell type': '\u5355\u5143\u683c\u7c7b\u578b',
  'Scope': '\u8303\u56f4',
  'Alignment': '\u5bf9\u9f50\u65b9\u5f0f',
  'H Align': '\u6c34\u5e73\u5bf9\u9f50',
  'V Align': '\u5782\u76f4\u5bf9\u9f50',
  'Top': '\u9876\u90e8\u5bf9\u9f50',
  'Middle': '\u5782\u76f4\u5c45\u4e2d',
  'Bottom': '\u5e95\u90e8\u5bf9\u9f50',
  'Header cell': '\u8868\u5934\u5355\u5143\u683c',
  'Row group': '\u884c\u7ec4',
  'Column group': '\u5217\u7ec4',
  'Row type': '\u884c\u7c7b\u578b',
  'Header': '\u8868\u5934',
  'Body': '\u8868\u4f53',
  'Footer': '\u8868\u5c3e',
  'Border color': '\u8fb9\u6846\u989c\u8272',
  'Insert template': '\u63d2\u5165\u6a21\u677f',
  'Templates': '\u6a21\u677f',
  'Template': '\u6a21\u677f',
  'Text color': '\u6587\u5b57\u989c\u8272',
  'Background color': '\u80cc\u666f\u8272',
  'Custom...': '\u81ea\u5b9a\u4e49...',
  'Custom color': '\u81ea\u5b9a\u4e49\u989c\u8272',
  'No color': '\u65e0',
  'Table of Contents': '\u5185\u5bb9\u5217\u8868',
  'Show blocks': '\u663e\u793a\u533a\u5757\u8fb9\u6846',
  'Show invisible characters': '\u663e\u793a\u4e0d\u53ef\u89c1\u5b57\u7b26',
  'Words: {0}': '\u5b57\u6570\uff1a{0}',
  '{0} words': '{0} \u5b57',
  'File': '\u6587\u4ef6',
  'Edit': '\u7f16\u8f91',
  'Insert': '\u63d2\u5165',
  'View': '\u89c6\u56fe',
  'Format': '\u683c\u5f0f',
  'Table': '\u8868\u683c',
  'Tools': '\u5de5\u5177',
  'Powered by {0}': '\u7531{0}\u9a71\u52a8',
  'Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help': '\u5728\u7f16\u8f91\u533a\u6309ALT-F9\u6253\u5f00\u83dc\u5355\uff0c\u6309ALT-F10\u6253\u5f00\u5de5\u5177\u680f\uff0c\u6309ALT-0\u67e5\u770b\u5e2e\u52a9'
})
