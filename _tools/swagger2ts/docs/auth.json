{"openapi": "3.0.1", "info": {"title": "工程服务", "description": "工程服务", "contact": {"name": "cloud", "url": "{url}", "email": "<EMAIL>"}, "version": "202407261407"}, "externalDocs": {"description": "工程服务 系统"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "tags": [{"name": "支付", "description": "支付接口"}, {"name": "连接信息", "description": "连接信息接口"}, {"name": "短信", "description": "短信接口"}, {"name": "阿里云短信", "description": "阿里云短信"}, {"name": "支付订单接口", "description": "支付订单接口"}, {"name": "阿里云文件操作类", "description": "阿里云文件操作接口"}, {"name": "模板合成服务", "description": "模板合成服务"}, {"name": "报告查询接口", "description": "报告查询接口"}, {"name": "报告执行接口", "description": "报告执行接口"}, {"name": "[mg]内容信息", "description": "内容信息接口"}, {"name": "退款账单接口", "description": "退款账单接口"}, {"name": "支付账单接口", "description": "支付账单接口"}, {"name": "小程序消息记录", "description": "小程序消息记录接口"}, {"name": "支付账户", "description": "支付账户接口"}, {"name": "验证码", "description": "验证码"}, {"name": "短信记录", "description": "短信记录接口"}, {"name": "站内消息", "description": "站内消息接口"}, {"name": "邮件记录", "description": "邮件记录接口"}, {"name": "[open]开放内容接口", "description": "开放内容接口"}, {"name": "工具类", "description": "工具类接口"}, {"name": "短信回复", "description": "短信回复接口"}, {"name": "webSocket长连接消息", "description": "webSocket长连接消息"}, {"name": "文件操作类", "description": "文件操作接口"}, {"name": "[api]登录接口", "description": "登录过程为：调用用户效验接口，或者rid，再调用用户登录接口获得授权token。"}, {"name": "角色管理接口", "description": "角色管理接口"}, {"name": "邮件消息", "description": "邮件消息接口"}, {"name": "[mg]网站主题", "description": "网站主题接口,参考具体CMS内容描述"}, {"name": "[mg]CMS分类", "description": "CMS分类接口"}, {"name": "[open]开放主题接口", "description": "开放主题接口"}, {"name": "权限管理接口", "description": "权限管理接口"}, {"name": "角色授权管理接口", "description": "角色授权管理接口"}, {"name": "微信小程序码", "description": "微信小程序码消息接口"}, {"name": "退款", "description": "退款接口"}, {"name": "微信小程序消息", "description": "微信小程序消息接口"}], "paths": {"/sys/report/query/{id}": {"get": {"tags": ["报告查询接口"], "summary": "查询信息", "operationId": "get_4", "parameters": [{"name": "id", "in": "path", "description": "ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReportQueryVo_t1241"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["报告查询接口"], "summary": "修改数据", "operationId": "modify", "parameters": [{"name": "id", "in": "path", "description": "ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportQueryAddVo_t7333"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReportQueryVo_t1241"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["报告查询接口"], "summary": "删除查询数据", "operationId": "delete", "parameters": [{"name": "id", "in": "path", "description": "ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/report/query/{id}/columns": {"put": {"tags": ["报告查询接口"], "summary": "修改字段数据", "operationId": "modifyColumns", "parameters": [{"name": "id", "in": "path", "description": "ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportQueryModifyColumnVo_t5715"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReportQueryVo_t1241"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/{id}/notify": {"put": {"tags": ["站内消息"], "summary": "已读标记", "operationId": "putNotify", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxPutNotifyVo_t4675"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/{id}/del": {"put": {"tags": ["站内消息"], "summary": "删除标记", "operationId": "putDel", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxPutDelVo_t3457"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/{id}/notify": {"put": {"tags": ["站内消息"], "summary": "已读标记", "operationId": "putNotifyByMe", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxPutNotifyVo_t4675"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/{id}/del": {"put": {"tags": ["站内消息"], "summary": "删除标记", "operationId": "putDelByMe", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxPutDelVo_t3457"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/all/notify": {"put": {"tags": ["站内消息"], "summary": "全部标记为已读", "operationId": "putAllNotifyByMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxPutNotifyVo_t4675"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/all/del": {"put": {"tags": ["站内消息"], "summary": "全部标记为删除", "operationId": "putAllDelByMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxPutDelVo_t3457"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/{id}": {"get": {"tags": ["[mg]商品信息"], "summary": "获取商品详情", "operationId": "get_10", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MallProductDetailVo_t2398"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["[mg]商品信息"], "summary": "修改商品信息", "operationId": "modify_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "商品Id", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallProductModifyVo_t4675"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MallProductDetailVo_t2398"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/category/{id}": {"get": {"tags": ["[manage]分类信息"], "summary": "获取详情", "operationId": "get_11", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MallCategoryDetailVo_t5081"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["[manage]分类信息"], "summary": "修改", "operationId": "modify_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallCategoryAddVo_t975"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["[manage]分类信息"], "summary": "删除", "operationId": "delete_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/content/{id}": {"get": {"tags": ["[mg]内容信息"], "summary": "获取详情", "operationId": "get_12", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["[mg]内容信息"], "summary": "变更内容信息", "operationId": "update", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "文章信息详情"}}}, "delete": {"tags": ["[mg]内容信息"], "summary": "删除内容信息", "operationId": "delete_3", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "删除除内容信息成功"}}}}, "/cms/mg/subject/{subject_id}/content/{id}/top": {"put": {"tags": ["[mg]内容信息"], "operationId": "top", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CmsContentTopVo_t4425"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/content/{id}/open": {"put": {"tags": ["[mg]内容信息"], "operationId": "open", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CmsContentOpenVo_t2997"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/category/{id}": {"get": {"tags": ["[mg]CMS分类"], "summary": "获取详情", "operationId": "get_13", "parameters": [{"name": "subject_id", "in": "path", "description": "主题id", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CmsCategory_t7361"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["[mg]CMS分类"], "summary": "更新分类", "operationId": "update_1", "parameters": [{"name": "subject_id", "in": "path", "description": "主题id", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CmsCategoryModifyVo_t5809"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "更新分类成功"}}}, "delete": {"tags": ["[mg]CMS分类"], "summary": "删除分类", "operationId": "delete_4", "parameters": [{"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "删除分类成功"}}}}, "/cms/mg/subject/{id}": {"get": {"tags": ["[mg]网站主题"], "summary": "获取详情", "operationId": "get_14", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CmsSubjectDetailVo_t5881"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["[mg]网站主题"], "summary": "更新网站主题", "operationId": "update_2", "parameters": [{"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CmsSubjectModifyVo_t8159"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "更新网站主题成功"}}}, "delete": {"tags": ["[mg]网站主题"], "summary": "删除网站主题", "operationId": "delete_5", "parameters": [{"name": "id", "in": "path", "description": "Id", "required": true, "schema": {"type": "string"}}], "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "删除网站主题成功"}}}}, "/auth/sys/config/{id}": {"get": {"tags": ["系统配置信息"], "summary": "系统配置详情", "operationId": "get_15", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysConfig_t5574"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["系统配置信息"], "summary": "修改", "operationId": "modify_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysConfigAddVo_t2271"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["系统配置信息"], "summary": "删除", "operationId": "delete_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/role/{code}": {"get": {"tags": ["角色管理接口"], "summary": "获取角色", "operationId": "get_16", "parameters": [{"name": "code", "in": "path", "description": "角色code", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysRole_t5325"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["角色管理接口"], "summary": "更新角色信息", "operationId": "modify_4", "parameters": [{"name": "code", "in": "path", "description": "权限code", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleModifyVo_t2680"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["角色管理接口"], "summary": "删除角色", "operationId": "delete_7", "parameters": [{"name": "code", "in": "path", "description": "权限code", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/power/{code}": {"get": {"tags": ["权限管理接口"], "summary": "获取权限", "operationId": "get_17", "parameters": [{"name": "code", "in": "path", "description": "权限code", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysPower_t4562"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["权限管理接口"], "summary": "更新权限信息", "operationId": "modify_5", "parameters": [{"name": "code", "in": "path", "description": "权限code", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysPowerModifyVo_t9923"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["权限管理接口"], "summary": "删除权限", "operationId": "delete_9", "parameters": [{"name": "code", "in": "path", "description": "权限code", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/dic/dic/{id}": {"get": {"tags": ["系统数据字典"], "summary": "系统数据字典详情", "operationId": "get_18", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysDicBo_t5099"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "put": {"tags": ["系统数据字典"], "summary": "修改", "operationId": "modify_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDicAddVo_t4643"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["系统数据字典"], "summary": "删除", "operationId": "delete_10", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/wx/mp/msg/verify": {"post": {"tags": ["微信小程序消息"], "summary": "发送验证码", "operationId": "sendVerify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WxMpVerifySendVo_t9831"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WxMpRespVo_t8596"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/wx/mp/msg/send": {"post": {"tags": ["微信小程序消息"], "summary": "发送信息", "operationId": "send", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WxMpSendVo_t7179"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WxMpRespVo_t8596"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/wx/mp/msg/batch/send": {"post": {"tags": ["微信小程序消息"], "summary": "批量发送", "operationId": "sendBatch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WxMpBatchSendVo_t6063"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WxMpBatchRespVo_t7480"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/ws-msg/send": {"post": {"tags": ["webSocket长连接消息"], "summary": "发送", "operationId": "send_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WsMsgPushBo_t3436"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/ws-msg/batch/send": {"post": {"tags": ["webSocket长连接消息"], "summary": "批量发送", "operationId": "sendBatch_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WsMsgBatchSendVo_t4893"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/tmpl/convert": {"post": {"tags": ["模板合成服务"], "summary": "模板服务，数据组装，响应文件", "operationId": "convert", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TmplConvertParamVo_t464"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms/verify": {"post": {"tags": ["短信"], "summary": "发送短信验证码", "operationId": "sendVerify_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsVerifySendVo_t5680"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SmsRespVo_t7758"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms/send": {"post": {"tags": ["短信"], "summary": "发送短信", "operationId": "send_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsSendVo_t6341"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SmsRespVo_t7758"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms/batch/send": {"post": {"tags": ["短信"], "summary": "批量发送短信", "operationId": "sendBatch_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsBatchSendVo_t3230"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SmsBatchRespVo_t4647"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms-aliyun/send/callback": {"post": {"tags": ["阿里云短信"], "summary": "获取短信记录", "operationId": "callback", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SmsAliyunController$SmsReportBo_t7318"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AliSmsReportVo_t7746"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/res-link": {"post": {"tags": ["连接信息"], "summary": "创建连接", "operationId": "post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResLinkAddVo_t5021"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResLinkRespVo_t6943"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/report/query": {"get": {"tags": ["报告查询接口"], "summary": "查询列表信息", "operationId": "getList", "parameters": [{"name": "rows", "in": "query", "description": "每页条数", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "description": "页码，从0开始", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "code", "in": "query", "description": "Code信息", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReportQuery_t675"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["报告查询接口"], "summary": "创建数据", "operationId": "add", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportQueryAddVo_t7333"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReportQueryVo_t1241"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/report/exec/query/{code}/submit": {"post": {"tags": ["报告执行接口"], "summary": "提交任务查询任务", "operationId": "submit", "parameters": [{"name": "code", "in": "path", "description": "Code信息", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskProgressBo_t2229"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/report/exec/query/progress/{pid}": {"post": {"tags": ["报告执行接口"], "summary": "任务进度查询", "operationId": "progress", "parameters": [{"name": "pid", "in": "path", "description": "任务id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TaskProgressBo_t2229"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/send": {"post": {"tags": ["站内消息"], "summary": "发送站内", "operationId": "send_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxSendBo_t5778"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/batch/send": {"post": {"tags": ["站内消息"], "summary": "批量发送站内", "operationId": "sendBatch_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxBatchSendVo_t2216"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs": {"post": {"tags": ["文件操作类"], "summary": "文件上传接口", "operationId": "upload", "parameters": [{"name": "filename", "in": "query", "description": "文件名称", "required": false, "schema": {"type": "string"}}, {"name": "uuid", "in": "query", "description": "UUID", "required": false, "schema": {"type": "string"}}, {"name": "secrecy", "in": "query", "description": "保密文件，不公开", "required": false, "schema": {"type": "boolean"}}, {"name": "exp", "in": "query", "description": "过期时长，单位：秒（S）", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "content-type", "in": "header", "schema": {"type": "string", "enum": ["multipart/form-data"]}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "description": "文件数据", "format": "binary"}}}}, "multipart/*": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "description": "文件数据", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResDoc_t3377"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/text": {"post": {"tags": ["文件操作类"], "summary": "文本上传接口", "operationId": "text", "parameters": [{"name": "uuid", "in": "query", "description": "UUID", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FsUpdateTextVo_t4312"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResDoc_t3377"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/query_by_md5": {"post": {"tags": ["文件操作类"], "summary": "通过MD5获取文件信息", "operationId": "getListByMd5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FsQueryListByMd5ParamVo_t4338"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ResDoc_t3377"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/batch/query/info": {"post": {"tags": ["文件操作类"], "summary": "批量文件信息查询", "operationId": "infos", "parameters": [{"name": "uuid", "in": "query", "required": true, "schema": {"title": "uuid", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileInfoParamVo_t3318"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ResDoc_t3377"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/aliyun": {"post": {"tags": ["阿里云文件操作类"], "summary": "上传文件请求", "operationId": "uploadRequest", "parameters": [{"name": "uuid", "in": "query", "description": "UUID", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FsUploadPreParamVo_t854"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FsAliyunUploadPreVo_t1264"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/aliyun/{id}/uploaded": {"post": {"tags": ["阿里云文件操作类"], "summary": "上传完成标识符", "operationId": "uploaded", "parameters": [{"name": "id", "in": "path", "description": "文件id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/email/verify": {"post": {"tags": ["邮件消息"], "summary": "发送验证码", "operationId": "sendVerify_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailVerifySendVo_t4385"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EmailRespVo_t9854"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/email/send": {"post": {"tags": ["邮件消息"], "summary": "发送信息", "operationId": "send_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailSendVo_t8437"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EmailRespVo_t9854"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/refund/{id}/notify": {"get": {"tags": ["退款"], "summary": "退款回调GET", "operationId": "refundNotifyGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["退款"], "summary": "退款回调POST", "operationId": "refundNotifyPost", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/refund-bill": {"get": {"tags": ["退款账单接口"], "summary": "获取账单详情信息", "operationId": "getList_2", "parameters": [{"name": "subject_id", "in": "query", "required": true, "schema": {"title": "退款单号", "type": "string"}}, {"name": "bill_id", "in": "query", "required": true, "schema": {"title": "支付单号", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RefundBillVo_t7765"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["退款账单接口"], "summary": "创建以及修改账单", "operationId": "add_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundBillAddVo_t5096"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RefundBillRespVo_t9283"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay/{id}": {"get": {"tags": ["支付"], "summary": "支付页面", "operationId": "payUrl", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "account_id", "in": "query", "description": "account_id", "required": false, "schema": {"type": "string"}}, {"name": "open_id", "in": "query", "description": "open_id", "required": false, "schema": {"type": "string"}}, {"name": "device_code", "in": "query", "description": "设备code（COMPUTER、H5、WX_MP）", "required": false, "schema": {"type": "string"}}, {"name": "return_url", "in": "query", "description": "回跳地址", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["支付"], "summary": "支付页面", "operationId": "payUrlPost", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "account_id", "in": "query", "description": "account_id", "required": false, "schema": {"type": "string"}}, {"name": "open_id", "in": "query", "description": "open_id", "required": false, "schema": {"type": "string"}}, {"name": "device_code", "in": "query", "description": "设备code（COMPUTER、H5、WX_MP）", "required": false, "schema": {"type": "string"}}, {"name": "return_url", "in": "query", "description": "回跳地址", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay/{id}/return": {"get": {"tags": ["支付"], "summary": "支付回调GET", "operationId": "payReturnGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["支付"], "summary": "支付回调POST", "operationId": "payReturnPost", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay/{id}/notify": {"get": {"tags": ["支付"], "summary": "支付回调GET", "operationId": "payNotifyGet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["支付"], "summary": "支付回调POST", "operationId": "payNotifyPost", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay-orders": {"get": {"tags": ["支付订单接口"], "summary": "获取账单详情信息", "operationId": "getList_3", "parameters": [{"name": "subject_type", "in": "query", "required": true, "schema": {"title": "主题类型", "type": "string"}}, {"name": "subject_id", "in": "query", "required": true, "schema": {"title": "主题id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PayOrdersVo_t3263"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["支付订单接口"], "summary": "创建以及修改账单", "operationId": "add_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayOrdersAddVo_t4918"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PayOrdersRespVo_t1869"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product": {"get": {"tags": ["[mg]商品信息"], "summary": "获取商品列表", "operationId": "getList_6", "parameters": [{"name": "rows", "in": "query", "required": false, "schema": {"title": "每页多少行", "type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"title": "页码，从0开始", "type": "string"}}, {"name": "sort", "in": "query", "required": false, "schema": {"title": "排序字段", "type": "string"}}, {"name": "keyword", "in": "query", "required": false, "schema": {"title": "关键词", "type": "string"}}, {"name": "storeId", "in": "query", "required": false, "schema": {"title": "供应商Id", "type": "string"}}, {"name": "state", "in": "query", "required": false, "schema": {"title": "商品状态字段[UP/DOWN]", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MallProductListVo_t760"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["[mg]商品信息"], "summary": "添加商品信息", "operationId": "add_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallProductAddVo_t618"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MallProductDetailVo_t2398"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/{id}/up": {"post": {"tags": ["[mg]商品信息"], "summary": "上下架审核", "operationId": "up", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "商品Id", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallProductUpVo_t5271"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/{id}/archived": {"post": {"tags": ["[mg]商品信息"], "summary": "商品归档", "operationId": "archived", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "商品id", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallProductArchivedVo_t6368"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock/batch/modify": {"post": {"tags": ["[mg]商品库存信息"], "summary": "批量修改接口", "operationId": "modifyStock", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockModifyOneDepotSpacesParamVo_t2728"}}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock/batch/modify-attr": {"post": {"tags": ["[mg]商品库存信息"], "summary": "修改库存信息", "operationId": "batchModify", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockModifyAttrParamBo_t4921"}}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock/batch/delete": {"post": {"tags": ["[mg]商品库存信息"], "summary": "删除库存信息", "operationId": "batchDelete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockDeleteParamVo_t3357"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock/batch/deduct": {"post": {"tags": ["[mg]商品库存信息"], "summary": "调整库存", "operationId": "batchDeduct", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockDeductParamVo_t3226"}}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock/batch/add": {"post": {"tags": ["[mg]商品库存信息"], "summary": "批量添加库存信息", "operationId": "batchAdd", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StockAddParamVo_t9690"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MallProductStock_t9121"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/category": {"get": {"tags": ["[manage]分类信息"], "summary": "获取列表信息", "operationId": "getAllList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MallCategoryListVo_t8243"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["[manage]分类信息"], "summary": "添加", "operationId": "add_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MallCategoryAddVo_t975"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MallCategoryDetailVo_t5081"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/iot/iot/device": {"post": {"tags": ["设备表"], "summary": "添加设备表", "operationId": "insert", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IotDeviceAddVo_t7454"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/IotDevice_t8925"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject": {"get": {"tags": ["[mg]网站主题"], "summary": "获取列表", "operationId": "getList_8", "parameters": [{"name": "keyword", "in": "query", "description": "keyword", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CmsSubject_t9735"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["[mg]网站主题"], "summary": "添加网站主题", "operationId": "insert_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CmsSubjectAddVo_t1934"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "网站主题详情", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CmsSubjectDetailVo_t5881"}}}}}}}, "/cms/mg/subject/{subject_id}/content": {"get": {"tags": ["[mg]内容信息"], "summary": "获取列表", "operationId": "getList_9", "parameters": [{"name": "subject_id", "in": "path", "description": "主题id", "required": true, "schema": {"type": "string"}}, {"name": "_category_id", "in": "query", "description": "分类id", "required": false, "schema": {"type": "string"}}, {"name": "_page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "_rows", "in": "query", "description": "数量", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["[mg]内容信息"], "summary": "添加内容信息", "operationId": "insert_2", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/category": {"get": {"tags": ["[mg]CMS分类"], "summary": "获取列表", "operationId": "getList_10", "parameters": [{"name": "subject_id", "in": "path", "description": "主题id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CmsCategory_t7361"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["[mg]CMS分类"], "summary": "添加分类", "operationId": "insert_3", "parameters": [{"name": "subject_id", "in": "path", "description": "主题id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CmsCategoryAddVo_t1108"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}, "default": {"description": "网站分类", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CmsCategory_t7361"}}}}}}}, "/auth/wx/mp/auth/token": {"post": {"tags": ["微信小程序权限相关接口"], "summary": "注册数据", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WxMpRegisterParamVo_t7678"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenMessageVo_t805"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/wx/mp/auth/session": {"post": {"tags": ["微信小程序权限相关接口"], "summary": "使用code会换session", "operationId": "token", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WxMpSeesionParamVo_t6085"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WxMpSessionVo_t9252"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/user/user-bond": {"get": {"tags": ["用户绑定"], "summary": "获得绑定信息", "operationId": "getList_11", "parameters": [{"name": "ubtoken", "in": "query", "description": "用户绑定token，不能与user_id同时为空", "required": false, "schema": {"type": "string"}}, {"name": "user_id", "in": "query", "description": "当前用户id，不能与ubtoken同时为空", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserBondVo_t1801"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["用户绑定"], "summary": "绑定用户", "operationId": "bond", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBondAddVo_t800"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["用户绑定"], "summary": "解除绑定", "operationId": "unbond", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBondDeleteVo_t4659"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/user/user-bond/token": {"post": {"tags": ["用户绑定"], "summary": "签发token", "operationId": "token_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBondTokenParamVo_t4392"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenMessageVo_t805"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/user/user-bond/by-me": {"get": {"tags": ["用户绑定"], "summary": "获得绑定，用于开放查询信息", "operationId": "getListByMe_1", "parameters": [{"name": "ubtoken", "in": "query", "description": "用户绑定token，不能与user_id同时为空", "required": false, "schema": {"type": "string"}}, {"name": "user_id", "in": "query", "description": "当前用户id，不能与ubtoken同时为空", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserBondVo_t1801"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["用户绑定"], "summary": "绑定当前用户", "operationId": "bondByMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBondAddByMeVo_t3686"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["用户绑定"], "summary": "解除当前用户绑定", "operationId": "unbondByMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBondDeleteByMeVo_t4441"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/user/ubtoken": {"get": {"tags": ["用户绑定token"], "summary": "通过用户token获取绑定信息", "operationId": "getByUbtoken", "parameters": [{"name": "ubtoken", "in": "query", "description": "用户绑定token，不能与user_id同时为空", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UbTokenMsgBo_t1698"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["用户绑定token"], "summary": "签发ubtoken", "operationId": "createUbtoken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UbtokenAddBo_t8002"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UbtokenVo_t863"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/user/info/change-password/by-me": {"post": {"tags": ["用户基础信息"], "summary": "修改密码", "operationId": "changePasswordByMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoChangePasswordMeParamVo_t269"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/sys/config": {"get": {"tags": ["系统配置信息"], "summary": "系统配置列表", "operationId": "getList_12", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysConfig_t5574"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["系统配置信息"], "summary": "添加", "operationId": "add_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysConfigAddVo_t2271"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysConfig_t5574"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/role": {"get": {"tags": ["角色管理接口"], "summary": "获取角色列表", "operationId": "getList_13", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysRole_t5325"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["角色管理接口"], "summary": "新增角色信息", "operationId": "add_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRoleAddVo_t4668"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysRole_t5325"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/role-power": {"get": {"tags": ["角色授权管理接口"], "summary": "获取角色授权信息列表", "operationId": "getList_14", "parameters": [{"name": "role_code", "in": "query", "description": "角色Code", "required": false, "schema": {"type": "string"}}, {"name": "power_code", "in": "query", "description": "权限Code", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysRolePower_t6135"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["角色授权管理接口"], "summary": "增加了授权信息", "operationId": "add_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRolePower_t6135"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysRolePower_t6135"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["角色授权管理接口"], "summary": "删除角色授权信息", "operationId": "delete_8", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysRolePower_t6135"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/role-power/role/modify/power": {"post": {"tags": ["角色授权管理接口"], "summary": "修改角色的权限信息", "operationId": "modifyRolePower", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolePowerModifyPowerVo_t8099"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/power": {"get": {"tags": ["权限管理接口"], "summary": "获取权限列表", "operationId": "getList_15", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysPower_t4562"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["权限管理接口"], "summary": "新增权限信息", "operationId": "add_8", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysPowerAddVo_t1994"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysPower_t4562"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/power/batch/put/parent-code": {"post": {"tags": ["权限管理接口"], "summary": "批量修改父Code", "operationId": "putParentCode", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysPowerModifyParentCodeVo_t6126"}}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/dic/dic": {"get": {"tags": ["系统数据字典"], "summary": "系统数据字典列表", "operationId": "getList_16", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDicBo_t5099"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["系统数据字典"], "summary": "添加", "operationId": "add_9", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDicAddVo_t4643"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SysDicBo_t5099"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/token/revoke": {"post": {"tags": ["签发令牌"], "summary": "吊销token", "operationId": "revoke", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRevokeParamVo_t7444"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/token/refresh": {"post": {"tags": ["签发令牌"], "summary": "获取token", "operationId": "refresh", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRefreshParamVo_t6697"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenMessageVo_t805"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/login": {"post": {"tags": ["[api]登录接口"], "summary": "用户登录，错误码：MAN_MATCHINE_FAIL表示验证码校验不通过。", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginParamVo_t9485"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenMessageVo_t805"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/bridge/token": {"get": {"tags": ["授权桥梁接口"], "summary": "通过id获取Token", "operationId": "getTokenById", "parameters": [{"name": "id", "in": "query", "description": "id", "required": false, "schema": {"type": "string"}}, {"name": "uuid", "in": "query", "description": "uuid", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenMessageVo_t805"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "post": {"tags": ["授权桥梁接口"], "summary": "设置token", "operationId": "putToken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PutTokenBodyVo_t5284"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/auth/get-user-by-auth-user": {"post": {"tags": ["[sys]认证信息"], "summary": "获取用户权限信息(authUser)", "operationId": "authTokenUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthUser_t1422"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenModelBo_t4879"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/app/token/sign": {"post": {"tags": ["应用签发令牌"], "summary": "获取token", "operationId": "sign", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenSignParamVo_t1994"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenMessageVo_t805"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/wx/mp/msg/verify/{id}": {"get": {"tags": ["微信小程序消息"], "summary": "获取验证码", "operationId": "getMsgVerifyCode", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgVerifyCodeBo_t9347"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["微信小程序消息"], "summary": "删除验证码", "operationId": "delMsgVerifyCode", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "id", "type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/wx/mp/msg/record/{id}": {"get": {"tags": ["小程序消息记录"], "summary": "获取小程序记录", "operationId": "get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "主键", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgRecord_t4443"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/wx/mp/acode/acode": {"get": {"tags": ["微信小程序码"], "summary": "生成小程序码", "description": "将内容生成小程序码", "operationId": "buildACode", "parameters": [{"name": "sys_app_id", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "scene", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "size", "in": "query", "description": "尺寸", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "description": "类型：base64", "required": false, "schema": {"type": "string"}}, {"name": "auto_color", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "rgb", "in": "query", "description": "颜色rgb: 255,255,255", "required": false, "schema": {"type": "string"}}, {"name": "content", "in": "query", "description": "内容", "required": true}, {"name": "autoColor", "in": "query", "description": "自动颜色"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ToolsRespVo_t8566"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/tools/qrcode": {"get": {"tags": ["工具类"], "summary": "生成二维码", "description": "将内容生成二维码", "operationId": "buildQRCode", "parameters": [{"name": "content", "in": "query", "description": "内容", "required": true, "schema": {"type": "string"}}, {"name": "size", "in": "query", "description": "尺寸", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "onc", "in": "query", "description": "前置颜色", "required": false, "schema": {"type": "string"}}, {"name": "bgc", "in": "query", "description": "背景色", "required": false, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "类型：base64", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ToolsRespVo_t8566"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/tools/code": {"get": {"tags": ["工具类"], "summary": "生成条形码", "description": "将内容生成条形码", "operationId": "buildCode", "parameters": [{"name": "content", "in": "query", "description": "内容", "required": true, "schema": {"type": "string"}}, {"name": "width", "in": "query", "description": "宽度", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "height", "in": "query", "description": "高度", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "onc", "in": "query", "description": "前置颜色", "required": false, "schema": {"type": "string"}}, {"name": "bgc", "in": "query", "description": "背景色", "required": false, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "类型：base64", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ToolsRespVo_t8566"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms/verify/{id}": {"get": {"tags": ["短信"], "summary": "获取手机验证码", "operationId": "getMsgVerifyCode_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgVerifyCodeBo_t9347"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["短信"], "summary": "删除手机验证码", "operationId": "delMsgVerifyCode_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "id", "type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms/record/{id}": {"get": {"tags": ["短信记录"], "summary": "获取短信记录", "operationId": "get_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "主键", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgRecord_t4443"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/sms/receipt/{id}": {"get": {"tags": ["短信回复"], "summary": "获取短信回复记录", "operationId": "get_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "主键", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgReceipt_t369"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/res-link/{id}": {"get": {"tags": ["连接信息"], "summary": "查询详情", "operationId": "get_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResLink_t9276"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/res-link/find/{id}": {"get": {"tags": ["连接信息"], "summary": "查询详情，未找到时返回空", "operationId": "find", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResLink_t9276"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/report/query/{id}/def-columns": {"get": {"tags": ["报告查询接口"], "summary": "查询信息", "operationId": "getD<PERSON><PERSON>ields", "parameters": [{"name": "id", "in": "path", "description": "ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReportQueryColumnBo_t9504"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/report/query/count": {"get": {"tags": ["报告查询接口"], "summary": "查询列表数量信息", "operationId": "getListCount", "parameters": [{"name": "code", "in": "query", "description": "Code信息", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox": {"get": {"tags": ["站内消息"], "summary": "获取列表", "operationId": "getList_1", "parameters": [{"name": "userId", "in": "query", "description": "用户id", "required": false, "schema": {"type": "string"}}, {"name": "del", "in": "query", "description": "已删除", "required": false, "schema": {"type": "boolean"}}, {"name": "notify", "in": "query", "description": "已读", "required": false, "schema": {"type": "boolean"}}, {"name": "business_code", "in": "query", "description": "业务编号", "required": false, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "类型", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页面", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "rows", "in": "query", "description": "每页多少条", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MsgInbox_t9651"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/{id}": {"get": {"tags": ["站内消息"], "summary": "获取详情", "operationId": "get_5", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgInbox_t9651"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["站内消息"], "summary": "删除数据", "operationId": "delte", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/count": {"get": {"tags": ["站内消息"], "summary": "获取数量", "operationId": "getListCount_1", "parameters": [{"name": "userId", "in": "query", "description": "用户id", "required": false, "schema": {"type": "string"}}, {"name": "del", "in": "query", "description": "已删除", "required": false, "schema": {"type": "boolean"}}, {"name": "notify", "in": "query", "description": "已读", "required": false, "schema": {"type": "boolean"}}, {"name": "business_code", "in": "query", "description": "业务编号", "required": false, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "类型", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me": {"get": {"tags": ["站内消息"], "summary": "获取当前用户列表", "operationId": "getListByMe", "parameters": [{"name": "del", "in": "query", "description": "已删除", "required": false, "schema": {"type": "boolean"}}, {"name": "notify", "in": "query", "description": "已读", "required": false, "schema": {"type": "boolean"}}, {"name": "business_code", "in": "query", "description": "业务编号", "required": false, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "类型", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页面", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "rows", "in": "query", "description": "每页多少条", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MsgInbox_t9651"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/{id}": {"get": {"tags": ["站内消息"], "summary": "获取当前用户详情", "operationId": "getByMe", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgInbox_t9651"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["站内消息"], "summary": "删除数据", "operationId": "delteByMe", "parameters": [{"name": "id", "in": "path", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/count": {"get": {"tags": ["站内消息"], "summary": "获取当前用户数量", "operationId": "getListCountByMe", "parameters": [{"name": "del", "in": "query", "description": "已删除", "required": false, "schema": {"type": "boolean"}}, {"name": "notify", "in": "query", "description": "已读", "required": false, "schema": {"type": "boolean"}}, {"name": "business_code", "in": "query", "description": "业务编号", "required": false, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "类型", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/{id}": {"get": {"tags": ["文件操作类"], "summary": "文件下载接口", "operationId": "download", "parameters": [{"name": "id", "in": "path", "description": "文件Id", "required": true, "schema": {"type": "string"}}, {"name": "filename", "in": "query", "description": "文件名称", "required": false, "schema": {"type": "string"}}, {"name": "s", "in": "query", "description": "尺寸，1-8", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "d", "in": "query", "description": "是否为直接下载，控制浏览器直接下载，默认为false", "required": false, "schema": {"type": "boolean"}}, {"name": "uuid", "in": "query", "description": "uuid，用户授权可访问的文件", "required": false, "schema": {"type": "string"}}, {"name": "range", "in": "header", "description": "断点续传范围"}], "responses": {"200": {"description": "文件数据"}, "206": {"description": "文件数据"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/fs/{id}/info": {"get": {"tags": ["文件操作类"], "summary": "文件信息查询接口", "operationId": "info", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "文件id", "type": "string"}}, {"name": "uuid", "in": "query", "required": true, "schema": {"title": "uuid", "type": "string"}}], "responses": {"200": {"description": "文件数据", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResDoc_t3377"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/email/verify/{id}": {"get": {"tags": ["邮件消息"], "summary": "获取验证码", "operationId": "getMsgVerifyCode_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgVerifyCodeBo_t9347"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}, "delete": {"tags": ["邮件消息"], "summary": "删除验证码", "operationId": "delMsgVerifyCode_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "id", "type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/email/record/{id}": {"get": {"tags": ["邮件记录"], "summary": "获取小程序记录", "operationId": "get_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "主键", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MsgRecord_t4443"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/captcha/{uuid}": {"get": {"tags": ["验证码"], "summary": "获取code", "operationId": "getCode", "parameters": [{"name": "uuid", "in": "path", "description": "uuid", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaptchaMsgRo_t6330"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/captcha/img": {"get": {"tags": ["验证码"], "summary": "获取验证码图片", "operationId": "img", "parameters": [{"name": "uuid", "in": "query", "description": "uuid，必须32位", "required": true, "schema": {"type": "string"}}, {"name": "w", "in": "query", "description": "宽度", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "h", "in": "query", "description": "高度", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "description": "验证码类型：SC:普通，GC:GIF图片，CC:中文，CGC:中文GIF，AC:算数,RV:旋转", "required": false, "schema": {"type": "string", "enum": ["SC", "GC", "CC", "CGC", "AC", "RV"]}}, {"name": "rtype", "in": "query", "description": "响应数据类型, base64", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CaptchaRespVo_t1734"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/refund-bill/{id}": {"get": {"tags": ["退款账单接口"], "summary": "获取账单详情信息", "operationId": "get_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "账单Id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RefundBillVo_t7765"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay/{id}/result": {"get": {"tags": ["支付"], "summary": "支付结果", "operationId": "payBillResult", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "支付id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PayResultVo_t9046"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay/orders/{ordersId}/result": {"get": {"tags": ["支付"], "summary": "支付结果", "operationId": "payOrdersResult", "parameters": [{"name": "ordersId", "in": "path", "required": true, "schema": {"title": "支付订单", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PayResultVo_t9046"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay-orders/{id}": {"get": {"tags": ["支付订单接口"], "summary": "获取账单详情信息", "operationId": "get_8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "账单Id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PayOrdersVo_t3263"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay-bill": {"get": {"tags": ["支付账单接口"], "summary": "获取账单详情信息", "operationId": "getList_4", "parameters": [{"name": "subject_type", "in": "query", "required": true, "schema": {"title": "主题类型", "type": "string"}}, {"name": "subject_id", "in": "query", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "orders_id", "in": "query", "required": true, "schema": {"title": "订单id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PayBillVo_t7404"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/pay-bill/{id}": {"get": {"tags": ["支付账单接口"], "summary": "获取账单详情信息", "operationId": "get_9", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"title": "账单Id", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PayBillVo_t7404"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/pay/account": {"get": {"tags": ["支付账户"], "summary": "支付账户列表", "operationId": "getList_5", "parameters": [{"name": "ownerType", "in": "query", "description": "所有者类型", "required": false, "schema": {"type": "string"}}, {"name": "ownerId", "in": "query", "description": "所有者Id", "required": false, "schema": {"type": "string"}}, {"name": "channel", "in": "query", "description": "支付通道", "required": false, "schema": {"type": "string"}}, {"name": "verify", "in": "query", "description": "是否校验通过", "required": false, "schema": {"type": "boolean"}}, {"name": "valid", "in": "query", "description": "是否启用", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PayAccountVo_t3552"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock": {"get": {"tags": ["[mg]商品库存信息"], "summary": "获取库存列表", "operationId": "getList_7", "parameters": [{"name": "rows", "in": "query", "required": false, "schema": {"title": "每页多少行", "type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"title": "页码，从0开始", "type": "string"}}, {"name": "spaces_id", "in": "query", "required": false, "schema": {"title": "spacesId，多个可逗号(,)分割", "type": "string"}}, {"name": "depot_id", "in": "query", "required": false, "schema": {"title": "depotId，多个可逗号(,)分割", "type": "string"}}, {"name": "store_id", "in": "query", "required": false, "schema": {"title": "storeId", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MallProductStock_t9121"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/stock/count": {"get": {"tags": ["[mg]商品库存信息"], "summary": "获取库存数量", "operationId": "getListCount_2", "parameters": [{"name": "spaces_id", "in": "query", "required": false, "schema": {"title": "spacesId，多个可逗号(,)分割", "type": "string"}}, {"name": "depot_id", "in": "query", "required": false, "schema": {"title": "depotId，多个可逗号(,)分割", "type": "string"}}, {"name": "store_id", "in": "query", "required": false, "schema": {"title": "storeId", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/product/count": {"get": {"tags": ["[mg]商品信息"], "summary": "获取商品数量", "operationId": "getListCount_3", "parameters": [{"name": "keyword", "in": "query", "required": false, "schema": {"title": "关键词", "type": "string"}}, {"name": "storeId", "in": "query", "required": false, "schema": {"title": "供应商Id", "type": "string"}}, {"name": "state", "in": "query", "required": false, "schema": {"title": "商品状态字段[NEW/DOWN]", "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/category/{id}/property": {"get": {"tags": ["[manage]分类信息"], "summary": "获取属性详情", "operationId": "getProperty", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MallProductCategoryPropertyStructBo_t6310"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/mall/mg/category/{id}/detail": {"get": {"tags": ["[manage]分类信息"], "summary": "获取详情详情", "operationId": "getDetail", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MallProductCategoryBo_t1833"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/open/s/{subject_path}/{cid_page}": {"get": {"tags": ["[open]开放主题接口"], "summary": "查询内容信息", "operationId": "get<PERSON>ontent", "parameters": [{"name": "subject_path", "in": "path", "description": "主题地址", "required": true, "schema": {"type": "string"}}, {"name": "cid_page", "in": "path", "description": "主题地址，由两部分组成，{category_path}_{page}，categoryId可以为空，为空时所有", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/open/s/{subject_path}/cats": {"get": {"tags": ["[open]开放主题接口"], "summary": "查询分类信息", "operationId": "getCategory", "parameters": [{"name": "subject_path", "in": "path", "description": "主题地址", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CmsCategoryStaticVo_t1431"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/open/c/{subject_path}/{path}": {"get": {"tags": ["[open]开放内容接口"], "summary": "查询内容信息", "operationId": "getContent_1", "parameters": [{"name": "subject_path", "in": "path", "description": "主题地址", "required": true, "schema": {"type": "string"}}, {"name": "path", "in": "path", "description": "内容地址", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/open/c/{subject_path}/{path}/pv": {"get": {"tags": ["[open]开放内容接口"], "summary": "内容Pv统计", "operationId": "contentPv", "parameters": [{"name": "subject_path", "in": "path", "description": "主题地址", "required": true, "schema": {"type": "string"}}, {"name": "path", "in": "path", "description": "内容地址", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContentLogVo_t8306"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/content/path/{path}": {"get": {"tags": ["[mg]内容信息"], "summary": "通过path获取详情", "operationId": "get<PERSON>y<PERSON><PERSON>", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "path", "in": "path", "description": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/content/count": {"get": {"tags": ["[mg]内容信息"], "summary": "获取列表数量", "operationId": "getListCount_4", "parameters": [{"name": "subject_id", "in": "path", "required": true, "schema": {"title": "主题id", "type": "string"}}, {"name": "_category_id", "in": "query", "description": "分类id", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/{subject_id}/category/count": {"get": {"tags": ["[mg]CMS分类"], "summary": "获取列表数量", "operationId": "getListCount_5", "parameters": [{"name": "subject_id", "in": "path", "description": "主题id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/cms/mg/subject/count": {"get": {"tags": ["[mg]网站主题"], "summary": "获取列表数量", "operationId": "getListCount_6", "parameters": [{"name": "keyword", "in": "query", "description": "keyword", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int32"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/basic/map/region": {"get": {"tags": ["地图地理信息"], "summary": "获取列表", "operationId": "getAllList_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MapRegion_t887"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/basic/map/region/pub": {"get": {"tags": ["地图地理信息"], "summary": "公开列表", "operationId": "getPubAllList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MapRegion_t887"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/basic/aux/autocomplete/auto": {"get": {"tags": ["自动补全列表"], "summary": "查询自动补全", "operationId": "getAutocompletes", "parameters": [{"name": "bs_code", "in": "query", "description": "业务编号", "required": false, "schema": {"type": "string"}}, {"name": "code", "in": "query", "description": "Code信息", "required": false, "schema": {"type": "string"}}, {"name": "attr", "in": "query", "description": "属性信息", "required": false, "schema": {"type": "string"}}, {"name": "login", "in": "query", "description": "是否登录", "required": false, "schema": {"type": "boolean"}}, {"name": "keyword", "in": "query", "description": "关键词", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AuxAutocomplete_t7962"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/wx/mp/auth-bridge/send-token": {"get": {"tags": ["微信小程序授权桥梁"], "summary": "签发发送Token图片，扫码绑定", "operationId": "sendToken", "parameters": [{"name": "sys_app_id", "in": "query", "description": "sysApiId", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "图片类型:默认为小程序码, WX_MP, JSON", "required": false, "schema": {"type": "string"}}, {"name": "uuid", "in": "query", "description": "随机uuid，长度必须32", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WxMpAuthBridgeRespVo_t616"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/wx/mp/auth-bridge/accept-token": {"get": {"tags": ["微信小程序授权桥梁"], "summary": "签发接受Token图片，扫码登录", "operationId": "acceptToken", "parameters": [{"name": "sys_app_id", "in": "query", "description": "sysApiId", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "图片类型:默认为小程序码, WX_MP, JSON", "required": false, "schema": {"type": "string"}}, {"name": "uuid", "in": "query", "description": "随机uuid，长度必须32", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WxMpAuthBridgeRespVo_t616"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/user/power": {"get": {"tags": ["用户权限信息"], "summary": "获取用户权限信息", "operationId": "power", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserPowerModelVo_t4598"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/role-power/role-group": {"get": {"tags": ["角色授权管理接口"], "summary": "获取角色分组信息", "operationId": "getRoleGroupList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RolePowerGroupVo_t3038"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/power/role-power/power-group": {"get": {"tags": ["角色授权管理接口"], "summary": "获取权限分组信息", "operationId": "getPowerGroupList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RolePowerGroupVo_t3038"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/dic/dic/pub": {"get": {"tags": ["系统数据字典"], "summary": "公开列表", "operationId": "getPubList", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDicBo_t5099"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/biz/user/{id}": {"get": {"tags": ["Biz用户信息"], "summary": "获取用户信息", "operationId": "get_19", "parameters": [{"name": "id", "in": "path", "description": "用户id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/User_t7700"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/biz/user/by-me": {"get": {"tags": ["Biz用户信息"], "summary": "获取用户自身信息", "operationId": "getByMe_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/User_t7700"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/bridge/state": {"get": {"tags": ["授权桥梁接口"], "summary": "通过uuid获取当前状态", "operationId": "getStateById", "parameters": [{"name": "id", "in": "query", "description": "id号", "required": false, "schema": {"type": "string"}}, {"name": "uuid", "in": "query", "description": "uuid", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuthBridgeStateBo_t5940"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/auth/user": {"get": {"tags": ["[sys]认证信息"], "summary": "获取用户权限信息(token)", "operationId": "authTokenUser_1", "parameters": [{"name": "token", "in": "query", "description": "token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TokenModelBo_t4879"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/auth/auth/auth/role": {"get": {"tags": ["[sys]认证信息"], "summary": "获取角色权限信息", "operationId": "authRole", "parameters": [{"name": "role_code", "in": "query", "description": "角色权限信息获取：ROLE_OPEN", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TokenModelRoleBo_t2770"}}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/_test/say": {"get": {"tags": ["测试接口"], "summary": "测试", "operationId": "say", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/sys/msg-inbox/by-me/all": {"delete": {"tags": ["站内消息"], "summary": "删除全部数据", "operationId": "deleteAllByMe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MsgInboxDeleteAllVo_t8891"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}, "/iot/iot/device/{id}": {"delete": {"tags": ["设备表"], "summary": "删除设备表", "operationId": "delete_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Result_t6300"}}}}}}}}, "components": {"schemas": {"Result_t6300": {"title": "[Result]响应信息封装", "type": "object", "properties": {"errorCode": {"title": "错误标识", "type": "string", "example": "YHERT_ERROR_TAG"}, "success": {"title": "操作是否成功", "type": "boolean", "example": false}, "code": {"title": "错误代码", "type": "string", "example": "NOT_FUND_RESOURCE"}, "message": {"title": "详细的错误信息", "type": "string", "example": "未找到资源"}, "data": {"title": "附带属性", "type": "string"}}, "description": "异常信息响应封装结果"}, "ReportQueryAddVo_t7333": {"title": "[ReportQueryAddVo]报告查询添加信息", "required": ["execTotalQuery", "name", "queryCmd", "valid"], "type": "object", "properties": {"valid": {"title": "生效", "type": "boolean"}, "execTotalQuery": {"title": "执行统计查询", "type": "boolean"}, "code": {"title": "配置信息code，URL路径", "maxLength": 64, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "queryCmd": {"title": "查询指令", "type": "string"}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}}, "description": "报告查询添加信息"}, "ExcelExportColumnHandler_t538": {"type": "object"}, "ReportQueryColumnBo_t9504": {"title": "[ReportQueryColumnBo]报告字段", "type": "object", "properties": {"code": {"title": "字段名称", "type": "string"}, "name": {"title": "展示名称", "type": "string"}, "type": {"title": "类型", "type": "string", "enum": ["STRING", "NUMBER", "DATE", "DIC", "BOOLEAN", "FUN"]}, "dicCode": {"title": "数据字典Code", "type": "string"}, "dateFormat": {"title": "时间格式化方案", "type": "string"}, "trueValue": {"title": "为真时值", "type": "string"}, "falseValue": {"title": "为假时值", "type": "string"}, "fun": {"$ref": "#/components/schemas/ExcelExportColumnHandler_t538"}}}, "ReportQueryVo_t1241": {"title": "[ReportQueryVo]报告查询信息", "required": ["createTime", "execTotalQuery", "id", "name", "queryCmd", "valid"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "valid": {"title": "生效", "type": "boolean"}, "execTotalQuery": {"title": "执行统计查询", "type": "boolean"}, "code": {"title": "配置信息code，URL路径", "maxLength": 64, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "queryCmd": {"title": "查询指令", "type": "string"}, "columns": {"title": "字段信息", "type": "array", "items": {"$ref": "#/components/schemas/ReportQueryColumnBo_t9504"}}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}}, "description": "报告查询信息"}, "ReportQueryModifyColumnVo_t5715": {"title": "[ReportQueryModifyColumnVo]报告查询字段信息", "required": ["columns"], "type": "object", "properties": {"columns": {"title": "字段信息", "type": "array", "items": {"$ref": "#/components/schemas/ReportQueryColumnBo_t9504"}}}, "description": "报告字段信息"}, "MsgInboxPutNotifyVo_t4675": {"title": "[MsgInboxPutNotifyVo]阅读设置", "required": ["notify"], "type": "object", "properties": {"notify": {"title": "notify", "type": "boolean"}}}, "MsgInboxPutDelVo_t3457": {"title": "[MsgInboxPutDelVo]删除设置", "required": ["del"], "type": "object", "properties": {"del": {"title": "del", "type": "boolean"}}}, "MallProductModifySpaceVo_t2251": {"title": "[ProductModifySpaceVo]商品修改规格（SKU）", "required": ["id", "quantity", "selling", "unitPrice"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "默认商品图片", "maxLength": 256, "minLength": 0, "type": "string"}, "selling": {"title": "可销售", "type": "boolean"}, "unitPrice": {"title": "单价", "type": "number"}, "unitPriceShow": {"title": "显示价格", "type": "number"}, "upc": {"title": "商品upc条码", "maxLength": 32, "minLength": 0, "type": "string"}, "sku": {"title": "sku", "maxLength": 32, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 32, "minLength": 0, "type": "string"}, "quantity": {"title": "共享库存数量", "type": "integer", "format": "int32"}, "properties": {"title": "商品规格属性", "type": "array", "items": {"$ref": "#/components/schemas/MallProductPropertyVo_t214"}}, "id": {"title": "Id", "maxLength": 32, "minLength": 1, "type": "string"}}}, "MallProductModifyVo_t4675": {"title": "[ProductModifyVo]商品信息（SPU）", "required": ["contentPc", "name", "pic", "spaces"], "type": "object", "properties": {"spu": {"title": "SPU信息", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "图片信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "video": {"title": "视频信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "contentMobile": {"title": "移动版详情", "maxLength": 65535, "minLength": 0, "type": "string"}, "contentPc": {"title": "PC版详情", "maxLength": 65535, "minLength": 1, "type": "string"}, "properties": {"title": "商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]", "type": "array", "items": {"$ref": "#/components/schemas/MallProductPropertyVo_t214"}}, "spaces": {"title": "不同规格数据", "maxItems": 2147483647, "minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/MallProductModifySpaceVo_t2251"}}}, "description": "商品信息"}, "MallProductPropertyVo_t214": {"title": "[ProductPropertyVo]商品规格", "required": ["code", "groupName", "name", "value"], "type": "object", "properties": {"code": {"title": "属性code", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "属性名", "maxLength": 64, "minLength": 1, "type": "string"}, "groupName": {"title": "属性组名", "maxLength": 64, "minLength": 1, "type": "string"}, "value": {"title": "值", "maxLength": 64, "minLength": 1, "type": "string"}}}, "MallProductDetailSpaceVo_t6748": {"title": "[ProductDetailSpaceVo]商品新增规格（SKU）", "required": ["createTime", "id", "productId", "quantity", "selling", "unitPrice"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "默认商品图片", "maxLength": 256, "minLength": 0, "type": "string"}, "selling": {"title": "可销售", "type": "boolean"}, "unitPrice": {"title": "单价", "type": "number"}, "unitPriceShow": {"title": "显示价格", "type": "number"}, "upc": {"title": "商品upc条码", "maxLength": 32, "minLength": 0, "type": "string"}, "sku": {"title": "sku", "maxLength": 32, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 32, "minLength": 0, "type": "string"}, "quantity": {"title": "共享库存数量", "type": "integer", "format": "int32"}, "properties": {"title": "商品规格属性", "type": "array", "items": {"$ref": "#/components/schemas/MallProductPropertyVo_t214"}}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "规格id", "maxLength": 32, "minLength": 1, "type": "string"}, "productId": {"title": "商品id", "maxLength": 32, "minLength": 1, "type": "string"}, "storeId": {"title": "店铺id", "maxLength": 32, "minLength": 0, "type": "string"}, "auxSnapshotId": {"title": "快照Id", "maxLength": 32, "minLength": 0, "type": "string"}}}, "MallProductDetailVo_t2398": {"title": "[ProductDetailVo]商品信息", "required": ["archived", "brandName", "categoryId", "contentPc", "createTime", "id", "modifyTime", "name", "pic", "state", "storeId", "type", "unitPrice", "up", "upSubmit"], "type": "object", "properties": {"spu": {"title": "SPU信息", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "图片信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "video": {"title": "视频信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "contentMobile": {"title": "移动版详情", "maxLength": 65535, "minLength": 0, "type": "string"}, "contentPc": {"title": "PC版详情", "maxLength": 65535, "minLength": 1, "type": "string"}, "properties": {"title": "商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]", "type": "array", "items": {"$ref": "#/components/schemas/MallProductPropertyVo_t214"}}, "archived": {"title": "归档", "type": "boolean"}, "archivedRemark": {"title": "归档建议", "maxLength": 512, "minLength": 0, "type": "string"}, "archivedTime": {"title": "归档时间", "type": "string", "format": "date-time"}, "archivedUserId": {"title": "归档操作人id", "maxLength": 32, "minLength": 0, "type": "string"}, "categoryId": {"title": "商品分类", "maxLength": 32, "minLength": 1, "type": "string"}, "brandName": {"title": "品牌名称", "maxLength": 64, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "spu", "maxLength": 32, "minLength": 1, "type": "string"}, "state": {"title": "状态, NEW、UP、ARCHIVED", "maxLength": 32, "minLength": 1, "type": "string"}, "modifyTime": {"title": "修改时间", "type": "string", "format": "date-time"}, "type": {"title": "商品类型：BASE：基础商品", "maxLength": 32, "minLength": 1, "type": "string"}, "storeId": {"title": "店铺id", "maxLength": 32, "minLength": 1, "type": "string"}, "tag": {"title": "标签，逗号分隔", "maxLength": 2048, "minLength": 0, "type": "string"}, "unitPrice": {"title": "商品单价", "type": "number"}, "unitPriceShow": {"title": "商品单价", "type": "number"}, "up": {"title": "商品状态（-100：驳回，0: 未上架，100：上架中）", "type": "integer", "format": "int32"}, "upRemark": {"title": "上架意见", "maxLength": 256, "minLength": 0, "type": "string"}, "upSubmit": {"title": "上架申请", "type": "boolean"}, "upSubmitRemark": {"title": "上架申请理由", "maxLength": 512, "minLength": 0, "type": "string"}, "upSubmitTime": {"title": "上架申请", "type": "string", "format": "date-time"}, "upSubmitUserId": {"title": "上架申请人", "maxLength": 32, "minLength": 0, "type": "string"}, "upTime": {"title": "上架时间", "type": "string", "format": "date-time"}, "upUserId": {"title": "上架操作人id", "maxLength": 32, "minLength": 0, "type": "string"}, "spaces": {"title": "不同规格数据", "type": "array", "items": {"$ref": "#/components/schemas/MallProductDetailSpaceVo_t6748"}}}, "description": "商品信息"}, "MallCategoryAddVo_t975": {"title": "[MallProductCategoryAddVo]分类信息", "required": ["ban", "idParent"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 32, "minLength": 0, "type": "string"}, "ban": {"title": "禁用", "type": "boolean"}, "idParent": {"title": "父节点，根节点0", "maxLength": 32, "minLength": 1, "type": "string"}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}, "propertyStructs": {"title": "属性定义信息", "type": "array", "items": {"$ref": "#/components/schemas/MallProductCategoryPropertyStructBo_t6310"}}}, "description": "分类信息"}, "MallProductCategoryPropertyStructBo_t6310": {"title": "[MallProductCategoryPropertyStructBo]商品内容结构", "required": ["code", "name", "type"], "type": "object", "properties": {"categoryId": {"title": "分类id", "type": "string"}, "code": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "type": {"title": "数据类型", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "groupName": {"title": "分组名称", "maxLength": 256, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "[MallProductCategoryPropertyStructBo]商品内容结构"}, "CmsContentTopVo_t4425": {"title": "[CmsContentTopVo]cms内容变更", "required": ["_top"], "type": "object", "properties": {"_top": {"title": "置顶", "type": "boolean"}}, "description": "[CmsContentTopVo]cms内容变更"}, "CmsContentOpenVo_t2997": {"title": "[CmsContentOpenVo]cms内容变更", "required": ["_open"], "type": "object", "properties": {"_open": {"title": "展示", "type": "boolean"}}, "description": "[CmsContentOpenVo]cms内容变更"}, "CmsCategoryModifyVo_t5809": {"title": "[CmsCategoryModifyVo]cms分类编辑", "required": ["name", "ord"], "type": "object", "properties": {"idParent": {"title": "父ID，根节点为0", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序值", "type": "integer", "format": "int32"}, "remark": {"title": "备注", "maxLength": 4096, "minLength": 0, "type": "string"}}, "description": "[CmsCategoryModifyVo]cms分类编辑"}, "CmsSubjectContentStructBo_t1333": {"title": "[CmsSubjectContentStructBo]cms主题内容结构", "required": ["code", "dataType", "name"], "type": "object", "properties": {"dataType": {"title": "[DataType]数据结构类型", "type": "string", "description": "[DataType]数据结构类型", "enum": ["INT", "STRING", "FLOAT", "BOOL", "DATE", "ENUM", "LIST", "MAP"]}, "code": {"title": "字段", "maxLength": 32, "minLength": 1, "type": "string"}, "require": {"title": "是否是必须字段", "type": "boolean"}, "maxSize": {"title": "最大长度", "type": "integer", "format": "int32"}, "minSize": {"title": "最小长度", "type": "integer", "format": "int32"}, "若类型为LIST，则附带此子字段定义": {"type": "array", "items": {"$ref": "#/components/schemas/CmsSubjectContentStructListBo_t2061"}}, "值范围": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "控件类型": {"title": "控件内容", "type": "string"}, "约束信息": {"$ref": "#/components/schemas/CmsSubjectContentStructRestrictBo_t2592"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}, "show": {"title": "在列表展示", "type": "boolean"}, "index": {"title": "是否允许检索", "type": "boolean"}, "indexCode": {"title": "index字段", "type": "string"}}, "description": "[CmsSubjectContentStructBo]cms主题内容结构"}, "CmsSubjectContentStructListBo_t2061": {"title": "[CmsSubjectContentStructListBo]cms主题内容List结构", "required": ["code", "dataType", "name"], "type": "object", "properties": {"dataType": {"title": "[DataType]数据结构类型", "type": "string", "description": "[DataType]数据结构类型", "enum": ["INT", "STRING", "FLOAT", "BOOL", "DATE", "ENUM", "LIST", "MAP"]}, "code": {"title": "字段", "maxLength": 32, "minLength": 1, "type": "string"}, "require": {"title": "是否是必须字段", "type": "boolean"}, "maxSize": {"title": "最大长度", "type": "integer", "format": "int32"}, "minSize": {"title": "最小长度", "type": "integer", "format": "int32"}, "值范围": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "控件类型": {"title": "控件内容", "type": "string"}, "约束信息": {"$ref": "#/components/schemas/CmsSubjectContentStructRestrictBo_t2592"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "[CmsSubjectContentStructBo]cms主题内容List结构"}, "CmsSubjectContentStructRestrictBo_t2592": {"title": "[CmsSubjectContentStructRestrictBo]cms主题内容Restrict", "type": "object", "properties": {"极限": {"title": "极限", "type": "integer", "format": "int32"}, "枚举列表": {"type": "array", "items": {"$ref": "#/components/schemas/CmsSubjectContentStructRestrictCodeValueBo_t7019"}}}, "description": "[CmsSubjectContentStructRestrictBo]cms主题内容Restrict"}, "CmsSubjectContentStructRestrictCodeValueBo_t7019": {"title": "[CmsSubjectContentStructRestrictCodeValueBo]cms主题内容Restrict", "type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}}, "description": "[CmsSubjectContentStructRestrictCodeValueBo]cms主题内容Restrict"}, "CmsSubjectModifyVo_t8159": {"title": "[CmsSubjectModifyVo]cms主题更新", "required": ["contentStructs", "idParent", "name", "ord"], "type": "object", "properties": {"idParent": {"title": "父一级id，根节点传0", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "主题名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序", "type": "integer", "format": "int32"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}, "contentStructs": {"title": "内容结构", "type": "array", "items": {"$ref": "#/components/schemas/CmsSubjectContentStructBo_t1333"}}}, "description": "[CmsSubjectAddVo]cms主题更新"}, "SysConfigAddVo_t2271": {"title": "[SysConfigAddVo]系统系统配置信息", "required": ["code", "valid"], "type": "object", "properties": {"code": {"title": "Code信息", "maxLength": 256, "minLength": 1, "type": "string"}, "valid": {"title": "启用", "type": "boolean"}, "value": {"title": "值信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "remark": {"title": "备注", "maxLength": 65535, "minLength": 0, "type": "string"}}}, "SysRoleModifyVo_t2680": {"title": "[SysRoleModifyVo]修改系统角色", "required": ["code", "name"], "type": "object", "properties": {"code": {"title": "角色code(ROLE_前缀)", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "角色名称", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "修改系统角色管理内容"}, "SysPowerModifyVo_t9923": {"title": "[SysPowerModifyVo]系统权限管理内容", "required": ["code"], "type": "object", "properties": {"code": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "parentCode": {"title": "父code", "maxLength": 32, "minLength": 0, "type": "string"}, "depCode": {"title": "依赖code", "type": "string"}, "appCode": {"title": "应用编号(spring.application.name)", "maxLength": 32, "minLength": 0, "type": "string"}, "clsName": {"title": "类名称", "maxLength": 128, "minLength": 0, "type": "string"}, "clsMethod": {"title": "方法名称", "maxLength": 32, "minLength": 0, "type": "string"}, "httpMethod": {"title": "请求方法", "maxLength": 32, "minLength": 0, "type": "string"}, "url": {"title": "url地址", "maxLength": 128, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "系统权限管理内容"}, "SysDicAddVo_t4643": {"title": "[SysDicAddVo]系统数据字典", "required": ["name", "valid"], "type": "object", "properties": {"code": {"title": "code", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "valid": {"title": "启用", "type": "boolean"}, "fields": {"title": "字段信息", "type": "array", "items": {"$ref": "#/components/schemas/SysDicFieldBo_t7739"}}, "remark": {"title": "备注", "type": "string"}}}, "SysDicFieldBo_t7739": {"title": "[SysDicFieldBo]系统数据字典字段", "type": "object", "properties": {"code": {"title": "code", "maxLength": 32, "minLength": 1, "type": "string"}, "label": {"title": "显示内容", "type": "string"}, "labels": {"title": "字段信息", "type": "array", "items": {"$ref": "#/components/schemas/SysDicFieldI18nBo_t5027"}}, "remark": {"title": "备注", "type": "string"}}}, "SysDicFieldI18nBo_t5027": {"title": "[SysDicFieldI18nBo]系统数据字典字段国际化", "type": "object", "properties": {"language": {"title": "language", "maxLength": 32, "minLength": 1, "type": "string"}, "label": {"title": "显示内容", "type": "string"}}}, "WxMpVerifySendVo_t9831": {"title": "[WxMpVerifySendVo]发送验证码参数", "required": ["openId", "sysAppId", "tmplCode"], "type": "object", "properties": {"openId": {"title": "openId", "type": "string"}, "sysAppId": {"title": "应用id", "type": "string"}, "tmplCode": {"title": "短信模板", "type": "string"}, "toPage": {"title": "目标地址", "maxLength": 256, "minLength": 0, "type": "string"}}}, "WxMpRespVo_t8596": {"title": "[WxMpRespVo]消息发送回执", "required": ["id"], "type": "object", "properties": {"id": {"title": "Id", "type": "string"}}}, "WxMpSendVo_t7179": {"title": "[WxMpSendVo]发送参数", "required": ["openId", "param", "sysAppId", "tmplCode"], "type": "object", "properties": {"openId": {"title": "openId", "type": "string"}, "sysAppId": {"title": "应用id", "type": "string"}, "toPage": {"title": "目标地址", "maxLength": 256, "minLength": 0, "type": "string"}, "tmplCode": {"title": "短信模板", "type": "string"}, "param": {"title": "参数，json格式", "type": "string"}}}, "WxMpBatchSendVo_t6063": {"title": "[WxMpBatchSendVo]批量发送参数", "type": "object", "properties": {"bodies": {"title": "消息体", "type": "array", "items": {"$ref": "#/components/schemas/WxMpSendVo_t7179"}}}}, "WxMpBatchRespVo_t7480": {"title": "[WxMpBatchRespVo]消息批量发送回执", "required": ["resps"], "type": "object", "properties": {"resps": {"title": "resps", "type": "array", "items": {"$ref": "#/components/schemas/WxMpRespVo_t8596"}}}}, "WsMsgPushBo_t3436": {"title": "[WsMsgSendBo]长连接发送数据", "required": ["msg"], "type": "object", "properties": {"all": {"title": "所有用户", "type": "boolean"}, "login": {"title": "登录用户", "type": "boolean"}, "deviceTypes": {"title": "目标设备类型", "type": "array", "items": {"title": "目标设备类型", "type": "string"}}, "groupIds": {"title": "用户组id", "type": "array", "items": {"title": "用户组id", "type": "string"}}, "msg": {"$ref": "#/components/schemas/WsMsgPushBodyBo_t244"}, "uids": {"type": "array", "items": {"type": "string"}}}}, "WsMsgPushBodyBo_t244": {"title": "[WsMsgSendBodyBo]长连接批量参数", "required": ["bc", "d", "ec", "fc"], "type": "object", "properties": {"bc": {"title": "业务code", "type": "string"}, "fc": {"title": "功能code", "type": "string"}, "ec": {"title": "事件code", "type": "string"}, "d": {"title": "数据体", "type": "string"}}}, "WsMsgBatchSendVo_t4893": {"title": "[WsMsgBatchSendVo]批量发送参数", "type": "object", "properties": {"msgs": {"title": "消息体", "type": "array", "items": {"$ref": "#/components/schemas/WsMsgPushBo_t3436"}}}}, "TmplConvertParamVo_t464": {"title": "[TmplConvertParamVo]转换参数", "required": ["jxlsData", "jxlsTmplName", "type"], "type": "object", "properties": {"watermarkImg": {"title": "掩层图片，suc.img", "type": "string", "example": "mall_logo_yct.png"}, "type": {"title": "目标类型，pdf, xlsx", "type": "string", "example": "pdf"}, "jxlsData": {"title": "数据内容, json格式", "type": "string", "example": "\"{}\""}, "jxlsTmplName": {"title": "模板名称：scu.xlsx", "type": "string", "example": "tmpl01.xlsx"}, "watermarkPercent": {"title": "水印缩放比例", "type": "number", "format": "float"}}}, "SmsVerifySendVo_t5680": {"title": "[SmsVerifySendVo]短信发送参数", "required": ["phone", "signName", "tmplCode"], "type": "object", "properties": {"phone": {"title": "电话号码", "type": "string"}, "tmplCode": {"title": "短信模板", "type": "string"}, "signName": {"title": "短信签名", "type": "string"}}}, "SmsRespVo_t7758": {"title": "[SmsRespVo]消息发送回执", "required": ["id"], "type": "object", "properties": {"id": {"title": "Id", "type": "string"}}}, "SmsSendVo_t6341": {"title": "[SmsSendVo]短信发送参数", "required": ["param", "phone", "signName", "tmplCode"], "type": "object", "properties": {"phone": {"title": "电话号码", "type": "string"}, "signName": {"title": "短信签名", "type": "string"}, "tmplCode": {"title": "短信模板", "type": "string"}, "param": {"title": "参数，json格式", "type": "string"}, "againNum": {"title": "重试次数", "type": "integer", "format": "int32"}}}, "SmsBatchSendVo_t3230": {"title": "[SmsBatchSendVo]短信批量发送参数", "type": "object", "properties": {"bodies": {"title": "短信消息体", "type": "array", "items": {"$ref": "#/components/schemas/SmsSendVo_t6341"}}}}, "SmsBatchRespVo_t4647": {"title": "[SmsBatchRespVo]消息批量发送回执", "required": ["resps"], "type": "object", "properties": {"resps": {"title": "resps", "type": "array", "items": {"$ref": "#/components/schemas/SmsRespVo_t7758"}}}}, "SmsAliyunController$SmsReportBo_t7318": {"type": "object", "properties": {"phone_number": {"type": "string"}, "success": {"type": "boolean"}, "biz_id": {"type": "string"}, "out_id": {"type": "string"}, "send_time": {"type": "string"}, "report_time": {"type": "string"}, "err_code": {"type": "string"}, "err_msg": {"type": "string"}, "sms_size": {"type": "string"}}}, "AliSmsReportVo_t7746": {"title": "[AliSmsReportVo]消息发送回执", "type": "object", "properties": {"code": {"title": "code", "type": "integer", "format": "int32"}, "msg": {"title": "msg", "type": "string"}}}, "ResLinkAddVo_t5021": {"title": "[ResLinkAddVo]连接映射信息", "required": ["businessCode", "toPage", "type"], "type": "object", "properties": {"idType": {"title": "Id类型", "type": "string", "enum": ["SHOW_ID", "LONG_ID"]}, "expireTime": {"title": "过期时间", "type": "string", "format": "date-time"}, "businessCode": {"title": "业务编号", "maxLength": 32, "minLength": 1, "type": "string"}, "type": {"title": "类型", "maxLength": 32, "minLength": 1, "type": "string"}, "toPage": {"title": "跳转目标编号", "maxLength": 512, "minLength": 1, "type": "string"}, "data": {"title": "数据集合", "maxLength": 1024, "minLength": 0, "type": "string"}}, "description": "连接映射信息"}, "ResLinkRespVo_t6943": {"title": "[ResLinkRespVo]连接映射结果信息", "type": "object", "properties": {"id": {"title": "Id", "type": "string"}}, "description": "连接映射结果信息"}, "TaskProgressBo_t2229": {"title": "[TaskProgressBo]任务进度信息", "type": "object", "properties": {"id": {"title": "ID", "type": "string"}, "uid": {"title": "用户id", "type": "string"}, "type": {"title": "类型", "type": "string"}, "beginTime": {"title": "开始时间", "type": "string", "format": "date-time"}, "total": {"title": "总数", "type": "number", "format": "float"}, "progress": {"title": "进度", "type": "number", "format": "float"}, "finished": {"title": "完成", "type": "boolean"}, "success": {"title": "成功", "type": "boolean"}, "msgs": {"title": "记录信息", "type": "array", "items": {"title": "记录信息", "type": "string"}}, "data": {"title": "成功信息", "type": "string"}, "errorData": {"title": "失败信息", "type": "string"}}}, "MsgInboxSendBo_t5778": {"title": "[MsgInboxSendBo]发送参数", "required": ["businessCode", "title", "type", "userId"], "type": "object", "properties": {"businessCode": {"title": "业务编号", "maxLength": 32, "minLength": 1, "type": "string"}, "msg": {"title": "内容", "maxLength": 4096, "minLength": 0, "type": "string"}, "title": {"title": "标题", "maxLength": 256, "minLength": 1, "type": "string"}, "toData": {"title": "跳转数据集合", "maxLength": 1024, "minLength": 0, "type": "string"}, "toPage": {"title": "跳转目标编号", "maxLength": 512, "minLength": 0, "type": "string"}, "type": {"title": "类型", "maxLength": 32, "minLength": 1, "type": "string"}, "userId": {"title": "用户id，指定用户，ALL：代表所有人", "maxLength": 32, "minLength": 1, "type": "string"}}}, "MsgInboxBatchSendVo_t2216": {"title": "[MsgInboxBatchSendVo]批量发送参数", "type": "object", "properties": {"bodies": {"title": "消息体", "type": "array", "items": {"$ref": "#/components/schemas/MsgInboxSendBo_t5778"}}}}, "ResDoc_t3377": {"title": "[ResDoc]资源索引表", "required": ["createTime", "id", "userId", "versionNo"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "versionNo": {"title": "版本号", "maxLength": 32, "minLength": 1, "type": "string"}, "mimeType": {"title": "文件类型", "maxLength": 128, "minLength": 0, "type": "string"}, "suffix": {"title": "文件后缀", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "文件名", "maxLength": 128, "minLength": 0, "type": "string"}, "size": {"title": "文件大小", "type": "integer", "format": "int64"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "description": {"title": "描述", "maxLength": 1024, "minLength": 0, "type": "string"}, "path": {"title": "存储路径", "maxLength": 64, "minLength": 0, "type": "string"}, "md5": {"title": "文件MD5值", "maxLength": 64, "minLength": 0, "type": "string"}, "userId": {"title": "用户编号", "maxLength": 32, "minLength": 1, "type": "string"}, "uuid": {"title": "uuid", "maxLength": 32, "minLength": 0, "type": "string"}, "ban": {"title": "被禁用", "type": "boolean"}, "secrecy": {"title": "保密", "type": "boolean"}, "uploading": {"title": "上传中", "type": "boolean"}, "expireTime": {"title": "过期时间", "type": "string", "format": "date-time"}}, "description": "资源索引表"}, "FsUpdateTextVo_t4312": {"title": "[FsUpdateTextVo]上传文本", "required": ["content"], "type": "object", "properties": {"content": {"title": "文本内容", "type": "string"}, "filename": {"title": "文件名称", "type": "string"}, "contentType": {"title": "文件类型", "type": "string"}, "exp": {"title": "过期时间", "type": "integer", "format": "int32"}, "secrecy": {"title": "保密文件，不公开", "type": "boolean"}}}, "FsQueryListByMd5ParamVo_t4338": {"title": "[FsQueryListByMd5ParamVo]文件上传前数据查询", "required": ["md5", "size"], "type": "object", "properties": {"md5": {"title": "文件md5值，用于进行秒传检测，需要同时传递filesize", "maxLength": 64, "minLength": 0, "type": "string"}, "size": {"title": "文件尺寸，用于进行秒传检测，需要同时传递md5", "type": "integer", "format": "int64"}}}, "FileInfoParamVo_t3318": {"title": "[FileInfoParamVo]文件信息查询", "required": ["ids"], "type": "object", "properties": {"ids": {"title": "文件id", "type": "array", "items": {"title": "文件id", "type": "string"}}}}, "FsUploadPreParamVo_t854": {"title": "[FsUploadPreParamVo]文件上传与上传数据", "required": ["md5", "mimeType", "name", "size"], "type": "object", "properties": {"md5": {"title": "文件md5值，用于进行秒传检测，需要同时传递filesize", "maxLength": 64, "minLength": 0, "type": "string"}, "size": {"title": "文件尺寸，用于进行秒传检测，需要同时传递md5", "type": "integer", "format": "int64"}, "name": {"title": "文件名称", "maxLength": 128, "minLength": 0, "type": "string"}, "mimeType": {"title": "文件类型", "maxLength": 128, "minLength": 0, "type": "string"}, "exp": {"title": "过期时间", "type": "integer", "format": "int32"}, "secrecy": {"title": "保密文件，不公开", "type": "boolean"}}}, "FsAliyunUploadAuthPreVo_t7767": {"title": "[FsAliyunUploadAuthPreVo]阿里云文件认证信息", "type": "object", "properties": {"accessKeyId": {"title": "认证id", "type": "string"}, "accessKeySecret": {"title": "认证Secret", "type": "string"}, "expiration": {"title": "过期时间", "type": "string"}, "securityToken": {"title": "安全token", "type": "string"}, "filepath": {"title": "上传路径", "type": "string"}, "bucket": {"title": "桶信息", "type": "string"}, "region": {"title": "地址信息", "type": "string"}}}, "FsAliyunUploadPreVo_t1264": {"title": "[FsAliyunUploadPreVo]阿里云文件预上传对象", "type": "object", "properties": {"exist": {"title": "文件已存在", "type": "boolean"}, "auth": {"$ref": "#/components/schemas/FsAliyunUploadAuthPreVo_t7767"}, "doc": {"$ref": "#/components/schemas/ResDoc_t3377"}}}, "EmailAccountVo_t4519": {"title": "[EmailAccountVo]邮件账户", "required": ["email"], "type": "object", "properties": {"email": {"title": "目标email", "type": "string"}, "name": {"title": "目标用户", "type": "string"}}}, "EmailVerifySendVo_t4385": {"title": "[EmailVerifySendVo]发送验证码参数", "required": ["signName", "subject", "tmplCode", "toEmail"], "type": "object", "properties": {"subject": {"title": "主题", "type": "string"}, "signName": {"title": "短信签名", "type": "string"}, "toEmail": {"$ref": "#/components/schemas/EmailAccountVo_t4519"}, "tmplCode": {"title": "短信模板", "type": "string"}}}, "EmailRespVo_t9854": {"title": "[EmailRespVo]消息发送回执", "required": ["id"], "type": "object", "properties": {"id": {"title": "Id", "type": "string"}}}, "EmailAttachmentVo_t9297": {"title": "[EmailAttachmentVo]邮件附件", "type": "object", "properties": {"name": {"title": "名称", "type": "string"}, "url": {"title": "url", "type": "string"}}}, "EmailCidVo_t2593": {"title": "[EmailCidVo]邮件cid", "type": "object", "properties": {"cid": {"title": "cid", "type": "string"}, "name": {"title": "名称", "type": "string"}, "url": {"title": "url", "type": "string"}}}, "EmailSendVo_t8437": {"title": "[EmailSendVo]发送参数", "required": ["param", "subject", "tmplCode", "toEmails"], "type": "object", "properties": {"toEmails": {"title": "目标用户", "type": "array", "items": {"$ref": "#/components/schemas/EmailAccountVo_t4519"}}, "ccEmails": {"title": "抄送用户", "type": "array", "items": {"$ref": "#/components/schemas/EmailAccountVo_t4519"}}, "bccEmails": {"title": "隐蔽抄送用户", "type": "array", "items": {"$ref": "#/components/schemas/EmailAccountVo_t4519"}}, "signName": {"title": "发送人签名", "type": "string"}, "subject": {"title": "主题", "type": "string"}, "tmplCode": {"title": "短信模板", "type": "string"}, "param": {"title": "参数，json格式", "type": "string"}, "cids": {"title": "cid资源", "type": "array", "items": {"$ref": "#/components/schemas/EmailCidVo_t2593"}}, "attachments": {"title": "附件信息", "type": "array", "items": {"$ref": "#/components/schemas/EmailAttachmentVo_t9297"}}}}, "RefundBillAddVo_t5096": {"title": "[RefundBillAddVo]创建退款账单", "required": ["billId", "notifyUrl", "price", "subject", "subjectId"], "type": "object", "properties": {"billId": {"title": "支付账户id", "maxLength": 32, "minLength": 1, "type": "string"}, "subjectId": {"title": "主体信息（退款编号）", "maxLength": 128, "minLength": 1, "type": "string"}, "subjectThirdId": {"title": "三方订单号（订单号）", "type": "string"}, "subject": {"title": "主题描述信息", "maxLength": 128, "minLength": 1, "type": "string"}, "subtitle": {"title": "副标题", "maxLength": 512, "minLength": 0, "type": "string"}, "price": {"title": "金额，币种与支付时一致", "type": "number"}, "notifyUrl": {"title": "通知回调URL，POST", "maxLength": 512, "minLength": 0, "type": "string"}, "remark": {"title": "备注", "maxLength": 1024, "minLength": 0, "type": "string"}}}, "RefundBillRespVo_t9283": {"title": "[RefundBillRespVo]创建退款账单", "type": "object", "properties": {"id": {"title": "主键", "type": "string"}}}, "PayOrdersAddVo_t4918": {"title": "[PayOrdersAddVo]创建支付订单", "required": ["ownerId", "ownerType", "price", "subject", "subjectId", "subjectType"], "type": "object", "properties": {"ownerType": {"title": "收款账户类型", "maxLength": 32, "minLength": 1, "type": "string"}, "ownerId": {"title": "收款人id", "maxLength": 32, "minLength": 1, "type": "string"}, "ownerName": {"title": "收款账户所有者名称", "type": "string"}, "payOwnerType": {"title": "支付所有者类型", "type": "string"}, "payOwnerId": {"title": "支付所有者ID", "type": "string"}, "payOwnerName": {"title": "支付所有者名称", "type": "string"}, "accountId": {"title": "支付账户id", "type": "string"}, "subjectType": {"title": "账单类型（ORDERS：订单支付）", "maxLength": 32, "minLength": 0, "type": "string"}, "subjectId": {"title": "主体信息（订单号）", "maxLength": 128, "minLength": 1, "type": "string"}, "subjectThirdId": {"title": "三方订单号（订单号）", "type": "string"}, "subject": {"title": "主题描述信息", "maxLength": 128, "minLength": 1, "type": "string"}, "subtitle": {"title": "副标题", "maxLength": 512, "minLength": 0, "type": "string"}, "price": {"title": "金额", "type": "number"}, "currency": {"title": "币种，人民币CNY", "type": "string"}, "notifyUrl": {"title": "通知回调URL，POST", "type": "string"}, "returnUrl": {"title": "页面跳转地址", "maxLength": 512, "minLength": 0, "type": "string"}, "remark": {"title": "备注", "type": "string"}}}, "PayOrdersRespVo_t1869": {"title": "[PayOrdersRespVo]创建支付订单", "type": "object", "properties": {"id": {"title": "主键", "type": "string"}}}, "MallProductAddSpaceVo_t2920": {"title": "[ProductAddSpaceVo]商品新增规格（SKU）", "required": ["quantity", "selling", "unitPrice"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "默认商品图片", "maxLength": 256, "minLength": 0, "type": "string"}, "selling": {"title": "可销售", "type": "boolean"}, "unitPrice": {"title": "单价", "type": "number"}, "unitPriceShow": {"title": "显示价格", "type": "number"}, "upc": {"title": "商品upc条码", "maxLength": 32, "minLength": 0, "type": "string"}, "sku": {"title": "sku", "maxLength": 32, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 32, "minLength": 0, "type": "string"}, "quantity": {"title": "共享库存数量", "type": "integer", "format": "int32"}, "properties": {"title": "商品规格属性", "type": "array", "items": {"$ref": "#/components/schemas/MallProductPropertyVo_t214"}}}}, "MallProductAddVo_t618": {"title": "[ProductAddVo]商品信息（SPU）", "required": ["brandName", "categoryId", "contentPc", "name", "pic", "spaces", "type"], "type": "object", "properties": {"spu": {"title": "SPU信息", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "图片信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "video": {"title": "视频信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "contentMobile": {"title": "移动版详情", "maxLength": 65535, "minLength": 0, "type": "string"}, "contentPc": {"title": "PC版详情", "maxLength": 65535, "minLength": 1, "type": "string"}, "properties": {"title": "商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]", "type": "array", "items": {"$ref": "#/components/schemas/MallProductPropertyVo_t214"}}, "type": {"title": "商品类型：BASE：基础商品", "maxLength": 32, "minLength": 1, "type": "string"}, "categoryId": {"title": "商品分类", "maxLength": 32, "minLength": 1, "type": "string"}, "brandName": {"title": "品牌名称", "maxLength": 64, "minLength": 1, "type": "string"}, "spaces": {"title": "不同规格数据", "maxItems": 2147483647, "minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/MallProductAddSpaceVo_t2920"}}}, "description": "商品信息"}, "MallProductUpVo_t5271": {"title": "[ProductUpSubmitVo]商品上下架申请", "required": ["operate"], "type": "object", "properties": {"operate": {"title": "操作", "type": "string", "enum": ["UP", "DOWN"]}, "remark": {"title": "备注信息", "type": "string"}}}, "MallProductArchivedVo_t6368": {"title": "[ProductArchivedVo]商品归档", "required": ["archived"], "type": "object", "properties": {"archived": {"title": "是否归档", "type": "boolean"}, "remark": {"title": "备注信息", "type": "string"}}}, "StockModifyOneDepotSpacesParamVo_t2728": {"title": "[StockModifyOneDepotParamVo]调整库存接口", "required": ["allowNegative", "depotId", "incoming", "spacesId"], "type": "object", "properties": {"spacesId": {"title": "商品编号", "type": "string"}, "depotId": {"title": "仓库地区, 默认传：ALL", "type": "string"}, "allowNegative": {"title": "是否允许预定", "type": "boolean"}, "incoming": {"title": "预定数", "type": "integer", "format": "int32"}, "quantity": {"title": "库存数", "type": "integer", "format": "int32"}, "remark": {"title": "备注", "type": "string"}}}, "StockModifyAttrParamBo_t4921": {"title": "[StockModifyAttrParamBo]调整库存接口", "required": ["allowNegative", "depotId", "incoming", "spacesId"], "type": "object", "properties": {"spacesId": {"title": "spacesId", "type": "string"}, "depotId": {"title": "仓库id", "type": "string"}, "allowNegative": {"title": "库存允许为负数", "type": "boolean"}, "incoming": {"title": "预定数", "type": "integer", "format": "int32"}}}, "StockDeleteParamVo_t3357": {"title": "[StockStockBatchDeleteParamVo]删除库存信息", "required": ["spacesIds"], "type": "object", "properties": {"spacesIds": {"type": "array", "items": {"type": "string"}}, "depotIds": {"title": "仓库地区", "type": "array", "items": {"title": "仓库地区", "type": "string"}}}}, "StockDeductParamVo_t3226": {"title": "[StockStockDeductParamVo]调整库存接口", "required": ["allowNegative", "depotId", "num", "spacesId", "type"], "type": "object", "properties": {"spacesId": {"title": "商品编号", "type": "string"}, "depotId": {"title": "仓库地区", "type": "string"}, "type": {"title": "操作类型，ADD：增加， SET：设置", "type": "string"}, "num": {"title": "数量", "type": "integer", "format": "int32"}, "allowNegative": {"title": "是否允许预定", "type": "boolean"}, "remark": {"title": "备注", "type": "string"}}}, "StockAddParamVo_t9690": {"title": "[StockStockAddParamVo]调整库存接口", "required": ["allowNegative", "depotId", "incoming", "spacesId"], "type": "object", "properties": {"spacesId": {"title": "商品编号", "type": "string"}, "depotId": {"title": "仓库地区", "type": "string"}, "allowNegative": {"title": "库存允许为负数", "type": "boolean"}, "incoming": {"title": "预定数", "type": "integer", "format": "int32"}, "quantity": {"title": "库存数", "type": "integer", "format": "int32"}, "remark": {"title": "备注", "type": "string"}}}, "MallProductStock_t9121": {"title": "[MallProductStock]发布商品信息", "required": ["allowNegative", "createTime", "depotId", "id", "incoming", "quantity", "spacesId", "storeId"], "type": "object", "properties": {"allowNegative": {"title": "允许预定", "type": "boolean"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "depotId": {"title": "仓库，ALL：所有地区", "maxLength": 32, "minLength": 1, "type": "string"}, "id": {"title": "规格id", "maxLength": 32, "minLength": 1, "type": "string"}, "incoming": {"title": "预定数", "type": "integer", "format": "int32"}, "quantity": {"title": "库存数", "type": "integer", "format": "int32"}, "spacesId": {"title": "商品id", "maxLength": 32, "minLength": 1, "type": "string"}, "storeId": {"title": "店铺id", "maxLength": 32, "minLength": 1, "type": "string"}}, "description": "发布商品信息"}, "MallCategoryDetailVo_t5081": {"title": "[MallProductCategoryDetailVo]分类信息", "required": ["ban", "id", "idParent"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 32, "minLength": 0, "type": "string"}, "ban": {"title": "禁用", "type": "boolean"}, "idParent": {"title": "父节点，根节点0", "maxLength": 32, "minLength": 1, "type": "string"}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}, "propertyStructs": {"title": "属性定义信息", "type": "array", "items": {"$ref": "#/components/schemas/MallProductCategoryPropertyStructBo_t6310"}}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}}, "description": "分类信息"}, "IotDeviceAddVo_t7454": {"title": "[IotDeviceModifyVo]设备表修改", "type": "object", "properties": {"name": {"title": "设备名称", "maxLength": 64, "minLength": 0, "type": "string"}, "osVersion": {"title": "系统版本", "maxLength": 32, "minLength": 0, "type": "string"}, "roleCode": {"title": "角色信息", "maxLength": 256, "minLength": 0, "type": "string"}, "sn": {"title": "设备编号", "maxLength": 255, "minLength": 0, "type": "string"}, "secret": {"title": "授权码", "maxLength": 32, "minLength": 0, "type": "string"}, "type": {"title": "设备类型，ESP32", "maxLength": 32, "minLength": 0, "type": "string"}}, "description": "设备表修改"}, "IotDevice_t8925": {"title": "[IotDevice]设备表", "required": ["id"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "设备名称", "maxLength": 64, "minLength": 0, "type": "string"}, "osVersion": {"title": "系统版本", "maxLength": 32, "minLength": 0, "type": "string"}, "roleCode": {"title": "角色信息", "maxLength": 256, "minLength": 0, "type": "string"}, "sn": {"title": "设备编号", "maxLength": 255, "minLength": 0, "type": "string"}, "secret": {"title": "授权码", "maxLength": 32, "minLength": 0, "type": "string"}, "type": {"title": "设备类型，ESP32", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "bondType": {"title": "绑定主体，USER：用户", "maxLength": 32, "minLength": 0, "type": "string"}, "bondId": {"title": "绑定id", "maxLength": 32, "minLength": 0, "type": "string"}}, "description": "设备表"}, "CmsSubjectAddVo_t1934": {"title": "[CmsSubjectAddVo]cms主题添加", "required": ["contentStructs", "name", "ord", "path"], "type": "object", "properties": {"idParent": {"title": "父一级id，根节点传0", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "主题名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序", "type": "integer", "format": "int32"}, "path": {"title": "地址，支持：英文数字下划线", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}, "contentStructs": {"title": "内容结构", "type": "array", "items": {"$ref": "#/components/schemas/CmsSubjectContentStructBo_t1333"}}}, "description": "[CmsSubjectAddVo]cms主题添加"}, "CmsSubjectDetailVo_t5881": {"title": "[CmsSubjectDetailVo]cms主题详情", "type": "object", "properties": {"createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "type": "string"}, "idParent": {"title": "父一级id", "type": "string"}, "modifyTime": {"title": "更新时间", "type": "string", "format": "date-time"}, "name": {"title": "主题名称", "type": "string"}, "ord": {"title": "排序", "type": "integer", "format": "int32"}, "path": {"title": "地址，支持：英文数字下划线", "type": "string"}, "remark": {"title": "备注信息", "type": "string"}, "contentStructs": {"title": "内容结构", "type": "array", "items": {"$ref": "#/components/schemas/CmsSubjectContentStructBo_t1333"}}}, "description": "[CmsSubjectDetailVo]cms主题详情"}, "CmsCategoryAddVo_t1108": {"title": "[CmsCategoryAddVo]cms分类添加", "required": ["name", "ord"], "type": "object", "properties": {"idParent": {"title": "父ID，根节点为0", "maxLength": 32, "minLength": 0, "type": "string"}, "path": {"title": "地址，支持：英文数字下划线", "maxLength": 64, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序值", "type": "integer", "format": "int32"}, "remark": {"title": "备注", "maxLength": 4096, "minLength": 0, "type": "string"}}, "description": "[CmsCategoryAddVo]cms分类添加"}, "CmsCategory_t7361": {"title": "[CmsCategory]CMS分类", "required": ["id", "idParent", "name", "ord", "path", "subjectId"], "type": "object", "properties": {"createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "createUserId": {"title": "创建人", "maxLength": 32, "minLength": 0, "type": "string"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "idParent": {"title": "父ID，根节点为0", "maxLength": 32, "minLength": 1, "type": "string"}, "path": {"title": "地址，支持：英文数字下划线", "maxLength": 64, "minLength": 1, "type": "string"}, "modifyTime": {"title": "修改时间", "type": "string", "format": "date-time"}, "modifyUserId": {"title": "修改人", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序值", "type": "integer", "format": "int32"}, "remark": {"title": "备注", "maxLength": 4096, "minLength": 0, "type": "string"}, "subjectId": {"title": "主题信息", "maxLength": 32, "minLength": 1, "type": "string"}}, "description": "CMS分类"}, "WxMpRegisterParamVo_t7678": {"title": "[WxMpRegisterParamVo]微信小程序注册参数", "required": ["ubtoken"], "type": "object", "properties": {"ubtoken": {"title": "ubtoken数据", "type": "string"}, "headImg": {"title": "头像", "type": "string"}, "nickname": {"title": "昵称", "type": "string"}, "phoneNumberIv": {"title": "手机号加密iv", "type": "string"}, "phoneNumberEncryptedData": {"title": "手机号加密后密文", "type": "string"}, "json": {"title": "附带参数", "type": "string"}}}, "TokenMessageVo_t805": {"title": "[TokenMessageVo]token信息", "type": "object", "properties": {"token": {"title": "token", "type": "string", "example": "abe3dg3wdg3t2fwef"}, "expries": {"title": "token有效期（单位：秒）", "type": "integer", "format": "int64", "example": 15000000000}, "rtoken": {"title": "刷新Token", "type": "string"}, "rexpries": {"title": "刷新token有效期（单位：秒）", "type": "integer", "format": "int64", "example": 15000000000}}, "description": "登录后的Token信息"}, "WxMpSeesionParamVo_t6085": {"title": "[WxMpSeesionParamVo]请求参数", "required": ["code", "sysAppId"], "type": "object", "properties": {"code": {"title": "code", "type": "string"}, "sysAppId": {"title": "当前系统sysAppId，非微信的appId", "type": "string"}}}, "WxMpSessionVo_t9252": {"title": "[WxMpSessionVo]微信小程序token响应数据", "type": "object", "properties": {"openId": {"title": "用户在当前应用唯一标识openId", "type": "string", "example": "14234"}, "unionId": {"title": "用户唯一标识unionId", "type": "string", "example": "12343"}, "ubtoken": {"title": "当前用户绑定token", "type": "string", "example": "abe3dg3wdg3t2fwef"}, "ubexpries": {"title": "ubtoken有效期（单位：秒）", "type": "integer", "format": "int64", "example": 15000000000}}}, "UserBondAddVo_t800": {"title": "[UserBondAddVo]用户绑定表", "required": ["ubtoken", "userId"], "type": "object", "properties": {"ubtoken": {"title": "用户绑定token", "type": "string"}, "userId": {"title": "当前用户可传0", "type": "string"}}, "description": "用户绑定表"}, "UserBondTokenParamVo_t4392": {"title": "[UserBondTokenParamVo]用户绑定表", "required": ["ubtoken", "userId"], "type": "object", "properties": {"ubtoken": {"title": "用户绑定token", "type": "string"}, "userId": {"title": "用户标识id", "type": "string"}}, "description": "用户绑定表"}, "UserBondAddByMeVo_t3686": {"title": "[UserBondAddByMeVo]用户绑定自身", "required": ["ubtoken"], "type": "object", "properties": {"ubtoken": {"title": "用户绑定token", "type": "string"}}, "description": "用户绑定表"}, "UbtokenAddBo_t8002": {"title": "[UbtokenAddBo]用户绑定表", "required": ["openId", "sysAppId"], "type": "object", "properties": {"sysAppId": {"title": "系统应用id", "type": "string"}, "openId": {"title": "绑定该应用的用户id", "type": "string"}, "unionId": {"title": "用户标识id", "type": "string"}, "name": {"title": "用户名", "type": "string"}, "dataJson": {"title": "扩展信息", "type": "string"}}, "description": "用户绑定表"}, "UbtokenVo_t863": {"title": "[UbtokenVo]用户绑定表", "type": "object", "properties": {"ubtoken": {"title": "ubtoken", "type": "string"}, "ubexpries": {"title": "ubtoken有效期（单位：秒）", "type": "integer", "format": "int64", "example": 15000000000}}, "description": "用户绑定表"}, "UserInfoChangePasswordMeParamVo_t269": {"title": "[UserInfoChangePasswordParamVo]用户修改自身密码", "required": ["password"], "type": "object", "properties": {"oldPassword": {"title": "原始密码", "type": "string"}, "password": {"title": "新密码", "type": "string"}}}, "SysConfig_t5574": {"title": "[SysConfig]配置信息表", "required": ["code", "id", "valid"], "type": "object", "properties": {"code": {"title": "Code信息", "maxLength": 256, "minLength": 1, "type": "string"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "remark": {"title": "备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "valid": {"title": "启用", "type": "boolean"}, "value": {"title": "值信息", "maxLength": 65535, "minLength": 0, "type": "string"}}, "description": "配置信息表"}, "SysRoleAddVo_t4668": {"title": "[SysRoleAddVo]新增系统角色", "required": ["code", "name"], "type": "object", "properties": {"code": {"title": "角色code(ROLE_前缀)", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "角色名称", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "新增系统角色管理内容"}, "SysRole_t5325": {"title": "[SysRole]角色信息", "required": ["code", "hidden", "name"], "type": "object", "properties": {"code": {"title": "角色code(ROLE_前缀)", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "角色名称", "maxLength": 64, "minLength": 1, "type": "string"}, "hidden": {"title": "角色隐藏", "type": "boolean"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "角色信息"}, "SysRolePower_t6135": {"title": "[SysRolePower]角色对权限授权", "required": ["powerCode", "roleCode"], "type": "object", "properties": {"roleCode": {"title": "角色code", "maxLength": 32, "minLength": 1, "type": "string"}, "powerCode": {"title": "权限code", "maxLength": 32, "minLength": 1, "type": "string"}}, "description": "角色对权限授权"}, "RolePowerModifyPowerVo_t8099": {"title": "[RolePowerModifyPowerVo]修改角色权限", "required": ["powerCodes", "roleCode"], "type": "object", "properties": {"roleCode": {"title": "roleCode", "type": "string"}, "powerCodes": {"title": "新的权限Code", "type": "array", "items": {"title": "新的权限Code", "type": "string"}}}}, "SysPowerAddVo_t1994": {"title": "[SysPowerAddVo]新增系统权限", "required": ["code"], "type": "object", "properties": {"code": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "parentCode": {"title": "父code", "maxLength": 32, "minLength": 0, "type": "string"}, "depCode": {"title": "依赖code", "type": "string"}, "appCode": {"title": "应用编号(spring.application.name)", "maxLength": 32, "minLength": 0, "type": "string"}, "clsName": {"title": "类名称", "maxLength": 128, "minLength": 0, "type": "string"}, "clsMethod": {"title": "方法名称", "maxLength": 32, "minLength": 0, "type": "string"}, "httpMethod": {"title": "请求方法", "maxLength": 32, "minLength": 0, "type": "string"}, "url": {"title": "url地址", "maxLength": 128, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "新增系统权限管理内容"}, "SysPower_t4562": {"title": "[SysPower]系统权限表", "required": ["code"], "type": "object", "properties": {"code": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "parentCode": {"title": "父code", "maxLength": 32, "minLength": 0, "type": "string"}, "depCode": {"title": "依赖code", "type": "string"}, "appCode": {"title": "应用编号(spring.application.name)", "maxLength": 32, "minLength": 0, "type": "string"}, "clsName": {"title": "类名称", "maxLength": 128, "minLength": 0, "type": "string"}, "clsMethod": {"title": "方法名称", "maxLength": 32, "minLength": 0, "type": "string"}, "httpMethod": {"title": "请求方法", "maxLength": 32, "minLength": 0, "type": "string"}, "url": {"title": "url地址", "maxLength": 128, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "系统权限表"}, "SysPowerModifyParentCodeVo_t6126": {"title": "[SysPowerModifyParentCodeVo]系统权限修改系统父Code", "required": ["code"], "type": "object", "properties": {"code": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "parentCode": {"title": "父code", "maxLength": 32, "minLength": 0, "type": "string"}}, "description": "系统权限修改系统父Code"}, "SysDicBo_t5099": {"title": "[SysDicBo]系统数据字典", "required": ["id", "name", "valid"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "code": {"title": "code", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "valid": {"title": "启用", "type": "boolean"}, "fields": {"title": "字段信息", "type": "array", "items": {"$ref": "#/components/schemas/SysDicFieldBo_t7739"}}, "remark": {"title": "备注", "type": "string"}}}, "TokenRevokeParamVo_t7444": {"title": "[TokenRevokeParamVo]Token吊销参数", "type": "object", "properties": {"rtoken": {"title": "刷新token", "type": "string"}, "token": {"title": "token", "type": "string"}}}, "TokenRefreshParamVo_t6697": {"title": "[TokenRefreshParamVo]Token签发参数", "required": ["rtoken"], "type": "object", "properties": {"rtoken": {"title": "刷新token", "type": "string"}}}, "LoginParamVo_t9485": {"title": "[LoginParamVo]用户登录参数信息", "required": ["password", "username"], "type": "object", "properties": {"username": {"title": "用户名", "maxLength": 32, "minLength": 1, "type": "string", "example": "admin"}, "password": {"title": "密码，预处理后结果md5(pwd)", "type": "string", "example": "21232f297a57a5a743894a0e4a801fc3"}, "captchaUuid": {"title": "验证码uuid", "type": "string", "example": "4a0e4a801f21232f297a57a5a74389c3"}, "captchaCode": {"title": "验证码", "type": "string", "example": "a4b2"}}}, "PutTokenBodyVo_t5284": {"title": "[PutTokenBodyVo]设置token", "required": ["id"], "type": "object", "properties": {"id": {"title": "id", "type": "string"}}}, "AuthUser_t1422": {"title": "[AuthUser]授权用户信息", "type": "object", "properties": {"id": {"title": "主账户Id", "type": "string", "example": "test001_admin"}, "deviceType": {"title": "终端类型", "type": "string", "example": "USER"}, "type": {"title": "类型：RREFRESH_TOKEN: 刷新token", "type": "string", "example": "TOKEN"}, "roleCodes": {"title": "具有的角色", "type": "array", "example": "ROLE_ADMIN,ROLE_USER", "items": {"title": "具有的角色", "type": "string", "example": "ROLE_ADMIN,ROLE_USER"}}, "data": {"title": "用户携带的信息", "type": "string", "example": "\"{'schoolId':'adminid'}\""}}}, "TokenModelBo_t4879": {"title": "[TokenModelBo]token相关信息", "type": "object", "properties": {"authUser": {"$ref": "#/components/schemas/AuthUser_t1422"}, "id": {"type": "string"}, "token": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "exp": {"type": "string", "format": "date-time"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/TokenModelRoleBo_t2770"}}, "tokenExpiresTimeLong": {"type": "integer", "format": "int64"}}}, "TokenModelPowerBo_t3552": {"title": "[TokenModelPowerBo]token权限信息", "type": "object", "properties": {"code": {"title": "主键", "type": "string"}, "appCode": {"title": "应用编号(spring.application.name)", "type": "string"}, "clsName": {"title": "类名称", "type": "string"}, "clsMethod": {"title": "方法名称", "type": "string"}, "httpMethod": {"title": "请求方法", "type": "string"}, "url": {"title": "url地址", "type": "string"}}}, "TokenModelRoleBo_t2770": {"title": "[TokenModelRoleBo]token角色信息", "type": "object", "properties": {"code": {"title": "角色code", "type": "string", "example": "ROLE_ADMIN"}, "powers": {"title": "权限信息", "type": "array", "items": {"$ref": "#/components/schemas/TokenModelPowerBo_t3552"}}}}, "TokenSignParamVo_t1994": {"title": "[TokenSignParamVo]Token签发参数", "required": ["clientId", "none", "sign", "timestemp"], "type": "object", "properties": {"clientId": {"title": "客户端id", "type": "string"}, "none": {"title": "客户端随机uuid", "type": "string"}, "timestemp": {"title": "时间戳，单位：毫秒", "type": "integer", "format": "int64"}, "sign": {"title": "签名：SHA256(clientId + timestemp + none + clientSecret)", "type": "string"}}}, "MsgVerifyCodeBo_t9347": {"title": "[MsgVerifyCodeBo]验证码返回值", "type": "object", "properties": {"account": {"title": "账户", "type": "string"}, "code": {"title": "code", "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}}}, "MsgRecord_t4443": {"title": "[MsgRecord]消息日志", "required": ["again", "againNum", "createTime", "id", "sendResult", "sent", "type"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "type": {"title": "消息类型(SMS：短信、EMAIL：邮件、MP：小程序)", "maxLength": 32, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "businessCode": {"title": "业务编号，建议：auth.user.head", "maxLength": 32, "minLength": 0, "type": "string"}, "tmplCode": {"title": "模板编号", "maxLength": 32, "minLength": 0, "type": "string"}, "subject": {"title": "标题", "maxLength": 512, "minLength": 0, "type": "string"}, "contentTmpl": {"title": "模板内容", "maxLength": 65535, "minLength": 0, "type": "string"}, "contentData": {"title": "对象内容", "maxLength": 65535, "minLength": 0, "type": "string"}, "contentDetail": {"title": "详细内容", "maxLength": 65535, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "toCode": {"title": "目标账户", "maxLength": 65535, "minLength": 0, "type": "string"}, "ccCode": {"title": "抄送账户", "maxLength": 65535, "minLength": 0, "type": "string"}, "bccCode": {"title": "隐蔽抄送账户", "maxLength": 65535, "minLength": 0, "type": "string"}, "attachment": {"title": "附件url", "maxLength": 65535, "minLength": 0, "type": "string"}, "cid": {"title": "cid", "maxLength": 65535, "minLength": 0, "type": "string"}, "signName": {"title": "签名", "maxLength": 64, "minLength": 0, "type": "string"}, "toPage": {"title": "目标地址", "maxLength": 256, "minLength": 0, "type": "string"}, "sent": {"title": "已发送", "type": "boolean"}, "sendTime": {"title": "发送时间", "type": "string", "format": "date-time"}, "platformBizId": {"title": "平台业务id号", "maxLength": 64, "minLength": 0, "type": "string"}, "platformUpExtendCode": {"title": "上传编号", "type": "integer", "format": "int64"}, "platformTmplCode": {"title": "平台模板编号", "maxLength": 64, "minLength": 0, "type": "string"}, "sendResult": {"title": "发送结果（WAIT：等待，SUCCESS：成功，FAIL：失败）", "maxLength": 32, "minLength": 1, "type": "string"}, "sendResultTime": {"title": "发送结果回执时间", "type": "string", "format": "date-time"}, "sendResultMsg": {"title": "发送结果信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "again": {"title": "已重试", "type": "boolean"}, "againTime": {"title": "重试时间", "type": "string", "format": "date-time"}, "againNum": {"title": "重试次数", "type": "integer", "format": "int32"}, "againId": {"title": "重试id", "type": "string"}, "parentId": {"title": "上一级id", "type": "string"}}, "description": "消息日志"}, "ToolsRespVo_t8566": {"title": "[ToolsRespVo]工具响应类", "type": "object", "properties": {"content": {"title": "内容", "type": "string"}}}, "MsgReceipt_t369": {"title": "[MsgReceipt]消息回执", "required": ["id", "receiveTime", "type"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "type": {"title": "类型(SMS:短信,EMAIL:邮件,MP:小程序)", "maxLength": 32, "minLength": 1, "type": "string"}, "recordId": {"title": "找到关联的record_id", "maxLength": 32, "minLength": 0, "type": "string"}, "receiveTime": {"title": "接收到的时间", "type": "string", "format": "date-time"}, "remoteCode": {"title": "远端账户", "maxLength": 32, "minLength": 0, "type": "string"}, "destCode": {"title": "目标编号", "maxLength": 32, "minLength": 0, "type": "string"}, "signName": {"title": "签名", "maxLength": 32, "minLength": 0, "type": "string"}, "content": {"title": "内容", "maxLength": 65535, "minLength": 0, "type": "string"}}, "description": "消息回执"}, "ResLink_t9276": {"title": "[ResLink]连接映射", "required": ["createTime", "id"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "expireTime": {"title": "过期时间", "type": "string", "format": "date-time"}, "businessCode": {"title": "业务编号", "maxLength": 32, "minLength": 1, "type": "string"}, "type": {"title": "类型", "maxLength": 32, "minLength": 1, "type": "string"}, "toPage": {"title": "跳转目标编号", "maxLength": 512, "minLength": 1, "type": "string"}, "data": {"title": "数据集合", "maxLength": 1024, "minLength": 0, "type": "string"}}, "description": "连接映射"}, "ReportQuery_t675": {"title": "[ReportQuery]报告查询信息", "required": ["createTime", "execTotalQuery", "id", "name", "queryCmd", "valid"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "valid": {"title": "生效", "type": "boolean"}, "execTotalQuery": {"title": "执行统计查询", "type": "boolean"}, "code": {"title": "配置信息code，URL路径", "maxLength": 64, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "queryCmd": {"title": "查询指令", "type": "string"}, "columnsJson": {"title": "字段信息的JSON格式", "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}}, "description": "报告查询信息"}, "MsgInbox_t9651": {"title": "[MsgInbox]站内信息", "required": ["businessCode", "createTime", "del", "id", "notify", "title", "type", "userId"], "type": "object", "properties": {"businessCode": {"title": "业务编号", "maxLength": 32, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "del": {"title": "删除", "type": "boolean"}, "delTime": {"title": "删除时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "msg": {"title": "内容", "maxLength": 512, "minLength": 0, "type": "string"}, "notify": {"title": "已读", "type": "boolean"}, "notifyTime": {"title": "已读时间", "type": "string", "format": "date-time"}, "title": {"title": "标题", "maxLength": 256, "minLength": 1, "type": "string"}, "toData": {"title": "跳转数据集合", "maxLength": 1024, "minLength": 0, "type": "string"}, "toPage": {"title": "跳转目标编号", "maxLength": 512, "minLength": 0, "type": "string"}, "type": {"title": "类型", "maxLength": 32, "minLength": 1, "type": "string"}, "userId": {"title": "用户id", "maxLength": 32, "minLength": 1, "type": "string"}}, "description": "站内信息"}, "CaptchaMsgRo_t6330": {"title": "[CaptchaMsgRo]结果数据", "type": "object", "properties": {"uuid": {"title": "uuid", "type": "string"}, "code": {"title": "验证码值", "type": "string"}, "level": {"title": "验证码类型安全级别", "type": "integer", "format": "int32"}, "type": {"title": "验证码类型，SC:普通，GC:GIF图片，CC:中文，CGC:中文GIF，AC:算数", "type": "string", "enum": ["SC", "GC", "CC", "CGC", "AC", "RV"]}}}, "CaptchaRespVo_t1734": {"title": "[CaptchaRespVo]验证码响应类", "type": "object", "properties": {"content": {"title": "内容", "type": "string"}}}, "RefundBillVo_t7765": {"title": "[RefundBillVo]支付账单详情", "type": "object", "properties": {"accountId": {"title": "账户id", "type": "string"}, "billId": {"title": "账单号", "type": "string"}, "channel": {"title": "支付通道", "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "退款单号", "type": "string"}, "notifyUrl": {"title": "通知url", "type": "string"}, "operateId": {"title": "操作id", "type": "string"}, "operateType": {"title": "操作目标（BILL，LEDGER）", "type": "string"}, "price": {"title": "金额", "type": "number"}, "currency": {"title": "币种，人民币CNY", "type": "string"}, "refund": {"title": "已退款完成", "type": "boolean"}, "refundPlatformNo": {"title": "退款三方流水号", "type": "string"}, "refundTime": {"title": "退款时间", "type": "string", "format": "date-time"}, "subject": {"title": "主题", "type": "string"}, "subjectId": {"title": "退款主题编号", "type": "string"}, "subjectThirdId": {"title": "主题三方id", "type": "string"}, "remark": {"title": "备注", "type": "string"}}}, "PayResultVo_t9046": {"title": "[PayResultVo]支付结果", "type": "object", "properties": {"pay": {"title": "支付成功", "type": "boolean"}}}, "PayOrdersVo_t3263": {"title": "[PayOrdersVo]支付订单详情", "required": ["createTime", "id", "invalid", "ownerId", "ownerType", "pay", "payNotify", "price", "refundPrice", "subject", "subjectId"], "type": "object", "properties": {"accountId": {"title": "第三方账户表", "maxLength": 32, "minLength": 0, "type": "string"}, "channel": {"title": "支付通道", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建日志时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "invalid": {"title": "账单无效", "type": "boolean"}, "notifyUrl": {"title": "通知回调URL，POST", "maxLength": 65535, "minLength": 0, "type": "string"}, "ownerId": {"title": "收款公司账户", "maxLength": 32, "minLength": 1, "type": "string"}, "ownerName": {"title": "收款公司名称", "type": "string"}, "ownerType": {"title": "所有者类型", "maxLength": 32, "minLength": 1, "type": "string"}, "pay": {"title": "支付状态", "type": "boolean"}, "payAccount": {"title": "支付账户", "maxLength": 64, "minLength": 0, "type": "string"}, "payBankCode": {"title": "交易银行卡编码", "maxLength": 32, "minLength": 0, "type": "string"}, "payBankNo": {"title": "交易银行卡号", "maxLength": 32, "minLength": 0, "type": "string"}, "payNotify": {"title": "支付结果通知（NOTHING：无需通知，WAIT：待通知，SUCCESS：通知成功，FAIL：通知失败）", "maxLength": 32, "minLength": 1, "type": "string"}, "payNotifyMsg": {"title": "支付推送错误信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "payNotifyTime": {"title": "支付消息推送时间", "type": "string", "format": "date-time"}, "payOwnerId": {"title": "支付公司id", "maxLength": 32, "minLength": 0, "type": "string"}, "payOwnerName": {"title": "支付公司名称", "maxLength": 64, "minLength": 0, "type": "string"}, "payOwnerType": {"title": "支付所有者类型", "maxLength": 32, "minLength": 0, "type": "string"}, "payPlatformNo": {"title": "交易平台号（alipay，weixinpay的交易号）", "maxLength": 128, "minLength": 0, "type": "string"}, "payRemark": {"title": "支付备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "payTime": {"title": "支付时间", "type": "string", "format": "date-time"}, "payUserId": {"title": "支付用户id", "maxLength": 32, "minLength": 0, "type": "string"}, "payUserName": {"title": "支付用户名称", "maxLength": 32, "minLength": 0, "type": "string"}, "payUserPhone": {"title": "支付用户联系方式", "maxLength": 32, "minLength": 0, "type": "string"}, "price": {"title": "金额", "type": "number"}, "currency": {"title": "币种，人民币CNY", "type": "string"}, "refundPrice": {"title": "已退款金额", "type": "number"}, "refundRemark": {"title": "退款备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "refundTime": {"title": "退款时间", "type": "string", "format": "date-time"}, "refund": {"title": "已全额退款", "type": "boolean"}, "remark": {"title": "备注信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "returnUrl": {"title": "页面跳转地址", "maxLength": 65535, "minLength": 0, "type": "string"}, "subject": {"title": "主体描述信息", "maxLength": 128, "minLength": 1, "type": "string"}, "subjectId": {"title": "主体信息（订单号）", "maxLength": 128, "minLength": 1, "type": "string"}, "subjectThirdId": {"title": "主题三方Id", "maxLength": 32, "minLength": 0, "type": "string"}, "subjectType": {"title": "主体类型（ORDERS：订单支付）", "maxLength": 32, "minLength": 0, "type": "string"}, "subtitle": {"title": "副标题", "maxLength": 128, "minLength": 0, "type": "string"}}}, "PayBillVo_t7404": {"title": "[PayBillVo]支付账单详情", "required": ["createTime", "id", "invalid", "ownerId", "ownerType", "pay", "payNotify", "price", "refundPrice", "subject", "subjectId"], "type": "object", "properties": {"accountId": {"title": "第三方账户表", "maxLength": 32, "minLength": 0, "type": "string"}, "channel": {"title": "支付通道", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建日志时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "invalid": {"title": "账单无效", "type": "boolean"}, "notifyUrl": {"title": "通知回调URL，POST", "maxLength": 65535, "minLength": 0, "type": "string"}, "ownerId": {"title": "收款公司账户", "maxLength": 32, "minLength": 1, "type": "string"}, "ownerName": {"title": "收款公司名称", "type": "string"}, "ownerType": {"title": "所有者类型", "maxLength": 32, "minLength": 1, "type": "string"}, "pay": {"title": "支付状态", "type": "boolean"}, "payAccount": {"title": "支付账户", "maxLength": 64, "minLength": 0, "type": "string"}, "payBankCode": {"title": "交易银行卡编码", "maxLength": 32, "minLength": 0, "type": "string"}, "payBankNo": {"title": "交易银行卡号", "maxLength": 32, "minLength": 0, "type": "string"}, "payNotify": {"title": "支付结果通知（NOTHING：无需通知，WAIT：待通知，SUCCESS：通知成功，FAIL：通知失败）", "maxLength": 32, "minLength": 1, "type": "string"}, "payNotifyMsg": {"title": "支付推送错误信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "payNotifyTime": {"title": "支付消息推送时间", "type": "string", "format": "date-time"}, "payOwnerId": {"title": "支付公司id", "maxLength": 32, "minLength": 0, "type": "string"}, "payOwnerName": {"title": "支付公司名称", "maxLength": 64, "minLength": 0, "type": "string"}, "payOwnerType": {"title": "支付所有者类型", "maxLength": 32, "minLength": 0, "type": "string"}, "payPlatformNo": {"title": "交易平台号（alipay，weixinpay的交易号）", "maxLength": 128, "minLength": 0, "type": "string"}, "payRemark": {"title": "支付备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "payTime": {"title": "支付时间", "type": "string", "format": "date-time"}, "payUserId": {"title": "支付用户id", "maxLength": 32, "minLength": 0, "type": "string"}, "payUserName": {"title": "支付用户名称", "maxLength": 32, "minLength": 0, "type": "string"}, "payUserPhone": {"title": "支付用户联系方式", "maxLength": 32, "minLength": 0, "type": "string"}, "price": {"title": "金额", "type": "number"}, "currency": {"title": "币种，人民币CNY", "type": "string"}, "refundPrice": {"title": "已退款金额", "type": "number"}, "refundRemark": {"title": "退款备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "refundTime": {"title": "退款时间", "type": "string", "format": "date-time"}, "refund": {"title": "已全额退款", "type": "boolean"}, "remark": {"title": "备注信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "returnUrl": {"title": "页面跳转地址", "maxLength": 65535, "minLength": 0, "type": "string"}, "subject": {"title": "主体描述信息", "maxLength": 128, "minLength": 1, "type": "string"}, "subjectId": {"title": "主体信息（订单号）", "maxLength": 128, "minLength": 1, "type": "string"}, "subjectThirdId": {"title": "主题三方Id", "maxLength": 32, "minLength": 0, "type": "string"}, "subjectType": {"title": "主体类型（ORDERS：订单支付）", "maxLength": 32, "minLength": 0, "type": "string"}, "subtitle": {"title": "副标题", "maxLength": 128, "minLength": 0, "type": "string"}}}, "PayAccountVo_t3552": {"title": "[PayAccountVo]第三方支付账户", "required": ["allow", "ban", "channel", "createTime", "id", "name", "ownerId", "ownerName", "ownerType", "platformConfirm", "valid", "verify"], "type": "object", "properties": {"allow": {"title": "是否已授权", "type": "boolean"}, "allowTime": {"title": "授权时间", "type": "string", "format": "date-time"}, "ban": {"title": "账户被禁止", "type": "boolean"}, "banRemark": {"title": "账户禁止原因", "maxLength": 65535, "minLength": 0, "type": "string"}, "banTime": {"title": "账户被禁止时间", "type": "string", "format": "date-time"}, "banUserId": {"title": "账户禁止操作人", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 32, "minLength": 1, "type": "string"}, "ownerId": {"title": "所有者id", "maxLength": 32, "minLength": 1, "type": "string"}, "ownerName": {"title": "所有者名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ownerType": {"title": "所有者类型：（ORG：供应商，SOTRE：店铺，USER：用户）", "maxLength": 32, "minLength": 1, "type": "string"}, "platformAccountJson": {"title": "平台账户信息", "maxLength": 65535, "minLength": 0, "type": "string"}, "platformConfirm": {"title": "平台方确认，支付宝审核", "maxLength": 5, "minLength": 1, "type": "string"}, "platformConfirmRemark": {"title": "平台方确认备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "platformConfirmTime": {"title": "平台方确认时间", "type": "string", "format": "date-time"}, "platformConfirming": {"title": "平台方确认中", "maxLength": 5, "minLength": 0, "type": "string"}, "remark": {"title": "备注", "maxLength": 65535, "minLength": 0, "type": "string"}, "returnUrl": {"title": "绑定完成后回调地址", "maxLength": 65535, "minLength": 0, "type": "string"}, "serviceAccountId": {"title": "服务商账户id", "maxLength": 32, "minLength": 0, "type": "string"}, "channel": {"title": "类型（ALIPAY：支付宝，WX_PAY：微信支付）", "maxLength": 32, "minLength": 1, "type": "string"}, "valid": {"title": "启用", "type": "boolean"}, "verify": {"title": "验证接口", "type": "boolean"}, "verifyBillId": {"title": "验证订单id", "maxLength": 32, "minLength": 0, "type": "string"}, "verifyRemark": {"title": "验证结果", "maxLength": 65535, "minLength": 0, "type": "string"}, "verifyTime": {"title": "验证时间", "type": "string", "format": "date-time"}}, "description": "第三方支付账户"}, "MallProductListSpaceVo_t8295": {"title": "[ProductListSpaceVo]商品sku信息", "required": ["createTime", "id", "productId", "selling", "unitPrice"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "默认商品图片", "maxLength": 256, "minLength": 0, "type": "string"}, "selling": {"title": "可销售", "type": "boolean"}, "unitPrice": {"title": "单价", "type": "number"}, "unitPriceShow": {"title": "显示价格", "type": "number"}, "upc": {"title": "商品upc条码", "maxLength": 32, "minLength": 0, "type": "string"}, "sku": {"title": "sku", "maxLength": 32, "minLength": 0, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "规格id", "maxLength": 32, "minLength": 1, "type": "string"}, "productId": {"title": "商品id", "maxLength": 32, "minLength": 1, "type": "string"}, "storeId": {"title": "店铺id", "maxLength": 32, "minLength": 0, "type": "string"}, "auxSnapshotId": {"title": "快照Id", "maxLength": 32, "minLength": 0, "type": "string"}}, "description": "商品sku信息"}, "MallProductListVo_t760": {"title": "[ProductListVo]商品信息", "required": ["archived", "brandName", "createTime", "id", "modifyTime", "name", "pic", "state", "storeId", "type", "unitPrice", "up", "upSubmit"], "type": "object", "properties": {"spu": {"title": "SPU信息", "maxLength": 32, "minLength": 0, "type": "string"}, "name": {"title": "名称", "maxLength": 64, "minLength": 0, "type": "string"}, "pic": {"title": "图片信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "video": {"title": "视频信息", "maxLength": 1024, "minLength": 0, "type": "string"}, "archived": {"title": "归档", "type": "boolean"}, "archivedRemark": {"title": "归档建议", "maxLength": 512, "minLength": 0, "type": "string"}, "archivedTime": {"title": "归档时间", "type": "string", "format": "date-time"}, "archivedUserId": {"title": "归档操作人id", "maxLength": 32, "minLength": 0, "type": "string"}, "categoryId": {"title": "商品类型", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "spu", "maxLength": 32, "minLength": 1, "type": "string"}, "modifyTime": {"title": "修改时间", "type": "string", "format": "date-time"}, "type": {"title": "商品类型：BASE：基础商品", "maxLength": 32, "minLength": 1, "type": "string"}, "state": {"title": "状态, NEW、UP、ARCHIVED", "maxLength": 32, "minLength": 1, "type": "string"}, "storeId": {"title": "店铺id", "maxLength": 32, "minLength": 1, "type": "string"}, "brandName": {"title": "品牌名称", "maxLength": 64, "minLength": 1, "type": "string"}, "unitPrice": {"title": "商品单价", "type": "number"}, "unitPriceShow": {"title": "商品单价", "type": "number"}, "up": {"title": "商品状态（-100：驳回，0: 未上架，100：上架中）", "type": "integer", "format": "int32"}, "upRemark": {"title": "上架意见", "maxLength": 256, "minLength": 0, "type": "string"}, "upSubmit": {"title": "上架申请", "type": "boolean"}, "upSubmitRemark": {"title": "上架申请理由", "maxLength": 512, "minLength": 0, "type": "string"}, "upSubmitTime": {"title": "上架申请", "type": "string", "format": "date-time"}, "upSubmitUserId": {"title": "上架申请人", "maxLength": 32, "minLength": 0, "type": "string"}, "upTime": {"title": "上架时间", "type": "string", "format": "date-time"}, "upUserId": {"title": "上架操作人id", "maxLength": 32, "minLength": 0, "type": "string"}, "spaces": {"title": "不同规格数据", "type": "array", "items": {"$ref": "#/components/schemas/MallProductListSpaceVo_t8295"}}}, "description": "商品信息"}, "MallCategoryListVo_t8243": {"title": "[MallCategoryListVo]分类信息", "required": ["ban", "createTime", "id", "idParent"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 32, "minLength": 0, "type": "string"}, "ban": {"title": "禁用", "type": "boolean"}, "idParent": {"title": "父节点，根节点0", "maxLength": 32, "minLength": 1, "type": "string"}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}}, "description": "分类信息"}, "MallProductCategoryBo_t1833": {"title": "[MallProductCategoryBo]分类信息", "required": ["ban", "createTime", "id", "idParent"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "name": {"title": "名称", "maxLength": 32, "minLength": 0, "type": "string"}, "ban": {"title": "禁用", "type": "boolean"}, "idParent": {"title": "父节点，根节点0", "maxLength": 32, "minLength": 1, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "parentCategory": {"$ref": "#/components/schemas/MallProductCategoryBo_t1833"}, "propertyStructs": {"title": "属性定义信息", "type": "array", "items": {"$ref": "#/components/schemas/MallProductCategoryPropertyStructBo_t6310"}}, "remark": {"title": "备注", "maxLength": 512, "minLength": 0, "type": "string"}}, "description": "分类信息"}, "CmsCategoryStaticVo_t1431": {"title": "[CmsCategoryStaticVo]cms分类显示", "required": ["name", "ord"], "type": "object", "properties": {"name": {"title": "名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序值", "type": "integer", "format": "int32"}, "path": {"title": "访问地址", "maxLength": 64, "minLength": 0, "type": "string"}}, "description": "[CmsCategoryStaticVo]cms分类显示"}, "ContentLogVo_t8306": {"title": "[ContentLogVo]内容日志响应", "type": "object", "properties": {"pv": {"title": "PV访问量", "type": "integer", "format": "int32"}}}, "CmsSubject_t9735": {"title": "[CmsSubject]网站主题", "required": ["createTime", "id", "modifyTime", "name", "ord", "path"], "type": "object", "properties": {"contentStructJson": {"title": "内容结构定义", "maxLength": 65535, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "idParent": {"title": "父一级id", "maxLength": 32, "minLength": 0, "type": "string"}, "modifyTime": {"title": "更新时间", "type": "string", "format": "date-time"}, "name": {"title": "主题名称", "maxLength": 64, "minLength": 1, "type": "string"}, "ord": {"title": "排序", "type": "integer", "format": "int32"}, "path": {"title": "地址，支持：英文数字下划线", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注信息", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "网站主题"}, "MapRegion_t887": {"title": "[MapRegion]地图地理信息", "required": ["code", "createTime", "level", "name"], "type": "object", "properties": {"code": {"title": "地址编号", "maxLength": 32, "minLength": 1, "type": "string"}, "codeParent": {"title": "父节点，0为根节点", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "level": {"title": "级别", "type": "integer", "format": "int32"}, "name": {"title": "地址名称", "maxLength": 64, "minLength": 1, "type": "string"}, "remark": {"title": "备注", "maxLength": 256, "minLength": 0, "type": "string"}}, "description": "地图地理信息"}, "AuxAutocomplete_t7962": {"title": "[AuxAutocomplete]自动补全列表", "required": ["bsCode", "createTime", "id", "valid"], "type": "object", "properties": {"attr": {"title": "附带属性信息：prop.code(cpu)", "maxLength": 32, "minLength": 0, "type": "string"}, "bsCode": {"title": "业务编号：mall.category.prop", "maxLength": 32, "minLength": 1, "type": "string"}, "code": {"title": "主编号： categoryId(2001)", "maxLength": 32, "minLength": 0, "type": "string"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "remark": {"title": "备注", "maxLength": 256, "minLength": 0, "type": "string"}, "userId": {"title": "用户id", "maxLength": 32, "minLength": 0, "type": "string"}, "valid": {"title": "有效性", "type": "boolean"}, "value": {"title": "推荐字符串", "maxLength": 4096, "minLength": 0, "type": "string"}}, "description": "自动补全列表"}, "WxMpAuthBridgeRespVo_t616": {"title": "[WxMpAuthBridgeRespVo]桥接响应信息", "type": "object", "properties": {"id": {"title": "id", "type": "string"}}}, "UserBondVo_t1801": {"title": "[UserBondVo]用户绑定表", "type": "object", "properties": {"userId": {"title": "用户id", "type": "string"}, "sysAppId": {"title": "系统应用id", "type": "string"}, "openId": {"title": "绑定该应用的用户id", "type": "string"}, "unionId": {"title": "用户标识id", "type": "string"}, "createTime": {"title": "绑定时间", "type": "string", "format": "date-time"}}, "description": "用户绑定表"}, "UbTokenMsgBo_t1698": {"title": "[UbTokenMsgBo]用户绑定表", "type": "object", "properties": {"sysAppId": {"title": "系统应用id", "type": "string"}, "openId": {"title": "绑定该应用的用户id", "type": "string"}, "unionId": {"title": "用户标识id", "type": "string"}, "createTime": {"title": "绑定时间", "type": "string", "format": "date-time"}, "过期时间": {"title": "过期时间", "type": "string", "format": "date-time"}, "expries": {"type": "integer", "format": "int64"}}, "description": "用户绑定表"}, "UserPowerModelVo_t4598": {"title": "[UserPowerModelVo]用户权限信息", "type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/components/schemas/TokenModelRoleBo_t2770"}}}}, "RolePowerGroupVo_t3038": {"title": "[RolePowerGroupVo]角色分组信息", "type": "object", "properties": {"code": {"title": "code", "type": "string"}, "count": {"title": "数量", "type": "integer", "format": "int32"}}}, "User_t7700": {"title": "[User]用户信息表", "required": ["id"], "type": "object", "properties": {"id": {"title": "主键", "maxLength": 32, "minLength": 1, "type": "string"}, "username": {"title": "用户名", "maxLength": 32, "minLength": 0, "type": "string"}, "password": {"title": "用户密码md5(md5(pwd).toLowerCase()+pwd_salt)", "maxLength": 32, "minLength": 0, "type": "string"}, "pwdSalt": {"title": "密码盐", "maxLength": 32, "minLength": 0, "type": "string"}, "ban": {"title": "账户禁用", "type": "boolean"}, "startTime": {"title": "账户生效时间", "type": "string", "format": "date-time"}, "endTime": {"title": "账户禁用时间", "type": "string", "format": "date-time"}, "createTime": {"title": "创建时间", "type": "string", "format": "date-time"}, "roleCode": {"title": "角色信息", "type": "string"}, "loginLastAddr": {"title": "上次登录地址", "type": "string"}, "loginLastIp": {"title": "上次登录IP", "type": "string"}, "loginLastTime": {"title": "上次登录时间", "type": "string", "format": "date-time"}, "loginThisAddr": {"title": "这次登录地址", "type": "string"}, "loginThisIp": {"title": "这次登录IP", "type": "string"}, "loginThisTime": {"title": "这次登录时间", "type": "string", "format": "date-time"}}, "description": "用户信息表"}, "AuthBridgeStateBo_t5940": {"title": "[AuthBridgeStateBo]授权桥状态信息", "type": "object", "properties": {"type": {"title": "授权桥类型", "type": "string", "enum": ["SEND_TOKEN", "ACCEPT_TOKEN"]}, "state": {"title": "[AuthBridgeState]授权桥状态信息", "type": "string", "enum": ["BEGIN", "PENDING", "END"]}}}, "MsgInboxDeleteAllVo_t8891": {"title": "[MsgInboxDeleteAllVo]删除设置", "type": "object", "properties": {"notify": {"title": "notify", "type": "boolean"}, "del": {"title": "del", "type": "boolean"}}}, "UserBondDeleteVo_t4659": {"title": "[UserBondDeleteVo]用户绑定表", "required": ["appId", "openId", "userId"], "type": "object", "properties": {"appId": {"title": "appId", "type": "string"}, "openId": {"title": "openId", "type": "string"}, "userId": {"title": "用户id", "type": "string"}}, "description": "用户绑定表"}, "UserBondDeleteByMeVo_t4441": {"title": "[UserBondDeleteByMeVo]解绑用户自己", "required": ["appId", "openId"], "type": "object", "properties": {"appId": {"title": "appId", "type": "string"}, "openId": {"title": "openId", "type": "string"}}, "description": "用户绑定表"}}}}