
const fs = require('fs/promises');
const ofs = require('fs');
const path = require('path');
var http = require('http');


(async () => {
  const config = require('../../swagger2ts');

  for (let i = 0; i < config.swaggers.length; i++) {
    let swagger = config.swaggers[i];
    let obj = null;
    if (swagger.url && (swagger.url.startsWith('http://') || swagger.url.startsWith('https://'))) {

      let resp = await new Promise((r, j) => {
        const req = http.request(swagger.url, (res) => {
          let data = '';
          res.on('data', (chunk) => {
            data += chunk;
          });
          res.on('end', () => {
            try {
              const json = JSON.parse(data);
              r({ data: json, status: res.statusCode })
            } catch (e) {
              console.error('Error parsing JSON:', e.message);
            }
          });
        });
        req.on('error', (e) => {
          console.error(`Problem with request: ${e.message}`);
          j(e);
        });
        req.end();
      });
      if (!resp.status || resp.status >= 400 || resp.status < 100) {
        throw Error(`生成失败，请求数据失败，${swagger.url}，响应内容：${resp}`);
      }
      obj = resp.data;
    } else {
      let url = path.join(__dirname, `../../${swagger.url}`);
      obj = JSON.parse(await fs.readFile(url));
    }

    await analysisSwagger(obj, swagger, { gconfig: config });
  }
  console.log(`构建完成生成完成。`);

})();

const clearDir = async (swagger, config, { gconfig }) => {
  let filepath = config.outdir ? `/${config.outdir}` : ``;
  let outfile = path.join(__dirname, `../../${gconfig.outbasedir}${filepath}`);
  // await fs.removeSync(outfile, {});
  return new Promise((r, j) => {
    ofs.rm(outfile, { force: true, recursive: true }, () => {
      r();
    })
  });
}

// 分析Swagger
const analysisSwagger = async (swagger, config, { gconfig }) => {
  await clearDir(swagger, config, { gconfig });
  let tag2list = await swagger2list(swagger);
  for (let tag in tag2list) {
    let list = tag2list[tag];
    // 解析出的url
    let urlPre = await analysisUrlPre(list);
    urlPre = urlPre.replace(/\-(\w)/g, (all, letter) => letter.toUpperCase());
    // 合成后的方法
    let methodList = [];
    // 需要装载的数据对象
    let paramVoName2ref = {};
    let fileMsg = {
      keyvalue: false
    };
    let voList = [];
    for (let tagIdx in list) {
      let methodMsg = list[tagIdx];
      // 方法名称
      buildMethod({ urlPre, config }, methodMsg, methodList, paramVoName2ref, voList, swagger);
    }

    {
      // 合成Vo所需对象
      let _ref2obj = { ...paramVoName2ref };
      for (let voName in paramVoName2ref) {
        buildVo(_ref2obj, swagger, voName, voList, fileMsg);
      }
    }

    // 合成后内容
    let fileStr = buildFileMsg(methodList, voList, fileMsg);

    {
      let filepath = config.outdir ? `/${config.outdir}${urlPre}` : `${urlPre}`;
      filepath += 'Api.ts';
      let outfile = path.join(__dirname, `../../${gconfig.outbasedir}${filepath}`);
      await writeFile(outfile, fileStr);
      console.log(`写出文件${gconfig.outbasedir}${filepath}成功。`)
    }
  }
}

const writeFile = async (outfile, data) => {
  const mkdirDir = (fp) => {
    if (!ofs.existsSync(fp)) {
      let dirpath = path.dirname(fp);
      mkdirDir(dirpath);
      ofs.mkdirSync(fp);
    }
  }
  let dirpath = path.dirname(outfile);
  mkdirDir(dirpath);
  await fs.writeFile(outfile, data);
}

const buildFileMsg = (methodList, voList, fileMsg) => {
  // 合成文件
  let methodStrs = methodList.join('\n');
  let boBodyStr = voList.join('\n');
  let fileStr = `
import { request } from '@/utils/request'${'\n'}\
import { config } from '@/config/global.config'${'\n'}\
${'\n'}\
${fileMsg && fileMsg.keyvalue ? 'type KeyValue<T> = { [K in string]: T }\n\n' : ''}\
${boBodyStr}\
export default {\
${'\n'}\
${methodStrs}\
${'\n'}\
}\
${'\n'}\
`
  return fileStr;
}

// 合成Vo
const buildVo = (paramName2ref, swagger, name, voList, fileMsg) => {
  // 合成Vo所需对象
  let voMsg = paramName2ref[name];
  let swaggerVo = ref2model(voMsg.ref, swagger);
  let paramFieldList = [];

  let type = null;
  for (let field in swaggerVo.properties) {
    let fieldObj = swaggerVo.properties[field];

    if (fieldObj.type == 'array') {
      let stype = 'void';
      stype = swaggerType2jsType(fieldObj.items);
      if (stype == null) {
        let _ref = fieldObj.items.$ref;
        if (!_ref) {
          if (fieldObj.items.type && fieldObj.items.type == 'object') {
            stype = 'any';
          } else {
            if (fieldObj.items.type) {
              stype = 'any';
            } else {
              if (field == 'childrens') {
                stype = 'any';
              } else {
                stype = 'any';
                // throw new Error('不支持的类型，请联系研发人员。');
              }
            }
          }
        } else {
          stype = buildDependVo(voMsg, field, paramName2ref, swagger, _ref, voList);
        }
      }
      type = `Array<${stype}>`;
    } else if (fieldObj.type == 'object') {
      let stype = swaggerType2jsType(fieldObj.additionalProperties);
      if (stype == null) {
        let _ref = fieldObj.additionalProperties.$ref;
        if (!_ref) {
          throw new Error('不支持的类型，请联系研发人员。');
        }
        stype = buildDependVo(voMsg, field, paramName2ref, swagger, _ref, voList);
      }
      type = `KeyValue<${stype}>`;
      fileMsg.keyvalue = true;
    } else {
      type = swaggerType2jsType(fieldObj);
      if (!type) {
        if (fieldObj.$ref) {
          let _ref = fieldObj.$ref;
          type = buildDependVo(voMsg, field, paramName2ref, swagger, _ref, voList);
        } else {
          if (field == 'childrens') {
            type = 'any';
          } else {
            throw new Error('不支持的类型，请联系研发人员。');
          }
        }
      }
    }
    let fieldDesc = fieldObj.title ? fieldObj.title : fieldObj.description;
    fieldDesc = fieldDesc ? fieldDesc : field;
    if (swaggerVo.required && swaggerVo.required.indexOf(field) >= 0) {
      fieldDesc = fieldDesc + `, 必须字段`
    }
    // if (fieldDesc) {
    if (fieldObj.minLength || fieldObj.minLength == 0) {
      fieldDesc = fieldDesc + `, 最小长度：${fieldObj.minLength}`
    }
    if (fieldObj.maxLength || fieldObj.maxLength == 0) {
      fieldDesc = fieldDesc + `, 最大长度：${fieldObj.maxLength}`
    }
    if (fieldObj.enum) {
      let txt = fieldObj.enum.join(', ');
      fieldDesc = fieldDesc + `, 值：${txt}`
    }
    paramFieldList.push(`\
  /**\n\
   * ${fieldDesc}${'\n'}\
   */\n\
  ${field}?: ${type};\
`);
    //         } else {
    //             paramFieldList.push(`\
    //     ${field}?: ${type};\
    // `);
    //         }
  }

  let paramFieldStr = paramFieldList.join('\n');
  let classBody = `\
/**\n\
 * ${swaggerVo.title}路径请求参数\n\
 */\n\
export interface ${voMsg.name} {${'\n'}\
${'\n'}\
${paramFieldStr}
}${'\n'}\
${'\n'}\
`
  voList.push(classBody);
}

// 构建依赖vo
const buildDependVo = (voMsg, field, paramName2ref, swagger, _ref, voList) => {
  let nref = putRef2obj(_ref, null, paramName2ref);

  if (!nref.new) {
    return nref.name;
  } else {
    buildVo(paramName2ref, swagger, nref.name, voList);
    return nref.name;
  }
}

// 构建方法
const buildMethod = (tagObj, methodMsg, methodList, paramVoName2ref, voList, swagger) => {
  let { urlPre, config } = tagObj;
  let { url, method, methodObj } = methodMsg;
  let operationId = dealOperationId(methodObj);
  let { paramPaths, paramQuerys, bodyVoName } = dealParam(methodObj, paramVoName2ref, swagger);

  let paramList = [];
  let paramDescList = [];
  let paramConfigList = [];
  // 第二个参数内容
  let param02BodyStr = '';
  // 路径参数
  let { url: methodUrl } = paramPathDeal(paramPaths, url, paramList, paramDescList);
  // 请求参数
  if (paramQuerys && paramQuerys.length > 0) {
    let uqs = urlPre.split('/');
    uqs.push(operationId);
    let name = uqs.filter(x => x).map(x => strFirstUpper(x)).join('') + 'QueryParam';
    paramDescList.push(`   * @param query 请求参数`);
    paramList.push(`query: ${name}`);
    paramConfigList.push(`params: query`);

    paramQueryDeal(methodObj, name, paramQuerys, voList);
  }
  // 请求body
  if (bodyVoName) {
    paramList.push(`body: ${bodyVoName}`);
    paramDescList.push(`   * @param body 请求Body参数`);
    if (method == 'get' || method == 'delete') {
      paramConfigList.push('data: body');
    } else {
      param02BodyStr = `body, `;
    }
  } else {
    if (method != 'get' && method != 'delete') {
      param02BodyStr = "{}, ";
    }
  }
  // 响应参数
  let returnStr = 'void';
  {
    let resp = methodObj.responses[200];
    if (resp && resp.content) {
      let schema = getOneBody(resp.content).schema;
      let r = buildRefVo(schema, paramVoName2ref, swagger);
      if (r) {
        returnStr = r;
      }
    }
  }

  // 合成方法
  let paramDescStr = paramDescList.length > 0 ? '\n' + paramDescList.join('\n') : '';
  let paramPathStr = paramList.join(', ');
  let paramConfigStr = paramConfigList.length > 0 ? paramConfigList.join(', ') + ', ' : '';
  let returnDescStr = returnStr && returnStr != 'void' ? '\n   * @returns 数据' : '';
  let methodStr = `
  /**\n\
   * ${methodObj.summary}${paramDescStr}${returnDescStr}
   */
  ${operationId}: async (${paramPathStr}): Promise<${returnStr}> => await request.${method}(${'`${config.baseUrl.apiUrl}'}${config.basePath}${methodUrl}${'`'}, ${param02BodyStr}{ ${paramConfigStr}token: false, pageRole: false }),\
${'\n'}\
`;
  methodList.push(methodStr);

}

// 参数构建对象参数
const buildRefVo = (schema, paramVoName2ref, swagger) => {
  let returnStr = null;
  if (schema != null) {
    if (schema.type == 'array') {
      if (schema.items.$ref) {
        let ref = schema.items.$ref;
        // let uqs = urlPre.split('/');
        // uqs.push(operationId);
        // let name = uqs.filter(x => x).map(x => strFirstUpper(x)).join('') + 'Resp';
        let name = null;
        returnStr = putRef2obj(ref, name, paramVoName2ref, swagger).name;
      } else if (schema.items.type = 'string') {
        returnStr = 'string';
      }
      returnStr = `Array<${returnStr}>`;
    } else if (schema.$ref) {
      let ref = schema.$ref;
      // let uqs = urlPre.split('/');
      // uqs.push(operationId);
      // let name = uqs.filter(x => x).map(x => strFirstUpper(x)).join('') + 'Resp';
      let name = null;
      returnStr = putRef2obj(ref, name, paramVoName2ref, swagger).name;
    } else if (schema.type) {
      returnStr = swaggerType2jsType(schema);
    }
  }
  return returnStr;
}

// 处理请求Query参数
const paramQueryDeal = (methodObj, name, paramQuerys, voList) => {
  let paramFieldList = [];
  for (let i in paramQuerys) {
    let query = paramQuerys[i];
    let type = swaggerType2jsType(query);
    let desc = query.description;
    desc = desc ? desc : query.name;
    if (query.required) {
      desc = desc + `, 必须参数`;
    }
    paramFieldList.push(`\
  /**\n\
   * ${desc}${'\n'}\
   */\n\
  ${query.name}?: ${type};\
`);
  }
  // 信息
  let paramFieldStr = paramFieldList.join('\n');
  let classBody = `\
/**\n\
 * ${methodObj.summary}路径请求参数\n\
 */\n\
export interface ${name} {${'\n'}\
${'\n'}\
${paramFieldStr}
}${'\n'}\
${'\n'}\
`
  voList.push(classBody);
}

// 处理路径参数
const paramPathDeal = (paramPaths, url, paramList, paramDescList) => {
  for (let i in paramPaths) {
    let paramPath = paramPaths[i];
    let type = swaggerType2jsType(paramPath);
    let name = paramPath.name;
    url = replaceAll(url, '{' + name + '}', '${' + name + '}');
    paramList.push(`${name}: ${type}`);

    paramDescList.push(`   * @param ${name}: ${paramPath.description ? paramPath.description : name}`)
  }
  return { url };
}

// 处理请求方法名称
const dealOperationId = (methodObj) => {
  let operationId = methodObj.operationId;
  if (operationId) {
    let idx = operationId.lastIndexOf('Using');
    if (idx >= 0) {
      operationId = operationId.substring(0, idx);
    }
    idx = operationId.lastIndexOf('_');
    if (idx >= 0) {
      operationId = operationId.substring(0, idx);
    }
  }
  return operationId;
}

// 用于分析当前方法所需参数
const dealParam = (methodObj, paramVoName2ref, swagger) => {
  let paramPaths = [];
  let paramQuerys = [];
  for (let pidx in methodObj.parameters) {
    let param = methodObj.parameters[pidx];
    if (param.in == 'path') {
      paramPaths.push(param);
    } else if (param.in == 'query') {
      paramQuerys.push(param);
    }
  }
  let bodyVoName = null;
  // 请求body分析
  if (!bodyVoName && methodObj.requestBody) {
    let body = getOneBody(methodObj.requestBody.content);
    if (body && body.schema) {
      let r = buildRefVo(body.schema, paramVoName2ref, swagger);
      if (r) {
        bodyVoName = r;
      }
    }
  }
  return { paramPaths, paramQuerys, bodyVoName }
}

const getOneBody = (body) => {
  for (let key in body) {
    return body[key];
  }
  return null;
}

// ref信息
const putRef2obj = (ref, defName, paramVoName2ref, swagger) => {
  let name = defName ? defName : dealModelName(ref, swagger);
  name = name ? name : refDeal(ref);

  let paramObj = paramVoName2ref[name];
  if (paramObj) {
    // 重名判定
    if (paramObj.ref != ref) {
      let tag = '123456789';
      let cts = ref.split('_');
      if (cts.length > 1) {
        tag = cts[1];
      }

      let nname = null;
      for (let t in tag) {
        let et = tag[t];
        nname = name + et;
        if (paramVoName2ref[nname]) {
          if (paramVoName2ref[nname].ref == ref) {
            // 找到相同对象
            return { name: nname, new: false };
          }
          // 名称仍然被占用
          nname = null;
        } else {
          break;
        }
      }
      if (!nname) {
        throw new Error('模型名称重复，且列表不够使用, name: ' + name);
      }
      // 新对象
      name = nname;
      paramObj = {
        name: name,
        ref: ref,
      };
      paramVoName2ref[name] = paramObj;
      return { name: nname, new: true };
    } else {
      return { name: name, new: false };
    }
  } else {
    // 未重名，新对象
    paramObj = {
      name: name,
      ref: ref,
    };
    paramVoName2ref[name] = paramObj;
    return { name: name, new: true };
  }
}

// 通过ref提取对象名称
const dealModelName = (ref, swagger) => {
  if (!ref) {
    return null;
  }
  let nref = refDeal(ref);
  nref = nref.split('_')[0];
  // let bean = ref2model(ref, swagger);
  return nref;
}

// 通过ref提取对象
const ref2model = (ref, swagger) => {
  if (!ref || !swagger) {
    return null;
  }
  let rc = ref.replace('#/', '');
  let sa = rc.split('/');
  let sv = swagger;
  for (let key in sa) {
    sv = sv[sa[key]];
  }
  return sv;
}

// ref 切割
const refDeal = (ref) => {
  let idx = ref.lastIndexOf('/');
  if (idx >= 0) {
    ref = ref.substring(idx + 1, ref.length);
  }
  return ref;
}

// 分析url，进行匹配
const analysisUrlPre = async (list = [{ url: '', method: '', methodObj: {} }]) => {
  let url = null;
  for (let i in list) {
    let obj = list[i];
    if (!url) {
      url = obj.url;
      let idx = url.indexOf('{');
      if (idx >= 0) {
        url = url.substring(0, idx - 1);
      }
      continue;
    }
    let turl = obj.url;
    while (!(turl.startsWith(url + '/') || turl == url)) {
      let us = url.split('/');
      us = us.slice(0, us.length - 1);
      url = us.join('/');
    }
  }
  return url;
}

// 将swagger中方法按照tag进行归类
const swagger2list = async (swagger) => {
  // 准备数据，解析内容
  let tage2list = {};
  for (let url in swagger.paths) {
    let urlObj = swagger.paths[url];
    let methodStrs = '';

    for (let method in urlObj) {
      let methodObj = urlObj[method];
      let tag = methodObj.tags && methodObj.tags.length > 0 ? methodObj.tags[0] : 'default';
      let list = tage2list[tag];
      if (!list) {
        list = [];
        tage2list[tag] = list;
      }
      list.push({
        url,
        method,
        methodObj,
      });
    }
  }
  return tage2list;
}

const swaggerType2jsType = (stype) => {
  let type = stype.type;
  if (type == 'string') {
    if ('date-time' == stype.format) {
      return 'number';
    }
    return 'string';
  } else if (type == 'integer') {
    return 'number';
  } else if (type == 'number') {
    return 'number';
  } else if (type == 'boolean') {
    return 'boolean';
  } else if (type == 'array') {
    let items = swaggerType2jsType(stype.items);
    return `Array<${items}>`;
  } else if (stype.schema) {
    return swaggerType2jsType(stype.schema)
  } else {
    return null;
  }
}

// 将首字母大写
const strFirstUpper = (str) => {
  if (!str || str.length <= 0) {
    return str;
  }
  return str.substring(0, 1).toUpperCase() + str.substring(1, str.length);
}

// 全部替换字符串
const replaceAll = (str, oldStr, newStr) => {
  return str.replace(new RegExp(oldStr, 'g'), newStr);
}

