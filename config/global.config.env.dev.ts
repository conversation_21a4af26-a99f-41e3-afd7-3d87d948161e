import { ConfigBaseUrl, ConfigFs, ConfigSysInfo } from "./global.config";

export default {

  baseUrl: <ConfigBaseUrl>{
    wwwUrl: 'https://mg.vspace.yheart.cn',
    // fileApiUrl: '/api/sys/fs',
    fileApiUrl: '',
    // fileApiUrl: 'http://api.cloud.yheart.cn/sys/fs',

    // apiUrl: 'https://mg.cloud.yheart.cn/api',
    // apiUrl: 'https://mg.vspace.yheart.cn/api',
    apiUrl: '/api',
    // apiUrl: 'http://localhost:8080/api',
    // apiUrl: 'http://localhost:8081',
    // apiUrl: 'http://localhost:8081/cloud-module-auth-server',
    // wsUrl: 'ws://localhost:8088/ws/msg',
    wsUrl: 'ws://mg.goodgifts.yheart.cn/ws/msg',
  },
  fs: <ConfigFs>{
    // alioss and server
    uptype: 'server',
    downtype: 'server',
  },
  sysInfo: <ConfigSysInfo>{
    projectAppId: 'mg',
    wxMpSysAppId: '20K7WWTC959BTB97',
    sysAppId: '0',
  }

}
