
// process.env.NODE_ENV

import { MenuItem } from "~/model/common";

let env = process.env.NODE_ENV;
env = env ? env : 'dev';
const envConfig = require('~/config/global.config.env.' + env)

export type ConfigBaseUrl = {
  /**
   * 当前页面地址
   */
  wwwUrl: string,
  /**
   * 文件系统文件url，为空时使用默认的服务器地址
   */
  fileApiUrl: string,
  /**
   * 服务器api接口地址
   */
  apiUrl: string,
  /**
   * WebSocket地址
   */
  wsUrl: string,
}
export type ConfigFs = {
  /**
   * 文件模式: alioss and server
   */
  uptype: 'alioss' | 'server',
  downtype: 'alioss' | 'server',
}
export type ConfigSysInfo = {
  /**
   * 小程序appId，对应后端服务器的id
   */
  wxMpSysAppId: string,

  sysAppId: string,

  /**
   * 项目APPID号
   */
  projectAppId: string,
}


export type ConfigOption = {
  [key: string]: any;
  i18n: {
    locale: string,
    locales: {
      [key: string]: {
        title: string;
        msg: any;
      }
    }
  },
  baseUrl: ConfigBaseUrl,
  fs: ConfigFs,
  sysInfo: ConfigSysInfo,
  menu: {
    items: MenuItem[]
  }
}

let config: ConfigOption = {
  i18n: {
    locale: 'zh',
    locales: {
      'en': { title: 'English', msg: require('~/locales/en.json') },
      'zh': { title: '中文', msg: require('~/locales/zh.json') },
    }
  },
  menu: {
    items: [
      {
        icon: "el-icon-odometer",
        name: "index",
        url: "/def",
        role: [],
      },
      {
        icon: "el-icon-shopping-cart-full",
        name: "mall",
        role: [],
        subs: [
          {
            name: 'mall-mg',
            role: ["POWER_MALL_MANAGE"],
            subs: [
              {
                name: "mall-mallinfo",
                url: "/mg/mall/mallinfo",
              },
              {
                name: "mall-category",
                url: "/mg/mall/category",
              },
              {
                name: "mall-brand",
                url: "/mg/mall/brand",
              },
              {
                name: "mall-product",
                url: "/mg/mall/product",
              },
              {
                name: "mall-orders",
                url: "/mg/mall/orders",
              },
            ]
          },
          {
            name: 'mall-store',
            role: ["POWER_MALL_MANAGE"],
            subs: [
              {
                name: "mall-freight",
                url: "/store/mall/freight",
              },
              {
                name: "mall-product",
                url: "/store/mall/product",
              },
              {
                name: "mall-orders",
                url: "/store/mall/orders",
              },
            ]
          }
        ]
      },
      {
        icon: "el-icon-house",
        name: "wms",
        subs: [
          {
            name: "wms-warehouse",
            url: "/def/wms/warehouse",
            urlExp: ['^/def/wms/warehouse/.*'],
            role: ["POWER_WMS_MANAGE"],
          },
          {
            name: "wms-container",
            url: "/def/wms/container",
            role: ["POWER_WMS_MANAGE"],
          },
          {
            name: "wms-product",
            url: "/def/wms/product",
            role: ["POWER_WMS_MANAGE"],
          },
          {
            name: "wms-transaction",
            url: "/def/wms/transaction",
            role: ["POWER_WMS_MANAGE"],
          },
          {
            name: "wms-warehouse-box",
            url: "/def/wms/warehouse-box",
            role: ["POWER_WMS_MANAGE"],
          },
          {
            name: "wms-inventory",
            url: "/def/wms/inventory",
            role: ["POWER_WMS_MANAGE"],
          },
        ]
      },
      {
        icon: "el-icon-mobile-phone",
        name: "cms-manage",
        subs: [
          {
            icon: "el-icon-document",
            name: "cms-subject",
            url: "/def/cms/subject",
            role: ["POWER_CMS_SUBJECT"],
          }
        ]
      },
      {
        icon: "el-icon-user",
        name: "user",
        subs: [{
          name: "user-mg",
          url: "/mg/user/mg",
          toPageRole: 'ROLE_ADMIN',
          role: ["ROLE_ADMIN"],
        }]
      },
      {
        icon: "el-icon-lock",
        name: "role_power",
        subs: [
          {
            name: "power-role",
            url: "/def/power/role",
            role: ["POWER_ROLE_MANAGE"],
          },
          {
            name: "power-power",
            url: "/def/power/power",
            role: ["POWER_POWER_MANAGE"],
          },
          {
            name: "power-rule-USER-MALL_CATEGORY",
            url: "/def/power/rule/USER/MALL_CATEGORY",
            role: ["POWER_RULE_MANAGE"],
          },
          {
            name: "power-rule-USER-WMS_WAREHOUSE",
            url: "/def/power/rule/USER/WMS_WAREHOUSE",
            role: ["POWER_RULE_MANAGE"],
          },
        ],
      },
      {
        icon: "el-icon-setting",
        name: "sysbase",
        subs: [
          {
            name: "sysbase-dic",
            url: "/def/sysbase/dic",
            role: ["ROLE_ADMIN"],
          },
          {
            name: "sysbase-msg-inbox-sender",
            url: "/def/sysbase/msg-inbox/sender",
            role: ["ROLE_ADMIN"],
          },
          {
            name: "sysbase-sysconfig",
            url: "/def/sysbase/sysconfig",
            role: ["ROLE_ADMIN"],
          },
          {
            name: "sysbase-report-query",
            url: "/def/sysbase/report/query",
            role: ["ROLE_ADMIN"],
          },
          {
            name: "sysbase-log",
            url: "/def/sysbase/log",
            role: ["ROLE_ADMIN"],
          },
          {
            name: "sysbase-bpm",
            url: "/def/sysbase/bpm",
            role: ["ROLE_ADMIN"],
          },
          {
            name: 'sysbase-maintain-upgrade',
            url: "/def/sysbase/maintain/upgrade",
            role: ["ROLE_ADMIN"],
          },
        ],
      },
      {
        icon: "el-icon-notebook-2",
        name: "demo2",
        subs: [
          {
            name: "demo",
            // url: "/def/demo",
            // show: false,
            // keeplived: true,
            // pageRole: ['ROLE_ADMIN'],
            // toPageRole: 'ROLE_ADMIN',
            // role: ["ROLE_ADMIN"],
            subs: [
              {
                name: "demo",
                url: "/def/demo",
                // show: false,
                // keeplived: true,
                // pageRole: ['ROLE_ADMIN'],
                // toPageRole: 'ROLE_ADMIN',
                // role: ["ROLE_ADMIN"],
              }
            ]
          },
          {
            name: "demo2",
            url: "/def/demo/02",
            pageRole: ['ROLE_ADMIN'],
            // toPageRole: 'ROLE_ADMIN',
            // role: ["ROLE_ADMIN"],
          },
          {
            name: "demo3",
            url: "/def/demo/03",
            pageRole: ['ROLE_LOGIN'],
            // toPageRole: 'ROLE_LOGIN',
          },
          {
            name: "demo4",
            url: "/def/demo/04",
            // pageRole: ['ROLE_ADMIN'],
            toPageRole: 'ROLE_ADMIN',
          },
          {
            name: "demo5",
            url: "/def/demo/05",
            // pageRole: ['ROLE_ADMIN'],
            toPageRole: 'ROLE_LOGIN',
          },
        ]
      },
      {
        icon: "el-icon-odometer",
        role: [],
        show: false,
        subs: [
          {
            icon: "el-icon-odometer",
            name: "def-msg-msg-inbox",
            url: "/def/msg/msg-inbox",
          }
        ]
      }
    ]
  },
  cookie: {
    path: '/'
  },
  auth: {
    token_store_pre: ''
  },
  baseUrl: {
    wwwUrl: '',
    fileApiUrl: '',
    apiUrl: '',
    wsUrl: '',
  },
  fs: {
    // alioss and server
    uptype: 'server',
    downtype: 'server',
  },
  sysInfo: {
    projectAppId: 'mg',
    sysAppId: '0',
    wxMpSysAppId: '',
  }
}

// 合并参数
for (const key in envConfig.default) {
  config[key] = envConfig.default[key];
}

// 导出参数
export { config };
