# sys-web

## Build Setup

```bash
# 默认Node版本14

# install dependencies
$ npm install --legacy-peer-deps

# serve with hot reload at localhost:3000
$ npm run dev
$ rm -rf .nuxt && clear && npm run dev

# build for production and launch server
$ npm run build
$ npm run start

# generate static project
$ npm run generate
```

For detailed explanation on how things work, check out [Nuxt.js docs](https://nuxtjs.org).

引入数据预览功能
docker run --name keking -p 8012:8012 -d keking/kkfileview

使用方式：http://127.0.0.1:8012/index




ts脚本转js脚本
全局安装typescript工具
使用tsc命令进行转换(es2019为目标js版本规范)
$ tsc -t es2019 excelUtils.ts


Excel操作
Excel导出；
需要增加了xlsx模块
$ npm i xlsx

引入utils目录下的excelUtils工具类。
用法示例：
本地数据：
let list = [];
for (let i = 0; i < 100; i++) {
    list.push({
        name: `张三${i}`,
        score: i,
        no: `2020${i}`,
        ok: i % 2 == 0,
    });
}

let op = <ExcelExportByListOneSheetOption>{};
op.data = list;
op.filename = "测试.xlsx";
// 字段信息
op.columns = [
    { code: "name", name: "名称" },
    { code: "no", name: "学号" },
    { code: "score", name: "成绩" },
    {
        code: "ok",
        type: "boolean",
        name: "过关",
        truename: "是",
        falsename: "否",
    },
];
excelUtils.exportData(op);

远端数据：
let op = {};
op.filename = "测试.xlsx";
// 字段信息
op.columns = [
{
    code: "idx",
    name: "序号",
    deal: (data, column, idx, ctx) => {
    return idx + 1;
    },
},
{ code: "name", name: "名称" },
{ code: "code", name: "code" },
{ code: "size", name: "尺寸" },
{
    code: "createTime",
    name: "创建时间",
    type: "datetime",
    format: "yyyy-MM-DD hh:mm:ss",
},
];
// 进度显示
op.processCallback = (ctx) => {
console.debug(ctx.count, "/", ctx.all);
};
op.requestCount = async () => {
// 修改为查询远端数据
let num = await getDiscoveryAuctionsItemsApi.getCount(this.query);
return num;
};
op.requestData = async (rows, page) => {
let query = JSON.parse(JSON.stringify(this.query));
query.page = page;
query.rows = rows;
let list = await getDiscoveryAuctionsItemsApi.getList(query);
return list;
};
await excelUtils.exportByRequest(op);


Excel导入功能：
组件：
y-excelimport(
    title="导入用户数据",
    :columns="importCloumns",
    @import="handleImportExcel"
)
JS代码
importCloumns: any = [
    { code: "name", name: "半年销量", require: true },
    { code: "code", name: "商家数量", require: false },
];
handleImportExcel(e: ImportEvent) {
    // 添加业务逻辑，调用接口保存到服务器。
    console.log("===import>>", e.data);
    e.close();
}


