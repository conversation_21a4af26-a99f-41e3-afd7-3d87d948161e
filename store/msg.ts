// import { config } from '~/config/global.config'
import { ActionTree, GetterTree, Module, ModuleTree, MutationTree, Store, ActionContext } from 'vuex';
import sysMsgInboxApi from '~/api/sys/msgInboxApi';
import basicBpmWorkflowApi from '~/api/basic/bpm/user/workflowApi';

export interface MsgState {
  unreadCount: number,
  bpmTodoCount: number,
}

export class MsgStore implements Module<MsgState, any> {
  namespaced = true;
  state = () => <MsgState>{
    unreadCount: -1,
    bpmTodoCount: -1,
  };
  mutations: MutationTree<MsgState> = {
    setUnreadCount(state: MsgState, count: number) {
      state.unreadCount = count;
    },
    setBpmTodoCount(state: MsgState, count: number) {
      state.bpmTodoCount = count;
    },
  };
  actions: ActionTree<MsgState, any> = {
    async refreshUnreadCount(ctx: ActionContext<MsgState, any>) {
      let unreadCount = await sysMsgInboxApi.getListCountByMe({ state: 'NEW' });
      ctx.commit('setUnreadCount', unreadCount);
    },
    async refreshBpmTodoCount(ctx: ActionContext<MsgState, any>) {
      let bpmTodoCount = await basicBpmWorkflowApi.getListCount({});
      ctx.commit('setBpmTodoCount', bpmTodoCount);
    },
  }
}

let msg = new MsgStore();
export default msg;

