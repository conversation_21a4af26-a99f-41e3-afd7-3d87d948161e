// const cookieparser = process.server ? require('cookieparser') : undefined
// import cookie from '~/utils/cookieUtils'
// import { config } from '~/config/global.config'

export const state = () => {
    return {
        // locale: null,
    }
}

export const mutations = {
    // // 此处为设置locale
    // setI18nLocal (state, locale) {
    //     state.locale = locale;
    //     if (localManager) {
    //         localManager.set('i18n_local', locale, 365 * 24 * 60 * 60 * 1000);
    //     }
    // }
}

export const actions = {
    // getI18nLocal ({ state, commit }) {
    //     // return new Promise((resolve, reject) => {
    //     let locale = state.locale;
    //     if (locale) {
    //         return locale;
    //     }
    //     locale = localManager.get('i18n_local');
    //     return locale;
    //     // });
    // }
}
