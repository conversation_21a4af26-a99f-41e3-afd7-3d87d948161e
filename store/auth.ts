import cookieStoreManager from '~/utils/sessionCookieUtils'
import localStoreManager from '~/utils/localStorageUtils';
import bizUserApi, { UserByMeInfoVo } from '~/api/auth/biz/userApi'
import authTokenApi, { TokenRevokeParamVo } from '~/api/auth/auth/tokenApi'
import { ActionTree, Module, MutationTree, ActionContext } from 'vuex';

import { config } from '~/config/global.config'
import { TokenMessageVo } from '~/model/common'
import powerApi, { UserPowerModelVo } from '~/api/auth/user/powerApi';
import networkUtils, { RequestBuffer } from '~/utils/networkUtils';
import authUtils from '~/utils/authUtils';
import { BadRequestException } from '~/utils/request';

type User = UserByMeInfoVo;

const getTokenKey = (name: string): string => config.auth.token_store_pre ? config.auth.token_store_pre + name : name;

const VALUE_KEY_PAGE_ROLE = 'page_role';

export interface AuthState {
  user: User | null,
  power: UserPowerModelVo | null,
  pageRole: string | null,
}

// const tokenStoreManager = localStoreManager;

// Cookie存储
const tokenStoreManager = {
  COOKIE_SESSION_KEY: 'token.session',
  set: (key: string, value: string, expries: number) => {
    cookieStoreManager.set(tokenStoreManager.COOKIE_SESSION_KEY, 'true');
    return localStoreManager.set(key, value, expries);
  },
  clear: (key: string) => {
    cookieStoreManager.clear(key);
    localStoreManager.clear(key);
  },
  get: (key: string) => {
    let cv = cookieStoreManager.get(tokenStoreManager.COOKIE_SESSION_KEY);
    if (!cv) {
      localStoreManager.clear(getTokenKey('token_running'));
      localStoreManager.clear(getTokenKey('token_token'));
      localStoreManager.clear(getTokenKey('token_rtoken'));
    }
    return localStoreManager.get(key);
  },
}

let powerLoadBuffer: RequestBuffer<UserPowerModelVo> | null = null;
let userLoadBuffer: RequestBuffer<User> | null = null;
let refreshTokenBuffer: RequestBuffer<string | null> | null = null;

export class AuthStore implements Module<AuthState, any> {
  namespaced = true;
  state = () => <AuthState>{
    user: null,
    power: null,
    pageRole: null,
  };
  mutations: MutationTree<AuthState> = {
    // 内部管理，请勿调用
    _setToken(state: AuthState, token: TokenMessageVo) {
      if (token && token.token) {
        // 保存token，refresh_token，以及过期标识
        tokenStoreManager.set(getTokenKey('token_running'), 'token_running', token.expries! * 1000 * 4 / 5);
        tokenStoreManager.set(getTokenKey('token_token'), token.token, token.expries! * 1000);
        if (token.id) {
          tokenStoreManager.set(getTokenKey('token_id'), token.id, token.expries! * 1000);
        }
        if (token.rtoken) {
          tokenStoreManager.set(getTokenKey('token_rtoken'), token.rtoken, token.rexpries! * 1000);
        }
      }
    },
    setUesr(state, user) {
      state.user = user;
    },
    _clearUser(state) {
      state.user = null;
      state.power = null;
      if (userLoadBuffer) {
        // userLoadBuffer.requestList = [];
        userLoadBuffer = null;
      }
      tokenStoreManager.clear(getTokenKey('token_running'));
      tokenStoreManager.clear(getTokenKey('token_token'));
      tokenStoreManager.clear(getTokenKey('token_id'));
      tokenStoreManager.clear(getTokenKey('token_rtoken'));
    },
    _setPower(state, power: UserPowerModelVo) {
      state.power = power;
      if (powerLoadBuffer) {
        // powerLoadBuffer.requestList = [];
        powerLoadBuffer = null;
      }
    },
    _setPageRole(state, pageRole: string) {
      console.log("切换身份：" + pageRole);
      state.pageRole = pageRole;
      localStoreManager.set(VALUE_KEY_PAGE_ROLE, pageRole);
    }
  };
  actions: ActionTree<AuthState, any> = {
    getToken(ctx: ActionContext<AuthState, any>): Promise<string | null> {
      return new Promise((resolve, reject) => {
        var tokenKey = getTokenKey('token_token');
        let token = tokenStoreManager.get(tokenKey);
        let tokenRunning = tokenStoreManager.get(getTokenKey('token_running'));
        if (tokenRunning && token) {
          // token有效，且距离过期时间较长
          resolve(token);
          return;
        }
        // token未找到或token即将过期
        let rtoken = tokenStoreManager.get(getTokenKey('token_rtoken'));
        if (!rtoken) {
          // refreshToken未找到
          resolve(null);
          return;
        }
        if (token) {
          // token有效，但即将过期，触发刷新动作
          tokenStoreManager.set(getTokenKey('token_running'), 'token_running', 60 * 1000);
          ctx.dispatch('refreshToken');
          // authTokenApi.refresh({ rtoken }).then(res => {
          //     ctx.commit('_setToken', res)
          // })
          resolve(token);
          return;
        }
        // token已过期，但具备刷新token，等待利用刷新token进行更新
        ctx.dispatch('refreshToken').then(res => {
          ctx.commit('_setToken', res)
          resolve(tokenStoreManager.get(tokenKey));
        }).catch(res => {
          reject(res);
        });
      });
    },
    async getPowerList(ctx: ActionContext<AuthState, UserPowerModelVo>): Promise<UserPowerModelVo> {
      if (ctx.state.power) {
        return ctx.state.power;
      }
      const queryPowerCb = async () => {
        let power = await powerApi.power();
        // console.log('加载到用户权限：', JSON.stringify(power));
        ctx.commit('_setPower', power);
        return power;
      };

      let power: any = null;
      if (powerLoadBuffer) {
        power = await powerLoadBuffer.request(queryPowerCb);
      } else {
        powerLoadBuffer = networkUtils.buildRequestBuffer();
        power = await powerLoadBuffer.request(queryPowerCb);
      }
      return power;
    },
    async putPageRole(ctx: ActionContext<AuthState, any>, pageRole: string | null): Promise<string | null> {
      if (!pageRole) {
        pageRole = null;
        ctx.commit('_setPageRole', pageRole);
        return pageRole;
      }
      if (!await ctx.dispatch('hasPower', pageRole)) {
        pageRole = null;
      }
      ctx.commit('_setPageRole', pageRole);
      return pageRole;
    },
    async getPageRole(ctx: ActionContext<AuthState, any>): Promise<string | null> {
      if (ctx.state.pageRole) {
        return ctx.state.pageRole;
      }
      let pageRole = localStoreManager.get(VALUE_KEY_PAGE_ROLE);
      if (pageRole) {
        pageRole = await ctx.dispatch('putPageRole', pageRole);
        if (pageRole) {
          return pageRole;
        }
      }
      // 角色没有设置，需要抽取角色
      let power: UserPowerModelVo = await ctx.dispatch('getPowerList');
      if (!power || !power.roles || power.roles.length <= 0) {
        pageRole = null;
        pageRole = await ctx.dispatch('putPageRole', pageRole);
        if (pageRole) {
          return pageRole;
        }
      }

      let roleCode = '';
      let defRoleCodes = ["ROLE_SYSTEM", 'ROLE_ADMIN', 'ROLE_LOGIN', 'ROLE_OPEN'];
      for (let role of power.roles!) {
        if (defRoleCodes.indexOf(role.code!) >= 0) {
          continue;
        }
        roleCode = role.code!;
        break;
      }
      if (!roleCode) {
        let userRoleCodes = new Set(power.roles!.map(x => x.code!));
        for (let rc of defRoleCodes) {
          if (!userRoleCodes.has(rc)) {
            continue;
          }
          roleCode = rc;
          break;
        }
      }
      return await ctx.dispatch('putPageRole', roleCode);
    },
    async hasPower(ctx: ActionContext<AuthState, any>, power: Array<string> | string | undefined | null): Promise<boolean> {
      if (!power || power.length <= 0) {
        return true;
      }

      let probj = await ctx.dispatch('getPowerList');
      return authUtils.hasPower(probj, power);
    },
    async getUserInfo(ctx: ActionContext<AuthState, User>): Promise<User | null> {
      if (ctx.state.user) {
        return ctx.state.user;
      }
      const queryPowerCb = async (): Promise<User> => {
        let user = await bizUserApi.getByMe();
        ctx.commit('setUesr', user);
        return user;
      };
      if (userLoadBuffer) {
        return await userLoadBuffer.request(queryPowerCb);
      } else {
        userLoadBuffer = networkUtils.buildRequestBuffer();
        return await userLoadBuffer.request(queryPowerCb);
      }
    },
    async refreshToken(ctx: ActionContext<AuthState, any>): Promise<string | null> {
      let rtokenCb: () => Promise<string | null> = () => new Promise((resolve, reject) => {
        var tokenKey = getTokenKey('token_token');
        // token未找到或token即将过期
        let rtoken = tokenStoreManager.get(getTokenKey('token_rtoken'));
        if (!rtoken) {
          // refreshToken未找到
          resolve(null);
          return;
        }
        // token已过期，但具备刷新token，等待利用刷新token进行更新
        authTokenApi.refresh({ rtoken }).then(res => {
          ctx.commit('_setToken', res);
          ctx.dispatch('ws/refreshWs', null, { root: true });
          resolve(tokenStoreManager.get(tokenKey));
        }).catch(res => {
          if (res instanceof BadRequestException) {
            ctx.commit('_setPower', null);
            ctx.commit('_clearUser');
          } else if (res && res.code && res.code == 'SIGN_FAIL') {
            ctx.commit('_setPower', null);
            ctx.commit('_clearUser');
          } else {
            ctx.commit('_setPower', null);
            ctx.commit('_clearUser');
          }
          ctx.dispatch('ws/refreshWs', null, { root: true });
          reject(res);
        });
      });
      if (refreshTokenBuffer) {
        return refreshTokenBuffer.request(rtokenCb);
      } else {
        refreshTokenBuffer = networkUtils.buildRequestBuffer();
        return refreshTokenBuffer.request(rtokenCb);
      }
    },
    async setToken({ commit, dispatch }, token): Promise<void> {
      commit('_setToken', token);
      await dispatch('ws/refreshWs', null, { root: true });
    },
    async getTokenRid({ commit, dispatch }): Promise<string | null> {
      const tk = await dispatch('getToken');
      if (!tk) {
        return null;
      }
      return tokenStoreManager.get(getTokenKey('token_id'));
    },
    async logout({ commit, dispatch }): Promise<void> {
      var tokenKey = getTokenKey('token_token');
      let token = tokenStoreManager.get(tokenKey);
      // token未找到或token即将过期
      let rtoken = tokenStoreManager.get(getTokenKey('token_rtoken'));
      let param: TokenRevokeParamVo = {}
      if (token) {
        param.token = token;
      }
      if (rtoken) {
        param.rtoken = rtoken;
      }
      await authTokenApi.revoke(param);
      commit("_clearUser");
      commit("_setPower", null);
      commit("_setPageRole", null);
      await dispatch('ws/refreshWs', null, { root: true });
    },
  }
}

const auth = new AuthStore();
export default auth;

