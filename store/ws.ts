// import { config } from '~/config/global.config'
import { Ws } from '@/utils/wsUtils'
import { ActionTree, GetterTree, Module, ModuleTree, MutationTree, Store, ActionContext } from 'vuex';
import { WsMsgSendBodyBo } from '@/model/common';
import { NotFoundException } from '~/utils/request';
import wsTicketApi from '~/api/sys/wsTicketApi';

// 全局变量
let wsBody: Ws | null = null;

export interface WsState {
  url: string | null,
  // ws: Ws | null,
  msg: WsMsgSendBodyBo | null,
  connectedCount: number,
  disconnectedCount: number,
}


export class WsStore implements Module<WsState, any> {
  namespaced = true;
  state = () => <WsState>{
    url: null,
    // ws: null,
    msg: null,
    connectedCount: 0,
    disconnectedCount: 0,
  };
  mutations: MutationTree<WsState> = {
    setUrl(state: WsState, url: string) {
      state.url = url;
    },
    putMsg(state: WsState, msg: WsMsgSendBodyBo) {
      if (msg) {
        state.msg = msg;
      }
    },
    addConnectedCount(state: WsState) {
      state.connectedCount++;
    },
    addDisconnectedCount(state: WsState) {
      state.disconnectedCount++;
    },
  };
  actions: ActionTree<WsState, any> = {
    async refreshWs(ctx: ActionContext<WsState, any>) {
      if (wsBody) {
        wsBody.close();
        wsBody = null;
      }
      if (!ctx.state.url) {
        return;
      }
      let token = await ctx.dispatch('auth/getToken', {}, { root: true });
      if (wsBody) {
        return;
      }
      if (!token) {
        // 没登录时不连接
        return;
      }
      let url = token ? ctx.state.url : ctx.state.url;
      const ticketCb = async () => {
        let token = await ctx.dispatch('auth/getToken', {}, { root: true });
        if (!token) {
          return null;
        }
        return (await wsTicketApi.buildByToken()).ticket ?? null;
      }
      let ws = new Ws(url, ticketCb, data => {
        ctx.commit('putMsg', data);
      });
      ws.connectedCb = () => {
        ctx.commit('addConnectedCount');
      }
      ws.disconnectCb = () => {
        ctx.commit('addDisconnectedCount');
      }
      wsBody = ws;
      ws.open();
    },
    async isConnected(ctx: ActionContext<WsState, any>): Promise<boolean> {
      if (!wsBody) {
        return false;
      }
      return wsBody.opened;
    },
    async send(ctx: ActionContext<WsState, any>, body: { type: string, data: string }) {
      if (!wsBody) {
        throw new NotFoundException('NOT_INIT', 'wsBody为初始化')
      }
      return await wsBody.send(body.type, body.data);
    },
    async push(ctx: ActionContext<WsState, any>, body: { type: string, data: string }) {
      if (!wsBody) {
        throw new NotFoundException('NOT_INIT', 'wsBody为初始化')
      }
      return await wsBody.push(body.type, body.data);
    },
  }
}

let ws = new WsStore();
export default ws;

