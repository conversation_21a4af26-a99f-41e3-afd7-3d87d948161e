// const cookieparser = process.server ? require('cookieparser') : undefined
// import cookie from '~/utils/cookieUtils'
import storeManager from '~/utils/localStorageUtils'
import { ActionTree, GetterTree, Module, ModuleTree, MutationTree, Store, ActionContext } from 'vuex';
import dicUtils from '~/utils/dicUtils';

export interface I18nState {
  locale?: string | null;
}

export class I18nStore implements Module<I18nState, any> {
  namespaced = true;
  state = () => <I18nState>{
    locale: null
  };
  mutations: MutationTree<I18nState> = {
    // 此处为设置locale
    setI18nLocal(state: I18nState, locale) {
      dicUtils.setI18n(locale);
      state.locale = locale;
      if (storeManager) {
        storeManager.set('i18n_local', locale, 365 * 24 * 60 * 60 * 1000);
      }
    }
  };
  actions: ActionTree<I18nState, any> = {
    getI18nLocal(ctx: ActionContext<I18nState, any>) {
      let locale = ctx.state.locale;
      if (locale) {
        return locale;
      }
      locale = storeManager.get('i18n_local');
      return locale;
    }
  }
}

let i18n = new I18nStore();
export default i18n;

