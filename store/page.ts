// const cookieparser = process.server ? require('cookieparser') : undefined
// import cookie from '~/utils/cookieUtils'

// import { config } from '~/config/global.config'

import { ActionTree, GetterTree, Module, ModuleTree, MutationTree, Store, ActionContext } from 'vuex';

export interface PageState {
  etag: string | null;
  pageUpdated: boolean;
}

export class PageStore implements Module<PageState, any> {
  namespaced = true;
  state = () => <PageState>{
    etag: null,
    pageUpdated: false,
  };
  mutations: MutationTree<PageState> = {
    // 保存页面更新标志
    setEtag(state: PageState, data: string | null) {
      if (!data) {
        return;
      }
      if (state.etag) {
        if (state.etag != data) {
          state.pageUpdated = true;
        }
      }
      state.etag = data;
    },
  };
  actions: ActionTree<PageState, any> = {
  }
}

let page = new PageStore();
export default page;



