import persistentStoreManager from '~/utils/localStorageUtils'
import { ActionTree, Module, MutationTree, ActionContext } from 'vuex';
import idUtils from '~/utils/idUtils';
import cryptoApi, { SysCryptoPubkeyVo } from '~/api/basic/sys/cryptoApi';
import { config } from '~/config/global.config';

const DRIVER_ID_KEY = 'driver.id';
const TRANSACTION_PUBLICK_KEY = 'transaction.public_key';

export interface AppState {
  driverId: string | null,
}

export class AppStore implements Module<AppState, any> {
  namespaced = true;
  state = () => <AppState>{
    driverId: null,
  };
  mutations: MutationTree<AppState> = {
    // 设置driverId
    _setDriverId(state: AppState, driverId: string) {
      state.driverId = driverId
    },
  };
  actions: ActionTree<AppState, any> = {
    async getDriverId(ctx: ActionContext<AppState, any>): Promise<string> {
      let driverId: string | null = ctx.state.driverId;
      if (driverId) {
        return driverId;
      }
      driverId = persistentStoreManager.get(DRIVER_ID_KEY)
      if (driverId) {
        ctx.commit('_setDriverId', driverId);
        return driverId;
      }
      driverId = await idUtils.getFp();
      driverId = driverId ? driverId : idUtils.uuid();
      ctx.commit('_setDriverId', driverId);
      persistentStoreManager.set(DRIVER_ID_KEY, driverId);
      return driverId;
    },
    async getTransactionPublicKey(ctx: ActionContext<AppState, any>): Promise<SysCryptoPubkeyVo> {
      let publicKeyStr = persistentStoreManager.get(TRANSACTION_PUBLICK_KEY);
      if (publicKeyStr) {
        return JSON.parse(publicKeyStr);
      }
      let pk: SysCryptoPubkeyVo = await cryptoApi.getTransactionPubKey(config.sysInfo.projectAppId);
      persistentStoreManager.set(TRANSACTION_PUBLICK_KEY, JSON.stringify(pk), 1000 * 60 * 60 * 24 * 3);
      return pk;
    }
  }
}

let app = new AppStore();
export default app;

