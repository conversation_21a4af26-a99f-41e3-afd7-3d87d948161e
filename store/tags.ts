// const cookieparser = process.server ? require('cookieparser') : undefined
// import cookie from '~/utils/cookieUtils'

// import { config } from '~/config/global.config'

import localStoreManager from '~/utils/localStorageUtils';

import { ActionTree, GetterTree, Module, ModuleTree, MutationTree, Store, ActionContext } from 'vuex';
import { TagNode } from '~/model/common';

const VALUE_KEY_PAGE_COLLAPSE = 'page_collapse';

export type PageInfo = {
  path?: string,
  name?: string,
  pageRole?: string,
}

export interface TagsState {
  tagsList: TagNode[] | null;
  collapse: boolean | null;
  reloadCount: number | null;
  url2pageInfo: Map<string, PageInfo>;
}

export class TagsStore implements Module<TagsState, TagNode> {
  namespaced = true;
  state = () => <TagsState>{
    tagsList: [],
    collapse: false,
    reloadCount: 0,
    url2pageInfo: new Map()
  };
  mutations: MutationTree<TagsState> = {
    putUrl2pageInfo(state: TagsState, data: { key: string, value: PageInfo | null }) {
      if (data.value) {
        state.url2pageInfo.set(data.key, data.value);
      } else {
        state.url2pageInfo.delete(data.key);
      }
    },
    // 侧边栏折叠
    hadndleCollapse(state: TagsState, data: boolean) {
      localStoreManager.set(VALUE_KEY_PAGE_COLLAPSE, data ? '1' : '0');
      state.collapse = data;
    },
    // 
    initCollapse(state: TagsState) {
      let c = localStoreManager.get(VALUE_KEY_PAGE_COLLAPSE);
      if (!c) {
        return;
      }
      state.collapse = !(+c);
    },
    // 
    delTagsItem(state: TagsState, data: { index: number }) {
      state.tagsList!.splice(data.index, 1);
    },
    // 
    putTagsItem(state: TagsState, data: TagNode) {
      state.tagsList!.push(data);
    },
    setTagMsg(state: TagsState, data: { path: string, data: TagNode }) {
      for (let i in state.tagsList!) {
        let tag = state.tagsList![i];
        if (tag.path == data.path) {
          state.tagsList[i] = data.data;
          return;
        }
      }
    },
    //
    setTagsItem(state: TagsState, data: TagNode[]) {
      state.tagsList = [...data]
    },
    // 
    clearTags(state: TagsState) {
      state.tagsList = []
    },
    // closeCurrentTag(state: TagsState, data) {
    //   let len = state.tagsList!.length;
    //   for (let i = 0; i < len; i++) {
    //     const item = state.tagsList![i];
    //     if (item.path === data.$route.fullPath) {
    //       if (i < len - 1) {
    //         data
    //           .$router
    //           .push(state.tagsList![i + 1].path);
    //       } else if (i > 0) {
    //         data
    //           .$router
    //           .push(state.tagsList![i - 1].path);
    //       } else {
    //         data
    //           .$router
    //           .push("/");
    //       }
    //       state.tagsList!.splice(i, 1);
    //       break;
    //     }
    //   }
    // },
    reloadPage(state: TagsState) {
      state.reloadCount = state.reloadCount! + 1;
    }
  };
  actions: ActionTree<TagsState, any> = {
    // getI18nLocal (ctx: ActionContext<TagsState, any>) {
    //     let locale = ctx.state.locale;
    //     if (locale) {
    //         return locale;
    //     }
    //     locale = storeManager.get('i18n_local');
    //     return locale;
    // }
  }
}

let tags = new TagsStore();
export default tags;



