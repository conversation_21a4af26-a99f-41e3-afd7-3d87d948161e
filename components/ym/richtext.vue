<template lang="pug">
.comp-ym-richtext
  .show-win(v-show="_disabled")
    div(v-html="content")
  .editor(v-show="!_disabled")
    editor(
      :init="tinymceInit",
      v-model="content",
      :key="tinymceFlag",
      :deiabled="true"
    )
  el-dialog(:visible="previewVisible", title="图片上传")
    el-form(ref="form", label-width="80px")
      el-form-item
        y-upload(limit="20", type="pic", v-model="uploadImg")
    template(#footer)
      el-button(type="primary") 添加
      el-button(@click="() => (previewVisible = false)") 取消
</template>

<script lang="ts">
import { Component, Vue, Watch } from "nuxt-property-decorator";
import tinymce from "tinymce/tinymce";
import "tinymce/themes/silver/theme";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/plugins/textcolor";
import "tinymce/plugins/advlist";
import "tinymce/plugins/table";
import "tinymce/plugins/lists";
import "tinymce/plugins/paste";
import "tinymce/plugins/preview";
import "tinymce/plugins/fullscreen";
import "tinymce/plugins/link";
import "tinymce/plugins/autoresize";
import "tinymce/plugins/code";
import "tinymce/plugins/image";
import "tinymce/icons/default/icons";
import urlUtils from "~/utils/urlUtils";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import fileuploadUtils from "~/utils/fileuploadUtils";

var tinymceFlagCount = 1;

@Component({
  name: "ym-richtext",
  props: ["value", "disabled"],
  components: {
    editor: Editor,
  },
})
export default class YmRichtext extends Vue {
  content = "";
  tinymceInit = {};
  previewVisible = false;
  tinymceFlag = tinymceFlagCount++;
  uploadImg = "";
  _disabled = false;

  created() {
    this._disabled = commonFunUtils.parseBoolean(this.$props.disabled, false);
    this.tinymceInit = {
      skin_url: "/tinymce/skins/ui/oxide",
      language_url: `/tinymce/langs/zh_CN.js`,
      content_css: `/tinymce/skins/content/default/content.css`,
      language: "zh_CN",
      // height: document.body.offsetHeight - 300,
      // height: 300,
      browser_spellcheck: true, // 拼写检查
      branding: false, // 去水印
      // elementpath: false,  //禁用编辑器底部的状态栏
      statusbar: false, // 隐藏编辑器底部的状态栏
      paste_data_images: true, // 允许粘贴图像
      menubar: false, // 隐藏最上方menu
      autoresize_max_height: 500,
      convert_urls: false, // 确保不进行URL转换
      plugins:
        "advlist table lists paste preview fullscreen code link autoresize image",
      toolbar:
        "fontselect | fontsizeselect | forecolor backcolor bold italic underline strikethrough |" +
        " alignleft aligncenter alignright alignjustify | image quicklink h2 h3 blockquote table numlist bullist |" +
        " link preview fullscreen code",
      formats: {
        alignright: {
          selector: "p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img",
          classes: "right",
        },
        bold: { inline: "span", styles: { color: "#f00" } },
        forecolor: {
          inline: "span",
          styles: { color: "%value", fontWeight: "700" },
        },
        custom_format: {
          block: "h1",
          attributes: { title: "Header" },
          styles: { color: "red" },
        },
        underline: {
          inline: "u",
          styles: { "text-decoration": "underline" },
        },
      },
      /**
       * 为tinymce添加自定义插入图片按钮
       */
      setup: (editor: any) => {
        // editor.ui.registry.addButton("imageUpload", {
        //     tooltip: "插入图片",
        //     icon: "image",
        //     onAction: () => {
        //         this.uploadImg = "";
        //         this.previewVisible = true;
        //     },
        // });
      },
      images_upload_handler: (blobInfo: any, success: any, failure: any) => {
        let file: File = blobInfo.blob();
        fileuploadUtils
          .upload({
            file: file,
            onProgress: () => {},
          })
          .then((res) => {
            success(this.getUrl(res.id!));
          });
      },
    };
  }

  getUrl(id: string): string | null {
    return urlUtils.file2url(id);
  }

  mounted() {
    tinymce.init({});
  }

  @Watch("$props.disabled")
  disabledChange() {
    this._disabled = commonFunUtils.parseBoolean(this.$props.disabled, false);
  }

  @Watch("content")
  contentChange(newVal: string, oldVal: string) {
    compUtils.formEmitInput(this, newVal);
  }

  // 同步
  @Watch("$props.value")
  valueChangeEven() {
    let val = this.$props.value;
    this.content = this.$props.value;
    if (this.content != val) {
      val = val == undefined ? "" : val;
      this.content = val;
      this.tinymceFlag = tinymceFlagCount++;
    }
  }
}
</script>

<style lang="scss" scoped>
.comp-ym-richtext {
  .show-win {
    border: solid 1px #d9d9d9;
    padding: 10px;
  }
}
.el-form-item.is-error .comp-ym-richtext::v-deep .editor .tox {
  border: solid 1px #f56c6c;
}
</style>
    