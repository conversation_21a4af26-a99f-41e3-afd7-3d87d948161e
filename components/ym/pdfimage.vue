<template lang="pug">
.comp-ym-pdfimage(ref="pdfView")
  .pdf-page(v-for="page in pages")
    canvas(:style="{ display: _showText ? '' : 'none' }")
    el-image(
      :src="page.data",
      :preview-src-list="pages.map((x) => x.data)",
      v-if="!_showText"
    )
    .text-layer(:style="{ display: _showText ? '' : 'none' }")
  .loading-content(v-if="loading", v-loading="loading")
</template>

<script lang="ts">
import { Component, mixins, Watch, Vue } from "nuxt-property-decorator";
import commonFunUtils from "~/utils/commonFunUtils";

@Component({
  name: "ym-excelimport",
  props: ["url", "password", "scale", "showText"],
})
export default class YmExcelimport extends Vue {
  pages: Array<{
    canvas?: HTMLCanvasElement;
    scale?: number;
    textLayout?: HTMLDivElement;
    data?: string;
  }> = [];
  loading = false;
  needRefresh = false;
  loadingPer = 0;

  get _showText() {
    return commonFunUtils.parseBoolean(this.$props.showText, true);
  }
  get _scale() {
    return +(this.$props.scale ? this.$props.scale : 1.5);
  }

  get _url() {
    return this.$props.url ? this.$props.url : "";
  }

  get _password() {
    return this.$props.password ? this.$props.password : null;
  }

  async mounted() {
    await this.refresh();
  }

  async refresh() {
    if (this.loading) {
      this.needRefresh = true;
      return;
    }
    try {
      this.loading = true;
      await this.initPdf({
        url: this._url,
        password: this._password,
        scale: this._scale,
      });
    } finally {
      this.loading = false;
      if (this.needRefresh) {
        this.needRefresh = false;
        await this.refresh();
      }
    }
  }

  buildSVG(viewport: any, textContent: any, pdfjsLib: any) {
    const SVG_NS = "http://www.w3.org/2000/svg";
    // Building SVG with size of the viewport (for simplicity)
    const svg = document.createElementNS(SVG_NS, "svg:svg");
    svg.setAttribute("width", viewport.width + "px");
    svg.setAttribute("height", viewport.height + "px");
    svg.style.width = "100%";
    svg.style.height = "100%";
    // items are transformed to have 1px font size
    svg.setAttribute("font-size", "1");

    // processing all items
    textContent.items.forEach(function (textItem: any) {
      // we have to take in account viewport transform, which includes scale,
      // rotation and Y-axis flip, and not forgetting to flip text.
      const tx = pdfjsLib.Util.transform(
        pdfjsLib.Util.transform(viewport.transform, textItem.transform),
        [1, 0, 0, -1, 0, 0]
      );
      const style = textContent.styles[textItem.fontName];
      const text = document.createElementNS(SVG_NS, "svg:text");
      text.setAttribute("transform", "matrix(" + tx.join(" ") + ")");
      text.setAttribute("font-family", style.fontFamily);
      // text.setAttribute("width", textItem.width);
      // text.setAttribute("height", textItem.height);
      text.setAttribute("fill", "rgba(255,0,0,0)");
      text.textContent = textItem.str;
      svg.append(text);
    });
    return svg;
  }

  initPdf(src: { url: string; password?: string | null; scale?: number }) {
    const that: any = this;
    return new Promise(async (r, j) => {
      if (!document.getElementById("pdf-js-script")) {
        const script = document.createElement("script");
        script.id = "pdf-js-script";
        script.src = `${
          process.env.BASE_PATH ? process.env.BASE_PATH : ""
        }/js/pdf/pdf.js`;
        script.type = "module";
        script.crossOrigin = "anonymous";
        script.onload = () => {
          console.log("pdf.js loaded");
          // 这里可以执行依赖 pdf.js 的初始化逻辑
          that.__initPdf(src).then(r).catch(j);
        };
        document.head.appendChild(script);
      } else {
        // 脚本已经存在，可以直接执行初始化
        console.log("pdf.js has");
        that.__initPdf(src).then(r).catch(j);
      }
    });
  }

  __initPdf(src: { url: string; password?: string | null; scale?: number }) {
    src.password = src.password ? src.password : null;
    src.scale = src.scale ? src.scale : 1.5;
    if (!src.url) {
      this.pages = [];
      return;
    }

    this.pages = [];
    const that = this;
    return new Promise((r, j) => {
      (async () => {
        let pdfjsLib = (<any>globalThis).pdfjsLib;
        if (!pdfjsLib) {
          that.$notify.error("初始化pdf组件出错");
          return;
        }
        pdfjsLib.GlobalWorkerOptions.workerSrc = "./js/pdf/pdf.worker.js";
        // Using DocumentInitParameters object to load binary data.
        // let loadingTask = PDFJS.getDocument({ data: pdfData });
        var loadingTask = pdfjsLib.getDocument(src);
        let pdf: any = await loadingTask.promise;
        let total = pdf.numPages;
        that.pages = [];
        for (let i = 0; i < total; i++) {
          that.pages.push({ scale: src.scale });
        }
        that.$nextTick(() => {
          let pagePromises = [];
          let view: any = that.$refs.pdfView;
          for (let i = 0; i < total; i++) {
            let page = that.pages[i];
            let pageDiv = view.childNodes[i];
            let canvas: HTMLCanvasElement = pageDiv.querySelector("canvas");
            let textLayout: HTMLDivElement =
              pageDiv.querySelector(".text-layer");
            page.canvas = canvas;
            page.textLayout = textLayout;

            pagePromises.push(
              (async () => {
                let pdfPage: any = await pdf.getPage(i + 1);

                var viewport = pdfPage.getViewport({ scale: page.scale });

                // Prepare canvas using PDF page dimensions
                var context = canvas.getContext("2d");
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                // Render PDF page into canvas context
                var renderContext = {
                  canvasContext: context,
                  viewport: viewport,
                };
                await pdfPage.render(renderContext).promise;
                let textContent = await pdfPage.getTextContent();
                const svg = that.buildSVG(viewport, textContent, pdfjsLib);
                if (textLayout.childElementCount > 0) {
                  textLayout.innerHTML = "";
                }
                textLayout.append(svg);
                pdfPage.cleanup();

                page.data = canvas.toDataURL("image/png");
                that.$set(that.pages, i, page);
              })()
            );
          }
          Promise.all(pagePromises)
            .then((res) => r(res))
            .catch((err) => j(err));
        });
      })().catch((err) => j(err));
    });
  }

  @Watch("$props.url")
  async urlEven() {
    await this.refresh();
  }
  @Watch("$props.scale")
  async scaleEven() {
    await this.refresh();
  }
  @Watch("$props.password")
  async passwordEven() {
    await this.refresh();
  }
}
</script>
<style lang="scss" scoped>
.comp-ym-pdfimage::v-deep {
  .pdf-page {
    margin-bottom: 10px;
    // border: solid 1px #888;
    width: fit-content;
    position: relative;

    canvas {
      border: 1px solid black;
      direction: ltr;
      width: 100%;
    }

    .text-layer {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;
      opacity: 1;
    }
  }

  .loading-content {
    min-height: 100px;
  }
}
</style>
