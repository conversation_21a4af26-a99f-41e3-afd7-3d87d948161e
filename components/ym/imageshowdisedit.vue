<template lang="pug">
.comp-imgeshowdisplayedit
  el-row.rows
    el-col.col-win.left(:span="10")
      .activity-win
        img.bgimg(:src="getImgUrl(content.bgImgUrl)")
        .btn(ref="btnWins")
          template(v-for="(item, idx) in content.points")
            .item(
              :key="idx",
              :style="{ top: `${(pointMove && pointMove.idx == idx ? pointMove.yr + +item.top : +item.top) * 100}%`, left: `${(pointMove && pointMove.idx == idx ? pointMove.xr + +item.left : +item.left) * 100}%`, width: `${+item.width * 100}%`, height: `${+item.height * 100}%` }",
              @click="handlePointClick(item)"
            )
              .item-title
                .move(@mousedown="(e) => handlePointMouseDown(e, idx)")
                  //- i.el-icon-rank
                .delete
                  el-popconfirm(
                    title="确认要删除此标签吗？",
                    @confirm="handleDeleteColumnClick(idx)"
                  )
                    el-button.red(
                      slot="reference",
                      type="text",
                      icon="el-icon-delete",
                      style="padding: 0"
                    )
              .item-body
                span {{ item.code }}
        .column-add(v-if="columnAddObj")
          .show-add
            .x-line(:style="{ top: `${columnAddObj.mousePoint.y}px` }")
            .y-line(:style="{ left: `${columnAddObj.mousePoint.x}px` }")
            .point(
              v-if="columnAddObj.type == 'second'",
              :style="computeColumnAddPointStyle"
            )
          .mouse-click(
            @mousemove="handleAddColumnMove",
            @click="handleAddColumnClick",
            ref="columnAdd"
          )

    el-col.col-win.right(:span="14")
      .config
        .mgb20
          h2 图片
          el-alert.mgb10(type="success") 资源服务启动命令：http-server -p 8086 --cors="*"
          | 基础路径：
          el-input.mgb10(v-model="bgImgBaseUrl")
          | 图片相对路径：
          el-input(v-model="content.bgImgUrl")
        .mgb20
          h2 框选坐标
          .mgb10
            el-button(type="primary", @click="handleAddColumnBeginClick")
              | {{ columnAddObj ? "取消标记" : "标记新字段" }}
          .mgb10 注意，顺序与层级有关，会影响点击事件的覆盖情况
          div(ref="columnTable")
            el-table(:data="content.points")
              el-table-column(width="35", label="排序")
                template(#default="scope")
                  .y-sorthandle
                    i.el-icon-rank
              el-table-column(label="Code")
                template(#default="scope")
                  el-input(v-model="scope.row.code")
              el-table-column(label="坐标")
                template(#default="scope")
                  .point-position
                    .label top:
                    el-input-number(
                      v-model="scope.row.top",
                      :step="0.001",
                      type="number",
                      controls-position="right"
                    )
                  .point-position
                    .label left:
                    el-input-number(
                      v-model="scope.row.left",
                      :step="0.001",
                      type="number",
                      controls-position="right"
                    )
                  .point-position
                    .label width:
                    el-input-number(
                      v-model="scope.row.width",
                      :step="0.001",
                      type="number",
                      controls-position="right"
                    )
                  .point-position
                    .label height:
                    el-input-number(
                      v-model="scope.row.height",
                      :step="0.001",
                      type="number",
                      controls-position="right"
                    )
              el-table-column(label="操作", width="80")
                template(#default="scope")
                  div
                    el-popconfirm(
                      title="确认要删除此字段吗？",
                      @confirm="handleDeleteColumnClick(scope.$index)"
                    )
                      el-button.red(
                        slot="reference",
                        type="text",
                        icon="el-icon-delete"
                      ) {{ $t("common.base.delete") }}
        div
          h2 JSON内容
          el-input(type="textarea", :row="6", v-model="contentJson")
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import sortableUtils from "~/utils/sortableUtils";
import { BaseVue } from "~/model/vue";
import moment from "moment";
import compUtils from "~/utils/compUtils";
import urlUtils from "~/utils/urlUtils";

export type Point = {
  x: number;
  y: number;
};
export type PointContent = {
  code: string;
  top: number;
  left: number;
  width: number;
  height: number;
};

export type Content = {
  points: Array<PointContent>;
  bgImgUrl: string;
};

@Component({
  name: "ym-imageshowedit",
  props: ["value", "imgBaseUrl"],
})
export default class YmImageshowedit extends mixins(BaseVue) {
  bgImgBaseUrl = this._imgBaseUrl;
  contentJson = "{}";
  content: Content = {
    bgImgUrl: "",
    points: [],
  };

  // 新创建
  columnAddObj: null | {
    type: "first" | "second";
    mousePoint: Point;
    startPoint: Point | null;
  } = null;

  // 拖拽移动
  pointMove: null | {
    x: number;
    y: number;
    xr: number;
    yr: number;
    idx: number;
  } = null;

  get _value() {
    return this.$props.value ? this.$props.value : "{}";
  }

  get _imgBaseUrl() {
    return this.$props.imgBaseUrl ? this.$props.imgBaseUrl : "";
  }

  getImgUrl(img: string) {
    if (!img) {
      return "";
    }
    if (img.startsWith("http://") || img.startsWith("https://")) {
      return img;
    }
    return this._imgBaseUrl ? this._imgBaseUrl + img : urlUtils.file2url(img);
  }

  async mounted() {
    this.initSortable();

    this.contentJson = this._value;
  }

  initSortable() {
    let el: any = this.$refs.columnTable;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "content.points", {
      handle: ".y-sorthandle",
    });
  }

  get computeColumnAddPointStyle() {
    if (!this.columnAddObj) {
      return;
    }
    let columnAddObj = this.columnAddObj;
    let obj = {
      top:
        columnAddObj.mousePoint.y > columnAddObj.startPoint!.y
          ? columnAddObj.startPoint!.y
          : columnAddObj.mousePoint.y,
      left:
        columnAddObj.mousePoint.x > columnAddObj.startPoint!.x
          ? columnAddObj.startPoint!.x
          : columnAddObj.mousePoint.x,
      width: Math.abs(columnAddObj.mousePoint.x - columnAddObj.startPoint!.x),
      height: Math.abs(columnAddObj.mousePoint.y - columnAddObj.startPoint!.y),
    };
    return {
      top: `${obj.top}px`,
      left: `${obj.left}px`,
      width: `${obj.width}px`,
      height: `${obj.height}px`,
    };
  }

  handleAddColumnMove(e: MouseEvent) {
    if (!this.columnAddObj) {
      return;
    }
    if (!this.$refs.columnAdd) {
      return;
    }
    let width = (<any>this.$refs.columnAdd).clientWidth;
    let height = (<any>this.$refs.columnAdd).clientHeight;

    let x = e.offsetX;
    let y = e.offsetY;
    if (x == -0) {
      return;
    }
    x = x < 0 ? 0 : x;
    x = x > width ? width : x;
    y = y < 0 ? 0 : y;
    y = y > height ? height : y;
    if (x != -0) {
      this.columnAddObj.mousePoint.x = x;
    }
    if (y != -0) {
      this.columnAddObj.mousePoint.y = y;
    }
  }

  handlePointMouseDown(e: MouseEvent, idx: number) {
    this.content.points[idx];
    this.pointMove = { x: e.offsetX, y: e.offsetY, xr: 0, yr: 0, idx };
  }
  /**
   * 点击保存点
   */
  handleAddColumnClick(e: MouseEvent) {
    if (!this.columnAddObj) {
      return;
    }
    this.handleAddColumnMove(e);
    if (this.columnAddObj.type == "first") {
      // 第一次点击
      this.columnAddObj.startPoint = this.columnAddObj.mousePoint;
      this.columnAddObj.mousePoint = {
        x: this.columnAddObj.startPoint.x,
        y: this.columnAddObj.startPoint.y,
      };
      this.columnAddObj.type = "second";
      return;
    }
    // 第二次点击
    if (!this.$refs.columnAdd) {
      return;
    }
    let width = (<any>this.$refs.columnAdd).clientWidth;
    let height = (<any>this.$refs.columnAdd).clientHeight;
    let date_str = moment().format("YYYYMMDDHHmm");
    let pointContent: PointContent = <any>{};
    pointContent.code = `VALUE_ACTIVITY_POINT_${date_str}_${
      this.content.points.length + 1
    }`;
    let obj = {
      top:
        this.columnAddObj.mousePoint.y > this.columnAddObj.startPoint!.y
          ? this.columnAddObj.startPoint!.y
          : this.columnAddObj.mousePoint.y,
      left:
        this.columnAddObj.mousePoint.x > this.columnAddObj.startPoint!.x
          ? this.columnAddObj.startPoint!.x
          : this.columnAddObj.mousePoint.x,
      width: Math.abs(
        this.columnAddObj.mousePoint.x - this.columnAddObj.startPoint!.x
      ),
      height: Math.abs(
        this.columnAddObj.mousePoint.y - this.columnAddObj.startPoint!.y
      ),
    };
    pointContent.top = +(obj.top / height).toFixed(5);
    pointContent.left = +(obj.left / width).toFixed(5);
    pointContent.width = +(obj.width / width).toFixed(5);
    pointContent.height = +(obj.height / height).toFixed(5);
    this.content.points.push(pointContent);

    this.columnAddObj = null;
  }
  /**
   * 新增字段
   */
  async handleAddColumnBeginClick() {
    if (this.columnAddObj != null) {
      this.columnAddObj = null;
      return;
    }
    this.columnAddObj = {
      type: "first",
      mousePoint: { x: 0, y: 0 },
      startPoint: null,
    };
  }

  /**
   * 删除字段
   */
  async handleDeleteColumnClick(idx: number) {
    this.content.points.splice(idx, 1);
  }

  async handlePointClick(item: PointContent) {
    this.$notify.info(`点击到了：${item.code}模块`);
  }

  @Watch("content", { deep: true })
  contentChange(newValue: any, oldValue: any) {
    let cj = JSON.stringify(this.content);
    if (this.contentJson == cj) {
      return;
    }
    this.contentJson = cj;
    compUtils.formEmitInput(this, this.contentJson);
  }

  @Watch("contentJson")
  contentJsonChange(newValue: any, oldValue: any) {
    if (this.contentJson) {
      this.content = JSON.parse(this.contentJson);
      if (!this.content.points) {
        this.content.points = [];
      }
    } else {
      this.contentJson = "{}";
    }
  }

  @Watch("$props.value")
  valueChange(newValue: any, oldValue: any) {
    this.contentJson = this._value;
  }
}
</script>
<style lang="scss" scoped>
h2 {
  margin-bottom: 10px;
}
.comp-imgeshowdisplayedit {
  // display: inline-flex;
  width: 100%;
  height: 100%;

  .rows {
    width: 100%;
    height: 100%;

    .col-win {
      height: 100%;
      overflow-y: auto;
      // padding: 10px;
    }
    .col-win.left {
      padding: 16px;
    }
    .col-win.right {
      padding: 10px 10px 300px 0;
    }
  }
}

.config {
  margin-left: 20px;
  // width: calc(100% - 500px);
  .point-position {
    display: inline-flex;
    width: 100%;
    margin-bottom: 3px;

    .label {
      width: 50px;
      text-align: right;
      line-height: 100%;
      align-content: center;
      font-size: 14px;
      margin-right: 5px;
    }
    .el-input {
      width: 60%;
    }
  }
}

.activity-win {
  width: 100%;
  position: relative;
  // width: 500px;
  border: solid 1px #000;
  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.5);

  img.bgimg {
    width: 100%;
  }
  .btn {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
    .item {
      border: solid 1px #000;
      background-color: rgba(255, 0, 0, 0.3);
      position: absolute;

      .item-title {
        justify-content: space-between;
        display: flex;
        width: 100%;
        height: 30%;
        .move {
          cursor: grab;
        }
        .delete {
          cursor: pointer;
        }
      }
      .item-body {
        width: 100%;
        height: 70%;

        text-align: center;
        color: #0004ff;
        font-weight: 800;
        font-size: 12px;
        line-height: 100%;
        align-content: center;
        word-wrap: break-word;
      }
    }
  }

  .column-add {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;

    background-color: rgba(0, 0, 0, 0.3);

    .x-line {
      position: absolute;
      width: 100%;
      height: 1px;
      background-color: red;
    }
    .y-line {
      position: absolute;
      width: 1px;
      height: 100%;
      background-color: red;
    }
    .point {
      position: absolute;
      background-color: rgba(255, 0, 0, 0.5);
      border: solid 2px #000;
    }

    .show-add {
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .mouse-click {
      position: absolute;
      width: 100%;
      height: 100%;
    }
  }
}
</style>

