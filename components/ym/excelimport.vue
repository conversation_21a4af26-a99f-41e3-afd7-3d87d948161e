<template>
  <div class="comp-ym-excelimport">
    <div @click="openImport">
      <slot>
        <el-button>{{ ytitle }}</el-button>
      </slot>
    </div>
    <input class="display-none" ref="fileinput" type="file" @change="readfile" value="选择文件"
           accept=".xls, .xlsx" />
    <el-dialog :title="ytitle" :visible.sync="visible" :close-on-press-escape="false"
               :close-on-click-modal="false" width="80%">
      <div slot="default">
        <div class="form-start">
          将表单
          <el-select v-model="sheetname" @change="refreshTable">
            <el-option v-for="item in res" :key="item.sheetname" :value="item.sheetname"
                       :label="item.sheetname">{{ item.sheetname }}</el-option>
          </el-select>
          中，从第
          <el-input v-model="ystartrow" type="number" style="width: 100px"
                    @change="headlRowChange" />
          行开始到第
          <el-input v-model="yendrow" type="number" style="width: 100px"
                    @change="handleEndRowChange" />
          行(最大{{ rowlen }})行结束的数据进行导入。(<span class="red">红色标记行</span>表示不导入此行)
        </div>
        <!-- 数据表格 -->
        <div class="show-table">
          <el-table :data="showdata" :row-class-name="tableRowClassName">
            <el-table-column label="序号" width="50">
              <template slot-scope="scope">{{ (formPage - 1) * fromRow + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column v-for="(item, index) in headers" :key="item.name" align="center">
              <template #header>
                <el-select v-model="headers[index].option" @change="headleColumnSelectChange()"
                           :class="{'color-red': headers[index].require}" style="width: 130px;">
                  <el-option value="" label="(不选择)">(不选择)</el-option>
                  <el-option v-for="(item2) in cols" :key="item2.code" :value="item2.code"
                             :label="(item2.require ? '*' : '') + item2.name" :class="{'comp-ym-excelimport-select-color-red': item2.require, 
                                        'display-none': item.option != item2.code && !item2.show}">
                    {{ (item2.require ? '*' : '')  + item2.name }}
                  </el-option>
                </el-select>
              </template>

              <template slot-scope="scope">
                {{ scope.row[index] }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="form-end">
          <el-pagination background @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :page-sizes="[5, 10, 20, 50, 100, 200]" :page-size="fromRow"
                         :current-page="formPage" :total="data.length"
                         layout="total, sizes, prev, pager, next">
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submit">导 入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, VuexModule } from "nuxt-property-decorator";
import excelUtils, { ExcelAnalysisSheetMsg } from "~/utils/excelUtils";

export interface ImportEvent {
  ok: boolean;
  data: Array<any>;
  close: () => void;
}

type ResExcelAnalysisSheetMsg = ExcelAnalysisSheetMsg & {
  beforDeal?: boolean;
};

/**
 * befordealcb: 解析完成，显示数据前调用进行预处理：(obj: ExcelAnalysisSheetMsg) => string[][] | Promise<string[][]>
 */
@Component({
  name: "ym-excelimport",
  props: ["title", "columns", "size", "startrow", "endrow", "befordealcb"],
})
export default class YmExcelimport extends Vue {
  visible = false;

  sheetname = "";
  res = <ResExcelAnalysisSheetMsg[] | null>null;
  data: Array<Array<string>> = [];
  formPage = 1;
  fromRow = 5;
  showdata: Array<Array<string>> = [];

  headers: Array<any> = [];
  cols: Array<any> = [];

  ystartrow = 1;
  yendrow = 1;
  rowlen = 0;

  get ytitle() {
    return this.$props.title ? this.$props.title : "导入数据";
  }

  get ycolumns() {
    return this.$props.columns ? this.$props.columns : [];
  }

  get yfile() {
    return this.$props.file ? this.$props.file : null;
  }

  get ysize() {
    return this.$props.size ? this.$props.size : 10;
  }

  private getBefordealcb() {
    return this.$props.befordealcb
      ? this.$props.befordealcb
      : this.defBeforDealCb;
  }

  private defBeforDealCb(obj: ExcelAnalysisSheetMsg): string[][] {
    if (!obj || !obj.data || obj.data.length <= 0) {
      return obj.data;
    }
    let data = obj.data;
    let emptyIdx = data.length;
    for (let i = data.length - 1; i >= 0; i--) {
      let d = data[i];
      if (!d || d.length <= 0) {
        emptyIdx = i;
        continue;
      }
      let ok = false;
      for (let j = 0; j < d.length; j++) {
        if (d[j]) {
          ok = true;
          break;
        }
      }
      if (ok) {
        break;
      }
      emptyIdx = i;
    }
    if (emptyIdx == data.length) {
      return data;
    }
    data.splice(emptyIdx, data.length - emptyIdx);
    return data;
  }

  async mounted() {
    if (this.yfile) {
      await this.readfile();
    }
  }

  async openImport() {
    let ff = <any>this.$refs.fileinput;
    ff.value = "";
    ff.click();
  }

  initRes() {
    this.visible = false;
    this.sheetname = "";
    this.res = [];
    this.data = [];
    this.showdata = [];
    this.formPage = 1;
    this.fromRow = this.ysize;
    this.ystartrow = 2;
    this.yendrow = 2;
    this.rowlen = 0;
    this.headers = [];
    this.cols = [];
  }

  /**
   * 重新读取文件并解析，并初始化数据
   */
  async readfile() {
    this.initRes();
    if (this.ycolumns.length <= 0) {
      this.$notify.error(<any>{
        title: "导入失败",
        message: "字段信息配置有误，请联系管理员。",
      });
      return;
    }

    let rfile: any = this.$refs.fileinput;
    if (!rfile) {
      return;
    }
    let res = <ExcelAnalysisSheetMsg[] | null>null;
    let load = this.$loading({ text: "文件解析中，请稍后" });
    try {
      res = await excelUtils.readExcel(rfile);
    } catch (error) {
      this.$notify.error(<any>{
        title: "导入失败",
        message: "读取文件出错。",
      });
      return false;
    } finally {
      load.close();
    }
    if (res == null || res.length <= 0) {
      this.$notify.error(<any>{
        title: "导入失败",
        message: "文件中未找到数据。",
      });
      return;
    }
    this.res = res;
    this.visible = true;
    this.sheetname = this.res[0].sheetname;

    await this.refreshTable();
  }

  /**
   * 通过sheetName确定表格并进行初始化
   */
  async refreshTable() {
    // 查找数据
    let nowRes: ResExcelAnalysisSheetMsg | null = null;
    for (let i = 0; i < this.res!.length; i++) {
      let d = this.res![i];
      if (d.sheetname == this.sheetname) {
        nowRes = d;
        break;
      }
    }
    if (nowRes && !nowRes?.beforDeal) {
      let cb = this.getBefordealcb();
      let data = await cb(nowRes);
      nowRes.data = data;
      nowRes.beforDeal = true;
    }
    let data = nowRes?.data;
    // 解析数据
    if (!data || data.length <= 0) {
      this.$notify.error(<any>{
        title: "导入失败",
        message: "列表中未找到数据。",
      });
      return;
    }

    this.ystartrow = 2;
    this.yendrow = data.length;
    this.formPage = 1;

    await this.refreshColumn(data);
  }

  checkRowChange() {
    this.ystartrow = +this.ystartrow;
    this.yendrow = +this.yendrow;
    this.ystartrow = this.ystartrow < 1 ? 1 : this.ystartrow;
    this.ystartrow =
      this.ystartrow > this.rowlen ? this.rowlen : this.ystartrow;
    this.yendrow =
      this.yendrow < this.ystartrow ? this.ystartrow : this.yendrow;
    this.yendrow = this.yendrow > this.rowlen ? this.rowlen : this.yendrow;
  }

  /**
   * 通过具体的表单数据进行
   * @param data 表单数据
   */
  async refreshColumn(data: any) {
    if (!data || data.length <= 0) {
      this.ystartrow = 0;
      this.yendrow = 0;
    }
    let len = data.length;
    this.rowlen = len;
    this.checkRowChange();

    let cols = JSON.parse(JSON.stringify(this.ycolumns));
    cols.forEach((x: any) => (x.show = true));
    this.cols = cols;

    let headers = [];
    // debugger;
    if (data && data.length > 0 && data.length > this.ystartrow - 1) {
      for (let i = 0; i < data[this.ystartrow - 1].length; i++) {
        let name = "字段" + i;
        headers.push({ name: name, option: "", require: false });
      }
    }
    this.headers = headers;
    this.data = data;

    this._analysisDefaultHeader();

    await this.refreshShowData();
    await this.refreshColumnStyle();
  }

  /**
   * 分析表单头
   */
  _analysisDefaultHeader() {
    let startidx = this.ystartrow - 1;
    if (startidx <= 0) {
      return;
    }
    if (startidx >= this.data.length) {
      return;
    }
    let row = this.data[startidx - 1];
    let checks = []; // 已选列
    for (let i = 0; i < row.length; i++) {
      checks.push(null);
    }
    // 必须字段全等于优先匹配
    for (let j = 0; j < this.cols.length; j++) {
      let col = this.cols[j];
      if (checks.indexOf(col.code) >= 0) {
        continue;
      }
      for (let i = 0; i < row.length; i++) {
        if (this.headers.length < i + 1 || this.headers[i].option) {
          continue;
        }
        if (col.require) {
          if (row[i] && "*" + col.name == row[i]) {
            checks[i] = col.code;
            this.headers[i].option = col.code;
            break;
          }
        }
      }
    }
    // 普通字段全等等于
    for (let j = 0; j < this.cols.length; j++) {
      let col = this.cols[j];
      if (checks.indexOf(col.code) >= 0) {
        continue;
      }
      for (let i = 0; i < row.length; i++) {
        if (this.headers.length < i + 1 || this.headers[i].option) {
          continue;
        }
        if (row[i] && col.name == row[i]) {
          checks[i] = col.code;
          this.headers[i].option = col.code;
          break;
        }
      }
    }
    // 模糊匹配
    for (let j = 0; j < this.cols.length; j++) {
      let col = this.cols[j];
      if (checks.indexOf(col.code) >= 0) {
        continue;
      }
      for (let i = 0; i < row.length; i++) {
        if (this.headers.length < i + 1 || this.headers[i].option) {
          continue;
        }
        if (
          row[i] &&
          (row[i].indexOf(col.name) >= 0 || col.name.indexOf(row[i]) >= 0)
        ) {
          checks[i] = col.code;
          this.headers[i].option = col.code;
          break;
        }
      }
    }
  }

  tableRowClassName(row: any) {
    console.log("===");
    let css = "";
    let idx = (this.formPage - 1) * this.fromRow + row.rowIndex;
    if (idx < this.ystartrow - 1 || idx > this.yendrow - 1) {
      css = css + "background-red col";
    } else {
      css = css + "";
    }
    return css;
  }

  async handleEndRowChange() {
    this.checkRowChange();
    this.refreshShowData();
  }

  async headlRowChange() {
    await this.refreshColumn(this.data);
  }

  async headleColumnSelectChange() {
    await this.refreshColumnStyle();
  }

  async handleSizeChange(val: number) {
    let newPage = Math.floor(((this.formPage - 1) * this.fromRow) / val) + 1;
    this.fromRow = val;
    this.formPage = newPage;
    await this.refreshShowData();
  }

  async handleCurrentChange(val: number) {
    this.formPage = val;
    await this.refreshShowData();
  }

  async refreshShowData() {
    let end = this.formPage * this.fromRow;
    end = this.data.length < end ? this.data.length : end;
    let showdata = [];
    for (let i = this.fromRow * (this.formPage - 1); i < end; i++) {
      showdata.push(this.data[i]);
    }
    this.showdata = showdata;
  }

  async refreshColumnStyle() {
    // 渲染样式
    let code2col: any = {};
    for (let i = 0; i < this.cols.length; i++) {
      code2col[this.cols[i].code] = this.cols[i];
    }

    let codes = [];
    for (let i = 0; i < this.headers.length; i++) {
      if (this.headers[i].option) {
        let col = code2col[this.headers[i].option];
        this.headers[i].require = col.require;
        codes.push(this.headers[i].option);
      } else {
        this.headers[i].require = false;
      }
    }
    for (let i = 0; i < this.cols.length; i++) {
      let show = codes.indexOf(this.cols[i].code) < 0;
      this.$set(this.cols[i], "show", show);
    }
  }

  async cancel() {
    this.initRes();
    this.visible = false;
  }

  async submit() {
    if (this.data.length <= 0) {
      this.$notify.error(<any>{
        title: "导入失败",
        message: "当前解析到的数据条数为0，无法导入。",
      });
      return;
    }
    let notOks = [];
    for (let i = 0; i < this.cols.length; i++) {
      if (this.cols[i].show && this.cols[i].require) {
        notOks.push(this.cols[i]);
      }
    }
    if (notOks.length > 0) {
      this.$notify.error(<any>{
        title: "导入失败",
        message: "当前必选字段未全部匹配，无法导入。",
      });
      return;
    }

    // 合成数据
    let hds = [];
    for (let i = 0; i < this.headers.length; i++) {
      if (this.headers[i].option) {
        hds.push({ code: this.headers[i].option, idx: i });
      }
    }
    // 拼接
    let result = [];
    for (let i = this.ystartrow - 1; i < this.yendrow; i++) {
      let o: any = {};
      let d = this.data[i];
      for (let j = 0; j < hds.length; j++) {
        if (d.length > j) {
          o[hds[j].code] = d[hds[j].idx];
        } else {
          o[hds[j].code] = null;
        }
      }
      result.push(o);
    }
    // 事件
    let that = this;
    let even: ImportEvent = {
      ok: true,
      data: result,
      close: () => {
        that.visible = false;
      },
    };
    this.$emit("import", even);
  }
}
</script>

<style lang="scss">
.comp-ym-excelimport-select-color-red {
  color: red;
}
</style>
<style lang="scss" scoped>
.comp-ym-excelimport {
  .display-none {
    display: none;
  }

  .show-table::v-deep {
    .color-red .el-input .el-input__inner {
      color: red !important;
    }

    tr.background-red.col {
      // background-color: rgba(255, 0, 0, 0.76);
      background-color: rgba(255, 0, 0, 0.5);
    }
  }
  .form-start {
    margin-bottom: 10px;

    .red {
      color: rgba(255, 0, 0, 0.5);
    }
  }
  .form-end {
    margin-top: 10px;
  }
}
</style>
