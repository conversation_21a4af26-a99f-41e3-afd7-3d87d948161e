<template lang="pug">
.comp-ym-imageshowdisplay
  img.bgimg(:src="getImgUrl(content.bgImgUrl)")
  .btn
    template(v-for="(item, idx) in content.points")
      .item(
        :key="idx",
        @click="(e) => handleItemClick(item, e)",
        :style="{ top: `${item.top * 100}%`, left: `${item.left * 100}%`, width: `${item.width * 100}%`, height: `${item.height * 100}%` }"
      )
</template>

<script lang="ts">
import { Component, Watch, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import urlUtils from "~/utils/urlUtils";

export type PointContent = {
  code: string;
  top: number;
  left: number;
  width: number;
  height: number;
};

export type Content = {
  points: Array<PointContent>;
  bgImgUrl: string;
};

@Component({
  name: "ym-imageshowdisplay",
  props: ["value", "imgBaseUrl"],
})
export default class YmImageshowdisplay extends mixins(BaseVue) {
  content: Content = { points: [], bgImgUrl: "" };

  get _value() {
    return this.$props.value ? this.$props.value : "{}";
  }
  get _imgBaseUrl() {
    return this.$props.imgBaseUrl ? this.$props.imgBaseUrl : "";
  }

  getImgUrl(img: string) {
    if (!img) {
      return "";
    }
    if (img.startsWith("http://") || img.startsWith("https://")) {
      return img;
    }
    return this._imgBaseUrl ? this._imgBaseUrl + img : urlUtils.file2url(img);
  }

  async mounted() {
    await this.refresh();
  }

  async refresh() {
    this.content = JSON.parse(this._value);
  }

  async handleItemClick(item: PointContent, e: Event) {
    this.$emit("click", item);
  }

  @Watch("$props.value")
  async valueEven() {
    this.refresh();
  }
}
</script>
<style lang="scss" scoped>
.comp-ym-imageshowdisplay {
  position: relative;
  img.bgimg {
    width: 100%;
  }
  .btn {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0px;
    top: 0px;
  }
  .item {
    /* background-color: rgba(255, 0, 0, 0.3); */
    position: absolute;
    text-align: center;
    color: #000000;
    font-size: 30px;
    line-height: 100%;
    align-content: center;
  }
}
</style>

