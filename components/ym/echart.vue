<template>
  <div :style="{ width: '100%', height: '100%' }" ref="mainWin">
    <div :style="{ width: '100%', height: '100%' }" ref="chart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "nuxt-property-decorator";

import * as echarts from "echarts";
// const echarts = require("echarts/lib/echarts");
// 引入基本模板
// 引入柱状图组件
// require("echarts/lib/chart/bar");
import "echarts/lib/chart/bar";
// 引入提示框和title组件
// require("echarts/lib/component/tooltip");
import "echarts/lib/component/tooltip";
// require("echarts/lib/component/title");
import "echarts/lib/component/title";
import {
  GridComponent,
  TitleComponent,
  LegendComponent,
  TooltipComponent,
} from "echarts/components";
import { DomSizeListener } from "~/utils/domUtils";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "echarts/charts";
echarts.use([
  <any>GridComponent,
  TitleComponent,
  LegendComponent,
  TooltipComponent,
]);
echarts.use([<any>LineChart, BarChart, PieChart]);

@Component({
  name: "ym-echart",
  props: ["title", "subtext", "value", "x-filed", "y-field", "type"],
})
export default class YmEchart extends Vue {
  private myChart: any = null;
  private domSizeListener: DomSizeListener | null = null;

  async mounted() {
    await this.init();
    await this.valueEven();
  }

  async destroyed() {
    if (this.domSizeListener) {
      this.domSizeListener.stop();
      this.domSizeListener = null;
    }
  }

  //交互控制
  get _data() {
    return this.$props.value ? this.$props.value : [];
  }
  set _data(data) {
    this.$props.data = data;
  }
  get _title() {
    return this.$props.title ? this.$props.title : "";
  }
  get _subtext() {
    return this.$props.subtext ? this.$props.subtext : "";
  }
  get _xFiled() {
    return this.$props.xFiled ? this.$props.xFiled : "id";
  }
  get _type() {
    return this.$props.type;
  }

  get _yFiled() {
    let yf = this.$props.yField ? this.$props.yField : [];
    let type = this._type;
    let yfs: Array<any> = [];
    if (yf instanceof Array) {
      for (let i = 0; i < yf.length; i++) {
        if (typeof yf[i] == "object") {
          let _type = yf[i].type ? yf[i].type : type;
          let name = yf[i].name ? yf[i].name : yf[i].field;

          if (_type == "pie") {
            yfs.push({
              type: _type,
              name: this._xFiled,
              label: name,
              field: yf[i].field,
            });
          } else {
            yfs.push({
              type: _type,
              name,
              label: name,
              field: yf[i].field,
            });
          }
        } else {
          yfs.push({
            type: type,
            name: yf[i],
            label: yf[i],
            field: yf[i],
          });
        }
      }
    } else {
      if (type == "pie") {
        yfs.push({
          type: type,
          name: this._xFiled,
          label: this._xFiled,
          field: yf,
        });
      } else {
        yfs.push({
          type: type,
          name: yf,
          label: yf,
          field: yf,
        });
      }
    }
    return yfs;
  }

  @Watch("$props.value")
  async valueEven() {
    await this.drawLine();
  }
  @Watch("$props.type")
  async typeEven() {
    await this.drawLine();
  }
  @Watch("$props.yField")
  async yFieldEven() {
    await this.drawLine();
  }
  @Watch("$props.xField")
  async xFieldEven() {
    await this.drawLine();
  }
  @Watch("$props.title")
  async titleEven() {
    await this.drawLine();
  }

  async init() {
    if (!this.domSizeListener) {
      let that = this;
      this.domSizeListener = new DomSizeListener(this.$refs.mainWin, () => {
        if (that.myChart) {
          let width = (<any>that.$refs.mainWin).clientWidth;
          let height = (<any>that.$refs.mainWin).clientHeight;
          that.myChart.resize({ width, height });
          // that.drawLine();
        }
      });
      this.domSizeListener.start();
    }
    if (!this.myChart) {
      // 基于准备好的dom，初始化echarts实例
      let doc: any = this.$refs.chart;
      this.myChart = echarts.init(doc);
    }
  }

  async drawLine() {
    await this.init();

    let data = this._data;
    let yfield = this._yFiled;
    let xfield = this._xFiled;

    let xs = [];
    let sdata: Array<any> = [];
    let series: Array<any> = [];
    let legenddata: Array<any> = [];
    let types: Array<any> = [];
    for (let j = 0; j < yfield.length; j++) {
      let yf = yfield[j];
      let t: Array<any> = [];
      types.push(yf.type);
      legenddata.push(yf.name);
      sdata.push(t);
      series.push({
        data: t,
        name: yf.label,
        type: yf.type,
      });
    }
    for (let i = 0; i < data.length; i++) {
      let da = data[i];
      xs.push(da[xfield]);
      for (let j = 0; j < yfield.length; j++) {
        let dc = yfield[j];
        if (dc.type == "pie") {
          sdata[j].push({ value: da[dc.field], name: da[dc.name] });
        } else {
          sdata[j].push(da[dc.field]);
        }
      }
    }
    let option: any = {
      tooltip: {
        trigger: "item",
      },
      series,
    };
    option.title = {
      text: this._title,
      subtext: this._subtext,
    };
    if (!(types.length == 1 && types[0] == "pie")) {
      option.legend = {
        data: legenddata,
      };
      option.xAxis = {
        type: "category",
        data: xs,
      };
      option.yAxis = {
        type: "value",
      };
    }

    // 绘制图表
    this.myChart.setOption(option);
  }
}
</script>
