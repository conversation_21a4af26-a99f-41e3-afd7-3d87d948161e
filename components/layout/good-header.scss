@import '@/static/css/_var.scss';

.header {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 108px;
  padding: 0 48px;
  font-size: 22px;
  color: $--color-main-text;
  // background-color: $--color-main-background;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.collapse-btn:hover {
  background: mix($--color-white, $--color-main-background, 10%);
}

.header-flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  // margin: 0 3px;
}

.header-nav {
  display: flex;
  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 0; /* 可调控制层级关系 */
}

.nav-btn {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  font-family: 'PingFangSC';
  font-size: 14px;
  border-radius: 28px;
  background-blend-mode: overlay;
  cursor: pointer;

  img {
    width: 18px;
    height: 18px;
    margin-right: 12px;
  }
}

.nav-active {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 13%, rgba(255, 255, 255, 0.1));
}

.header .logo {
  line-height: 100%;
  z-index: 1;
  img {
    width: 60px;
    height: 60px;
  }
}

.header-right {
  display: flex;
  align-items: center;
  z-index: 1;
  // border: 1px solid red;
}

.header-user-con {
  display: flex;
  height: 70px;
  align-items: center;
}

.btn-fullscreen {
  transform: rotate(45deg);
  margin-right: 5px;
  font-size: 24px;
}

.btn-bell {
  position: relative;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;
  margin: 0 12px;
}

.btn-bell .header-txt {
  color: $--color-main-text;
  font-size: 18px;
  cursor: pointer;
  text-align: center;
}

.add-btn {
  font-family: 'PingFangSC';
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  padding: 12px 16px;
  margin-right: 12px;
  border-radius: 28px;
  background-image: linear-gradient(63deg, #4f0293 0%, #5899ff 100%);
  cursor: pointer;
}

.user-avator {
  margin-left: 12px;
  cursor: pointer;
}

.user-avator img {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;

}

.el-dropdown-link {
  color: $--color-main-text;
  cursor: pointer;
}

.el-dropdown-menu__item {
  text-align: center;
}

/** 对话框样式 */
::v-deep {
  .share-modal {
    background-color: rgb(23, 22, 24);
    border-radius: 28px;
    font-family: 'PingFangSC';
  }

  .el-dialog__header {
    padding-bottom: 0;
    padding-top: 30px;
  }

  .el-dialog__body {
    display: flex;
  }
}

.share-modal-header {
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'PingFangSC';
  font-size: 18px;
  font-weight: 500;
  // border: 1px solid red;

  img {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
}

.share-modal-body {
  display: flex;
  flex-direction: column;
  padding: 0 24px;
}

.case-share {
  font-family: 'PingFangSC';
  margin-bottom: 48px;

  p {
    font-size: 12px;
    color: rgba(235, 235, 245, 0.7);
    padding-bottom: 24px;
  }

  .case-share-article {
    display: flex;
    justify-content: left;

  }

    .case-share-box {
      width: 205px;
      height: 123px;
      margin-right: 24px;
      // perspective: 1000px;
      position: relative;
  
      &:hover {
        .case-share-bg {
          transform: rotate(-4.65deg);
        }

        .case-share-content{
          transform: rotate(4.65deg);
        }
      }
    }
  .card{
    width: 100%;
    height: 100%;
    border-radius: 20px;
    transition: transform 0.3s ease-in-out;
    // backface-visibility: hidden;
    // transform-style: preserve-3d;
    transform-origin:left center;
  }
  .case-share-bg{
    position: absolute;
    z-index: 1;
    background-image: linear-gradient(54deg, #4f0293 4%, #5899ff 96%);
  }
  .case-share-content{
    position: absolute;
    z-index: 2;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 24px 16px;
    border-radius: 20px;
    border: solid 1.4px rgba(255, 255, 255, 0.15);
    background-color: rgba(35, 33, 36);
    cursor: pointer;

    
    img {
      width: 18px;
      height: 18px;
      margin-bottom: 16px;
    }

    .case-share-item-title {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      line-height: 21px;
    }

    .case-share-item-desc {
      font-size: 12px;
      color: rgba(235, 235, 245, 0.7);
      line-height: 18px;
      margin-top: 2px;
    }
  }
}