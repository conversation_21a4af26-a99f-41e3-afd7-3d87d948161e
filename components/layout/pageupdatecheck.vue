<template lang="pug">
.comp-layout-pageupdatecheck
  .header
</template>

<script lang="ts">
import { Component, Vue, Watch, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import { ResponseBody } from "~/plugins/axios-accessor";
import { request } from "~/utils/request";
import routerUtils from "~/utils/routerUtils";

@Component({
  name: "comp-layout-pageupdatecheck",
})
export default class CompLayoutPageupdatecheck extends mixins(BaseVue) {
  timer: any = null;

  async mounted() {
    this.initTimer();
    this.check();
  }

  initTimer() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.timer = setTimeout(() => {
      this.initTimer();
      this.check();
    }, 60000);
  }

  async check() {
    let wwwUrl = routerUtils.getLocalUrl(this);
    let responseBody = <ResponseBody>(
      await request.get(wwwUrl, { responseAll: true })
    );
    if (!responseBody || !responseBody.response) {
      return;
    }
    let response = responseBody.response;
    if (!(response.status > 0 && response.status < 400)) {
      return;
    }
    if (!response.headers && !response.headers["etag"]) {
      return;
    }
    let etag = response.headers["etag"];
    this.$store.commit("page/setEtag", etag);
  }

  async beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  @Watch("$store.state.page.pageUpdated")
  public pageUpdate() {
    if (
      window.confirm(
        '页面已更新，是否立刻刷新页面？\n注意：如果正在进行填写表单等操作，为了保证当前表单数据不被清空，请点击"取消"，等到表单提交后，手动刷新页面'
      )
    ) {
      window.location.reload();
    }
  }
}
</script>

<style lang="sass" scoped>
</style>
