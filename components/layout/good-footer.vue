<template lang="pug">
.footer
  .logo
    img(src="/img/logo.svg")
  .info
    span 隐私政策
    .line
    span 使用条款
</template>

<script lang="ts">
import { Component, Vue, Watch, mixins } from "nuxt-property-decorator";
import { BaseVue, VueWs } from "~/model/vue";

@Component({
  name: "layout-footer",
})
export default class LayoutHeader extends mixins(BaseVue) {

}
</script>

<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: space-between;
  padding: 24px 48px;
}

.logo {
  img {
    width: 40px;
    height: 40px;
  }
}

.info {
  font-family: 'PingFangSC';
  font-size: 14px;
  color: rgba(235, 235, 245, 0.7);
  display: flex;
  align-items: center;

  span {
    cursor: pointer;
  }
}

.line {
  width: 1px;
  height: 12px;
  background-color: rgba(235, 235, 245, 0.7);
  margin: 2px 12px 0px;
}
</style>
