<template lang="pug">
.loading-page(v-if="loading")
  .loading-content
    i.el-icon-loading
    p.el-loading-text
      span 加载中，请稍后
      span.loading-text-end {{ content }}
</template>
  
  <script>
export default {
  data: () => ({
    loading: false,
    timerCount: 0,
    content: '',
    timer: null,
  }),
  methods: {
    start () {
      let that = this;
      this.loading = true;

      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }

      this.timerCount = 0;
      this.timer = setInterval(() => {
        // debugger
        let count = that.timerCount++;
        let c = '';
        for (let i = 0; i < (count % 4); i++) {
          c += '.';
        }
        that.content = c;
      }, 500);
    },
    finish () {
      this.loading = false;
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
  },
};
</script>
  
<style lang="scss" scoped>
.loading-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  /* padding-top: 1.041667rem; */
  font-size: 0.15625rem;
  font-family: sans-serif;
  z-index: 100;

  display: flex;
  justify-content: center;
  align-items: center;

  .loading-content {
    // color: #ab3724;
    color: rgba(255, 255, 255, 0.9);
    height: 200px;
    // font-size: 0.2rem;

    .el-icon-loading {
      font-size: 56px; /* 调整字体大小为你想要的值 */
    }
    .el-loading-text {
      font-size: 20px; /* 调整字体大小为你想要的值 */
      padding-left: 30px;
    }

    .loading-text-end {
      width: 30px;
      display: inline-flex;
    }
  }
}
</style>
