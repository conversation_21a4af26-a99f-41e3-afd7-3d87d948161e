<template lang="pug">
.header
  .logo.header-flex-center
    img(src="/img/logo.svg")
  .header-nav
    .nav-btn(:class="activeNav === 'home'?'nav-active':''" @click="clickNav('home')")
      img(src="/img/home/<USER>")
      span 发现
    .nav-btn(:class="activeNav === 'case'?'nav-active':''" @click="clickNav('case')")
      img(src="/img/home/<USER>")
      span 案例
    .nav-btn(:class="activeNav === 'file'?'nav-active':''" @click="clickNav('file')")
      img(src="/img/home/<USER>")
      span 素材
  .header-right
    .header-user-con
      .add-btn(@click="creativeShare") 创意分享
      //- <!-- 国际化 -->
      .btn-bell.header-flex-center
        el-dropdown.header-flex-center(@command="handleI18nCommand")
          span.header-txt
            i.el-icon-setting
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(v-if="$i18n.locale !== 'en'", command="en") English
              el-dropdown-item(v-if="$i18n.locale !== 'zh'", command="zh") 简体中文
      .btn-bell.header-flex-center
        el-dropdown(@command="handleMsgCommand")
          el-badge(
            :value="unreadCount + bpmTodoCount",
            :hidden="unreadCount + bpmTodoCount <= 0",
            :max="10"
          )
            .header-txt
              i.header-txt.el-icon-bell
          template(#dropdown)
            el-dropdown-menu
              el-dropdown-item(command="/def/base/bpm/todo")
                .router-txt(to="/def/base/bpm/todo") 待办事项
                  el-badge.mgl10(
                    :value="bpmTodoCount",
                    :hidden="bpmTodoCount <= 0"
                  )
              el-dropdown-item(command="/def/base/msg-inbox")
                .router-txt(to="/def/base/msg-inbox") 站内消息
                  el-badge.mgl10(
                    :value="unreadCount",
                    :hidden="unreadCount <= 0"
                  )
      //- <!-- 用户头像及下拉菜单 -->
      el-dropdown.user-name.header-flex-center(
        trigger="hover",
        @command="userHandleCommand"
      )
        .user-avator.header-flex-center
          el-avatar(:src="_headerImg") {{ _ua }}
        template(#dropdown)
          el-dropdown-menu
            el-dropdown-item(command="changePassword") {{ $t("cmpl.layout.header.changePassword") }}
            el-dropdown-item(
              v-if="$auth.v_hasPower('ROLE_ADMIN')",
              divided,
              command="proxyswtich2user"
            ) {{ $t("cmpl.layout.header.proxyswtich2user") }}
            el-dropdown-item(
              v-if="userInfo.authProxyUser",
              divided,
              command="proxyback"
            ) {{ $t("cmpl.layout.header.proxyback") }}({{ userInfo.authProxyUserInfo.name }})
            el-dropdown-item(divided, command="logout") {{ $t("cmpl.layout.header.logout") }}
  el-dialog(
    :visible.sync="visible",
    width="760px",
    :before-close="handleClose",
    :show-close="false",
    custom-class="share-modal"
  )
    .share-modal-header(slot="title")
      p 创意分享
      img(src="/img/home/<USER>" @click="visible = false")
    .share-modal-body
      .case-share
        p 案例分享
        .case-share-article
          .case-share-box(@click="goPage('dams/asset/add?type=GOOD_CASE')")
            .card.case-share-bg
            .card.case-share-content
              img(src="/img/home/<USER>")
              .case-share-item-title 古德案例
              .case-share-item-desc 中标案例上传
          .case-share-box(@click="goPage('dams/asset/add?type=DESIGN_PROPOSAL')")
            .card.case-share-bg
            .card.case-share-content
              img(src="/img/home/<USER>")
              .case-share-item-title 设计提案
              .case-share-item-desc 未中标产品/策划提案
      .case-share
        p 素材分享
        .case-share-article
          .case-share-box(@click="goPage('dams/asset/add?type=KV_DESIGN')")
            .card.case-share-bg
            .card.case-share-content
              img(src="/img/home/<USER>")
              .case-share-item-title KV设计
              .case-share-item-desc 创意视觉库
          .case-share-box(@click="goPage('dams/asset/add?type=MODEL_3D')")
            .card.case-share-bg
            .card.case-share-content
              img(src="/img/home/<USER>")
              .case-share-item-title 3D模型
              .case-share-item-desc 三维素材上传
          .case-share-box(@click="goPage('dams/asset/add?type=MARKET_INSPIRATION')")
            .card.case-share-bg
            .card.case-share-content
              img(src="/img/home/<USER>")
              .case-share-item-title 市场灵感
              .case-share-item-desc 捕捉创意瞬间
</template>

<script lang="ts">
import { Component, Vue, Watch, mixins } from "nuxt-property-decorator";
import { User, WsMsgSendBodyBo } from "~/model/common";
import { BaseVue, VueWs } from "~/model/vue";
import sysconfigUtils from "~/utils/sysconfigUtils";
import autAuthApi from "~/api/auth/auth/authApi";
import compUtils from "~/utils/compUtils";

@Component({
  name: "layout-header",
})
export default class LayoutHeader extends mixins(BaseVue) {
  userInfo: User = {
    name: "",
    username: "",
  };
  sysInfo = {
    title: "",
  };
  fullscreen = false;
  activeNav = 'home';
  visible = false;
  get _ua() {
    if (!this.userInfo) {
      return "";
    } else if (this.userInfo.name) {
      return this.userInfo.name.substring(0, 1);
    } else if (this.userInfo.username) {
      return this.userInfo.username.substring(0, 1);
    } else {
      return "";
    }
  }

  get _headerImg() {
    return "";
    // return this.userInfo.headerImg
    //   ? urlUtils.file2url(this.userInfo.headerImg)
    //   : "";
  }

  get collapse() {
    return this.$store.state.tags.collapse;
  }

  get unreadCount() {
    return this.$store.state.msg.unreadCount;
  }

  get bpmTodoCount() {
    return this.$store.state.msg.bpmTodoCount;
  }

  goPage(path: string){
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push({
      path: `/${role}/${path}`,
    });
  }
  clickNav(nav:string){
    this.activeNav = nav;
  }
  creativeShare() {
    this.visible = true;
  }
  handleClose(){
    this.visible = false;
  }
  // 用户名下拉菜单选择事件
  async userHandleCommand(command: string) {
    if (command == "logout") {
      await this.logout();
    } else if (command == "changePassword") {
      this.$router.push("/def/base/user/change-password");
    } else if (command == "proxyswtich2user") {
      this.$router.push("/def/power/proxy");
    } else if (command == "proxyback") {
      await this.backUser();
    }
  }

  async backUser() {
    let result = await autAuthApi.proxyBack();
    await this.$store.dispatch("auth/setToken", result);

    this.$notify.success("切换成功");

    this.$router.push("/");
    setTimeout(() => {
      window.location.reload();
    }, 10);
  }

  async handleI18nCommand(command: string) {
    (<any>this.$i18n).switchLocale(command);
  }

  async handleMsgCommand(command: string) {
    this.$router.push(command);
  }

  @Watch(VueWs.WATCH_WS_MSG)
  async wsMsg(msg: WsMsgSendBodyBo) {
    if (msg.bc != "sys") {
      return;
    }
    if (msg.fc != "msg-inbox") {
      return;
    }
    if (msg.ec != "add") {
      return;
    }
    if (!msg.d) {
      return;
    }
    this.$msg
      .refreshMsgCount()
      .then((res) => {})
      .catch((e) => {});
  }

  // 侧边栏折叠
  collapseChage() {
    this.$store.commit("tags/hadndleCollapse", !this.collapse);
  }

  async mounted() {
    // 无需等待
    if (document.body.clientWidth < 1500) {
      this.collapseChage();
    }

    this.loadUserInfo().then(() => {});

    if (this.unreadCount < 0) {
      this.$msg
        .refreshMsgCount()
        .then((res) => {})
        .catch((e) => {});
    }
    if (this.bpmTodoCount < 0) {
      this.$msg
        .refreshBpmTodoCount()
        .then((res) => {})
        .catch((e) => {});
    }

    let title = sysconfigUtils.getValueDef("SYS_ADMIN_TITLE", "管理系统");
    this.sysInfo.title = title;
    document.title = title;
    // document.head.querySelector('title'). = this.sysInfo.title;
  }

  async loadUserInfo() {
    let userInfo = await this.$auth.getUserInfo();
    this.userInfo = userInfo ? userInfo : {};
  }

  async logout() {
    await this.$store.dispatch("auth/logout");
    // this.$router.push("/");
    this.$router.go(0);
    // this.$router.go(0);
  }
}
</script>

<style lang="sass" scoped>
@import './good-header.scss'
</style>
