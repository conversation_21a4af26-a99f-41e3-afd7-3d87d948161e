@import '@/static/css/_var.scss';

.header {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 70px;
  font-size: 22px;
  color: $--color-main-text;
  background-color: $--color-main-background;
}

.collapse-btn:hover {
  background: mix($--color-white, $--color-main-background, 10%);
}

.header-flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  margin: 0 3px;
}

.collapse-btn {
  float: left;
  padding: 0 21px;
  cursor: pointer;
  line-height: 70px;
}

.header .logo {
  float: left;
  line-height: 100%;
}

.header-right {
  float: right;
  padding-right: 50px;
}

.header-user-con {
  display: flex;
  height: 70px;
  align-items: center;
}

.btn-fullscreen {
  transform: rotate(45deg);
  margin-right: 5px;
  font-size: 24px;
}

.btn-bell {
  position: relative;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;

  margin: 10px;
}

.btn-bell .header-txt {
  color: $--color-main-text;
  font-size: 24px;
  cursor: pointer;
  text-align: center;
}

.user-avator img {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.el-dropdown-link {
  color: $--color-main-text;
  cursor: pointer;
}

.el-dropdown-menu__item {
  text-align: center;
}