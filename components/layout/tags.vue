<template lang="pug">
.tags(v-if="tagsList && tagsList.length > 0")
  //- 标签列表
  .tags-list
    ul(ref="tagul")
      li.tags-li(
        v-for="(item, index) in tagsList",
        :class="{ active: isActive(item) }",
        :key="index"
      )
        router-link.tags-li-title(
          :to="item.fullPath",
          :title="getMenuName(item)"
        ) {{ getMenuName(item) }}
        span.tags-li-icon(@click="closeTags(index)")
          i.el-icon-close
  //- 关闭控制界面
  .tags-close-box
    el-dropdown(@command="handleTags")
      el-button(size="mini", type="primary") {{ $t("cmpl.layout.tags.operate") }}
        i.el-icon-arrow-down.el-icon--right
      template(#dropdown)
        el-dropdown-menu(size="small")
          el-dropdown-item(command="other") {{ $t("cmpl.layout.tags.closeOther") }}
          el-dropdown-item(command="all") {{ $t("cmpl.layout.tags.closeAll") }}
</template>

<script lang="ts">
// import { Route } from "ant-design-vue/types/breadcrumb/breadcrumb";
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import { TagNode } from "~/model/common";
import { BaseVue } from "~/model/vue";
import routerUtils from "~/utils/routerUtils";
import sortableUtils from "~/utils/sortableUtils";

@Component({
  name: "layout-lags",
})
export default class LayoutTags extends mixins(BaseVue) {
  tagsList: Array<TagNode> = [];

  mounted() {
    this.tagsList = this.$store.state.tags.tagsList;
    this.initSortable();
  }

  getMenuName(item: TagNode) {
    let key = `menu.tag-${item.name}`;
    let title = "";
    if (this.$te(key)) {
      title = this.$tc(key);
    } else {
      title = this.$tc("menu." + item.name);
    }
    return title;
  }

  initSortable() {
    let that = this;
    this.$nextTick(() => {
      let el = this.$refs.tagul;
      if (!el) {
        return;
      }
      sortableUtils.sortable(el, this, "tagsList", {
        getData: () => [...that.tagsList],
        end: (data: any) => {
          that.$store.commit("tags/setTagsItem", []);
          that.$nextTick(() => {
            that.$store.commit("tags/setTagsItem", data);
          });
        },
      });
    });
  }

  isActive(item: TagNode) {
    return item.path === (<any>this.$route).path;
  }

  // 关闭单个标签
  closeTags(index: number) {
    const delItem = this.tagsList[index];
    this.$store.commit("tags/delTagsItem", { index });
    const item = this.tagsList[index]
      ? this.tagsList[index]
      : this.tagsList[index - 1];
    if (item) {
      delItem.path === (<any>this.$route).fullPath &&
        this.$router.push(item.path!);
    } else {
      this.$router.push("/");
    }
  }
  // 关闭全部标签
  closeAll() {
    this.$store.commit("tags/clearTags");
    this.$router.push("/");
  }
  // 关闭其他标签
  closeOther() {
    let routePath = routerUtils.getRoutePath(this);
    const curItem = this.tagsList.filter((item: any) => {
      return item.path === routePath;
    });
    this.$store.commit("tags/setTagsItem", curItem);
  }

  handleTags(command: string) {
    command === "other" ? this.closeOther() : this.closeAll();
  }

  @Watch("$store.state.tags.tagsList")
  async tagsChange(list: Array<any>) {
    this.tagsList = list;
    this.initSortable();
  }
}
</script>

<style lang="sass" scoped>
@import './tags.scss'
</style>
