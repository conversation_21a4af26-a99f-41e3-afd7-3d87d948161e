<template lang="pug">
.sidebar
  //- 菜单列表
  el-menu.sidebar-el-menu(
    :default-active="onRoutes",
    :collapse="collapse",
    unique-opened,
    popper-class="main-menu"
  )
    template(v-for="(item, idx1) in items")
      template(v-if="item.subs")
        el-submenu(
          :index="getKey(item, 'n-' + idx1)",
          :key="getKey(item, 'n-' + idx1)"
        )
          template(#title)
            i(v-if="item.icon", :class="item.icon")
            span {{ $t("menu." + item.name) }}
          //- 二级菜单内部
          template(v-for="(subItem, idx2) in item.subs")
            el-submenu(
              v-if="subItem.subs",
              :index="getKey(subItem, 'n-' + idx1 + '-' + idx2)",
              :key="getKey(subItem, 'n-' + idx1 + '-' + idx2)"
            )
              template(#title) {{ $t("menu." + subItem.name) }}
              //- 三级菜单内部
              el-menu-item(
                v-for="(threeItem, idx3) in subItem.subs",
                :key="getKey(threeItem, 'n-' + idx1 + '-' + idx2 + '-' + idx3)",
                :index="getKey(threeItem, 'n-' + idx1 + '-' + idx2 + '-' + idx3)",
                @click="toPage(threeItem)"
              ) {{ $t("menu." + threeItem.name) }}
            //- 二级菜单无三级菜单
            el-menu-item(
              v-else,
              :index="getKey(subItem, 'n-' + idx1 + '-' + idx2)",
              :key="getKey(subItem, 'n-' + idx1 + '-' + idx2)",
              @click="toPage(subItem)"
            ) {{ $t("menu." + subItem.name) }}
      //- 一级菜单，无二级菜单
      template(v-else)
        el-menu-item(
          :index="getKey(item, 'n-' + idx1)",
          :key="getKey(item, 'n-' + idx1)",
          @click="toPage(item)"
        )
          i(v-if="item.icon", :class="item.icon")
          template(#title) {{ $t("menu." + item.name) }}

  .hidden-loading(v-if="loadingItem")
    div(v-for="(item, idx1) in loadingItem")
      nuxt-link(v-if="item.url", :to="item.url")
      div(v-if="item.subs")
        div(v-for="(subItem, idx2) in item.subs")
          nuxt-link(v-if="subItem.url", :to="subItem.url")
          div(v-for="(threeItem, idx3) in subItem.subs")
            nuxt-link(v-if="threeItem.url", :to="threeItem.url")
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { config } from "~/config/global.config";
import menuUtils from "~/utils/menuUtils";
import { MenuItem } from "~/model/common";
import { BaseVue, VueAuth } from "~/model/vue";
import routerUtils from "~/utils/routerUtils";

type MenuItemNode = MenuItem & {
  // mid?: string;
};

@Component({
  name: "layout-sidebar",
})
export default class LayoutSidebar extends mixins(BaseVue) {
  loadingItem: MenuItemNode[] | null = null;
  items: MenuItemNode[] = [];
  menus: MenuItemNode[] = config.menu.items;

  keyDownControl = false;

  get onRoutes() {
    let routePath = routerUtils.getRoutePath(this);
    let menu = menuUtils.findMenuReg(routePath);
    return menu ? menu._mid : "u";
  }
  get collapse() {
    return this.$store.state.tags.collapse;
  }

  getKey(item: MenuItemNode, def: string) {
    return item._mid;
  }

  async mounted() {
    window.addEventListener("keydown", this.handleKeyDown);
    window.addEventListener("keyup", this.handleKeyUp);
    await this.mountMenu();
    this.loadPageResource();
  }

  async loadPageResource() {
    setTimeout(() => {
      console.info("开始预加载其它页面资源");
      this.loadingItem = this.items;
    }, 5000);
  }

  async beforeDestroy() {
    window.removeEventListener("keydown", this.handleKeyDown);
    window.removeEventListener("keyup", this.handleKeyUp);
  }

  handleKeyDown(event: any) {
    if (event.key == "Control" || event.key == "Meta") {
      this.keyDownControl = true;
    }
  }
  handleKeyUp(event: any) {
    if (event.key == "Control" || event.key == "Meta") {
      this.keyDownControl = false;
    }
  }

  async mountMenu() {
    await this.refreshMenu();

    if (this.$route.path == "/" && this.items.length > 0) {
      this.$router.push(this._getFirstItem(this.items).url);
    }
  }

  async refreshMenu() {
    let items: MenuItem[] = [];
    await this._loadMenu(this.menus, items);
    this.items = items;
  }

  _getFirstItem(items: Array<any>): any {
    if (!items || items.length <= 0) {
      return null;
    }
    let item = items[0];
    if (item.subs && item.subs.length > 0) {
      return this._getFirstItem(item.subs);
    } else {
      return item;
    }
  }

  async _loadMenu(menus: MenuItem[], items: MenuItem[]) {
    for (let i in menus) {
      let item: MenuItem = menus[i];
      if (false == item.show) {
        continue;
      }
      // 当前是根目录
      if (item.role && item.role.length > 0) {
        if (!(await this.$auth.hasPower(item.role))) {
          continue;
        }
      }
      if (item.pageRole && item.pageRole.length > 0) {
        let pageRole = await this.$auth.getPageRole();
        if (!pageRole) {
          continue;
        } else {
          if (item.pageRole.indexOf(pageRole) < 0) {
            continue;
          }
        }
      }
      // 基础校验通过
      if (item.subs && item.subs.length > 0) {
        // 当前不是根目录
        let subitems: MenuItem[] = [];
        await this._loadMenu(item.subs, subitems);

        if (subitems.length > 0) {
          // 存在子目录
          let nitem = { ...item };
          nitem.subs = subitems;
          items.push(nitem);
        }
      } else {
        items.push({ ...item });
      }
    }
  }

  async toPage(item: any) {
    let toPageRole: string | null = item.toPageRole;
    if (toPageRole) {
      await this.$store.dispatch("auth/putPageRole", toPageRole);
    }

    let url = item.url;
    if (this.keyDownControl) {
      let wwwUrl = routerUtils.getLocalUrl(this);
      window.open(`${wwwUrl}${url}`);
    } else {
      this.$router.push(url);
    }
  }

  @Watch(VueAuth.WATCH_PAGE_ROLE)
  async pageRoleChange() {
    await this.refreshMenu();
  }

  @Watch("$route.path")
  async routePathChange() {
    if (this.$route.path == "/" && this.items.length > 0) {
      this.$router.push(this._getFirstItem(this.items).url);
    }
  }
}
</script>

<style lang='scss' scoped>
@import "@/static/css/_var";

// $--background-color: #324157;
$--background-color: mix($--color-white, $--color-main-background, 5%);
// $--text-color: $--color-main-text;
$--text-color: mix($--color-black, $--color-main-text, 5%);

// .sidebar::v-deep {
//   .el-menu {
//     color: $--text-color;
//     background-color: $--background-color;
//   }
//   .el-submenu__title {
//     color: $--text-color;

//     &:hover {
//       background-color: mix($--color-white, $--background-color, 10%);
//     }
//   }
//   .el-menu-item {
//     color: $--text-color;
//     background-color: $--background-color;

//     &:hover {
//       background-color: mix($--color-white, $--background-color, 10%);
//     }
//   }
//   .is-active.el-menu-item {
//     color: $--color-primary;
//     background-color: mix($--color-primary, $--background-color, 5%);
//   }
// }

.sidebar {
  display: block;
  position: absolute;
  left: 0;
  top: 70px;
  bottom: 0;
  overflow-y: scroll;
}
.sidebar::-webkit-scrollbar {
  width: 0;
}
.sidebar-el-menu:not(.el-menu--collapse) {
  width: 250px;
}
.sidebar > ul {
  height: 100%;
}

.hidden-loading {
  position: absolute;
  height: 0;
  width: 0;
  top: 90000;
}
</style>
