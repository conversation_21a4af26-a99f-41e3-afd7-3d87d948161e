@import '@/static/css/_var.scss';

.tags {
  position: relative;
  height: 30px;
  overflow: hidden;
  background: $--color-white;
  padding-right: 120px;
  box-shadow: 0 5px 10px #ddd;


  .tags-list {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    overflow-x: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

}

.tags ul {
  box-sizing: border-box;
  // width: 100%;
  width: max-content;
  height: 100%;


  .tags-li {
    float: left;
    margin: 3px 5px 2px 3px;
    border-radius: 3px;
    font-size: 12px;
    overflow: hidden;
    cursor: pointer;
    height: 23px;
    line-height: 23px;
    padding: 0 5px 0 12px;
    vertical-align: middle;
    -webkit-transition: all 0.3s ease-in;
    -moz-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;

    border: 1px solid $--border-color-base;
    background: $--background-color-base;

    & ::before {
      color: $--icon-color-base;
    }

    .tags-li-title {
      float: left;
      max-width: 80px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: 5px;

      color: $--color-text-regular;
    }

    &:hover {
      background: mix($--background-color-base, $--color-primary, 90%);

      .tags-li-title {
        color: $--color-primary;
      }

      & ::before {
        color: $--color-primary;
      }
    }

    &.active {
      border: 1px solid $--border-color-base;
      background-color: $--color-primary;

      & .tags-li-title {
        color: $--color-white;
      }

      & ::before {
        color: $--color-white;
      }
    }
  }

}

.tags-close-box {
  position: absolute;
  right: 0;
  top: 0;
  box-sizing: border-box;
  padding-top: 1px;
  text-align: center;
  width: 110px;
  height: 30px;
  box-shadow: -3px 0 15px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}