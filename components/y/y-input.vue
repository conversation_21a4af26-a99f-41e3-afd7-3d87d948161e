<!--
/**
 * 输入框组件
 *
 * @description 基于 Element UI 的输入框组件封装，支持文本输入和多行文本输入
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 支持文本输入和多行文本输入
 * - 提供前缀和后缀插槽支持
 * - 适配深色主题设计
 * - 完全兼容 Element UI 输入框功能
 */
-->
<template>
  <div class="y-input" :class="{ 'is-textarea': type === 'textarea' }">
    <el-input ref="elInput" :value="displayValue" :type="type" :placeholder="placeholder"
              :disabled="disabled" :readonly="readonly" :clearable="clearable"
              :prefix-icon="prefixIcon" :suffix-icon="suffixIcon" :rows="rows"
              :autosize="type === 'textarea' ? { minRows: rows } : false" @input="handleInput"
              @focus="handleFocus" @blur="handleBlur" @clear="clear">
      <template v-if="$slots.prefix" #prefix>
        <slot name="prefix"></slot>
      </template>
      <template v-if="$slots.suffix" #suffix>
        <slot name="suffix"></slot>
      </template>
    </el-input>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";

@Component({
  name: "YInput",
})
export default class YInput extends Vue {
  @Prop({ type: [String, Number], default: "" }) readonly value!: string | number;
  @Prop({ type: String, default: "text" }) readonly type!: string;
  @Prop({ type: String, default: "" }) readonly placeholder!: string;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly readonly!: boolean;
  @Prop({ type: Boolean, default: false }) readonly clearable!: boolean;
  @Prop({ type: String }) readonly prefixIcon!: string;
  @Prop({ type: String }) readonly suffixIcon!: string;
  @Prop({ type: Number, default: 2 }) readonly rows!: number;

  focused = false;

  // 计算属性：将value转换为字符串用于显示
  get displayValue(): string {
    return this.value?.toString() || "";
  }

  @Emit("input")
  handleInput(value: string) {
    // 如果是数字类型输入框，转换为数值
    if (this.type === "number") {
      const numValue = parseFloat(value);
      return isNaN(numValue) ? 0 : numValue;
    }
    return value;
  }

  @Emit("focus")
  handleFocus(event: FocusEvent) {
    this.focused = true;
    return event;
  }

  @Emit("blur")
  handleBlur(event: FocusEvent) {
    this.focused = false;
    return event;
  }

  @Emit("input")
  clear() {
    // 如果是数字类型，返回0，否则返回空字符串
    return this.type === "number" ? 0 : "";
  }

  focus() {
    (this.$refs.elInput as any).focus();
  }

  blur() {
    (this.$refs.elInput as any).blur();
  }
}
</script>

<style lang="scss" scoped>
.y-input {
  position: relative;
  width: 100%;

  // 覆盖 Element UI 的样式
  ::v-deep .el-input {
    width: 100%;

    .el-input__inner {
      box-sizing: border-box;
      padding: 8px;
      width: 100%;
      height: 40px;
      min-height: 40px;
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 8px;
      transition: all 0.2s ease;
      background-color: transparent;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI",
        Roboto, sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 150%;
      color: #ffffff;

      &::placeholder {
        color: rgba(235, 235, 245, 0.7);
      }

      &:hover {
        border-color: rgba(255, 255, 255, 0.3);
      }

      &:focus {
        border-color: rgba(255, 255, 255, 0.5);
      }

      &:disabled {
        cursor: not-allowed;
        color: rgba(235, 235, 245, 0.4);
        background-color: transparent;
      }

      // 隐藏数字输入框的 step 控件（上下箭头）
      &[type="number"] {
        -moz-appearance: textfield; // Firefox

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
      }
    }

    .el-input__prefix,
    .el-input__suffix {
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(235, 235, 245, 0.7);

      i {
        font-size: 16px;
      }
    }

    .el-input__clear {
      color: rgba(235, 235, 245, 0.5);

      &:hover {
        color: rgba(235, 235, 245, 0.8);
      }
    }
  }

  ::v-deep .el-textarea {
    .el-textarea__inner {
      box-sizing: border-box;
      padding: 8px;
      width: 100%;
      min-height: 80px;
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 8px;
      transition: all 0.2s ease;
      background-color: transparent;
      resize: none;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI",
        Roboto, sans-serif;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 150%;
      color: #ffffff;

      &::placeholder {
        color: rgba(235, 235, 245, 0.7);
      }

      &:hover {
        border-color: rgba(255, 255, 255, 0.3);
      }

      &:focus {
        border-color: rgba(255, 255, 255, 0.5);
      }

      &:disabled {
        cursor: not-allowed;
        color: rgba(235, 235, 245, 0.4);
        background-color: transparent;
      }
    }
  }
}
</style>
