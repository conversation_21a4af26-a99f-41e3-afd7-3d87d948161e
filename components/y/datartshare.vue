<template lang="pug">
.main-win
  iframe(ref="mainiframe", :src="_shareUrl", width="100%", frameborder="0")
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import routerUtils from "~/utils/routerUtils";

/**
 * 数据可视化分享
 * type: dashboard: 控制台(默认) player: 播放列表
 */
@Component({
  name: "y-datartshare",
  props: ["id", "code", "type"],
})
export default class YDatartshare extends mixins(BaseVue) {
  loading = 0;
  query = {};
  formData = {};
  oneLoadParam = {
    inited: false,
    initCount: 0,
    // ...param
  };
  bizData = {
    timer: <any>null,
    ifameHeight: 0,
  };

  get _id() {
    return this.$props.id ? this.$props.id : "";
  }

  get _code() {
    return this.$props.code ? this.$props.code : "";
  }
  get _type() {
    return this.$props.type && this.$props.type == "player"
      ? this.$props.type
      : "dashboard";
  }

  get _shareUrl() {
    let localUrl = routerUtils.getLocalUrl(this);
    const page = this._type == "player" ? "shareStoryPlayer" : "shareDashboard";
    return `${localUrl}/report/${page}/${this._id}?type=CODE&code=${this._code}`;
  }

  async mounted() {
    this.initIframeSizeListener();
    await this.init();
  }

  initIframeSizeListener() {
    console.log("resizeIframe");
    if (this.bizData.timer) {
      return;
    }

    const iframe = this.$refs.mainiframe;
    if (!iframe) {
      return;
    }

    const that = this;
    const resizeIframe = () => {
      try {
        const doc = (<any>iframe).contentWindow.document;
        if (!doc) {
          return;
        }
        const cs = doc.getElementsByClassName("react-grid-layout");
        if (!cs || cs.length <= 0) {
          return;
        }
        const height = cs[0].clientHeight;
        if (that.bizData.ifameHeight == height) {
          return;
        }
        (<any>iframe).style.height = height + "px";
      } catch (e) {
        console.error("无法访问 iframe 内容", e);
      }
    };
    this.bizData.timer = setInterval(resizeIframe, 100);
  }

  async destroyed() {
    if (this.bizData.timer) {
      clearInterval(this.bizData.timer);
      this.bizData.timer = null;
    }
  }

  async init() {
    this.loading++;
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.oneLoadParam.initCount++;
    try {
      await this.initOneLoad();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }
}
</script>

<style lang="scss" scoped>
.main-window {
  ifame {
    width: 100%;
  }
}
</style>

