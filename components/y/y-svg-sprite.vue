<!--
/**
 * SVG 精灵图组件
 *
 * @description SVG 图标管理组件，负责加载和管理所有 SVG 图标
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 自动从 /static/img/icon/svg/ 目录加载 SVG 文件
 * - 生成双版本精灵图：原始版本和可定制版本
 * - 智能检测 SVG 原始属性（stroke、fill）
 * - 异步并行加载提高性能
 * - 备用图标机制，加载失败时的降级处理
 */
-->
<template>
    <div class="svg-sprite-container" style="display: none;">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <!-- 原始版本（保留颜色） -->
            <symbol :id="'icon-' + icon.name + '-original'" v-for="icon in icons" :key="icon.name + '-original'" :viewBox="icon.viewBox">
                <g v-html="icon.originalContent"></g>
            </symbol>
            <!-- 可定制版本（移除颜色） -->
            <symbol :id="'icon-' + icon.name" v-for="icon in icons" :key="icon.name" :viewBox="icon.viewBox" :data-has-stroke="icon.hasStroke" :data-has-fill="icon.hasFill">
                <g v-html="icon.customContent"></g>
            </symbol>
        </svg>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

interface SvgIcon {
    name: string;
    viewBox: string;
    originalContent: string; // 保留原始颜色的内容
    customContent: string; // 移除颜色的内容
    hasStroke: boolean; // 原始SVG是否有stroke
    hasFill: boolean; // 原始SVG是否有fill
}

// 为require.context添加类型定义
declare const require: {
    context: (
        directory: string,
        useSubdirectories: boolean,
        regExp: RegExp
    ) => {
        keys(): string[];
        (id: string): any;
    };
};

@Component({
    name: "YSvgSprite"
})
export default class YSvgSprite extends Vue {
    icons: SvgIcon[] = [];
    loadedIcons: Set<string> = new Set();

    async mounted() {
        await this.loadIcons();
    }

    async loadIcons() {
        // 使用Webpack的require.context动态加载SVG文件
        try {
            const svgContext = require.context("~/static/img/icon/svg/", false, /\.svg$/);
            const iconNames = svgContext.keys().map((key: string) => {
                return key.replace(/^\.\/(.*?)\.svg$/, "$1");
            });

            // 并行加载所有图标以提高性能
            await Promise.all(iconNames.map(name => this.loadSvgIcon(name)));

            console.log("所有SVG图标加载完成:", this.icons.map(icon => icon.name).join(", "));
        } catch (error) {
            console.error("加载SVG图标失败:", error);
            // 加载备用图标
            const fallbackIcons = ["star", "close", "document", "documentActive", "picture", "pictureActive", "tickets", "ticketsActive", "tag", "tagActive"];
            await Promise.all(fallbackIcons.map(name => this.loadSvgIcon(name)));
        }
    }

    async loadSvgIcon(name: string) {
        // 避免重复加载
        if (this.loadedIcons.has(name)) {
            return;
        }

        try {
            const response = await fetch(`/img/icon/svg/${name}.svg`);
            if (!response.ok) {
                throw new Error(`Failed to load SVG: ${name}, status: ${response.status}`);
            }

            const svgText = await response.text();

            // 解析SVG内容
            const parser = new DOMParser();
            const svgDoc = parser.parseFromString(svgText, "image/svg+xml");
            const svgElement = svgDoc.querySelector("svg");

            if (svgElement) {
                const viewBox = svgElement.getAttribute("viewBox") || "0 0 24 24";

                // 获取原始内容（保留颜色）
                let originalContent = "";
                Array.from(svgElement.childNodes).forEach(node => {
                    if (node.nodeType === 1) {
                        originalContent += (node as Element).outerHTML;
                    }
                });

                // 检测原始SVG是否有stroke和fill
                const hasStroke = this.hasStrokeAttribute(svgElement);
                const hasFill = this.hasFillAttribute(svgElement);

                // 获取自定义内容（移除颜色）
                let customContent = "";
                Array.from(svgElement.childNodes).forEach(node => {
                    if (node.nodeType === 1) {
                        const element = node as Element;
                        const clonedElement = element.cloneNode(true) as Element;
                        this.removeInlineColors(clonedElement);
                        customContent += clonedElement.outerHTML;
                    }
                });

                this.icons.push({
                    name,
                    viewBox,
                    originalContent,
                    customContent,
                    hasStroke,
                    hasFill
                });

                // 标记为已加载
                this.loadedIcons.add(name);

                // 触发更新
                this.$forceUpdate();
            }
        } catch (error) {
            console.error(`Failed to load icon: ${name}`, error);
        }
    }

    // 递归移除元素及其子元素的内联颜色属性
    removeInlineColors(element: Element) {
        // 移除颜色相关的属性
        const colorAttributes = ["stroke", "fill", "stroke-opacity", "fill-opacity"];
        colorAttributes.forEach(attr => {
            if (element.hasAttribute(attr)) {
                element.removeAttribute(attr);
            }
        });

        // 递归处理子元素
        Array.from(element.children).forEach(child => {
            this.removeInlineColors(child);
        });
    }

    // 检测SVG是否有stroke属性
    hasStrokeAttribute(element: Element): boolean {
        // 检查当前元素
        if (element.hasAttribute("stroke") && element.getAttribute("stroke") !== "none") {
            return true;
        }

        // 递归检查子元素
        return Array.from(element.children).some(child => this.hasStrokeAttribute(child));
    }

    // 检测SVG是否有fill属性
    hasFillAttribute(element: Element): boolean {
        // 检查当前元素
        if (element.hasAttribute("fill") && element.getAttribute("fill") !== "none") {
            return true;
        }

        // 递归检查子元素
        return Array.from(element.children).some(child => this.hasFillAttribute(child));
    }
}
</script>
