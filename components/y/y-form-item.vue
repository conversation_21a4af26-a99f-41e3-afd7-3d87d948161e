<!--
/**
 * 表单项组件
 *
 * @description 动态渲染表单项的组件，支持各种表单控件
 * <AUTHOR>
 * @created 2025-07-28
 * @version 1.0.0
 *
 * @features
 * - 动态组件渲染
 * - 数据双向绑定
 * - 事件传递
 * - 条件显示
 * - 插槽支持
 */
-->
<template>
  <el-col v-if="shouldShow" v-bind="colProps">
    <el-form-item
      :label="itemConfig.label"
      :prop="itemConfig.field"
      :required="itemConfig.required"
      v-bind="formItemProps"
    >
      <!-- 特殊组件处理 -->
      <TagSelector
        v-if="componentName === 'tag-selector'"
        :value="fieldValue"
        v-bind="componentProps"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
      />

      <!-- 插槽内容 -->
      <template v-else-if="itemConfig.slot">
        <slot
          :name="itemConfig.slot"
          :item="itemConfig"
          :value="fieldValue"
          :form-data="formData"
        ></slot>
      </template>

      <!-- 动态组件 -->
      <component
        v-else
        :is="componentName"
        :value="fieldValue"
        v-bind="componentProps"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        v-on="componentListeners"
      />

      <!-- 帮助文本 -->
      <div v-if="itemConfig.help" class="form-item-help">
        {{ itemConfig.help }}
      </div>
    </el-form-item>
  </el-col>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";
import { FormItemConfig } from "~/components/y/types/form";
import { FormUtils } from "~/components/y/utils/form-utils";
import TagSelector from "~/components/ybiz/tag-selector.vue";

@Component({
  name: "YFormItem",
  components: {
    TagSelector
  }
})
export default class YFormItem extends Vue {
  @Prop({ type: Object, required: true }) readonly itemConfig!: FormItemConfig;
  @Prop({ type: Object, required: true }) readonly formData!: Record<string, any>;

  // 计算属性
  get shouldShow(): boolean {
    return FormUtils.shouldShowItem(this.itemConfig, this.formData);
  }

  get fieldValue(): any {
    return FormUtils.getFieldValue(this.formData, this.itemConfig.field);
  }

  get colProps(): any {
    return this.itemConfig.colProps || { span: 24 };
  }

  get componentName(): string {
    // 直接返回组件名称，Nuxt会自动处理组件注册
    return this.itemConfig.component;
  }

  get componentProps(): any {
    const props = { ...this.itemConfig.componentProps };

    // 添加通用属性
    if (this.itemConfig.disabled !== undefined) {
      props.disabled = this.itemConfig.disabled;
    }
    if (this.itemConfig.readonly !== undefined) {
      props.readonly = this.itemConfig.readonly;
    }

    return props;
  }

  get formItemProps(): any {
    return this.itemConfig.formItemProps || {};
  }

  get componentListeners(): any {
    // 过滤掉已经处理的事件
    const listeners = { ...this.$listeners };
    delete listeners.input;
    delete listeners.change;
    delete listeners.focus;
    delete listeners.blur;
    return listeners;
  }

  // 事件处理
  @Emit('input')
  handleInput(value: any): { field: string; value: any } {
    FormUtils.setFieldValue(this.formData, this.itemConfig.field, value);
    // 手动触发表单验证
    this.$nextTick(() => {
      this.triggerValidation();
    });
    return { field: this.itemConfig.field, value };
  }

  @Emit('change')
  handleChange(value: any): { field: string; value: any } {
    // 手动触发表单验证
    this.$nextTick(() => {
      this.triggerValidation();
    });
    return { field: this.itemConfig.field, value };
  }

  // 手动触发验证
  private triggerValidation(): void {
    // 查找父级表单组件
    let parent = this.$parent;
    while (parent && parent.$options.name !== 'YForm') {
      parent = parent.$parent;
    }

    if (parent) {
      // 调用表单的validateField方法
      (parent as any).validateField(this.itemConfig.field);
    }
  }

  @Emit('focus')
  handleFocus(event: Event): { field: string; event: Event } {
    return { field: this.itemConfig.field, event };
  }

  @Emit('blur')
  handleBlur(event: Event): { field: string; event: Event } {
    return { field: this.itemConfig.field, event };
  }
}
</script>

<style lang="scss" scoped>
.form-item-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
