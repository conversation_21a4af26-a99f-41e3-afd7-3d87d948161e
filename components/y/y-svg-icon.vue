<!--
/**
 * SVG 图标组件
 *
 * @description 用于显示 SVG 图标的基础组件，支持自定义描边颜色、填充颜色、大小
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 通过图标名称引用 SVG 精灵图中的图标
 * - 智能处理 SVG 原始颜色和自定义颜色
 * - 支持描边和填充颜色的独立控制
 * - 自动检测 SVG 原始属性并智能切换版本
 */
-->
<template>
    <div class="y-svg-icon" :class="{ 'force-color': forceColor, 'has-color': !!color, 'has-fill-color': !!fillColor, 'original-has-stroke': originalHasStroke, 'original-has-fill': originalHasFill }" :style="style">
        <svg class="svg-icon" aria-hidden="true" :key="name">
            <use :xlink:href="iconName"></use>
        </svg>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";

@Component({
    name: "YSvgIcon"
})
export default class YSvgIcon extends Vue {
    // 图标名称
    @Prop({ type: String, required: true }) readonly name!: string;

    // 图标颜色（描边颜色）
    @Prop({ type: String, default: "" }) readonly color!: string;

    // 填充颜色（可选，默认透明）
    @Prop({ type: String, default: "transparent" }) readonly fillColor!: string;

    // 是否强制覆盖SVG原始颜色（默认true）
    @Prop({ type: Boolean, default: true }) readonly forceColor!: boolean;

    // 图标大小
    @Prop({ type: [Number, String], default: 18 }) readonly size!: number | string;

    // 原始SVG是否有stroke
    originalHasStroke = false;

    // 原始SVG是否有fill
    originalHasFill = false;

    // 图标名称
    get iconName(): string {
        // 如果没有颜色值，使用原始版本（保留SVG原色）
        if (!this.color) {
            return `#icon-${this.name}-original`;
        }
        // 如果有颜色值，使用可定制版本（移除了内联颜色）
        return `#icon-${this.name}`;
    }

    // 样式对象
    get style() {
        return {
            color: this.color || "currentColor", // 描边颜色
            "--fill-color": this.fillColor, // 填充颜色通过CSS变量传递
            width: typeof this.size === "number" ? `${this.size}px` : this.size,
            height: typeof this.size === "number" ? `${this.size}px` : this.size
        };
    }

    // 监听名称变化，强制更新
    @Watch("name")
    onNameChange() {
        this.$forceUpdate();
        this.checkOriginalAttributes();
    }

    mounted() {
        this.checkOriginalAttributes();
    }

    // 检查原始SVG的属性
    checkOriginalAttributes() {
        this.$nextTick(() => {
            // 查找对应的symbol元素
            const symbolElement = document.querySelector(`#icon-${this.name}`) as SVGSymbolElement;
            if (symbolElement) {
                this.originalHasStroke = symbolElement.getAttribute("data-has-stroke") === "true";
                this.originalHasFill = symbolElement.getAttribute("data-has-fill") === "true";
            }
        });
    }
}
</script>

<style lang="scss" scoped>
.y-svg-icon {
    display: inline-block;

    .svg-icon {
        width: 100%;
        height: 100%;
        vertical-align: -0.15em;
        overflow: hidden;
    }

    &.has-fill-color .svg-icon {
        fill: var(--fill-color) !important;
    }

    &.has-color .svg-icon {
        stroke: currentColor !important;
        stroke-width: 1.5;
    }
}
</style>
