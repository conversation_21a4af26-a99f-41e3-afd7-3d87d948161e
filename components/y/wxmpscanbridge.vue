<template lang="pug">
.scan-win
  .scan-code(:style="{ backgroundImage: 'url(' + imgurl + ')' }")
</template>

<script lang="ts">
import {
  Component,
  mixins,
  Prop,
  Vue,
  VuexModule,
  Watch,
} from "nuxt-property-decorator";
import idUtils from "~/utils/idUtils";
import { config } from "~/config/global.config";

import authBridgeApi, { AuthBridgeStateBo } from "~/api/auth/auth/bridgeApi";
import { BaseVue } from "~/model/vue";

const VALUE_STATE_BEGIN = "BEGIN";
const VALUE_STATE_PENDING = "PENDING";
const VALUE_STATE_END = "END";

const VALUE_TYPE_WX_MP_INIT = "WX_MP_INIT";
const VALUE_TYPE_WX_MP_LOGIN = "WX_MP_LOGIN";
const VALUE_TYPE_WX_MP_BOND = "WX_MP_BOND";

@Component({
  name: "y-wxmpscanbridge",
  props: ["sysAppId", "type"],
})
export default class YWxMpScanBridge extends mixins(BaseVue) {
  wxmpUuid = "";
  imgurl = "";
  state = VALUE_STATE_END;
  running = false;

  get _sysApiId(): string {
    return this.$props.sysAppId ? this.$props.sysAppId : null;
  }

  get _type(): string {
    return this.$props.type ? this.$props.type : VALUE_TYPE_WX_MP_INIT;
  }

  async mounted() {
    this.refreshImg()
      .then(() => {})
      .catch(() => {});
  }

  async beforeDestroy() {
    this.running = false;
  }

  async refreshImg() {
    if (
      this._type != VALUE_TYPE_WX_MP_LOGIN &&
      this._type != VALUE_TYPE_WX_MP_BOND
    ) {
      this.running = false;
      return;
    }
    this.wxmpUuid = idUtils.uuid();
    this.running = true;
    this.state = VALUE_STATE_BEGIN;

    if (this._type == VALUE_TYPE_WX_MP_LOGIN) {
      this.imgurl = `${config.baseUrl.apiUrl}/auth/wx/mp/auth-bridge/accept-token?sys_app_id=${this._sysApiId}&uuid=${this.wxmpUuid}`;
    } else if (this._type == VALUE_TYPE_WX_MP_BOND) {
      let token = await this.$store.dispatch("auth/getToken");
      this.imgurl = `${config.baseUrl.apiUrl}/auth/wx/mp/auth-bridge/send-token?sys_app_id=${this._sysApiId}&uuid=${this.wxmpUuid}&access_token=${token}`;
    }

    await this.refreshStateTimeout();
  }

  async refreshStateTimeout() {
    let that = this;
    setTimeout(async () => {
      if (!that.running) {
        return;
      }
      if (
        that._type == VALUE_TYPE_WX_MP_LOGIN ||
        that._type == VALUE_TYPE_WX_MP_BOND
      ) {
        await that.refreshState();
        await that.refreshStateTimeout();
      } else {
        that.running = false;
      }
    }, 1000);
  }

  async refreshState() {
    try {
      let res = await authBridgeApi.getStateById({
        uuid: this.wxmpUuid,
      });
      if (
        (res.state === VALUE_STATE_PENDING || res.state === VALUE_STATE_END) &&
        VALUE_STATE_BEGIN === this.state
      ) {
        this.state = VALUE_STATE_PENDING;
        await this._scanOk(res);
      }
      if (VALUE_STATE_END === res.state && VALUE_STATE_PENDING === this.state) {
        this.state = VALUE_STATE_END;
        await this._loginSuccess(res);
        this.running = false;
        // 更新完成
        return;
      }
    } catch (error) {
      console.log(error);
    }
  }

  async _scanOk(res: AuthBridgeStateBo) {
    this.$emit("scan", res);
  }

  async _loginSuccess(res: AuthBridgeStateBo) {
    if (this._type == VALUE_TYPE_WX_MP_LOGIN) {
      let token = await authBridgeApi.getTokenById({
        uuid: this.wxmpUuid,
      });
      this.$emit("success", token);
    } else if (this._type == VALUE_TYPE_WX_MP_BOND) {
      this.$emit("success", res);
    }
  }

  @Watch("$props.type")
  stateChange(type: string, oldType: string) {
    this.refreshImg()
      .then(() => {})
      .catch(() => {});
  }
}
</script>

<style lang="scss" scoped>
// @import url("./yupload.scss");
.scan-win {
  width: 100%;
  height: 100%;

  .scan-code {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
  }
}
</style>
