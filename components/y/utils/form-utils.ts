/**
 * 表单工具函数
 */

import { FormItemConfig, COMPONENT_TYPES, DEFAULT_CONFIG } from '~/components/y/types/form';

export class FormUtils {
  /**
   * 生成默认的placeholder
   */
  static generatePlaceholder(component: string, label: string): string {
    const { INPUT_COMPONENTS, SELECT_COMPONENTS } = COMPONENT_TYPES;

    if (INPUT_COMPONENTS.includes(component as any)) {
      return `请输入${label}`;
    } else if (SELECT_COMPONENTS.includes(component as any)) {
      return `请选择${label}`;
    }

    return `请输入${label}`;
  }

  /**
   * 生成验证消息
   */
  static generateValidationMessage(component: string, label: string): string {
    const { INPUT_COMPONENTS, SELECT_COMPONENTS } = COMPONENT_TYPES;

    if (INPUT_COMPONENTS.includes(component as any)) {
      return `请输入${label}`;
    } else if (SELECT_COMPONENTS.includes(component as any)) {
      return `请选择${label}`;
    }

    return `请输入${label}`;
  }

  /**
   * 处理表单项配置，添加默认值
   */
  static processFormItem(item: FormItemConfig): FormItemConfig {
    const processedItem = { ...item };

    // 设置默认colProps
    if (!processedItem.colProps) {
      processedItem.colProps = { ...DEFAULT_CONFIG.colProps };
    } else {
      processedItem.colProps = {
        ...DEFAULT_CONFIG.colProps,
        ...processedItem.colProps
      };
    }

    // 设置默认componentProps
    if (!processedItem.componentProps) {
      processedItem.componentProps = {};
    }

    // 自动生成placeholder
    if (!processedItem.componentProps.placeholder) {
      processedItem.componentProps.placeholder = this.generatePlaceholder(
        processedItem.component,
        processedItem.label
      );
    }

    // 设置默认值
    if (processedItem.required === undefined) {
      processedItem.required = false;
    }
    if (processedItem.disabled === undefined) {
      processedItem.disabled = false;
    }
    if (processedItem.readonly === undefined) {
      processedItem.readonly = false;
    }
    if (processedItem.show === undefined) {
      processedItem.show = true;
    }

    return processedItem;
  }

  /**
   * 生成表单验证规则
   */
  static generateRules(formItems: FormItemConfig[]): Record<string, any[]> {
    const rules: Record<string, any[]> = {};

    formItems.forEach(item => {
      const fieldRules: any[] = [];

      // 必填规则
      if (item.required) {
        const message = this.generateValidationMessage(item.component, item.label);

        // 检查是否是数组类型的组件（如tag-selector）
        const isArrayComponent = ['tag-selector', 'y-multi-select-dropdown'].includes(item.component);

        if (isArrayComponent) {
          // 数组类型使用自定义验证器，参考原来的createArrayValidator
          fieldRules.push({
            required: true,
            message,
            validator: (rule: any, value: string[], callback: Function) => {
              if (value?.length > 0) {
                callback();
              } else {
                callback(new Error(message));
              }
            },
            trigger: 'blur'
          });
          fieldRules.push({
            required: true,
            message,
            validator: (rule: any, value: string[], callback: Function) => {
              if (value?.length > 0) {
                callback();
              } else {
                callback(new Error(message));
              }
            },
            trigger: 'change'
          });
        } else {
          // 普通类型使用required验证
          fieldRules.push({
            required: true,
            message,
            trigger: 'blur'
          });
          fieldRules.push({
            required: true,
            message,
            trigger: 'change'
          });
        }
      }

      // 自定义规则
      if (item.rules && item.rules.length > 0) {
        fieldRules.push(...item.rules);
      }

      if (fieldRules.length > 0) {
        rules[item.field] = fieldRules;
      }
    });

    return rules;
  }

  /**
   * 获取验证触发方式
   */
  static getTrigger(component: string): string {
    const { INPUT_COMPONENTS } = COMPONENT_TYPES;

    if (INPUT_COMPONENTS.includes(component as any)) {
      return 'blur';
    }

    return 'change';
  }

  /**
   * 检查表单项是否应该显示
   */
  static shouldShowItem(item: FormItemConfig, formData: Record<string, any>): boolean {
    if (typeof item.show === 'boolean') {
      return item.show;
    } else if (typeof item.show === 'function') {
      return item.show(formData);
    }
    return true;
  }

  /**
   * 获取字段值
   */
  static getFieldValue(formData: Record<string, any>, field: string): any {
    return formData[field];
  }

  /**
   * 设置字段值
   */
  static setFieldValue(formData: Record<string, any>, field: string, value: any): void {
    formData[field] = value;
  }

  /**
   * 深度合并对象，特殊处理验证规则数组
   */
  static deepMerge(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
      if (Array.isArray(source[key])) {
        // 对于数组（验证规则），进行合并而不是覆盖
        result[key] = [...(result[key] || []), ...source[key]];
      } else if (source[key] && typeof source[key] === 'object') {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  /**
   * 验证表单数据
   */
  static validateFormData(formItems: FormItemConfig[], formData: Record<string, any>): {
    isValid: boolean;
    errors: Record<string, string>;
  } {
    const errors: Record<string, string> = {};

    formItems.forEach(item => {
      if (!this.shouldShowItem(item, formData)) {
        return;
      }

      const value = this.getFieldValue(formData, item.field);

      // 必填验证
      if (item.required) {
        if (value === undefined || value === null || value === '' ||
            (Array.isArray(value) && value.length === 0)) {
          errors[item.field] = `${item.label}不能为空`;
        }
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}
