<template lang="pug">
.main-win
  //- function，复用二维码窗口
  mixin qrcodeWindow
    template(v-if="_showQrcode")
      el-popover.popover(
        style="width: 100%; height: 100%",
        popper-class="comp-upload-qrcode",
        trigger="hover",
        placement="right",
        @show="handlerMobileUploadPopoverShow",
        @after-leave="handlerMobileUploadPopoverHide"
      )
        .qrcode-win
          img.qrcode-img(
            v-if="bizData.mobileUploadImgUrl",
            :src="bizData.mobileUploadImgUrl"
          )
          .qrcode-text(v-if="!bizData.mobileUploadImgUrl")
            div(v-if="!bizData.mobileUploadUrl")
              p 加载中，请稍后...
            div(v-else)
              p 请在手机上进行数据上传
              p.red
                a(
                  href="javascript:void(0);",
                  @click="handlerModileUpSyncRevokeClick"
                ) 取消授权

          p.qrcode-down-text(v-if="bizData.mobileUploadImgUrl") 使用微信扫码可手机上传
        template(slot="reference")
          block
    template(v-else)
      block

  .list(
    ref="uploadDiv",
    :class="{ 'list-pic': _type == 'pic', 'list-other': _type != 'pic' }"
  )
    //- 上传按钮
    template(
      v-if="!_disabled && _limit > bizData.fileList.length + bizData.uploadingList.length"
    )
      template(v-if="_type != 'pic'")
        .item.upbtn
          +qrcodeWindow
            el-button(type="primary", @click="handleUploadInputClick") 上传

    //- 上传完成预览文件
    template(v-if="_type == 'pic'")
      .item.show.sorthandle(
        v-for="(file, idx) in bizData.fileList",
        :key="file.uid + '_' + idx"
      )
        .show-item-pic
          img(
            :src="getUrl(file, 3)",
            :alt="file.name",
            v-if="calImgHead(file)"
          )
          video(
            :id="file.id",
            :src="getUrl(file)",
            v-else-if="file.mimeType && file.mimeType.startsWith('video')"
          )

          .item-other(v-else)
            i.el-icon-document(v-if="!file.name")
            .title(v-if="file.name") {{ file.name }}

          span.mask
            span.mask-btn(
              v-if="calPreview(file)",
              @click="handlePictureCardPreview(file)"
            )
              i.el-icon-zoom-in
            span.mask-btn(@click="handleDownload(file)")
              i.el-icon-download
            span.mask-btn(v-if="!_disabled", @click="handleRemove(file)")
              i.el-icon-delete
    template(v-else)
      .item.show.sorthandle(
        v-for="(file, idx) in bizData.fileList",
        :key="file.uid + '_' + idx"
      )
        .title(@click="handleDownload(file)")
          i.el-icon-document
          | {{ file.name }}
        .right
          span.mask-btn(v-if="!_disabled", @click="handleRemove(file)")
            i.el-icon-delete

    //- 上传中
    template(v-if="_type == 'pic'")
      .item.uploading(
        v-for="(file, idx) in bizData.uploadingList",
        :key="file.uid + '_' + idx"
      )
        img(
          v-if="file.mimeType && file.mimeType.startsWith('image') && file.data",
          :src="file.data",
          :alt="file.name"
        )
        .progress
          .elp
            el-progress(
              :width="78",
              type="circle",
              text-color="#fff",
              :percentage="file.progress ? Math.floor((100 * file.progress.loaded) / file.progress.total) : 50"
            )
    template(v-else)
      .item.uploading(
        v-for="(file, idx) in bizData.uploadingList",
        :key="file.uid + '_' + idx"
      )
        .progress
          .elp
            el-progress(
              :stroke-width="20",
              :text-inside="true",
              text-color="#fff",
              :format="(p) => `${file.name} ${p}%`",
              :percentage="file.progress ? Math.floor((100 * file.progress.loaded) / file.progress.total) : 50"
            )

    //- 上传按钮
    template(
      v-if="!_disabled && _limit > bizData.fileList.length + bizData.uploadingList.length"
    )
      template(v-if="_type == 'pic'")
        .item.upbtn(@click="handleUploadInputClick")
          +qrcodeWindow
            .up-win
              i.el-icon-plus.icon-add

  .aux
    input(
      ref="fileinput",
      type="file",
      @change="handleFileInputChange",
      value="选择文件",
      :accept="_accept",
      :multiple="_multiple"
    )

  .preview(v-if="bizData.previewType == 'img'")
    .img-preview
      el-image(
        ref="previewImgsShow",
        :src="bizData.previewUrl",
        :preview-src-list="_imgurls"
      )
  el-dialog(:visible.sync="bizData.dialogTableVisible")
    pdfimage(v-if="bizData.previewType == 'pdf'", :url="bizData.previewUrl")
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { ResDoc } from "~/api/sys/fsApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import fileuploadUtils from "~/utils/fileuploadUtils";
import idUtils from "~/utils/idUtils";
import urlUtils from "~/utils/urlUtils";
import sortableUtils from "~/utils/sortableUtils";
import compUtils from "~/utils/compUtils";
import sysFsApi, {
  SysFsInfoQueryParam,
  FileInfoParamVo,
} from "~/api/sys/fsApi";
import sysFsUpSyncApi from "~/api/sys/fs/upSyncApi";

import pdfimage from "~/components/ym/pdfimage.vue";
import routerUtils from "~/utils/routerUtils";

type FileItem = ResDoc & {
  uid?: string;
};

export type FileUploadingItem = {
  uid?: string;
  name?: string;
  file?: File;
  data?: string;
  mimeType?: string;
  progress?: { total: number; loaded: number; isTrusted: boolean };
};

export type FileUploadingEvent = {
  status: "START" | "FINISH" | "ERROR";
  uploadingItem: FileUploadingItem;
  uploadingList: FileUploadingItem[];
};

// 事件
// uploading：对应FileUploadingEvent对象

@Component({
  name: "y-upload",
  props: [
    "value",
    "limit",
    "type",
    "accept",
    "multiple",
    "secrecy",
    "watermark",
    "exp",
    "disabled",
    "showQrcode",
    "uid",
    "ticket",
  ],
  components: {
    pdfimage,
  },
})
export default class YUpload extends mixins(BaseVue) {
  loading = 0;
  query = {};
  formData = {
    file: null,
  };
  oneLoadParam = {
    inited: false,
    initCount: 0,
    // ...param
  };
  bizData = {
    // 用户token
    token: <string | null>"",
    _value: "",
    // 文件列表
    fileList: <FileItem[]>[],
    // 上传中
    uploadingList: <FileUploadingItem[]>[],

    previewType: "img",
    previewUrl: <string | null>"",

    dialogTableVisible: false,

    mobileUploadUid: "",
    mobileUploadImgUrl: <string | null>"",
    mobileUploadUrl: <string | null>"",
    mobileUploadTimer: <any>null,
  };

  get _showQrcode() {
    return this._ticket || this._disabled
      ? false
      : commonFunUtils.parseBoolean(this.$props.showQrcode, false);
  }

  get _accept() {
    return this.$props.accept
      ? this.$props.accept
      : this._type == "pic"
      ? "image/*"
      : "";
  }

  calImgHead(file: FileItem) {
    let mimeType = file.mimeType;
    if (!mimeType) {
      return false;
    }
    if (mimeType!.startsWith("image")) {
      return true;
    }
    if (mimeType! == "application/pdf") {
      return true;
    }
    if (mimeType! == "application/msword") {
      return true;
    }
    if (mimeType! == "application/vnd.ms-excel") {
      return true;
    }
    if (
      mimeType! ==
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      return true;
    }
    if (
      mimeType! ==
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      return true;
    }
    if (mimeType! == "application/vnd.ms-powerpoint") {
      return true;
    }
    if (
      mimeType! ==
      "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    ) {
      return true;
    }
    return false;
  }
  calPreview(file: FileItem) {
    return this.calImgHead(file);
  }

  get _exp() {
    return this.$props.exp ? this.$props.exp : null;
  }

  get _disabled() {
    return commonFunUtils.parseBoolean(this.$props.disabled);
  }

  get _limit() {
    return this.$props.limit ? +this.$props.limit : 120;
  }

  get _type() {
    return this.$props.type ? this.$props.type : "file";
  }

  get _multiple(): boolean {
    return this.$props.multiple
      ? commonFunUtils.parseBoolean(this.$props.multiple)
      : false;
  }

  get _secrecy(): boolean {
    return this.$props.secrecy
      ? commonFunUtils.parseBoolean(this.$props.secrecy)
      : false;
  }

  get _watermark(): boolean {
    return this.$props.watermark
      ? commonFunUtils.parseBoolean(this.$props.watermark)
      : false;
  }

  get _imgurls() {
    return this.bizData.fileList
      .filter((x: FileItem) => x.mimeType && x.mimeType.startsWith("image"))
      .map((x: FileItem) => this.getUrl(x));
  }

  get _uuid() {
    return this.$props.uid ? this.$props.uid : fileuploadUtils.getUuid();
  }

  get _ticket() {
    return this.$props.ticket ? this.$props.ticket : null;
  }

  async mounted() {
    await this.init();

    this.boundSortable();
  }

  async destroyed() {
    await this.revokeUpSync();
  }

  boundSortable() {
    if (this._disabled) {
      return;
    }
    let that = this;
    let el: any = this.$refs.uploadDiv;
    let ul = el;
    sortableUtils.sortable(ul, this, "bizData.fileList", {
      handle: ".sorthandle",
      offset: that._type == "pic" ? 0 : 1,
      onMove: sortableUtils.buildOnMoveStop("sorthandle"),
      end: () => {
        that.putValueOut();
      },
    });
  }

  async init() {
    this.loading++;
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.oneLoadParam.initCount++;
    try {
      await this.initOneLoad();
      await this.valueEven();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  getUrl(fs: any, s: number | null = null, type: string | null = null) {
    let id = "";
    if (typeof fs == "object") {
      id = `${fs.id}${fs.suffix ? "." + fs.suffix : ""}`;
    } else {
      id = fs;
    }
    if (!id) {
      return "";
    }
    if (id.startsWith("http:") || id.startsWith("https:")) {
      return id;
    }
    if (this._secrecy) {
      return urlUtils.file2url(id, {
        s: s,
        uid: this._uuid,
        token: this.bizData.token,
      });
    } else {
      let t = type;
      if (t == null && s != null) {
        t = "head";
      }
      return urlUtils.file2url(id, { s, t });
    }
  }

  handleUploadInputClick() {
    let ff = <any>this.$refs.fileinput;
    ff.value = "";
    ff.click();
  }

  async handleFileInputChange() {
    let rfile: any = this.$refs.fileinput;
    if (!rfile) {
      return;
    }
    let fs = rfile.files;
    let that = this;

    if (fs.length <= 0) {
      return;
    }

    if (
      fs.length +
        this.bizData.fileList.length +
        this.bizData.uploadingList.length >
      this._limit
    ) {
      this.$message.error(
        `超过上传数量限制。当前限制最大文件数：${this._limit}`
      );
      return;
    }
    for (let f of fs) {
      let uploading: FileUploadingItem = {};
      uploading.uid = idUtils.uuid();
      uploading.name = f.name;
      uploading.file = f;
      uploading.mimeType = f.type;

      if (uploading.mimeType && this._type == "pic") {
        if (uploading.mimeType.startsWith("image")) {
          let data: any = await fileuploadUtils.readFile(f);
          uploading.data = data;
        }
      }

      const onProgress = (e: {
        total: number;
        loaded: number;
        isTrusted: boolean;
      }) => {
        // 强制刷新
        that.$set(uploading, "progress", e);
      };

      that.bizData.uploadingList.push(uploading);
      {
        // 事件触发
        const event: FileUploadingEvent = {
          status: "START",
          uploadingItem: uploading,
          uploadingList: this.bizData.uploadingList,
        };
        this.$emit("uploading", event);
      }
      fileuploadUtils
        .upload({
          file: <File>f,
          secrecy: this._secrecy,
          exp: this._exp,
          uuid: this._uuid,
          ticket: this._ticket,
          watermark: this._watermark,
          onProgress,
        })
        .then((data: FileItem) => {
          // 移除上传标识
          let idx = that.bizData.uploadingList.findIndex(
            (x) => x.uid == uploading.uid
          );
          if (idx >= 0) {
            that.bizData.uploadingList.splice(idx, 1);
          }
          // 后续逻辑
          data.uid = data.uid ? data.uid : idUtils.uuid();
          that.bizData.fileList.push(data);

          {
            // 事件触发
            const event: FileUploadingEvent = {
              status: "FINISH",
              uploadingItem: uploading,
              uploadingList: this.bizData.uploadingList,
            };
            this.$emit("uploading", event);
          }
          // 触发同步
          that.putValueOut();
        })
        .catch((e) => {
          // 移除上传标识
          let idx = that.bizData.uploadingList.findIndex(
            (x) => x.uid == uploading.uid
          );
          if (idx >= 0) {
            that.bizData.uploadingList.splice(idx, 1);
          }
          {
            // 事件触发
            const event: FileUploadingEvent = {
              status: "ERROR",
              uploadingItem: uploading,
              uploadingList: this.bizData.uploadingList,
            };
            this.$emit("uploading", event);
          }
          // 后续逻辑
          that.$message({
            message: "上传文件出现了错误，请稍后重试。",
            type: "error",
          });
        });
    }
  }

  handlePictureCardPreview(file: FileItem) {
    if (file.mimeType) {
      if (file.mimeType.startsWith("image")) {
        this.bizData.previewType = "img";
        this.bizData.previewUrl = this.getUrl(file);
        let imgsShow: any = this.$refs.previewImgsShow;
        imgsShow.showViewer = true;
      } else if (file.mimeType.startsWith("video")) {
        this.bizData.previewType = "video";
        this.bizData.previewUrl = this.getUrl(file);
        var videos = document.getElementById(file.id!);
        if (videos) {
          if (videos.requestFullscreen) {
            videos.requestFullscreen();
          }
        }
      } else if (file.mimeType.indexOf("pdf") >= 0) {
        this.bizData.previewType = "pdf";
        this.bizData.previewUrl = this.getUrl(file);
        this.bizData.dialogTableVisible = true;
      } else if (
        file.mimeType == "application/msword" ||
        file.mimeType == "application/vnd.ms-excel" ||
        file.mimeType ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.mimeType ==
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
        file.mimeType == "application/vnd.ms-powerpoint" ||
        file.mimeType ==
          "application/vnd.openxmlformats-officedocument.presentationml.presentation"
      ) {
        this.bizData.previewType = "pdf";
        this.bizData.previewUrl = this.getUrl(file, null, "pdf");
        this.bizData.dialogTableVisible = true;
      }
    }
  }

  handleDownload(file: any) {
    window.open(this.getUrl(file)!);
  }

  handleRemove(file: any) {
    for (let idx in this.bizData.fileList) {
      if (this.bizData.fileList[idx].id == file.id) {
        this.bizData.fileList.splice(+idx, 1);
        break;
      }
    }
    this.putValueOut();
  }

  putValueOut() {
    let values = new Array();
    for (let k in this.bizData.fileList) {
      let x = this.bizData.fileList[k];
      values.push(`${x.id}${x.suffix ? "." + x.suffix : ""}`);
    }
    let val = values.join(",");
    val = val ? val : "";
    if (this.bizData._value == val) {
      return;
    }
    this.bizData._value = val;
    compUtils.formEmitInput(this, val);
  }

  async upSyncInit() {
    if (this.bizData.mobileUploadTimer) {
      return;
    }
    const that = this;

    let param: any = {};
    param.limit = this._limit;
    param.type = this._type;
    param.accept = this._accept;
    param.multiple = this._multiple;
    param.secrecy = this._secrecy;
    param.exp = this._exp;
    param.uid = this._uuid;
    param.watermark = this._watermark;

    const pjson = JSON.stringify(param);

    this.bizData.mobileUploadTimer = setInterval(async () => {
      // 循环处理
      if (!that.bizData.mobileUploadUid) {
        return;
      }
      const result = await sysFsUpSyncApi.renew(
        that.bizData.mobileUploadUid,
        {}
      );
      if (!result || !result.valid) {
        return;
      }

      if (result.scaned) {
        that.bizData.mobileUploadImgUrl = null;
      }

      if (!result.value) {
        return;
      }

      let val = that.bizData._value;
      let vals = val ? val.split(",") : [];
      let change = false;
      for (let vv of result.value) {
        if (!vv) {
          continue;
        }
        let vvs = vv.split(",");
        for (let v of vvs) {
          if (!v) {
            continue;
          }
          if (vals.indexOf(v) < 0) {
            if (that._limit <= vals.length) {
              this.$message.error(
                `超过上传数量限制。当前限制最大文件数：${this._limit}`
              );
            } else {
              vals.push(v);
              change = true;
            }
          }
        }
      }
      if (change) {
        compUtils.formEmitInput(this, vals.join(","));
      }
    }, 1000);

    const result = await sysFsUpSyncApi.buildTicket({ baseMsg: pjson });
    this.bizData.mobileUploadUid = result.id!;

    let wurl = routerUtils.getLocalUrl(this);
    let url = `${wurl}/def/common/auxs/upload?id=${this.bizData.mobileUploadUid}`;
    this.bizData.mobileUploadUrl = url;
    this.bizData.mobileUploadImgUrl = urlUtils.buildQrcodeUrl(url);
  }

  async handlerMobileUploadPopoverShow() {
    await this.upSyncInit();
  }

  async handlerMobileUploadPopoverHide() {
    if (!this.bizData.mobileUploadTimer) {
      return;
    }
    if (!this.bizData.mobileUploadImgUrl) {
      // 已扫码
      return;
    }
    // 存在且未扫码
    await this.revokeUpSync();
  }

  async handlerModileUpSyncRevokeClick() {
    await this.revokeUpSync();
    await this.upSyncInit();
  }

  async revokeUpSync() {
    if (this.bizData.mobileUploadTimer) {
      clearInterval(this.bizData.mobileUploadTimer);
      this.bizData.mobileUploadTimer = null;
    }
    if (this.bizData.mobileUploadUid) {
      sysFsUpSyncApi.revoke(this.bizData.mobileUploadUid);
      this.bizData.mobileUploadUid = "";
    }
    this.bizData.mobileUploadImgUrl = null;
    this.bizData.mobileUploadUrl = null;
  }

  @Watch("$props.value")
  async valueEven() {
    let value = this.$props.value;
    if (this.bizData._value == value) {
      // 防止重复推送
      return;
    }
    if (!value) {
      this.bizData.fileList.splice(0, this.bizData.fileList.length);
      this.bizData._value = "";
      this.putValueOut();
    } else {
      const getId = (x: string) => {
        let idx = x.lastIndexOf(".");
        if (idx < 0) {
          return x;
        } else {
          return x.substring(0, idx);
        }
      };
      const getSuffix = (x: string) => {
        let idx = x.lastIndexOf(".");
        if (idx < 0) {
          return null;
        } else {
          let s = x.substring(idx + 1, x.length);
          return s ? s : null;
        }
      };
      let valIds: Array<string> = value.split(",");
      valIds = valIds.filter((x) => !!x);
      let vals = valIds.map((x) => getId(x)).filter((x) => !!x);
      // 构建现有数据信息映射表
      let id2file = new Map();
      this.bizData.fileList.forEach((x) => id2file.set(x.id, x));

      // 整理出需要请求服务器的对象
      let rids = vals.filter((id) => !id2file.get(id));
      if (rids.length > 0) {
        this.bizData.token = await this.$auth.getToken();

        let rqParam: SysFsInfoQueryParam = {};
        if (this._secrecy) {
          rqParam.uuid = this._uuid;
        }
        let body: FileInfoParamVo = {};
        body.ids = rids;
        let resDocs: Array<any> = await sysFsApi.infos(rqParam, body);
        for (let fs of resDocs) {
          fs.url = this.getUrl(fs);
          id2file.set(fs.id, fs);
        }
      }

      // 结果组装
      this.bizData.fileList.splice(0, this.bizData.fileList.length); // 清理数据
      for (let valId of valIds) {
        let id = getId(valId);
        let fs = id2file.get(id);
        if (!fs) {
          fs = {};
          fs.id = id;
          fs.suffix = getSuffix(valId);
          fs.name = `${fs.id}${fs.suffix ? "." + fs.suffix : ""}`;
          fs.mimeType = "";
        }
        fs.uid = fs.uid ? fs.uid : idUtils.uuid();
        this.bizData.fileList.push(fs);
      }
      this.putValueOut();
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./upload.scss";
</style>

