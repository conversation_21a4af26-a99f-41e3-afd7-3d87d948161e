<!--
/**
 * 复选框组件
 *
 * @description 复选框组件，支持选中/未选中状态切换，使用图片资源显示选中状态
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 点击切换选中状态
 * - 选中时显示自定义图标
 * - 未选中时显示边框样式
 * - 适配深色主题设计
 */
-->
<template>
  <div class="y-checkbox" @click="toggleChecked">
    <div class="y-checkbox__input">
      <img v-if="modelValue" src="/img/login/checkbox.png" class="y-checkbox__img" alt="checked" />
      <div v-else class="y-checkbox__box"></div>
    </div>
    <div class="y-checkbox__label-wrapper">
      <div class="y-checkbox__label">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit, Model } from "vue-property-decorator";

@Component({
  name: "YCheckbox"
})
export default class YCheckbox extends Vue {
  @Model('input', { type: Boolean, default: false }) readonly modelValue!: boolean;

  @Emit('input')
  toggleChecked() {
    return !this.modelValue;
  }
}
</script>

<style lang="scss" scoped>
.y-checkbox {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0;
  cursor: pointer;
  user-select: none;

  &__input {
    width: 16px;
    height: 16px;
    position: relative;
    flex: none;
  }

  &__box {
    width: 16px;
    height: 16px;
    box-sizing: border-box;
    background-color: transparent;
    border: 1.5px solid rgba(255, 255, 255, 0.15);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  &__img {
    width: 16px;
    height: 16px;
    object-fit: contain;
    display: block;
  }

  &__label-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 0 0 8px;
  }

  &__label {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: #FFFFFF;
  }

  &:hover {
    .y-checkbox__box {
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}
</style>
