<!--
/**
 * 标签组件
 *
 * @description 用于显示标签信息的组件，支持可关闭和图标显示
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 支持可关闭和图标显示
 * - 提供两种尺寸规格
 * - 深色主题设计
 * - 圆角矩形设计
 * - 半透明背景效果
 * - 支持图标和关闭按钮
 */
-->
<template>
  <div class="y-tag" :class="[`y-tag--${size}`]">
    <div v-if="showIcon" class="y-tag__icon">
      <slot name="icon"></slot>
    </div>
    <div class="y-tag__content">
      <slot></slot>
    </div>
    <div v-if="closable" class="y-tag__close" @click.stop="handleClose">
      <y-svg-icon name="close" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";
import YSvgIcon from "~/components/y/y-svg-icon.vue";
@Component({
  name: "YTag",
  components: {
    YSvgIcon
  }
})
export default class YTag extends Vue {
  @Prop({ type: Boolean, default: false }) readonly closable!: boolean;
  @Prop({ type: Boolean, default: false }) readonly showIcon!: boolean;
  @Prop({ type: String, default: "regular" }) readonly size!: "regular" | "small";

  @Emit('close')
  handleClose(event: MouseEvent) {
    event.stopPropagation();
    return event;
  }
}
</script>

<style lang="scss" scoped>
.y-tag {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;

  background: rgba(255, 255, 255, 0.1);
  background-blend-mode: overlay;
  border-radius: 28px;

  flex: none;
  flex-grow: 0;

  &--regular {
    padding: 12px 24px;
  }

  &--small {
    padding: 8px 12px;
  }

  &__icon {
    width: 18px;
    height: 18px;
    flex: none;
    order: 0;
    flex-grow: 0;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__content {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: #FFFFFF;
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  &__close {
    width: 18px;
    height: 18px;
    flex: none;
    order: 2;
    flex-grow: 0;
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
