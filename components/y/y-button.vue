<!--
/**
 * 按钮组件
 *
 * @description 通用按钮组件，支持多种样式和状态，集成 SVG 图标支持
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 提供两种主要样式：secondary（黑底白字）和 inverse（白底黑字）
 * - 支持图标显示，可设置图标位置
 * - 支持禁用状态
 * - 深色主题设计，适配项目整体风格
 */
-->
<template>
  <button
    class="y-button"
    :class="[
      `y-button--${type}`,
      { 'is-disabled': disabled }
    ]"
    :disabled="disabled"
    @click="handleClick"
  >
    <y-svg-icon v-if="icon && iconPosition === 'left'" :name="icon" class="y-button__icon y-button__icon--left" />
    <slot></slot>
    <y-svg-icon v-if="icon && iconPosition === 'right'" :name="icon" class="y-button__icon y-button__icon--right" />
  </button>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";

@Component({
  name: "YButton"
})
export default class YButton extends Vue {
  // 按钮类型：secondary(黑底白字)或inverse(白底黑字)
  @Prop({ type: String, default: "secondary" }) readonly type!: "secondary" | "inverse";

  // 是否禁用
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;

  // 图标名称
  @Prop({ type: String, default: "" }) readonly icon!: string;

  // 图标位置：left(文字前面)或right(文字后面)
  @Prop({ type: String, default: "left" }) readonly iconPosition!: "left" | "right";

  // 点击事件处理
  @Emit("click")
  handleClick(event: MouseEvent) {
    if (this.disabled) {
      event.preventDefault();
      return;
    }
    return event;
  }
}
</script>

<style lang="scss" scoped>
.y-button {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  gap: 10px;
  border-radius: 28px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 150%;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;

  &__icon {
    display: inline-flex;
    align-items: center;

    &--left {
      margin-right: 5px;
    }

    &--right {
      margin-left: 5px;
    }
  }

  // 黑底白字按钮（默认）
  &--secondary {
    background: rgba(255, 255, 255, 0.05);
    background-blend-mode: overlay;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #FFFFFF;

    &:hover:not(.is-disabled) {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
    }

    &:active:not(.is-disabled) {
      background: rgba(255, 255, 255, 0.15);
    }

    &.is-disabled {
      color: #A0A0A0;
      cursor: not-allowed;
    }
  }

  // 白底黑字按钮
  &--inverse {
    background: #FFFFFF;
    color: #171618;

    &:hover:not(.is-disabled) {
      background: rgba(255, 255, 255, 0.9);
    }

    &:active:not(.is-disabled) {
      background: rgba(255, 255, 255, 0.8);
    }

    &.is-disabled {
      background: #F2F2F2;
      color: #A0A0A0;
      cursor: not-allowed;
    }
  }
}
</style>
