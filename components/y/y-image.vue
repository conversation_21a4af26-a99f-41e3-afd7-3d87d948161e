<template>
  <div class="image-preview" :style="{ width: width, height: height }">
    <img :src="src" :alt="alt" :style="{ objectFit: objectFit, cursor: previewImages.length ? 'pointer' : 'default' }"
      @click="openPreview" />

    <!-- 预览弹窗 -->
    <div v-if="isPreviewVisible" class="preview-overlay">
      <div class="preview-header">
        <img src="/img/logo.svg" class="logo" alt="">
        <img src="/img/home/<USER>" class="close" alt="" @click="closePreview">
      </div>
      <div class="preview-content">
        <button class="nav-button left" @click.stop="prevImage" :disabled="isFirst">
          <span class="iconfont icon-left-1"></span>
        </button>
        <img :src="currentImage" alt="preview" class="preview-image" />
        <button class="nav-button right" @click.stop="nextImage" :disabled="isLast">
          <span class="iconfont icon-right-1-copy"></span>
        </button>
        <div class="preview-actions">
          <button @click="downloadImage">
            <span class="iconfont icon-download"></span>
            下载图片
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Component, Prop } from 'vue-property-decorator';

@Component({
  name: 'y-image',
})
export default class YImage extends Vue {
  /** 图片源 */
  @Prop({ default: '' }) readonly src!: string;

  /** 图片预览的图片列表，为空时不支持预览 */
  @Prop({ default: () => [] }) readonly previewImages!: string[];

  /** 图片的alt属性 */
  @Prop({ default: '' }) readonly alt!: string;

  /** 图片的宽度 */
  @Prop({ default: '100%' }) readonly width!: string;

  /** 图片的高度 */
  @Prop({ default: '100%' }) readonly height!: string;

  /**图片填充方式 */
  @Prop({ default: 'cover' }) readonly objectFit!: string;

  /**预览大图初始index */
  @Prop({ default: 0 }) readonly initialIndex!: number;

  isPreviewVisible: boolean = false;
  currentIndex: number = 0;

  get currentImage(): string {
    return this.previewImages.length > 0 ? this.previewImages[this.currentIndex] : this.src;
  }

  get isFirst(): boolean {
    return this.currentIndex <= 0;
  }

  get isLast(): boolean {
    return this.previewImages.length > 0
      ? this.currentIndex >= this.previewImages.length - 1
      : true;
  }

  openPreview() {
    if (!this.previewImages.length) {
      return;
    }
    this.currentIndex = this.initialIndex;
    this.isPreviewVisible = true;
  }

  closePreview() {
    this.isPreviewVisible = false;
  }

  nextImage() {
    if (!this.isLast) {
      this.currentIndex++;
    }
  }

  prevImage() {
    if (!this.isFirst) {
      this.currentIndex--;
    }
  }

  downloadImage() {
    const link = document.createElement('a');
    link.href = this.currentImage;
    link.download = this.currentImage.split('/').pop() || 'image.jpg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
</script>

<style lang="scss" scoped>
.image-preview img {
  width: 100%;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(23, 22, 24, 1);
  z-index: 999;
}

.preview-header {
  width: 100%;
  box-sizing: border-box;
  padding: 24px 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .logo {
    width: 60px;
    height: 60px;
  }

  .close {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
}

.preview-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  text-align: center;
  margin: 0 auto;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
}

.preview-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.nav-button {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.05);
  border: solid 1px rgba(255, 255, 255, 0.15);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

.nav-button.left {
  left: -40px;
}

.nav-button.right {
  right: -40px;
}

.nav-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.15);
  }
}

.preview-actions button {
  border-radius: 25%;
  color: rgba(235, 235, 245, 0.7);
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: solid 1px rgba(255, 255, 255, 0.15);
  border-radius: 28px;
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}
</style>
