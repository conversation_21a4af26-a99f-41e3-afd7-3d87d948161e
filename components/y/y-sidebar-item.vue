<!--
/**
 * 侧边栏项组件
 *
 * @description 侧边栏中的单个菜单项组件，支持图标切换和激活状态
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 支持图标切换和激活状态
 * - 悬停和点击交互效果
 * - 悬停时显示激活图标
 * - 激活状态时背景高亮
 * - 平滑的过渡动画效果
 * - 自动图标切换逻辑
 */
-->
<template>
    <div class="y-sidebar-item" :class="{ 'is-active': active, 'is-hovered': isHovered }" @click="handleClick" @mouseenter="isHovered = true" @mouseleave="isHovered = false">
        <div class="y-sidebar-item__icon">
            <component :is="'y-svg-icon'" :name="currentIcon" :key="'icon-' + currentIcon" :size="18" />
        </div>
        <div class="y-sidebar-item__content">
            <slot></slot>
        </div>
        <div class="y-sidebar-item__extra" v-if="$slots.extra">
            <slot name="extra"></slot>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit, Watch } from "vue-property-decorator";
import YSvgIcon from "./y-svg-icon.vue";

@Component({
    name: "YSidebarItem",
    components: {
        YSvgIcon
    }
})
export default class YSidebarItem extends Vue {
    @Prop({ type: Boolean, default: false }) readonly active!: boolean;
    @Prop({ type: String, default: "" }) readonly icon!: string;
    @Prop({ type: String, default: "" }) readonly activeIcon!: string;

    isHovered = false;

    // 使用计算属性确定当前应该显示的图标
    get currentIcon(): string {
        return this.showActiveIcon && this.activeIcon ? this.activeIcon : this.icon;
    }

    get showActiveIcon() {
        return this.active || this.isHovered;
    }

    @Emit("click")
    handleClick(event: MouseEvent) {
        return event;
    }

    @Watch("isHovered")
    @Watch("active")
    onStateChange() {
        // 强制重新渲染
        this.$forceUpdate();
    }
}
</script>

<style lang="scss" scoped>
.y-sidebar-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px 24px;
    gap: 12px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__content {
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 150%;
        color: rgba(235, 235, 245, 0.7);
        transition: color 0.3s ease;
    }

    &__extra {
        margin-left: auto;
    }

    &:hover:not(.is-active),
    &.is-active {
        background: rgba(255, 255, 255, 0.1);

        .y-sidebar-item__content {
            color: #ffffff;
        }
    }
}
</style>
