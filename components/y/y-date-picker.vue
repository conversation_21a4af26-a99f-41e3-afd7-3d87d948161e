<!--
/**
 * 日期选择器组件
 *
 * @description 自定义样式的日期选择器组件，基于 Element UI 的日期选择器封装
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 提供统一的深色主题外观
 * - 支持多种日期格式和选择类型
 * - 自定义外观设计，集成 SVG 图标
 * - 点击触发日期选择面板
 * - 支持焦点状态样式
 * - 完全兼容 Element UI 的日期选择器功能
 */
-->
<template>
    <div class="y-date-picker" :class="{ 'is-focused': focused }">
        <div class="y-date-picker__wrapper" @click="handleClick">
            <!-- 前缀图标 -->
            <div v-if="$slots.prefix || prefixIcon" class="y-date-picker__prefix">
                <slot name="prefix">
                    <i v-if="prefixIcon" :class="prefixIcon"></i>
                </slot>
            </div>

            <!-- 显示选中的日期 -->
            <div class="y-date-picker__display" :class="{ 'is-placeholder': !dateValue }">
                {{ displayValue }}
            </div>

            <!-- 日历图标 -->
            <div class="y-date-picker__icon">
                <y-svg-icon name="dete" :size="18" />
            </div>
        </div>

        <!-- 使用 Element UI 的日期选择器作为实际的选择器 -->
        <el-date-picker ref="datePicker" v-model="dateValue" :type="type" :placeholder="placeholder" :format="format" :value-format="valueFormat" :disabled="disabled" :readonly="readonly" :clearable="clearable" :picker-options="pickerOptions" style="position: absolute; opacity: 0;" @focus="handleFocus" @blur="handleBlur" @change="handleChange"></el-date-picker>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from "vue-property-decorator";
import YSvgIcon from "./y-svg-icon.vue";

@Component({
    name: "YDatePicker",
    components: {
        YSvgIcon
    }
})
export default class YDatePicker extends Vue {
    @Prop({ type: String, default: "" }) readonly value!: string;
    @Prop({ type: String, default: "date" }) readonly type!: string;
    @Prop({ type: String, default: "选择日期" }) readonly placeholder!: string;
    @Prop({ type: String, default: "yyyy/MM/dd" }) readonly format!: string;
    @Prop({ type: String, default: "yyyy-MM-dd" }) readonly valueFormat!: string;
    @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;
    @Prop({ type: Boolean, default: false }) readonly readonly!: boolean;
    @Prop({ type: Boolean, default: true }) readonly clearable!: boolean;
    @Prop({ type: String }) readonly prefixIcon!: string;
    @Prop({ type: Object, default: () => ({}) }) readonly pickerOptions!: object;

    dateValue = this.value;
    focused = false;

    mounted() {
        // 组件挂载后，确保隐藏的输入框位置正确
        this.$nextTick(() => {
            const datePickerEl = this.$refs.datePicker as Vue;
            if (datePickerEl && datePickerEl.$el) {
                const inputEl = datePickerEl.$el.querySelector("input");
                if (inputEl) {
                    this.positionInputElement(inputEl);
                }
            }
        });
    }

    get displayValue() {
        if (!this.dateValue) {
            return this.placeholder;
        }

        // 如果是 yyyy-MM-dd 格式，转换为 yyyy/MM/dd 显示
        if (this.valueFormat === "yyyy-MM-dd" && this.dateValue) {
            const [year, month, day] = this.dateValue.split("-");
            return `${year}/${month}/${day}`;
        }

        return this.dateValue;
    }

    @Watch("value")
    onValueChange(newValue: string) {
        this.dateValue = newValue;
    }

    @Watch("dateValue")
    onDateValueChange(newValue: string) {
        this.$emit("input", newValue);
    }

    @Emit("focus")
    handleFocus(event: FocusEvent) {
        this.focused = true;
        return event;
    }

    @Emit("blur")
    handleBlur(event: FocusEvent) {
        this.focused = false;
        return event;
    }

    @Emit("change")
    handleChange(value: string) {
        return value;
    }

    handleClick() {
        if (!this.disabled && !this.readonly) {
            // 直接触发日期选择器的点击事件
            const datePickerEl = this.$refs.datePicker as Vue;
            if (datePickerEl && datePickerEl.$el) {
                const inputEl = datePickerEl.$el.querySelector("input");
                if (inputEl) {
                    // 先将隐藏的输入框定位到我们的包装器位置
                    this.positionInputElement(inputEl);
                    // 然后触发点击
                    inputEl.click();
                    inputEl.focus();
                }
            }
        }
    }

    // 将隐藏的输入框定位到我们的包装器位置，以便弹出面板正确对齐
    positionInputElement(inputEl: HTMLElement) {
        const wrapper = this.$el.querySelector(".y-date-picker__wrapper") as HTMLElement;
        if (wrapper) {
            const rect = wrapper.getBoundingClientRect();
            const inputStyle = inputEl.style;

            // 设置输入框的位置和尺寸与包装器一致
            inputStyle.position = "absolute";
            inputStyle.top = "0";
            inputStyle.left = "0";
            inputStyle.width = `${rect.width}px`;
            inputStyle.height = `${rect.height}px`;
            inputStyle.opacity = "0";
            inputStyle.zIndex = "-1";
        }
    }
}
</script>

<style lang="scss" scoped>
.y-date-picker {
    position: relative;
    width: 100%;

    &__wrapper {
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 8px;
        gap: 6px;
        width: 100%;
        height: 40px;
        min-height: 40px;
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        transition: all 0.2s ease;
        background-color: transparent;
        cursor: pointer;

        &:hover {
            border-color: rgba(255, 255, 255, 0.3);
        }
    }

    &.is-focused &__wrapper {
        border-color: rgba(255, 255, 255, 0.5);
    }

    &__prefix {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: rgba(235, 235, 245, 0.7);
    }

    &__display {
        flex: 1;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 150%;
        color: #ffffff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.is-placeholder {
            color: rgba(235, 235, 245, 0.7);
        }
    }

    &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        line-height: 1;
    }

    &[disabled] {
        cursor: not-allowed;

        .y-date-picker__wrapper {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .y-date-picker__display {
            color: rgba(235, 235, 245, 0.4);
        }
    }
}

// 自定义 Element UI 日期选择器的样式
::v-deep {
    .el-picker-panel {
        background-color: #232124;
        border: 1px solid rgba(255, 255, 255, 0.15);

        .el-date-picker__header {
            color: #fff;

            .el-picker-panel__icon-btn {
                color: rgba(255, 255, 255, 0.7);

                &:hover {
                    color: #fff;
                }
            }

            .el-date-picker__header-label {
                color: #fff;

                &:hover {
                    color: #fff;
                }
            }
        }

        .el-date-table {
            th {
                color: rgba(255, 255, 255, 0.7);
            }

            td {
                color: rgba(255, 255, 255, 0.7);

                &.available:hover {
                    color: #fff;
                }

                &.current:not(.disabled) {
                    color: #232124;
                    background-color: #fff;
                }

                &.today {
                    color: #fff;
                }

                &.disabled {
                    color: rgba(255, 255, 255, 0.3);
                }
            }
        }

        .el-picker-panel__footer {
            background-color: #232124;
            border-top: 1px solid rgba(255, 255, 255, 0.15);

            .el-button {
                color: #fff;
                background-color: transparent;
                border: 1px solid rgba(255, 255, 255, 0.15);

                &:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                    border-color: rgba(255, 255, 255, 0.3);
                }

                &--primary {
                    background-color: #fff;
                    color: #232124;
                    border: none;

                    &:hover {
                        background-color: rgba(255, 255, 255, 0.9);
                    }
                }
            }
        }
    }
}
</style>
