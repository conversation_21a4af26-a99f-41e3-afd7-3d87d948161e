<!--
/**
 * 标签库弹窗组件
 *
 * @description 专门用于标签选择的弹窗组件，支持分类展示和多选功能
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 支持分类展示和多选功能
 * - 提供搜索和筛选能力
 * - 分类展示：按分类组织标签选项
 * - 多选功能：支持批量选择标签
 * - 搜索过滤：快速查找目标标签
 * - 统计信息：显示标签使用次数
 */
-->
<template>
    <el-dialog :visible.sync="dialogVisible" :title="dialogTitle" width="700px" custom-class="tag-library-dialog" :show-close="true" :append-to-body="true" :close-on-click-modal="false" @close="handleClose">
        <div class="tag-library-dialog-content">
            <!-- 左侧分类列表 -->
            <div class="category-list">
                <div v-for="category in categories" :key="category.value" class="category-item" :class="{ active: activeCategory === category.value }" @click="activeCategory = category.value">
                    {{ category.label }}
                </div>
            </div>

            <!-- 右侧选项列表 -->
            <div class="options-container">
                <div class="options-list">
                    <div v-for="option in filteredOptions" :key="option.value" class="option-item">
                        <y-checkbox v-model="option.checked" @input="handleOptionChange">
                            {{ option.label }}
                        </y-checkbox>
                        <span v-if="option.assetRelNum" class="count">{{ option.assetRelNum }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="tag-library-dialog-footer">
            <y-button type="secondary" @click="clearAll">清除全部</y-button>
            <y-button type="inverse" @click="confirm">确认</y-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from "vue-property-decorator";
import YCheckbox from "~/components/y/y-checkbox.vue";
import YButton from "~/components/y/y-button.vue";

/**
 * 选项接口定义
 */
interface TagOption {
    value: string;
    label: string;
    category: string;
    checked: boolean;
    assetRelNum?: number;
}

/**
 * 分类选项接口定义
 */
interface CategoryOption {
    value: string;
    label: string;
}

/**
 * 标签库弹窗组件
 *
 * 专门用于标签选择的弹窗组件，支持分类展示和多选功能
 */
@Component({
    name: "YTagLibraryDialog",
    components: {
        YCheckbox,
        YButton
    }
})
export default class YTagLibraryDialog extends Vue {
    /** 弹窗可见状态 */
    @Prop({ type: Boolean, default: false }) readonly visible!: boolean;

    /** 选中的值数组 */
    @Prop({ type: Array, default: () => [] }) readonly value!: string[];

    /** 弹窗标题 */
    @Prop({ type: String, default: "标签库" }) readonly dialogTitle!: string;

    /** 选项列表 */
    @Prop({ type: Array, default: () => [] }) readonly options!: Array<{ value: string; label: string; category?: string; assetRelNum?: number }>;

    /** 分类列表 */
    @Prop({ type: Array, default: () => [] }) readonly categories!: CategoryOption[];

    /** 当前激活的分类 */
    activeCategory = "";

    /** 本地选项数据 */
    localOptions: TagOption[] = [];

    /** 弹窗可见状态（内部） */
    get dialogVisible() {
        return this.visible;
    }

    set dialogVisible(value: boolean) {
        this.$emit("update:visible", value);
    }

    created() {
        this.initLocalOptions();
    }

    /**
     * 监听value变化，同步更新本地选项
     */
    @Watch("value", { immediate: true, deep: true })
    onValueChange() {
        this.initLocalOptions();
    }

    /**
     * 监听options变化，重新初始化
     */
    @Watch("options", { immediate: true, deep: true })
    onOptionsChange() {
        this.initLocalOptions();
    }

    /**
     * 根据当前分类过滤选项
     */
    get filteredOptions() {
        if (!this.activeCategory) {
            return this.localOptions;
        }
        return this.localOptions.filter(option => option.category === this.activeCategory);
    }

    /**
     * 初始化本地选项数据
     */
    initLocalOptions() {
        this.localOptions = this.options.map(option => ({
            value: option.value,
            label: option.label,
            category: option.category || "其他",
            checked: this.value.includes(option.value),
            assetRelNum: option.assetRelNum
        }));

        // 如果没有激活的分类，默认选择第一个
        if (!this.activeCategory && this.categories.length > 0) {
            this.activeCategory = this.categories[0].value;
        }
    }

    /**
     * 处理弹窗关闭事件
     */
    handleClose() {
        // 重置为原始选择状态
        this.initLocalOptions();
        this.$emit("close");
    }

    /**
     * 处理选项变更事件
     */
    handleOptionChange() {
        // 可以在这里添加额外的逻辑
        this.$emit("option-change", this.getSelectedValues());
    }

    /**
     * 清除所有选中项
     */
    clearAll() {
        this.localOptions.forEach(option => {
            option.checked = false;
        });
        this.$emit("clear-all");
    }

    /**
     * 获取选中的值
     */
    getSelectedValues(): string[] {
        return this.localOptions.filter(option => option.checked).map(option => option.value);
    }

    /**
     * 确认选择并关闭弹窗
     */
    @Emit("confirm")
    confirm() {
        const selectedValues = this.getSelectedValues();
        this.dialogVisible = false;
        return selectedValues;
    }
}
</script>

<style lang="scss" scoped>
::v-deep .tag-library-dialog {
    background: rgba(23, 22, 24, 1);
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);

    .el-dialog__header {
        padding: 20px 24px;

        .el-dialog__title {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            line-height: 24px;
        }

        .el-dialog__headerbtn {
            top: 20px;
            right: 24px;

            .el-dialog__close {
                color: rgba(255, 255, 255, 0.7);
                font-size: 20px;
                font-weight: bold;

                &:hover {
                    color: #fff;
                }
            }
        }
    }

    .el-dialog__body {
        padding: 0;
        color: #fff;
    }

    .tag-library-dialog-content {
        display: flex;
        height: 500px;

        .category-list {
            //width: 180px;
            padding: 16px;
            background: rgba(23, 22, 24, 1);
            max-height: 100%;
            overflow: auto;

            /* 自定义滚动条样式 */
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(23, 22, 24, 0.8);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;

                &:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            }

            .category-item {
                display: flex;
                align-items: center;
                padding: 16px 24px;
                border-radius: 16px;
                margin-bottom: 8px;
                color: rgba(235, 235, 245, 0.7);
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s;

                &:hover:not(.active) {
                    background: rgba(255, 255, 255, 0.05);
                }

                &.active {
                    background: rgba(255, 255, 255, 0.1);
                    color: rgba(255, 255, 255, 1);
                }
            }
        }

        .options-container {
            flex: 1;
            padding: 20px 32px;
            overflow-y: auto;

            /* 自定义滚动条样式 */
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(23, 22, 24, 0.8);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;

                &:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            }

            .options-list {
                display: flex;
                flex-direction: column;
                gap: 16px;

                .option-item {
                    padding: 8px 0;
                    width: 100%;
                    display: flex;
                    justify-content: space-between;

                    .count {
                        color: #ebebf5b2;
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .el-dialog__footer {
        padding: 16px 24px;
    }

    .tag-library-dialog-footer {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }
}
</style>
