<template lang="pug">
el-select(
  v-bind="$attrs",
  v-on="$listeners",
  :remote-method="$attrs['remote-method-page'] ? localRemoteMethod : $attrs['remote-method']",
  v-el-select-page="handleScrollEnd"
)
  template(v-for="(slotContent, slotName) in $slots")
    template(:slot="slotName", v-bind="slotContent")
      slot(:name="slotName")
</template>

<script lang="ts">
// 参数(rows分页最低9，推荐10)：remote-method-page: ({query:string, page:number, end: ()=>void})=>void

export type RemoteMethodParam = {
  /**
   * 初始化，变更搜索词之类的触发
   */
  init: boolean;
  /**
   * 关键词
   */
  query: string;
  /**
   * 第几页
   */
  page: number;
  /**
   * 结束回调
   */
  end: () => void;
};

import { DirectiveOptions } from "vue";
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";

const ElSelectPage: DirectiveOptions = {
  bind(el, binding) {
    const SELECT_DOM = (<any>el).querySelector(
      ".el-select-dropdown .el-scrollbar__wrap"
    );
    if (!SELECT_DOM) {
      return;
    }
    // SELECT_DOM 获取到的dmo节点 下面需要用到dmo节点的clientHeight
    SELECT_DOM.addEventListener("scroll", () => {
      if (
        SELECT_DOM.scrollTop + SELECT_DOM.clientHeight + 1 >=
        SELECT_DOM.scrollHeight
      ) {
        binding.value();
      }
    });
  },
};

@Component({
  name: "y-select",
  directives: {
    ElSelectPage,
  },
})
export default class YSelect extends mixins(BaseVue) {
  selectedValue = "";
  page = 0;
  end = false;
  keyword = "";

  handleScrollEnd() {
    this.page++;
    this.execRemoteMethod();
  }
  localRemoteMethod(query: string) {
    this.page = 0;
    this.keyword = query;
    this.end = false;
    this.execRemoteMethod();
  }
  execRemoteMethod() {
    let that = this;
    if (that.end) {
      return;
    }
    let rm = <any>this.$attrs["remote-method-page"];
    if (rm) {
      let param: RemoteMethodParam = {
        init: that.page == 0,
        query: that.keyword,
        page: that.page,
        end: () => {
          that.end = true;
        },
      };
      rm(param);
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
