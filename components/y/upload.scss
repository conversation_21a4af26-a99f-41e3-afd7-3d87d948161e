.main-win {
  padding: 0px;

  // 图片类型
  .list.list-pic {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;

    .item {
      margin: 6px;
      width: 26vw;
      min-width: 80px;
      max-width: 160px;
      height: 26vw;
      min-height: 80px;
      max-height: 160px;

      border-radius: 6px;

      display: flex;
      justify-content: center;
      align-items: center;

      border: solid #d9d9d9 1px;
      background-color: #fff;
    }

    .show {
      .show-item-pic {
        position: relative;
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        video {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .item-other {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;

          .el-icon-document {
            color: #bbb;
            font-size: 60px;
          }

          .title {
            font-size: 16px;
            color: #888;
            width: 90%;
            text-align: center;
            align-content: center;
            line-height: 100%;
            height: 100%;

            word-wrap: break-word;
            overflow-wrap: break-word;

          }
        }

        &:hover .mask {
          display: flex;
        }

        .mask {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          background-color: rgba(0, 0, 0, 0.3);

          display: none;
          // display: flex;
          justify-content: center;
          align-items: center;

          .mask-btn {
            cursor: pointer;
            color: #fff;
            font-size: 18px;
            padding: 4px;

            &:hover {
              color: #ccc;
            }
          }
        }
      }
    }

    .uploading {
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .progress {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .upbtn {
      cursor: pointer;

      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }

      .popover {
        width: 100%;
        height: 100%;
      }

      .up-win {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .icon-add {
        font-size: 20px;
        color: #bbb;
        font-weight: 800;
      }
    }

  }

  // 文件类型
  .list.list-other {
    .item {
      // padding: 3px;
      width: 100%;
      line-height: 100%;
      padding: 8px;
      margin-top: 4px;
    }

    .show {
      &:hover {
        background-color: rgba(255, 255, 255, 0.5);

        .title {
          color: #409EFF;
        }
      }

      border-radius: 6px;
      // margin: 2px;

      display: flex;
      cursor: pointer;

      color: #777;
      justify-content: space-between;

      .title {
        width: 100%;
        padding: 2px;

        i {
          margin-right: 2px;
        }
      }

      .right {
        .mask-btn {
          &:hover {
            color: #409EFF;
          }
        }
      }
    }

    .uploading {
      .progress {
        width: 100%;
      }
    }
  }

  .aux {
    display: none;
  }

  .img-preview {
    display: none;
  }
}

.comp-upload-qrcode {
  .qrcode-win {
    .qrcode-img {
      width: 200px;
      height: 200px;
    }

    .qrcode-text {
      width: 200px;
      height: 200px;
      background-color: #ccc;
      text-align: center;
      font-weight: 800;

      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .red {
        color: red;
      }
    }

    .qrcode-down-text {
      width: 100%;
      text-align: center;
    }
  }
}