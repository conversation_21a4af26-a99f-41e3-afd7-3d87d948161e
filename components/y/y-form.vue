<!--
/**
 * 动态表单组件
 *
 * @description 基于配置的动态表单组件，支持各种表单控件和布局
 * <AUTHOR>
 * @created 2025-07-28
 * @version 1.0.0
 *
 * @features
 * - 配置化表单渲染
 * - 自动表单验证
 * - 栅格布局支持
 * - 事件传递
 * - 插槽支持
 * - 条件显示
 */
-->
<template>
  <el-form
    ref="elForm"
    :model="formData"
    :rules="computedRules"
    v-bind="formProps"
    @validate="handleValidate"
  >
    <el-row v-if="useGrid" :gutter="gutter">
      <y-form-item
        v-for="(item, index) in processedFormItems"
        :key="`${item.field}_${index}`"
        :item-config="item"
        :form-data="formData"
        @input="handleFieldInput"
        @change="handleFieldChange"
        @focus="handleFieldFocus"
        @blur="handleFieldBlur"
      />
    </el-row>

    <template v-else>
      <y-form-item
        v-for="(item, index) in processedFormItems"
        :key="`${item.field}_${index}`"
        :item-config="item"
        :form-data="formData"
        @input="handleFieldInput"
        @change="handleFieldChange"
        @focus="handleFieldFocus"
        @blur="handleFieldBlur"
      />
    </template>
  </el-form>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit, Watch } from "vue-property-decorator";
import { FormItemConfig, DEFAULT_CONFIG } from "~/components/y/types/form";
import { FormUtils } from "~/components/y/utils/form-utils";
import YFormItem from "./y-form-item.vue";

@Component({
  name: "YForm",
  components: {
    'y-form-item': YFormItem
  }
})
export default class YForm extends Vue {
  @Prop({ type: Array, required: true }) readonly formItems!: FormItemConfig[];
  @Prop({ type: Object, required: true }) readonly formData!: Record<string, any>;
  @Prop({ type: Object, default: () => ({}) }) readonly rules!: Record<string, any[]>;
  @Prop({ type: Object, default: () => DEFAULT_CONFIG.formProps }) readonly formProps!: Record<string, any>;
  @Prop({ type: Boolean, default: true }) readonly useGrid!: boolean;
  @Prop({ type: Number, default: DEFAULT_CONFIG.gutter }) readonly gutter!: number;

  // 在mounted后暴露方法到实例上
  mounted() {
    // 将方法绑定到实例上，确保外部可以访问
    (this as any).validate = this.validateForm;
    (this as any).validateField = this.validateFormField;
    (this as any).resetFields = this.resetFormFields;
    (this as any).clearValidate = this.clearFormValidate;
  }

  // 计算属性
  get processedFormItems(): FormItemConfig[] {
    return this.formItems.map(item => FormUtils.processFormItem(item));
  }

  get computedRules(): Record<string, any[]> {
    // 合并自动生成的规则和手动传入的规则
    const autoRules = FormUtils.generateRules(this.processedFormItems);
    return FormUtils.deepMerge(autoRules, this.rules);
  }

  // 表单方法
  private validateForm(callback?: (valid: boolean, invalidFields?: any) => void): Promise<boolean> {
    return new Promise((resolve) => {
      const form = this.$refs.elForm as any;
      if (form && form.validate) {
        form.validate((valid: boolean, invalidFields: any) => {
          if (callback) {
            callback(valid, invalidFields);
          }
          resolve(valid);
        });
      } else {
        console.error('表单引用不存在或validate方法不可用');
        resolve(false);
      }
    });
  }

  private validateFormField(prop: string, callback?: (errorMessage: string) => void): void {
    const form = this.$refs.elForm as any;
    if (form && form.validateField) {
      form.validateField(prop, callback);
    }
  }

  private resetFormFields(): void {
    const form = this.$refs.elForm as any;
    if (form && form.resetFields) {
      form.resetFields();
    }
  }

  private clearFormValidate(props?: string | string[]): void {
    const form = this.$refs.elForm as any;
    if (form && form.clearValidate) {
      form.clearValidate(props);
    }
  }

  // 获取表单数据
  public getFormData(): Record<string, any> {
    return { ...this.formData };
  }

  // 设置表单数据
  public setFormData(data: Record<string, any>): void {
    Object.keys(data).forEach(key => {
      if (this.formData.hasOwnProperty(key)) {
        this.$set(this.formData, key, data[key]);
      }
    });
  }

  // 重置表单数据
  public resetFormData(): void {
    this.processedFormItems.forEach(item => {
      this.$set(this.formData, item.field, undefined);
    });
  }

  // 事件处理
  @Emit('field-input')
  handleFieldInput(data: { field: string; value: any }): { field: string; value: any } {
    return data;
  }

  @Emit('field-change')
  handleFieldChange(data: { field: string; value: any }): { field: string; value: any } {
    return data;
  }

  @Emit('field-focus')
  handleFieldFocus(data: { field: string; event: Event }): { field: string; event: Event } {
    return data;
  }

  @Emit('field-blur')
  handleFieldBlur(data: { field: string; event: Event }): { field: string; event: Event } {
    return data;
  }

  @Emit('validate')
  handleValidate(prop: string, valid: boolean, message: string): { prop: string; valid: boolean; message: string } {
    return { prop, valid, message };
  }

  // 监听器
  @Watch('formItems', { deep: true })
  onFormItemsChange(): void {
    this.$nextTick(() => {
      this.clearFormValidate();
    });
  }
}
</script>

<style lang="scss" scoped>
// 表单样式可以根据需要添加
</style>
