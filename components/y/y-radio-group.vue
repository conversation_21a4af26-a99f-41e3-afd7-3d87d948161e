<!--
/**
 * 单选按钮组组件
 *
 * @description 基于 el-radio-group 的自定义样式组件
 * <AUTHOR>
 * @created 2025-07-23
 * @version 2.0.0
 *
 * @features
 * - 基于 Element UI 的 el-radio-group
 * - 自定义深色主题样式
 * - 支持水平和垂直布局
 * - 保持 Element UI 的使用方式
 */
-->
<template>
  <el-radio-group
    class="y-radio-group"
    :class="{ 'is-horizontal': horizontal }"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <slot></slot>
  </el-radio-group>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component({
  name: "YRadioGroup",
  inheritAttrs: false
})
export default class YRadioGroup extends Vue {
  @Prop({ type: Boolean, default: false }) readonly horizontal!: boolean;
}
</script>

<style lang="scss">
.y-radio-group {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: flex-start;

  &.is-horizontal {
    flex-direction: row;
    gap: 24px;
    align-items: center;
  }

  .el-radio {
    margin-right: 0;
    margin-bottom: 0;
    white-space: normal;
  }
}
</style>
