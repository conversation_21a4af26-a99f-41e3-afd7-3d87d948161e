<!--
/**
 * 侧边栏容器组件
 *
 * @description 侧边栏的容器组件，提供统一的侧边栏样式框架
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 深色主题设计，圆角边框
 * - 深色背景（#171618）
 * - 圆角边框设计（28px）
 * - 内边距布局（16px）
 * - 半透明边框效果
 */
-->
<template>
  <div class="y-sidebar">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({
  name: "YSidebar"
})
export default class YSidebar extends Vue {}
</script>

<style lang="scss" scoped>
.y-sidebar {
  //width: 100%;
  height: auto;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 28px;
  padding: 16px;
  background: rgba(23, 22, 24, 1);

  // 侧边栏项之间的间距
  ::v-deep .y-sidebar-item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
