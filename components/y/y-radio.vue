<!--
/**
 * 单选按钮组件
 *
 * @description 基于 el-radio 的自定义样式组件
 * <AUTHOR>
 * @created 2025-07-23
 * @version 2.0.0
 *
 * @features
 * - 基于 Element UI 的 el-radio
 * - 自定义深色主题样式
 * - 圆形选择器设计
 * - 支持悬停和选中状态
 * - 白色边框和填充效果
 * - 保持 Element UI 的使用方式
 */
-->
<template>
    <el-radio class="y-radio" v-bind="$attrs" v-on="$listeners">
        <slot></slot>
    </el-radio>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({
    name: "YRadio",
    inheritAttrs: false
})
export default class YRadio extends Vue {
    mounted() {
        // 移除可能导致无障碍问题的aria-hidden属性
        this.$nextTick(() => {
            const radioInput = this.$el.querySelector(".el-radio__original");
            if (radioInput && radioInput.hasAttribute("aria-hidden")) {
                radioInput.removeAttribute("aria-hidden");
            }
        });
    }
}
</script>

<style lang="scss">
.y-radio {
    &.el-radio {
        height: 20px;
        line-height: 20px;
        display: flex;
        align-items: center;

        .el-radio__label {
            color: rgba(235, 235, 245, 0.7);
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: normal;
        }

        .el-radio__input {
            display: flex;
            align-items: center;
            .el-radio__inner {
                width: 18px;
                height: 18px;
                border-color: rgba(255, 255, 255, 0.3);
                background-color: transparent;
                border-width: 1.5px;

                &::after {
                    width: 10px;
                    height: 10px;
                    background-color: #ffffff;
                }
            }

            &.is-checked {
                .el-radio__inner {
                    border-color: #ffffff;

                    &::after {
                        background-color: #ffffff;
                        width: 10px;
                        height: 10px;
                    }
                }
            }
        }

        &:hover {
            .el-radio__input {
                .el-radio__inner {
                    border-color: #ffffff;
                }
            }

            .el-radio__label {
                color: #ffffff;
            }
        }

        &.is-checked {
            .el-radio__label {
                color: #ffffff;
            }
        }

        &.is-disabled {
            opacity: 0.5;

            .el-radio__input {
                .el-radio__inner {
                    border-color: rgba(255, 255, 255, 0.3);
                    background-color: transparent;
                }

                &.is-checked {
                    .el-radio__inner {
                        border-color: rgba(255, 255, 255, 0.5);
                        background-color: rgba(255, 255, 255, 0.5);
                    }
                }
            }

            .el-radio__label {
                color: rgba(235, 235, 245, 0.7);
            }
        }
    }
}
</style>
