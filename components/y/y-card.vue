<template>
  <div class="y-card" @click="handleClick">
    <div class="y-card-img">
      <div class="y-card-img-cover">
        <div class="y-card-img-operate">
          <div @click="handleLikeClick">
            <img src="/img/home/<USER>" alt="">
          </div>
          <div @click="handleCollectClick">
            <span class="iconfont icon-biaoqian"></span>
          </div>
        </div>
      </div>
      <img :src="info.img || defaultImg" alt="">
    </div>
    <div class="y-card-info">
      <div class="y-card-title">{{ info.name }}</div>
      <div class="y-card-desc">
        <img src="/img/home/<USER>" alt="">
        <span>{{ info.likes || 0 }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

interface CardInfo {
  name: string
  img: string
  likes?: number
}

@Component({
  name: 'YmCard',
})
export default class YmCard extends Vue {
  @Prop({ required: true }) readonly info!: CardInfo

  get defaultImg(): string {
    return '/img/default.png'
  }

  handleClick() {
    this.$emit('click', this.info)
  }

  handleLikeClick() {
    this.$emit('like', this.info)
  }

  handleCollectClick() {
    this.$emit('collect', this.info)
  }
}
</script>

<style lang="scss" scoped>
.y-card {
  width: 100%;
  box-sizing: border-box;
  padding: 16px;
  border-radius: 28px;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
  border: solid 1px rgba(255, 255, 255, 0.15);
  cursor: pointer;

  &:hover {
    .y-card-img-cover {
      display: block;
    }
  }
}

.y-card-img {
  width: 100%;
  position: relative;
  padding-top: 75%;

  &>img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    object-fit: cover;
  }

  .y-card-img-cover {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    border-radius: 20px;
    display: none;
    z-index: 1;

    .y-card-img-operate {
      position: absolute;
      z-index: 10;
      bottom: 20px;
      right: 10px;
      display: flex;
      align-items: center;

      &>div {
        display: inline-flex;
        width: 18px;
        height: 18px;
        padding: 10px;
        border-radius: 30px;
        background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
        border: solid 1px rgba(255, 255, 255, 0.15);
        margin-right: 10px;
        color: rgba(235, 235, 245, 0.7);
        justify-content: center;
        align-items: center;

        img {
          width: 100%;
          height: 100%;
        }

        &:hover {
          background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1));
          border: solid 1px rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

.y-card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.y-card-title {
  font-family: 'PingFangSC';
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

.y-card-desc {
  color: rgba(235, 235, 245, 0.7);
  font-family: 'PingFangSC';
  font-size: 12px;
  display: flex;
  align-items: center;

  img {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }
}
</style>
