<template lang="pug">
.comp-y-tag(ref="tagDiv")
  .tag-list
    el-tag.mgr10.show-tag(
      v-for="tag in values",
      :key="tag",
      :closable="!_disabled",
      :disable-transitions="false",
      @close="handleTagCloseClick(tag)"
    ) {{ tag }}

  template(v-if="adding")
    el-input.input-new-tag(
      v-if="!_autocompleteCb",
      v-model="addValue",
      ref="addInput",
      @keyup.enter.native="handleTagValueSubmitClick()",
      @blur="handleTagValueSubmitClick()"
    )
    el-autocomplete.input-new-tag(
      v-if="_autocompleteCb",
      v-model="addValue",
      size="small",
      ref="addAutocomplete",
      @keyup.enter.native="handleTagValueSubmitClick()",
      @blur="handleTagValueSubmitClick()",
      :fetch-suggestions="(k, cb) => handleAutocompleteSearch(k, cb)",
      placeholder="请输入内容",
      :trigger-on-focus="false"
    )
    el-button.button-new-tag(
      size="small",
      type="text",
      @click="handleTagValueSubmitClick()"
    ) 确认
    el-button.button-new-tag(
      size="small",
      type="text",
      @click="handleTagValueCancelClick()"
    ) 取消
  el-button.button-new-tag(
    v-if="!adding && !_disabled",
    size="small",
    type="text",
    @click="handleTagAddClick()"
  ) +添加
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import commonFunUtils from "~/utils/commonFunUtils";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import sortableUtils from "~/utils/sortableUtils";

// 参数：autocompleteCb(key: string, cb: (result: Array<any>) => void);

@Component({
  name: "y-tag",
  props: ["value", "type", "disabled", "autocompleteCb"],
})
export default class YTag extends mixins(BaseVue) {
  values: string[] = [];
  addValue = "";
  adding = false;

  get _disabled() {
    return commonFunUtils.parseBoolean(this.$props.disabled);
  }

  get _autocompleteCb() {
    return this.$props.autocompleteCb;
  }

  /**
   * 类型：split, json, array
   */
  get _type() {
    return this.$props.type ? this.$props.type : "split";
  }

  mounted() {
    this.valueEven();
    this.boundSortable();
  }

  boundSortable() {
    if (this._disabled) {
      return;
    }
    let that = this;
    let el: any = this.$refs.tagDiv;
    let ul = el.querySelector(".tag-list");
    sortableUtils.sortable(ul, this, "values", {
      // handle: ".sorthandle",
      end: () => {
        that.putValueOut();
      },
    });
  }

  putValueOut() {
    let val: any = null;
    if (this.values && this.values.length > 0) {
      if (this._type == "json") {
        val = JSON.stringify(this.values);
      } else if (this._type == "array") {
        val = [...this.values];
      } else {
        val = this.values.join(",");
      }
    }
    compUtils.formEmitInput(this, val);
  }

  @Watch("$props.value")
  async valueEven() {
    let value = this.$props.value;
    if (this._type == "json") {
      this.values = value ? JSON.parse(value) : [];
    } else if (this._type == "array") {
      if (value == null || value == undefined || value == "") {
        this.values = [];
      } else if (Array.isArray(value)) {
        this.values = value;
      } else {
        this.values = [];
      }
    } else {
      this.values = value ? value.split(",") : [];
    }
    this.adding = false;
    this.addValue = "";
  }

  async handleTagAddClick() {
    let that = this;
    this.adding = true;
    this.addValue = "";
    if (this._autocompleteCb) {
      this.$nextTick(() => {
        let inputRef: any = that.$refs[`addAutocomplete`];
        if (inputRef) {
          inputRef.focus();
        }
      });
    } else {
      this.$nextTick(() => {
        let inputRef: any = that.$refs[`addInput`];
        if (inputRef) {
          inputRef.focus();
        }
      });
    }
  }

  async handleTagValueSubmitClick() {
    setTimeout(async () => {
      if (!this.adding || !this.addValue) {
        return;
      }
      this.values.push(this.addValue);
      this.adding = false;
      this.putValueOut();
    }, 100);
  }

  async handleTagCloseClick(tag: string) {
    let idx = this.values.indexOf(tag);
    if (idx >= 0) {
      this.values.splice(idx, 1);
      this.putValueOut();
    }
  }

  async handleTagValueCancelClick() {
    this.adding = false;
  }

  async handleAutocompleteSearch(
    key: string,
    cb: (result: Array<any>) => void
  ) {
    if (this.$props.autocompleteCb) {
      this.$props.autocompleteCb(key, cb);
    }
  }
}
</script>
<style lang="scss" scoped>
.comp-y-tag {
  border-radius: 4px;

  .tag-list {
    display: inline;
  }

  .input-new-tag {
    width: 100px;
    margin-right: 10px;
  }
  .button-new-tag {
    margin-right: 10px;
  }
}
// .el-form-item.is-error .comp-y-tag {
//   border: solid 1px #f56c6c;
// }
</style>
