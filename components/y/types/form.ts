/**
 * 表单配置类型定义
 */

// 栅格属性
export interface ColProps {
  span?: number;
  offset?: number;
  push?: number;
  pull?: number;
  xs?: number | object;
  sm?: number | object;
  md?: number | object;
  lg?: number | object;
  xl?: number | object;
}

// 表单项配置
export interface FormItemConfig {
  /** 标签文本 */
  label: string;
  /** 字段名 */
  field: string;
  /** 组件名称 */
  component: string;
  /** 组件属性 */
  componentProps?: Record<string, any>;
  /** 是否必填 */
  required?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否只读 */
  readonly?: boolean;
  /** 栅格属性 */
  colProps?: ColProps;
  /** 自定义验证规则 */
  rules?: any[];
  /** 显示条件 */
  show?: boolean | ((formData: any) => boolean);
  /** 表单项属性 */
  formItemProps?: Record<string, any>;
  /** 插槽内容 */
  slot?: string;
  /** 帮助文本 */
  help?: string;
  /** 扩展属性 */
  [key: string]: any;
}

// 表单配置
export interface FormConfig {
  /** 表单项配置 */
  formItems: FormItemConfig[];
  /** 表单数据 */
  formData: Record<string, any>;
  /** 表单验证规则 */
  rules?: Record<string, any[]>;
  /** 表单属性 */
  formProps?: Record<string, any>;
  /** 是否显示栅格 */
  useGrid?: boolean;
  /** 栅格间距 */
  gutter?: number;
}

// 组件类型映射
export const COMPONENT_TYPES = {
  // 输入类组件
  INPUT_COMPONENTS: [
    'y-input',
    'el-input'
  ],
  // 选择类组件  
  SELECT_COMPONENTS: [
    'y-multi-select-dropdown',
    'y-date-picker',
    'el-select',
    'el-date-picker',
    'el-cascader',
    'tag-selector'
  ],
  // 其他组件
  OTHER_COMPONENTS: [
    'y-radio-group',
    'y-checkbox-group',
    'el-radio-group',
    'el-checkbox-group',
    'el-switch',
    'el-slider',
    'file-upload'
  ]
} as const;

// 默认配置
export const DEFAULT_CONFIG = {
  colProps: {
    span: 24
  },
  formProps: {
    labelPosition: 'top'
  },
  gutter: 24
} as const;
