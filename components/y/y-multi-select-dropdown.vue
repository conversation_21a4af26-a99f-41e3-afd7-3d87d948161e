<!--
/**
 * 多选下拉组件
 *
 * @description 支持搜索、多选、标签展示的下拉选择器组件，类似于 Element-UI 的 el-select 组件的多选模式
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 支持多选和搜索过滤
 * - 已选项以标签形式展示，可点击删除
 * - 类似 el-select 的交互体验（点击展开/收起）
 * - 键盘操作支持（回车选择、ESC关闭、退格删除）
 * - 点击外部自动关闭
 *
 * @props
 * - value: 当前选中的值数组
 * - options: 可选项数组，格式为 [{ value: string, label: string }]
 * - placeholder: 占位符文本
 *
 * @events
 * - input: 选中值变化时触发，返回新的值数组
 * - focus: 输入框获得焦点时触发
 * - blur: 输入框失去焦点时触发
 */
-->
<template>
    <div class="multi-select-container" :class="{ 'dropdown-open': isDropdownVisible }">
        <div class="input-wrapper" :class="{ 'keep-focus': keepFocusStyle }" @click="onContainerClick">
            <div class="content-area">
                <!-- 已选标签 -->
                <div v-for="(item, index) in value" :key="index" class="selected-tag">
                    <span>{{ getOptionLabel(item) }}</span>
                    <i class="el-icon-close tag-remove" @click="removeItem(item, $event)"></i>
                </div>
                <!-- 搜索输入框 -->
                <input ref="searchInput" type="text" v-model="searchQuery" :placeholder="value && value.length > 0 ? '' : placeholder" @input="onSearchInput" @keydown="onKeydown" @focus="onInputFocus" @blur="onInputBlur" @mousedown.prevent />
            </div>
            <div class="dropdown-icon" @click="toggleDropdown">
                <img src="/img/home/<USER>" alt="" />
            </div>
        </div>
        <transition name="dropdown-slide">
            <div v-if="isDropdownVisible" class="options-dropdown">
                <div class="dropdown-title">{{ dropdownTitle }}</div>
                <div class="options-container">
                    <div v-for="option in filteredOptions" :key="option.value" class="option-item" :class="{ 'option-selected': value.includes(option.value) }" @mousedown.prevent @click="selectOption(option.value)">
                        {{ option.label }}
                    </div>
                    <div v-if="filteredOptions.length === 0" class="empty-state">
                        暂无匹配的选项
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";

interface SelectOption {
    value: string;
    label: string;
}

/**
 * 多选下拉组件
 * 支持搜索、多选、标签展示的下拉选择器
 */
@Component({
    name: "YMultiSelectDropdown"
})
export default class YMultiSelectDropdown extends Vue {
    @Prop({ type: Array, default: () => [] }) readonly value!: string[];
    @Prop({ type: Array, default: () => [] }) readonly options!: SelectOption[];
    @Prop({ type: String, default: "请选择" }) readonly placeholder!: string;
    @Prop({ type: String, default: "相关选项" }) readonly dropdownTitle!: string;
    private searchQuery = "";
    private isDropdownVisible = false;
    private isInputFocused = false;
    private keepFocusStyle = false;

    /**
     * 根据搜索条件过滤选项
     */
    get filteredOptions(): SelectOption[] {
        if (!this.searchQuery.trim()) return this.options;
        return this.options.filter(option => option.label.toLowerCase().includes(this.searchQuery.toLowerCase()));
    }

    mounted() {
        document.addEventListener("click", this.handleOutsideClick);
    }

    beforeDestroy() {
        document.removeEventListener("click", this.handleOutsideClick);
    }

    /**
     * 处理点击组件外部事件
     */
    private handleOutsideClick(event: MouseEvent): void {
        const container = this.$el;
        if (container && !container.contains(event.target as Node)) {
            this.isDropdownVisible = false;
            this.isInputFocused = false;
            const input = this.$refs.searchInput as HTMLInputElement;
            if (input && document.activeElement === input) {
                input.blur();
            }
            // 延迟重置边框样式，与下拉框消失动画同步
            setTimeout(() => {
                this.keepFocusStyle = false;
            }, 300);
        }
    }

    /**
     * 根据值获取对应的显示标签
     */
    private getOptionLabel(value: string): string {
        const option = this.options.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    /**
     * 键盘事件处理
     */
    private onKeydown(event: KeyboardEvent): void {
        if (event.key === "Backspace" || event.key === "Delete") {
            if (!this.searchQuery.trim() && this.value.length > 0) {
                const lastItem = this.value[this.value.length - 1];
                this.removeItem(lastItem);
                event.preventDefault();
            }
        } else if (event.key === "Enter") {
            if (this.filteredOptions.length > 0) {
                this.selectOption(this.filteredOptions[0].value);
                event.preventDefault();
            }
        } else if (event.key === "Escape") {
            this.isDropdownVisible = false;
            this.blurInput();
            event.preventDefault();
        }
    }

    /**
     * 选择选项
     */
    @Emit("input")
    private selectOption(value: string): string[] {
        if (this.value.includes(value)) {
            return this.removeItemValue(value);
        }

        const newValue = [...this.value, value];
        this.searchQuery = "";
        this.isDropdownVisible = true;

        // 保持下拉框打开状态并聚焦输入框
        this.$nextTick(() => {
            this.focusInput();
        });

        return newValue;
    }

    /**
     * 移除选中项
     */
    @Emit("input")
    private removeItem(value: string, event?: Event): string[] {
        if (event) {
            event.stopPropagation();
        }
        const newValue = this.removeItemValue(value);
        this.$nextTick(() => {
            if (this.isInputFocused) {
                this.focusInput();
            }
        });
        return newValue;
    }

    /**
     * 从数组中移除指定值
     */
    private removeItemValue(value: string): string[] {
        const index = this.value.indexOf(value);
        if (index !== -1) {
            const newValue = [...this.value];
            newValue.splice(index, 1);
            return newValue;
        }
        return [...this.value];
    }

    /**
     * 切换下拉框显示状态
     */
    private toggleDropdown(): void {
        this.isDropdownVisible = !this.isDropdownVisible;
        if (this.isDropdownVisible) {
            this.keepFocusStyle = true;
            this.focusInput();
        } else {
            this.blurInput();
            // 延迟重置边框样式，与下拉框消失动画同步
            setTimeout(() => {
                this.keepFocusStyle = false;
            }, 300);
        }
    }

    /**
     * 处理容器点击事件
     */
    private onContainerClick(event: MouseEvent): void {
        const target = event.target as HTMLElement;
        if (target.closest(".dropdown-icon")) {
            return;
        }
        if (target.classList.contains("tag-remove") || target.closest(".tag-remove")) {
            return;
        }

        event.preventDefault();
        event.stopPropagation();

        if (this.isInputFocused && this.isDropdownVisible) {
            this.isDropdownVisible = false;
            this.blurInput();
            // 延迟重置边框样式，与下拉框消失动画同步
            setTimeout(() => {
                this.keepFocusStyle = false;
            }, 300);
        } else {
            this.isDropdownVisible = true;
            this.keepFocusStyle = true;
            this.focusInput();
        }

        this.$emit("focus");
    }

    /**
     * 输入框获得焦点
     */
    private onInputFocus(): void {
        this.isInputFocused = true;
        if (!this.isDropdownVisible) {
            this.isDropdownVisible = true;
        }
        this.$emit("focus");
    }

    /**
     * 输入框失去焦点
     */
    private onInputBlur(event: FocusEvent): void {
        const relatedTarget = event.relatedTarget as HTMLElement;
        if (relatedTarget && this.$el.contains(relatedTarget)) {
            this.$nextTick(() => {
                this.focusInput();
            });
            return;
        }

        // 延迟关闭下拉框，以便用户可以点击选项
        setTimeout(() => {
            // 检查是否点击了下拉框内的元素
            const activeElement = document.activeElement;
            if (activeElement && this.$el.contains(activeElement)) {
                return;
            }

            this.isInputFocused = false;
            this.isDropdownVisible = false;
            // 延迟重置边框样式，与下拉框消失动画同步
            setTimeout(() => {
                this.keepFocusStyle = false;
            }, 300);
        }, 150);

        this.$emit("blur");
    }

    /**
     * 搜索输入事件
     */
    private onSearchInput(): void {
        this.isDropdownVisible = true;
    }

    /**
     * 聚焦输入框
     */
    private focusInput(): void {
        this.$nextTick(() => {
            const input = this.$refs.searchInput as HTMLInputElement;
            if (input) {
                input.focus();
                this.isInputFocused = true;
            }
        });
    }

    /**
     * 输入框失焦
     */
    private blurInput(): void {
        this.$nextTick(() => {
            const input = this.$refs.searchInput as HTMLInputElement;
            if (input) {
                input.blur();
                this.isInputFocused = false;
            }
        });
    }
}
</script>

<style lang="scss" scoped>
.multi-select-container {
    position: relative;

    .input-wrapper {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        min-height: 40px;
        background-color: #171618;
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        padding: 8px;
        gap: 6px;
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: text;
        transition: border-color 0.1s ease, border-radius 0.1s ease, border-bottom 0.1s ease;

        &:hover {
            border-color: rgba(255, 255, 255, 0.3);
        }

        &:focus-within {
            border-color: rgba(255, 255, 255, 0.5);
        }
    }

    .el-form-item.is-error & {
        border-color: #f56c6c;
    }

    .content-area {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 6px;
        min-height: 21px;
        flex: 1;
        order: 1;

        .selected-tag {
            height: 24px;
            box-sizing: border-box;
            display: inline-flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 28px;
            padding: 2px 10px;
            color: rgba(235, 235, 245, 0.7);
            font-size: 14px;
            flex-shrink: 0;
            transition: all 0.2s ease;

            &:hover {
                border-color: rgba(255, 255, 255, 0.3);
            }

            .tag-remove {
                margin-left: 8px;
                cursor: pointer;
                font-size: 12px;

                &:hover {
                    color: rgba(255, 255, 255, 0.8);
                }
            }
        }

        input {
            flex: 1;
            min-width: 80px;
            height: 21px;
            background-color: transparent;
            border: none;
            color: #fff;
            padding: 0;
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 150%;
            outline: none;
            caret-color: #fff; /* 添加光标颜色 */

            &::placeholder {
                color: rgba(235, 235, 245, 0.7);
            }
        }
    }

    .dropdown-icon {
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(235, 235, 245, 0.7);
        cursor: pointer;
        order: 2;
        flex: none;

        img {
            width: 18px;
            height: 18px;
        }

        &:hover {
            color: rgba(255, 255, 255, 1);
        }
    }
}

.options-dropdown {
    position: absolute;
    width:100%;
    top: 100%;
    left: 0;
    box-sizing: border-box;
    background-color: #171618;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    border-top: none; /* 移除顶部边框，与主容器连接 */
    border-top-left-radius: 0; /* 移除顶部圆角 */
    border-top-right-radius: 0; /* 移除顶部圆角 */
    z-index: 100;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 24px 0px 24px;
    gap: 24px;
    transition: all 0.1s ease;
    max-height: 220px;

    .dropdown-title {
        padding-left: 16px;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 150%;
        height: 21px;
        color: #ffffff;
    }

    .options-container {
        width: 100%;
        box-sizing: border-box;
        padding: 0px 16px;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        align-content: flex-start;
        gap: 12px;
        overflow-y: auto;

        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .option-item {
            box-sizing: border-box;
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 12px 24px;
            gap: 12px;
            height: 45px;
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 150%;
            color: rgba(235, 235, 245, 0.7);
            cursor: pointer;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            transition: all 0.2s ease;

            &:hover {
                border-color: rgba(255, 255, 255, 0.3);
                color: rgba(255, 255, 255, 1);
                border-color: rgba(255, 255, 255, 0.5);
            }

            &.option-selected {
                background-color: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 1);
                border-color: rgba(255, 255, 255, 0.5);
            }
        }

        .empty-state {
            width: 100%;
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
        }
    }
}

.dropdown-slide-enter-active,
.dropdown-slide-leave-active {
    transition: all 0.1s ease;
}

.dropdown-slide-enter,
.dropdown-slide-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.dropdown-slide-enter-to,
.dropdown-slide-leave {
    opacity: 1;
    transform: translateY(0);
}

/* 当下拉框展开时的样式调整 */
.multi-select-container.dropdown-open .input-wrapper {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: none;
    border-color: rgba(255, 255, 255, 0.5);
}

.multi-select-container.dropdown-open .input-wrapper:hover {
    border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 当输入框聚焦时保持高亮边框 */
.multi-select-container .input-wrapper:focus-within {
    border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 下拉框动画期间保持边框高亮 */
.multi-select-container .input-wrapper.keep-focus {
    border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 优化下拉框动画的初始状态 */
.options-dropdown {
    transform-origin: top center;
}
</style>
