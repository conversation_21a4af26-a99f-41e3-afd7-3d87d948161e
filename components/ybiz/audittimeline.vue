<template lang="pug">
div
  el-timeline
    template(v-for="item in auditRecords")
      template(v-if="item.processNodeType == 'START'")
        el-timeline-item(
          :key="item.id",
          :timestamp="item.finishTime ? dateFormate(item.finishTime) : dateFormate(item.createTime)",
          type="primary"
        )
          div {{ item.name }}
      template(
        v-if="item.processNodeType == 'AUDIT' && item.state !== 'SKIP' && !isHidden(item)"
      )
        el-timeline-item(
          :key="item.id",
          :timestamp="item.finishTime ? dateFormate(item.finishTime) : dateFormate(item.createTime)",
          :type="item.state == 'FINISH' ? (item.auditResult == 'REJECT' ? 'danger' : 'success') : 'primary'"
        )
          div {{ item.name }}
          div(v-if="item.state == 'FINISH'") 审核结果：
            el-tag(:type="item.auditResult == 'REJECT' ? 'danger' : 'success'") {{ item.auditResult == "REJECT" ? "驳回" : "通过" }}
              template(v-if="item.auditResult == 'AUTO_PASS'") （自动通过）
          div(v-if="item.auditRemark") 审核建议：
            el-alert(:closable="false") {{ item.auditRemark }}
          div(v-if="item.auditParam && item.auditParam.appendix") 附件：
            y-upload(
              v-model="item.auditParam.appendix",
              :disabled="true",
              type="pic"
            )
          div(v-if="item.auditOwnerName") 操作人：{{ item.auditOwnerName }}
            span(v-if="item.auditOwnerNo") （{{ item.auditOwnerNo }}）
          div(v-if="item.state != 'FINISH'")
            el-tag(type="primary") 待处理
      template(v-if="item.processNodeType == 'END'")
        el-timeline-item(
          :key="item.id",
          :timestamp="item.finishTime ? dateFormate(item.finishTime) : dateFormate(item.createTime)",
          type="primary"
        )
          div {{ item.name }}
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { BpmWorkflowRecord } from "~/api/wms/mg/transactionApi";
import { BaseVue } from "~/model/vue";
import moment from "moment";
import commonFunUtils from "~/utils/commonFunUtils";

type BpmProp = {
  code?: string;
  value?: string;
};

type BpmRecord = BpmWorkflowRecord & {
  auditParam?: {};
  _processNodeProps?: BpmProp[];
};

@Component({
  name: "ybiz-audittimeline",
  props: ["value"],
})
export default class YbizAudittimeline extends mixins(BaseVue) {
  auditRecords = <BpmRecord[]>[];

  async mounted() {
    await this.init();
  }

  get records() {
    return this.$props.value ? this.$props.value : [];
  }

  isHidden(item: BpmRecord) {
    if (!item._processNodeProps) {
      return false;
    }
    let props: BpmProp[] = item._processNodeProps;
    for (let prop of props) {
      if (prop.code != "hiddenNode" || !prop.value) {
        continue;
      }
      return commonFunUtils.parseBoolean(prop.value);
    }
    return false;
  }

  async init() {
    let records: Array<BpmRecord> = [...this.records];
    records = records.map((x) => {
      x.auditParam = x.auditParamJson ? JSON.parse(x.auditParamJson) : {};
      x._processNodeProps = x.processNodeProps
        ? JSON.parse(x.processNodeProps)
        : [];
      return x;
    });
    this.auditRecords = records;
  }

  dateFormate(time: any) {
    if (!time) {
      return "";
    }
    return moment(time).format("yyyy-MM-DD HH:mm:ss");
  }

  @Watch("$props.value")
  async valueChange() {
    await this.init();
  }
}
</script>

<style lang="scss" scoped>
.add-product-btn {
  font-size: 14px;
}
</style>