<!--
/**
 * 标签选择器组件
 *
 * @description 专门用于标签库的选择器组件，集成标签库弹窗，支持分类展示和多选功能
 * <AUTHOR>
 * @created 2025-07-23
 * @version 1.0.0
 *
 * @features
 * - 专门用于标签库的选择器组件
 * - 集成标签库弹窗，支持分类展示和多选功能
 * - 提供标签展示和快速删除功能
 * - 适用于需要从预定义标签库中选择标签的场景
 * - 点击展开选择：点击输入框区域打开标签库弹窗
 * - 标签展示：已选标签以标签形式展示在输入框中
 * - 快速删除：点击标签上的关闭按钮快速删除
 * - 分类选择：支持按分类浏览和选择标签
 * - 统计信息：显示标签的使用次数
 */
-->
<template>
    <div class="selector-component">
        <!-- 输入框部分 -->
        <div class="input-wrapper" @click="openDialog">
            <!-- 已选标签 -->
            <div class="content-area">
                <template v-if="showTags">
                    <div v-for="(tag, index) in selectedTags" :key="index" class="selected-tag">
                        <span>{{ tag }}</span>
                        <i class="el-icon-close tag-remove" @click.stop="removeTag(tag)"></i>
                    </div>
                </template>
                <div class="placeholder" v-if="!selectedTags.length || !showTags">{{ placeholder }}</div>
            </div>
            <div class="dropdown-icon">
                <img src="/img/home/<USER>" />
            </div>
        </div>

        <!-- 标签库弹窗 -->
        <y-tag-library-dialog
            :visible.sync="dialogVisible"
            :value="value"
            :dialog-title="dialogTitle"
            :options="options"
            :categories="categories"
            @confirm="handleConfirm"
            @close="handleClose"
        />
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";
import YTagLibraryDialog from "~/components/y/y-tag-library-dialog.vue";

/**
 * 分类选项接口定义
 */
interface CategoryOption {
    value: string;
    label: string;
}

/**
 * 标签选择器组件
 *
 * 专门用于标签库的选择器，支持分类展示选项、多选功能、标签展示
 */
@Component({
    name: "TagSelector",
    components: {
        YTagLibraryDialog
    }
})
export default class TagSelector extends Vue {
    /** 选中的值数组 */
    @Prop({ type: Array, default: () => [] }) readonly value!: string[];

    /** 占位符文本 */
    @Prop({ type: String, default: "请选择" }) readonly placeholder!: string;

    /** 弹窗标题 */
    @Prop({ type: String, default: "选择器" }) readonly dialogTitle!: string;

    /** 选项列表 */
    @Prop({ type: Array, default: () => [] }) readonly options!: Array<{ value: string; label: string; category?: string; assetRelNum?: number }>;

    /** 分类列表 */
    @Prop({ type: Array, default: () => [] }) readonly categories!: CategoryOption[];

    /** 是否显示标签 */
    @Prop({ type: Boolean, default: true }) readonly showTags!: boolean;

    /** 弹窗可见状态 */
    dialogVisible = false;

    /** 是否获得焦点 */
    focused = false;

    /**
     * 获取选中的标签列表
     */
    get selectedTags() {
        return this.options
            .filter(option => this.value.includes(option.value))
            .map(option => option.label);
    }

    /**
     * 打开选择器弹窗
     */
    openDialog() {
        this.dialogVisible = true;
        this.handleFocus();
    }

    /**
     * 处理获得焦点
     */
    handleFocus() {
        this.focused = true;
        this.$emit('focus');
    }

    /**
     * 处理失去焦点
     */
    handleBlur() {
        this.focused = false;
        this.$emit('blur');
    }

    /**
     * 手动触发焦点（供外部调用）
     */
    focus() {
        this.handleFocus();
    }

    /**
     * 手动触发失焦（供外部调用）
     */
    blur() {
        this.handleBlur();
    }

    /**
     * 处理弹窗关闭事件
     */
    handleClose() {
        this.dialogVisible = false;
        this.handleBlur();
    }

    /**
     * 处理确认选择
     */
    @Emit("input")
    handleConfirm(selectedValues: string[]) {
        // 同时触发change事件，用于表单验证
        this.$emit("change", selectedValues);
        // 选择完成后触发blur事件
        this.$nextTick(() => {
            this.handleBlur();
        });
        return selectedValues;
    }

    /**
     * 移除指定标签
     */
    removeTag(tag: string) {
        const option = this.options.find(opt => opt.label === tag);
        if (option) {
            const newValue = this.value.filter(val => val !== option.value);
            this.$emit("input", newValue);
            // 同时触发change事件，用于表单验证
            this.$emit("change", newValue);
        }
    }
}
</script>

<style lang="scss" scoped>
.selector-component {
    position: relative;
    width: 100%;
    max-width: 696px;

    .input-wrapper {
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 8px;
        gap: 6px;
        width: 100%;
        height: 40px;
        min-height: 40px;
        background-color: #171618;
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
            border-color: rgba(255, 255, 255, 0.3);
        }

        .content-area {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 6px;
            min-height: 21px;
            flex: 1;
            order: 1;
            overflow: hidden;

            .selected-tag {
                height: 24px;
                box-sizing: border-box;
                display: inline-flex;
                align-items: center;
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 28px;
                padding: 2px 10px;
                color: rgba(235, 235, 245, 0.7);
                font-size: 14px;
                flex-shrink: 0;
                transition: all 0.2s ease;

                &:hover {
                    border-color: rgba(255, 255, 255, 0.3);
                }

                .tag-remove {
                    margin-left: 8px;
                    cursor: pointer;
                    font-size: 12px;

                    &:hover {
                        color: rgba(255, 255, 255, 0.8);
                    }
                }
            }

            .placeholder {
                color: rgba(235, 235, 245, 0.7);
                font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 150%;
            }
        }

        .dropdown-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(235, 235, 245, 0.7);
            cursor: pointer;
            order: 2;
            flex: none;

            img {
                width: 18px;
                height: 18px;
            }

            &:hover {
                color: rgba(255, 255, 255, 1);
            }
        }
    }
}
</style>
