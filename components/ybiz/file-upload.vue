<!--
/**
 * 文件上传组件
 *
 * @description 支持图片和文档两种类型的文件上传，集成 fileuploadUtils 实现真实上传功能
 * <AUTHOR>
 * @created 2025-07-23
 * @version 2.0.0
 *
 * @features
 * - 支持图片和文档两种类型的文件上传
 * - 拖拽上传和点击上传双重支持
 * - 集成 fileuploadUtils 实现真实文件上传
 * - 支持 MD5 校验和秒传功能
 * - 支持分片上传和断点续传
 * - 支持阿里云 OSS 和服务器直传两种模式
 * - 文件预览和实时进度显示
 * - 文件类型验证和大小格式化
 * - 图片类型支持缩略图预览
 * - 文档类型显示文件图标和信息
 * - 支持加密上传和水印功能
 * - 文件删除和重试上传功能
 * - 完整的事件回调系统
 */
-->
<template>
    <div class="media-upload-container" :class="{ 'has-tabs': enableTabs && type === 'image' }">
        <!-- Tab切换区域 -->
        <div v-if="enableTabs && type === 'image'" class="upload-tabs">
            <div
                v-for="tab in imageTabs"
                :key="tab.type"
                class="tab-item"
                :class="{
                    active: activeTab === tab.type,
                    'upload-hovered': isUploadHovered && activeTab === tab.type
                }"
                @click="handleTabClick(tab.type, $event)"
                @mouseenter.stop="handleTabHover(tab.type)"
                @mouseleave.stop="handleTabLeave(tab.type)"
            >
                <y-svg-icon name="upload" />
                <span class="tab-label">{{ tab.label }}</span>
                <span v-if="getFileCountByType(tab.type) > 0" class="tab-count"> {{ `(${getFileCountByType(tab.type)})` }}</span>
            </div>
        </div>

        <el-upload
            ref="upload"
            class="upload"
            :class="{
                'has-files': currentFileList.length > 0,
                'with-tabs': enableTabs && type === 'image',
                'is-hovered': isUploadHovered
            }"
            drag
            :action="uploadAction"
            multiple
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-remove="handleRemove"
            :file-list="[]"
            :accept="acceptTypes"
            :show-file-list="false"
            :auto-upload="false"
            :key="uploadKey"
            @mouseenter.native="handleUploadHover"
            @mouseleave.native="handleUploadLeave"
        >
            <y-svg-icon name="picture" :size="18" />
            <div class="el-upload__text">
                <span
                    >拖放 或者
                    <em>浏览文件</em>
                </span>
                <p>{{ uploadTypeText }}</p>
            </div>
        </el-upload>

        <!-- 图片类型预览 -->
        <div v-if="currentFileList.length > 0 && type === 'image'" class="uploaded-images">
            <div v-for="(file, index) in currentFileList" :key="index" class="uploaded-image-item" :class="{ uploading: file.uploading }">
                <img :src="file.url" :alt="file.name" />
                <div v-if="!file.uploading" class="delete-btn" @click="removeFileFromList(index)">
                    <y-svg-icon name="delete" :size="18" color="#fff" />
                </div>
                <!-- 首图标识 -->
                <div v-if="!file.uploading" class="first-image-indicator" @click="toggleFirstImage(index)">
                    <img :src="index === 0 ? firstImg : setFirstImg" />
                </div>
                <div v-if="file.uploading" class="upload-progress">
                    <svg>
                        <circle class="progress-bg" cx="16" cy="16" r="14"></circle>
                        <circle class="progress-bar" cx="16" cy="16" r="14" :style="{ '--progress': file.progress || 0 }"></circle>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 文件类型预览 -->
        <div v-if="currentFileList.length > 0 && type === 'document'" class="uploaded-files">
            <div v-for="(file, index) in currentFileList" :key="index" class="uploaded-file-item">
                <div class="file-content">
                    <div class="file-icon">
                        <img :src="getFileIconSrc(file.name)" />
                    </div>
                    <div class="file-info">
                        <div class="file-name">{{ file.name }}</div>
                        <div class="file-size">{{ formatFileSize(file.size) }}</div>
                    </div>
                    <div class="file-actions">
                        <div class="preview-btn" @click="previewDocument(file)">
                            <y-svg-icon name="view-document" :size="18" />
                        </div>
                        <div class="delete-btn" @click="removeFileFromList(index)">
                            <y-svg-icon name="delete" :size="18" />
                        </div>
                    </div>
                </div>
                <div class="progress-bar-linear">
                    <div class="progress-fill" :style="{ '--progress': (file.progress || 0) + '%' }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import YSvgIcon from "../y/y-svg-icon.vue";
import fileuploadUtils from "~/utils/fileuploadUtils";
import { ResDoc } from "~/api/sys/fsApi";
import urlUtils from "~/utils/urlUtils";

// 图片类型枚举
type ImageType = "ELEMENT" | "EXTEND" | "PRODUCT" | "SCENE" | "THREE_VIEW";

// extraImages数据结构
interface ExtraImage {
    type: ImageType;
    fileId: string[];
}

interface FileListItem {
    name: string;
    url: string;
    size?: number;
    uid?: number | string;
    status?: string;
    uploading?: boolean;
    progress?: number; // 0-100
    file?: File; // 原始文件对象
    uploadedDoc?: ResDoc; // 上传成功后的文档信息
    mimeType?: string; // 文件类型
    type?: ImageType; // 图片类型
    uploadId?: string; // 上传任务ID，用于标识和取消上传
    intervalId?: NodeJS.Timeout; // 模拟上传时的定时器ID
    retryCount?: number; // 重试次数
    errorMessage?: string; // 错误信息
}

// 标签页配置
interface TabConfig {
    type: ImageType;
    label: string;
}

@Component({
    name: "FileUpload",
    components: {
        YSvgIcon
    }
})
export default class FileUpload extends Vue {
    /** 上传文件类型：image-图片，document-文档 */
    @Prop({ type: String, default: "image" }) readonly type!: "image" | "document";

    /** 上传接口地址 */
    @Prop({ type: String, default: "" }) readonly uploadAction!: string;

    /** 是否启用加密上传 */
    @Prop({ type: Boolean, default: false }) readonly secrecy!: boolean;

    /** 是否为图片添加水印 */
    @Prop({ type: Boolean, default: false }) readonly watermark!: boolean;

    /** 文件过期时间（秒），null表示永不过期 */
    @Prop({ type: Number, default: null }) readonly exp!: number | null;

    /** 用户唯一标识符 */
    @Prop({ type: String, default: "" }) readonly uuid!: string;

    /** 上传凭证票据 */
    @Prop({ type: String, default: "" }) readonly ticket!: string;

    /** 是否执行真实上传，false时仅模拟上传进度 */
    @Prop({ type: Boolean, default: true }) readonly realUpload!: boolean;

    /** 外部传入的文件列表（非标签页模式） */
    @Prop({ type: Array, default: () => [] }) readonly files!: FileListItem[];

    /** 是否启用图片分类标签页功能 */
    @Prop({ type: Boolean, default: false }) readonly enableTabs!: boolean;

    /** 外部传入的分类图片数据（标签页模式），可以是ExtraImage[]或FileListItem[] */
    @Prop({ type: Array, default: () => [] }) readonly extraImages!: ExtraImage[] | FileListItem[];

    /** 内部文件列表，存储所有上传的文件信息 */
    fileList: FileListItem[] = [];

    /** 当前激活的标签页类型 */
    activeTab: ImageType = "ELEMENT";

    /** 上传区域是否处于悬停状态 */
    isUploadHovered: boolean = false;

    /** 存储上传区域原始边框颜色 */
    private _originalBorderColor: string = "";

    /** 存储上传区域原始背景色 */
    private _originalBackground: string = "";

    /** 鼠标X坐标，用于标签页切换时的悬停状态判断 */
    private mouseX: number = 0;

    /** 鼠标Y坐标，用于标签页切换时的悬停状态判断 */
    private mouseY: number = 0;

    /** 图片分类标签页配置 */
    imageTabs: TabConfig[] = [
        { type: "ELEMENT", label: "元素图" },
        { type: "EXTEND", label: "扩展图" },
        { type: "PRODUCT", label: "产品图" },
        { type: "SCENE", label: "场景图" },
        { type: "THREE_VIEW", label: "三视图" }
    ];

    /** PDF文件图标 */
    pdfImg: string = require("~/static/img/icon/pdf.png");

    /** 首图标识图标 */
    firstImg: string = require("~/static/img/icon/first-img.png");

    /** 设置首图图标 */
    setFirstImg: string = require("~/static/img/icon/set-first-img.png");

    /**
     * 监听外部传入的files变化
     */
    @Watch("files", { immediate: true, deep: true })
    onFilesChange(newFiles: FileListItem[]) {
        if (!this.enableTabs) {
            if (newFiles && newFiles.length > 0) {
                this.fileList = [...newFiles];
            } else if (newFiles && newFiles.length === 0 && this.fileList.length > 0) {
                this.fileList = [];
            }
        }
    }

    /**
     * 监听外部传入的extraImages变化
     */
    @Watch("extraImages", { immediate: true, deep: true })
    onExtraImagesChange(newExtraImages: ExtraImage[] | FileListItem[]) {
        if (this.enableTabs && newExtraImages) {
            this.convertExtraImagesToFileList(newExtraImages);
        }
    }

    mounted() {
        // 初始化时同步外部传入的文件列表
        if (!this.enableTabs && this.files && this.files.length > 0) {
            this.fileList = [...this.files];
        } else if (this.enableTabs && this.extraImages && this.extraImages.length > 0) {
            this.convertExtraImagesToFileList(this.extraImages);
        }

        // 添加el-upload-dragger的事件监听和初始化样式
        this.$nextTick(() => {
            const draggerEl = this.$el.querySelector(".el-upload-dragger") as HTMLElement;
            if (draggerEl) {
                this._originalBorderColor = draggerEl.style.borderColor || "rgba(255, 255, 255, 0.15)";
                this._originalBackground = draggerEl.style.background || "#232124";

                draggerEl.style.setProperty("border-color", "rgba(255, 255, 255, 0.15)", "important");
                draggerEl.style.setProperty("background", "#232124", "important");

                draggerEl.addEventListener("mouseenter", () => {
                    this.handleUploadHover();
                });
                draggerEl.addEventListener("mouseleave", () => {
                    this.handleUploadLeave();
                });
            }
        });
    }

    /**
     * 处理上传区域鼠标悬停事件
     */
    handleUploadHover() {
        this.isUploadHovered = true;

        this.$nextTick(() => {
            if (this.$refs.upload) {
                const uploadEl = this.$refs.upload as Vue;
                const draggerEl = uploadEl.$el.querySelector(".el-upload-dragger") as HTMLElement;

                if (draggerEl) {
                    draggerEl.style.setProperty("border-color", "rgba(0, 111, 238, 1)", "important");
                    draggerEl.style.setProperty("background", "rgba(0, 111, 238, 0.15)", "important");
                }
            }
        });
    }

    /**
     * 处理上传区域鼠标离开事件
     */
    handleUploadLeave() {
        this.isUploadHovered = false;

        this.$nextTick(() => {
            if (this.$refs.upload) {
                const uploadEl = this.$refs.upload as Vue;
                const draggerEl = uploadEl.$el.querySelector(".el-upload-dragger") as HTMLElement;

                if (draggerEl) {
                    draggerEl.style.removeProperty("border-color");
                    draggerEl.style.removeProperty("background");
                    draggerEl.style.setProperty("border-color", this._originalBorderColor || "rgba(255, 255, 255, 0.15)", "important");
                    draggerEl.style.setProperty("background", this._originalBackground || "#232124", "important");
                }
            }
        });
    }

    /**
     * 处理标签页鼠标悬停事件
     * @param type 图片类型
     */
    handleTabHover(type: ImageType) {
        if (this.activeTab === type) {
            this.$set(this, "isUploadHovered", true);

            this.$nextTick(() => {
                if (this.$refs.upload) {
                    const uploadEl = this.$refs.upload as Vue;
                    const draggerEl = uploadEl.$el.querySelector(".el-upload-dragger") as HTMLElement;

                    if (draggerEl) {
                        draggerEl.style.setProperty("border-color", "rgba(0, 111, 238, 1)", "important");
                        draggerEl.style.setProperty("background", "rgba(0, 111, 238, 0.15)", "important");
                    } else {
                        const draggerEls = document.querySelectorAll(".el-upload-dragger");
                        if (draggerEls.length > 0) {
                            const draggerEl = draggerEls[0] as HTMLElement;
                            draggerEl.style.setProperty("border-color", "rgba(0, 111, 238, 1)", "important");
                            draggerEl.style.setProperty("background", "rgba(0, 111, 238, 0.15)", "important");
                        }
                    }
                }
            });
        }
    }

    /**
     * 处理标签页鼠标离开事件
     * 当离开激活标签页时，重置上传区域的悬停效果
     * @param type 图片类型
     */
    handleTabLeave(type: ImageType) {
        if (this.activeTab === type) {
            this.$set(this, "isUploadHovered", false);

            this.$nextTick(() => {
                if (this.$refs.upload) {
                    const uploadEl = this.$refs.upload as Vue;
                    const draggerEl = uploadEl.$el.querySelector(".el-upload-dragger") as HTMLElement;

                    if (draggerEl) {
                        draggerEl.style.removeProperty("border-color");
                        draggerEl.style.removeProperty("background");
                        draggerEl.style.setProperty("border-color", this._originalBorderColor || "rgba(255, 255, 255, 0.15)", "important");
                        draggerEl.style.setProperty("background", this._originalBackground || "#232124", "important");
                    } else {
                        const draggerEls = document.querySelectorAll(".el-upload-dragger");
                        if (draggerEls.length > 0) {
                            const draggerEl = draggerEls[0] as HTMLElement;
                            draggerEl.style.removeProperty("border-color");
                            draggerEl.style.removeProperty("background");
                            draggerEl.style.setProperty("border-color", this._originalBorderColor || "rgba(255, 255, 255, 0.15)", "important");
                            draggerEl.style.setProperty("background", this._originalBackground || "#232124", "important");
                        }
                    }
                }
            });
        }
    }

    /**
     * 计算属性：获取当前标签页的文件列表
     * 在标签页模式下返回当前激活标签页的文件，否则返回全部文件
     */
    get currentFileList(): FileListItem[] {
        if (!this.enableTabs) {
            return this.fileList;
        }
        return this.fileList.filter(file => file.type === this.activeTab);
    }

    /**
     * 根据图片类型获取对应的文件数量
     * @param type 图片类型
     * @returns 文件数量
     */
    getFileCountByType(type: ImageType): number {
        try {
            if (!this.isValidImageType(type)) {
                return 0;
            }
            return this.fileList.filter(file => file.type === type).length;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 切换标签页
     * @param type 要切换到的图片类型
     */
    switchTab(type: ImageType) {
        if (!this.isValidImageType(type)) {
            return;
        }

        this.activeTab = type;
        this.$emit("tab-change", { activeTab: type, fileCount: this.getFileCountByType(type) });

        this.$nextTick(() => {
            const activeTabEl = this.$el.querySelector(`.tab-item.active`) as HTMLElement;
            if (activeTabEl) {
                const rect = activeTabEl.getBoundingClientRect();
                const isMouseOverActiveTab = this.mouseX >= rect.left && this.mouseX <= rect.right && this.mouseY >= rect.top && this.mouseY <= rect.bottom;

                if (isMouseOverActiveTab) {
                    this.handleTabHover(type);
                } else {
                    this.isUploadHovered = false;
                }
            }
        });
    }

    /**
     * 将extraImages转换为内部fileList格式
     * @param extraImages 外部传入的分类图片数据，可能是ExtraImage[]或FileListItem[]
     */
    convertExtraImagesToFileList(extraImages: ExtraImage[] | FileListItem[]) {
        try {
            this.fileList = [];
            if (!Array.isArray(extraImages)) {
                return;
            }

            if (extraImages.length > 0 && "url" in extraImages[0]) {
                this.fileList = [...(extraImages as FileListItem[])];
                return;
            }

            (extraImages as ExtraImage[]).forEach(extraImage => {
                if (!extraImage || typeof extraImage !== "object") {
                    return;
                }

                if (!extraImage.type || !this.isValidImageType(extraImage.type)) {
                    return;
                }

                if (!Array.isArray(extraImage.fileId)) {
                    return;
                }

                extraImage.fileId.forEach(fileId => {
                    if (!fileId || typeof fileId !== "string") {
                        return;
                    }

                    const fileItem: FileListItem = {
                        name: `${extraImage.type}_${fileId}`,
                        url: urlUtils.file2url(fileId) || "",
                        uid: fileId,
                        uploading: false,
                        progress: 100,
                        type: extraImage.type,
                        uploadedDoc: { id: fileId } as ResDoc
                    };
                    this.fileList.push(fileItem);
                });
            });
        } catch (error) {
            this.fileList = [];
        }
    }

    /**
     * 验证图片类型是否有效
     * @param type 要验证的类型
     * @returns 是否为有效的图片类型
     */
    isValidImageType(type: string): type is ImageType {
        const validTypes: ImageType[] = ["ELEMENT", "EXTEND", "PRODUCT", "SCENE", "THREE_VIEW"];
        return validTypes.includes(type as ImageType);
    }

    /**
     * 验证文件大小是否符合要求
     * @param file 要验证的文件对象
     * @param maxSizeMB 最大文件大小（MB），默认50MB
     * @returns 验证结果
     */
    validateFileSize(file: File, maxSizeMB: number = 50): boolean {
        const maxSize = maxSizeMB * 1024 * 1024;
        if (file.size > maxSize) {
            this.$message.error(`文件大小不能超过${maxSizeMB}MB`);
            return false;
        }
        return true;
    }

    /**
     * 验证文件类型是否符合要求
     * 根据组件类型（image/document）验证对应的文件格式
     * @param file 要验证的文件对象
     * @returns 验证结果
     */
    validateFileType(file: File): boolean {
        if (this.type === "image") {
            const isValidType = file.type === "image/jpeg" || file.type === "image/png" || file.type === "image/gif";
            if (!isValidType) {
                this.$message.error("只能上传 JPG/PNG/GIF 格式的图片!");
                return false;
            }
        } else {
            const isPDF = file.type === "application/pdf";
            const isPPT = file.type === "application/vnd.ms-powerpoint" || file.type === "application/vnd.openxmlformats-officedocument.presentationml.presentation";

            if (!(isPDF || isPPT)) {
                this.$message.error("只能上传 PDF 或 PPT 格式的文件!");
                return false;
            }
        }
        return true;
    }

    /**
     * 验证标签页状态是否有效
     * 在标签页模式下检查当前激活的标签页类型是否合法
     * @returns 验证结果
     */
    validateTabState(): boolean {
        if (this.enableTabs && !this.isValidImageType(this.activeTab)) {
            this.$message.error("当前标签页类型无效，请刷新页面重试");
            return false;
        }
        return true;
    }

    /**
     * 计算属性：获取文件上传接受的文件类型
     * @returns MIME类型字符串
     */
    get acceptTypes(): string {
        return this.type === "image" ? "image/png,image/jpeg,image/gif" : "application/pdf,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation";
    }

    /**
     * 计算属性：获取上传提示文本
     * @returns 支持的文件类型说明文本
     */
    get uploadTypeText(): string {
        return this.type === "image" ? "支持文件类型PNG, JPG 或者 GIF" : "支持文件类型 PPT 或者 PDF";
    }

    /**
     * 计算属性：生成上传组件的唯一key
     * 用于在文件列表变化时强制重新渲染上传组件
     * @returns 唯一标识字符串
     */
    get uploadKey(): string {
        if (this.enableTabs) {
            return `${this.type}-${this.activeTab}-${this.currentFileList.length}`;
        }
        return `${this.type}-${this.fileList.length}`;
    }

    /**
     * 获取用户UUID
     */
    get _uuid(): string {
        return this.uuid ? this.uuid : fileuploadUtils.getUuid();
    }

    /**
     * 获取上传票据
     */
    get _ticket(): string | null {
        return this.ticket ? this.ticket : null;
    }

    /**
     * 根据文件扩展名获取对应的图标路径
     * @param fileName 文件名
     * @returns 图标文件路径
     */
    getFileIconSrc(fileName: string): string {
        const extension = fileName
            .split(".")
            .pop()
            ?.toLowerCase();

        if (extension === "pdf") {
            return this.pdfImg;
        } else if (extension === "ppt" || extension === "pptx") {
            return "/static/img/icon/ppt.png";
        }
        return "/static/img/icon/file.png";
    }

    /**
     * 格式化文件大小显示
     * @param size 文件大小（字节）
     * @returns 格式化后的大小字符串
     */
    formatFileSize(size?: number): string {
        if (!size) return "未知大小";

        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return (size / 1024).toFixed(1) + " KB";
        } else {
            return (size / (1024 * 1024)).toFixed(1) + " MB";
        }
    }

    /**
     * Element UI上传组件的beforeUpload钩子
     * @param file 待上传的文件对象
     */
    beforeUpload(file: File) {
        try {
            if (!this.validateFileType(file)) {
                return false;
            }

            if (!this.validateFileSize(file)) {
                return false;
            }

            if (!this.validateTabState()) {
                return false;
            }

            this.processFile(file);
            return false;
        } catch (error) {
            this.$message.error("文件验证失败，请重试");
            return false;
        }
    }

    /**
     * Element UI上传组件的文件变化处理
     * @param file 文件对象
     * @param fileList 文件列表
     */
    handleFileChange(file: any, fileList: any[]) {
        if (file.status === "ready") {
            this.processFile(file.raw);
        }
    }

    /**
     * 处理文件上传
     * @param file 待处理的文件对象
     */
    async processFile(file: File) {
        try {
            if (!file || !(file instanceof File)) {
                this.$message.error("无效的文件对象");
                return;
            }

            const fileItem: FileListItem = {
                name: file.name,
                url: "",
                size: file.size,
                uid:
                    Date.now() +
                    Math.random()
                        .toString(36)
                        .substring(2, 15),
                uploading: true,
                progress: 0,
                file: file,
                mimeType: file.type,
                type: this.enableTabs ? this.activeTab : undefined
            };

            this.fileList.push(fileItem);
            const fileIndex = this.fileList.length - 1;

            // 为图片类型生成预览
            if (this.type === "image") {
                try {
                    const data = await fileuploadUtils.readFile(file);
                    if (data && typeof data === "string") {
                        fileItem.url = data;
                    }
                } catch (error) {
                    this.$message.warning("图片预览生成失败，但不影响上传");
                }
            }

            // 根据配置选择上传方式
            if (this.realUpload) {
                this.realUploadFile(fileItem, fileIndex);
            } else {
                this.simulateUploadProgress(fileIndex);
            }

            // 通知父组件文件列表更新
            this.emitFileListUpdate();

            // 触发文件添加事件
            this.$emit("file-added", {
                file: fileItem,
                index: fileIndex,
                type: this.enableTabs ? this.activeTab : undefined
            });
        } catch (error) {
            this.$message.error("文件处理失败，请重试");

            this.$emit("process-error", {
                error,
                errorType: "process",
                message: "文件处理失败"
            });
        }
    }

    /**
     * 统一的文件列表更新事件发送
     */
    emitFileListUpdate() {
        try {
            const safeFileList = Array.isArray(this.fileList) ? [...this.fileList] : [];

            if (this.enableTabs) {
                this.$emit("update:extraImages", safeFileList);
            } else {
                this.$emit("update:files", safeFileList);
            }
        } catch (error) {
            console.error("Error in emitFileListUpdate:", error);
        }
    }

    /**
     * 处理上传成功事件
     * @param response 服务器响应
     * @param file 文件对象
     * @param fileList 文件列表
     */
    handleUploadSuccess(response: any, file: any, fileList: any[]) {
        this.$message.success("上传成功!");
        this.$emit("upload-success", response, file);
    }

    /**
     * 处理上传失败事件
     * @param error 错误信息
     * @param file 文件对象
     * @param fileList 文件列表
     */
    handleUploadError(error: any, file: any, fileList: any[]) {
        this.$message.error("上传失败!");
        this.$emit("upload-error", error, file);
    }

    /**
     * 处理文件移除事件（Element UI内置）
     * @param file 文件对象
     * @param fileList 文件列表
     */
    handleRemove(file: any, fileList: any[]) {
        this.fileList = fileList;
        this.$emit("update:files", this.fileList);
    }

    /**
     * 从文件列表中移除指定文件
     * @param index 文件在当前列表中的索引
     */
    removeFileFromList(index: number) {
        try {
            const currentList = this.currentFileList;
            const fileItem = currentList[index];

            if (!fileItem) {
                return;
            }

            const actualIndex = this.fileList.findIndex(f => f.uid === fileItem.uid);
            if (actualIndex === -1) {
                return;
            }

            // 如果文件正在上传，先取消上传
            if (fileItem.uploading) {
                if (fileItem.intervalId) {
                    clearInterval(fileItem.intervalId);
                    fileItem.intervalId = undefined;
                }

                fileItem.uploadId = undefined;

                this.$emit("upload-cancel", {
                    file: fileItem,
                    index: actualIndex,
                    reason: "removed"
                });
            }

            this.$emit("file-remove", {
                file: fileItem,
                index: actualIndex,
                type: fileItem.type
            });

            this.fileList.splice(actualIndex, 1);
            this.emitFileListUpdate();
        } catch (error) {
            this.$message.error("删除文件失败，请重试");
        }
    }

    /**
     * 切换首图位置
     * @param index 要设为首图的文件索引
     */
    toggleFirstImage(index: number) {
        if (index === 0) {
            return;
        }

        if (this.enableTabs) {
            const currentList = this.currentFileList;
            const selectedFile = currentList[index];

            const currentTypeFiles = this.fileList.filter(f => f.type === this.activeTab);
            const firstFileIndex = this.fileList.findIndex(f => f.uid === currentTypeFiles[0].uid);
            const selectedFileIndex = this.fileList.findIndex(f => f.uid === selectedFile.uid);

            this.fileList.splice(selectedFileIndex, 1);
            this.fileList.splice(firstFileIndex, 0, selectedFile);

            this.emitFileListUpdate();
        } else {
            const selectedFile = this.fileList[index];
            this.fileList.splice(index, 1);
            this.fileList.unshift(selectedFile);

            this.emitFileListUpdate();
        }
    }

    /**
     * 真实文件上传处理
     * @param fileItem 文件项对象
     * @param fileIndex 文件索引
     */
    async realUploadFile(fileItem: FileListItem, fileIndex: number) {
        if (!fileItem.file) {
            this.$emit("upload-error", {
                file: fileItem,
                error: new Error("上传文件对象为空"),
                errorType: "missing_file"
            });
            return;
        }

        const that = this;
        let retryCount = 0;
        const maxRetries = 3;

        const attemptUpload = async (): Promise<void> => {
            try {
                const onProgress = (e: { total: number; loaded: number; isTrusted: boolean }) => {
                    if (!e || typeof e.total !== "number" || typeof e.loaded !== "number") {
                        return;
                    }

                    const progress = Math.floor((e.loaded / e.total) * 100);
                    fileItem.progress = progress;

                    that.emitFileListUpdate();

                    that.$emit("upload-progress", {
                        file: fileItem,
                        progress: progress,
                        loaded: e.loaded,
                        total: e.total
                    });
                };

                const uploadedDoc = await fileuploadUtils.upload({
                    file: fileItem.file!,
                    secrecy: this.secrecy,
                    exp: this.exp,
                    uuid: this._uuid,
                    ticket: this._ticket,
                    watermark: this.watermark,
                    onProgress
                });

                if (!uploadedDoc || !uploadedDoc.id) {
                    throw new Error("上传成功但返回数据无效");
                }

                fileItem.uploading = false;
                fileItem.progress = 100;
                fileItem.uploadedDoc = uploadedDoc;

                if (this.type === "image" && !fileItem.url && uploadedDoc.id) {
                    fileItem.url = urlUtils.file2url(uploadedDoc.id) || "";
                }

                this.emitFileListUpdate();

                this.$emit("upload-success", {
                    file: fileItem,
                    response: uploadedDoc
                });

                this.$message.success(`${fileItem.name} 上传成功!`);
            } catch (error) {
                if (retryCount < maxRetries) {
                    retryCount++;
                    await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                    return attemptUpload();
                }

                fileItem.uploading = false;
                fileItem.progress = 0;
                fileItem.status = "error";

                this.emitFileListUpdate();

                this.$emit("upload-error", {
                    file: fileItem,
                    error: error,
                    retries: retryCount
                });

                this.$message.error(`${fileItem.name} 上传失败! 已重试${retryCount}次`);
            }
        };

        await attemptUpload();
    }

    /**
     * 模拟文件上传进度
     * 用于非真实上传模式下的进度展示
     * @param fileIndex 文件在列表中的索引
     */
    simulateUploadProgress(fileIndex: number) {
        const fileItem = this.fileList[fileIndex];
        if (!fileItem) {
            return;
        }

        const uploadId = Date.now().toString();
        fileItem.uploadId = uploadId;

        const stages = [
            { target: 30, speed: 200, increment: [5, 10] },
            { target: 70, speed: 300, increment: [3, 8] },
            { target: 95, speed: 500, increment: [1, 3] },
            { target: 100, speed: 1000, increment: [0.5, 1] }
        ];

        let currentStage = 0;

        const interval = setInterval(() => {
            if (fileItem.uploadId !== uploadId) {
                clearInterval(interval);
                return;
            }

            if (fileItem.progress! >= 100) {
                fileItem.uploading = false;
                fileItem.progress = 100;
                fileItem.status = "success";
                fileItem.uploadedDoc = {
                    id: `mock-${Date.now()}-${Math.random()
                        .toString(36)
                        .substring(2, 10)}`,
                    name: fileItem.name,
                    size: fileItem.size
                } as ResDoc;

                clearInterval(interval);

                if (this.type === "image" && !fileItem.url && fileItem.file) {
                    try {
                        fileuploadUtils
                            .readFile(fileItem.file)
                            .then(data => {
                                if (data && typeof data === "string") {
                                    fileItem.url = data;
                                    this.emitFileListUpdate();
                                }
                            })
                            .catch(() => {
                                // 静默处理预览生成失败
                            });
                    } catch (e) {
                        // 静默处理错误
                    }
                }

                this.emitFileListUpdate();

                this.$emit("upload-success", {
                    file: fileItem,
                    response: fileItem.uploadedDoc,
                    simulated: true
                });

                return;
            }

            const stage = stages[currentStage];

            if (fileItem.progress! >= stage.target && currentStage < stages.length - 1) {
                currentStage++;
            }

            const currentConfig = stages[currentStage];
            const [min, max] = currentConfig.increment;
            const increment = Math.random() * (max - min) + min;
            fileItem.progress = Math.min(100, (fileItem.progress || 0) + increment);

            this.emitFileListUpdate();

            this.$emit("upload-progress", {
                file: fileItem,
                progress: fileItem.progress,
                simulated: true
            });
        }, stages[currentStage].speed);

        fileItem.intervalId = interval;
    }

    /**
     * 预览图片文件
     * @param file 要预览的文件项
     */
    previewImage(file: FileListItem) {
        try {
            if (file.uploading) {
                this.$message.warning("文件正在上传中，请等待上传完成后预览");
                return;
            }

            let previewUrl: string | null = "";

            if (file.uploadedDoc && file.uploadedDoc.id) {
                previewUrl = urlUtils.file2url(file.uploadedDoc.id);
            } else if (file.url) {
                previewUrl = file.url;
            }

            if (previewUrl) {
                this.$emit("file-preview", {
                    file: file,
                    url: previewUrl,
                    type: "image"
                });

                window.open(previewUrl, "_blank");
            } else {
                this.$message.warning("无法生成预览链接，请重新上传文件");
            }
        } catch (error) {
            this.$message.error("预览图片失败，请重试");
        }
    }

    /**
     * 预览文档文件
     * @param file 要预览的文件项
     */
    previewDocument(file: FileListItem) {
        try {
            if (file.uploading) {
                this.$message.warning("文件正在上传中，请等待上传完成后预览");
                return;
            }

            let previewUrl: string | null = "";

            if (file.uploadedDoc && file.uploadedDoc.id) {
                previewUrl = urlUtils.file2url(file.uploadedDoc.id);
            } else if (file.url) {
                previewUrl = file.url;
            }

            if (previewUrl) {
                this.$emit("file-preview", {
                    file: file,
                    url: previewUrl,
                    type: "document",
                    extension: file.name
                        .split(".")
                        .pop()
                        ?.toLowerCase()
                });

                window.open(previewUrl, "_blank");
            } else {
                this.$message.warning("无法生成预览链接，请重新上传文件");
            }
        } catch (error) {
            this.$message.error("预览文档失败，请重试");
        }
    }

    /**
     * 处理标签页点击事件
     * @param type 图片类型
     * @param event 鼠标事件
     */
    handleTabClick(type: ImageType, event: MouseEvent) {
        this.mouseX = event.clientX;
        this.mouseY = event.clientY;
        this.switchTab(type);
    }
}
</script>

<style lang="scss" scoped>
@import "./file-upload.scss";
</style>
