// 媒体资源上传样式
.media-upload-container {
    width: 100%;

    // 标签页样式
    .upload-tabs {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        position: relative;
        width: 100%;
        height: 38px;

        .tab-item {
            box-sizing: border-box;
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 8px 16px 9px;
            gap: 4px;
            height: 38px;
            background: #232124;
            border-width: 1px 1px 0px 1px;
            border-style: solid;
            border-color: rgba(255, 255, 255, 0.15);
            border-radius: 16px 16px 0px 0px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #ffffff;
            z-index: 2;

            .tab-label {
                font-family: "PingFang SC";
                font-style: normal;
                font-weight: 600;
                font-size: 14px;
                line-height: 150%;
                transition: color 0.3s ease;
            }

            .tab-count {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 20px;
                padding: 0 6px;
                font-size: 14px;
                color: #ffffff;
            }

            &:hover {
                border-color: rgba(255, 255, 255, 0.5);

                .tab-label {
                    color: #ffffff;
                }
            }

            &.active {
                border-bottom: none;
                margin-bottom: 0;
                position: relative;
                border-style: dashed;
                border-width: 2px 2px 0px 2px;

                &::after {
                    content: "";
                    position: absolute;
                    bottom: -2px;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background: #232124;
                    z-index: 3;

                    &:hover {
                        background: rgba(0, 111, 238, 0.15) !important;
                    }
                }

                .tab-label {
                    color: #ffffff;
                }

                &:hover,
                &.upload-hovered {
                    border-color: rgba(0, 111, 238, 1) !important;
                    background: rgba(0, 111, 238, 0.15) !important;
                }
            }
        }
    }

    .upload {
        width: 100%;
        margin-bottom: 24px;

        &.has-files {
            margin-bottom: 0;
        }

        &.is-hovered .el-upload-dragger {
            border-color: rgba(0, 111, 238, 1) !important;
            background: rgba(0, 111, 238, 0.15) !important;
        }
    }

    // 自定义图片列表样式
    .uploaded-images {
        display: grid;
        grid-template-columns: repeat(auto-fill, 132px);
        gap: 8px;
        margin-top: 24px;

        .uploaded-image-item {
            position: relative;
            width: 132px;
            height: 128px;
            border-radius: 20px;
            overflow: hidden;
            background-color: rgba(255, 255, 255, 0.05);

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: #00000066;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 5;
            }

            &:hover::before {
                opacity: 1;
            }

            &.uploading::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: #00000066;
                z-index: 5;
            }

            .delete-btn {
                position: absolute;
                top: 8px;
                right: 8px;
                width: 24px;
                height: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                z-index: 15;
                opacity: 0;
                transition: opacity 0.3s;
            }

            .first-image-indicator {
                position: absolute;
                left: 16px;
                bottom: 12px;
                height: 22px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                z-index: 15;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }

            .upload-progress {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 36px;
                height: 36px;
                z-index: 15;

                svg {
                    width: 36px;
                    height: 36px;
                    transform: rotate(-90deg);

                    .progress-bg {
                        fill: none;
                        stroke: rgba(255, 255, 255, 0.3);
                        stroke-width: 4;
                    }

                    .progress-bar {
                        fill: none;
                        stroke: #fff;
                        stroke-width: 4;
                        stroke-linecap: round;
                        stroke-dasharray: 87.96;
                        stroke-dashoffset: calc(87.96 - 87.96 * var(--progress, 0) / 100);
                        transition: stroke-dashoffset 0.3s ease;
                    }
                }
            }

            &:hover .delete-btn {
                opacity: 1;
            }
        }
    }

    // 文件列表样式
    .uploaded-files {
        margin-top: 24px;

        .uploaded-file-item {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 16px;
            gap: 8px;
            width: 100%;
            height: 89px;
            margin-bottom: 12px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 20px;

            .file-content {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                gap: 12px;
            }

            .file-icon {
                width: 40px;
                height: 40px;
                border-radius: 4px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    border-radius: 4px;
                }
            }

            .file-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;

                .file-name {
                    height: 21px;
                    font-family: "PingFang SC";
                    font-style: normal;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 150%;
                    color: #ffffff;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .file-size {
                    height: 18px;
                    font-family: "PingFang SC";
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 150%;
                    color: rgba(235, 235, 245, 0.7);
                }
            }

            .file-actions {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 12px;

                .preview-btn,
                .delete-btn {
                    width: 18px;
                    height: 18px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }
            }

            .progress-bar-linear {
                height: 6px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;
                flex: none;
                order: 1;
                align-self: stretch;
                flex-grow: 0;
                position: relative;
                overflow: hidden;

                .progress-fill {
                    position: absolute;
                    height: 6px;
                    left: 0;
                    top: 0;
                    background: #ffffff;
                    border-radius: 3px;
                    transition: width 0.3s ease;
                    width: var(--progress, 0%);
                }
            }
        }
    }
}

// 覆盖Element UI样式
::v-deep {
    .el-upload {
        width: 100%;
        display: block;
    }

    .with-tabs {
        .el-upload-dragger {
            border-top-left-radius: 0 !important;
            z-index: 1;
        }
    }

    .el-upload-dragger {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 24px;
        gap: 16px;
        width: 100%;
        height: 250px;
        background: #232124 !important;
        border: 2px dashed rgba(255, 255, 255, 0.15) !important;
        border-radius: 20px;
        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 1;
        transition: all 0.3s ease;

        .y-svg-icon {
            color: #232124;
            width: 18px;
            height: 18px;
            flex: none;
            order: 0;
            flex-grow: 0;
            padding: 8px;
            margin: 0;
        }

        .el-upload__text {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 0px;
            gap: 6px;
            height: 45px;
            flex: none;
            order: 1;
            align-self: stretch;
            flex-grow: 0;
            margin: 0;

            > span {
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 0px;
                gap: 4px;
                width: auto;
                height: 21px;
                flex: none;
                order: 0;
                flex-grow: 0;
                color: #ffffff;
                font-family: "PingFang SC";
                font-style: normal;
                font-weight: 600;
                font-size: 14px;
                line-height: 150%;
            }

            em {
                color: #448ef4;
                font-family: "PingFang SC";
                font-style: normal;
                font-weight: 600;
                font-size: 14px;
                line-height: 150%;
                flex: none;
                order: 2;
                flex-grow: 0;
            }

            p {
                height: 18px;
                font-family: "PingFang SC";
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 150%;
                text-align: center;
                color: rgba(235, 235, 245, 0.7);
                flex: none;
                order: 1;
                align-self: stretch;
                flex-grow: 0;
                margin: 0;
            }
        }
    }
}
