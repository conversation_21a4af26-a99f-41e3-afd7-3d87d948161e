<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.sysbase-bpm") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        //- el-form-item(prop="keyword", label="关键词")
        //-   el-input(
        //-     v-model="query.keyword",
        //-     placeholder="关键词",
        //-     @keyup.enter.native="handleSearchClick"
        //-   )
        //- el-form-item
        //-   el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading>0"
    )
      el-table-column(prop="code", label="业务编号", width="230")
      el-table-column(prop="name", label="名称")
      el-table-column(prop="state", label="是否禁用")
        template(#default="scope")
          el-tag(:type="!scope.row.ban ? 'success' : 'danger'") {{ !scope.row.ban ? "启用中" : "禁用" }}
      //- el-table-column(prop="nodeFirstCode", label="第一个流程节点")
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")
      el-table-column(prop="processRecordId", label="当前版本Id", width="200")

      el-table-column(label="操作", width="250", align="center")
        template(#default="scope")
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            @click="handleTaskClick(scope.row)"
          ) 任务列表
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            @click="handleCopyClick(scope.row)"
          ) 复制
          el-popconfirm(
            title="确认要所有任务的流程版本到最新版吗？",
            @confirm="handleUpdateBpmProcess2newestClick(scope.row)"
          )
            el-button.mgl10(slot="reference", type="text") 更新版本
          el-popconfirm(
            title="确认要删除数据吗？",
            @confirm="handleDeleteClick(scope.row)"
          )
            el-button.mgl10(slot="reference", type="text") {{ $t("common.base.delete") }}
    //- .y-footer
    //-   //- 分页信息
    //-   .pagination
    //-     el-pagination(
    //-       background,
    //-       :page-sizes="[5, 10, 20, 50, 100]",
    //-       layout="total, sizes, prev, pager, next",
    //-       :current-page="query.page",
    //-       :page-size="query.rows",
    //-       :total="query.total",
    //-       @current-change="handlePageChange",
    //-       @size-change="handleSizeChange"
    //-     )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import contentApi, { BpmProcessListVo } from "~/api/basic/bpm/processApi";
import workflowApi from "~/api/basic/bpm/workflowApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

type TableContent = BpmProcessListVo;

const urlPre = "/sysbase/bpm";

@Component({
  name: "role-sysbase-bpm",
})
export default class SysbaseBpmIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    contents: <Array<TableContent>>[],
  };

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.keyword = compUtils.getQueryValue(this, "keyword", "");
    this.query.page = +compUtils.getQueryValue(this, "page", "1");
    this.query.rows = +compUtils.getQueryValue(this, "rows", "20");
    await this.loadContent();
  }

  async activated() {
    await this.init();
  }

  async loadContent() {
    this.loading++;
    try {
      this.bizData.contents = await contentApi.getAllList();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }

  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContent();
  }

  async handleAddClick() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${urlPre}/edit`);
  }

  async handleModifyClick(record: TableContent, edit: boolean = true) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(
      `/${role}${urlPre}/edit?code=${record.code}${edit ? "" : "&edit=false"}`
    );
  }

  async handleCopyClick(record: TableContent) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${urlPre}/edit?cpcode=${record.code}`);
  }

  async handleTaskClick(record: TableContent) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${urlPre}/workflow?process_code=${record.code}`);
  }

  async handleUpdateBpmProcess2newestClick(
    record: TableContent,
    edit: boolean = true
  ) {
    this.loading++;
    try {
      await workflowApi.batchUpdateBpmProcess2newest({
        processCodes: [record.code!],
      });
      await this.loadContent();
      this.$notify.success(<any>{ message: "刷新成功" });
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleDeleteClick(record: TableContent, edit: boolean = true) {
    this.loading++;
    try {
      await contentApi.delete(record.code!);
      await this.loadContent();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
    // this.$router.push(
    //   `${urlPre}/edit?code=${record.code}${edit ? "" : "&edit=false"}`
    // );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

