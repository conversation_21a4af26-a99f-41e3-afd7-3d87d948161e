<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 流程管理
      el-breadcrumb-item 任务列表
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        @submit.native.prevent="() => {}"
      )
        //- el-form-item(prop="add")
          //- el-button(
          //-   type="primary",
          //-   icon="el-icon-plus",
          //-   @click="handleAddClick"
          //- ) {{ $t("common.base.add") }}
        el-form-item(prop="states", label="状态")
          el-select(
            v-model="query.states",
            @change="handleSearchClick",
            multiple
          )
            el-option(
              v-for="option in $dic.getOptions('basic.bpm.workflow.state')",
              :key="option.value",
              :label="option.label",
              :value="option.value"
            )
        el-form-item(prop="keyword", label="关键词")
          el-input(
            v-model="query.keyword",
            placeholder="关键词",
            @keyup.enter.native="handleSearchClick"
          )
        el-form-item
          el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading>0",
      @expand-change="handleProductsExpandChange"
    )
      el-table-column(type="expand", label="节点信息")
        template(#default="scope")
          .workflow-detail(v-if="scope.row.records")
            el-form.demo-table-expand(
              label-position="left",
              label-width="80px"
            )
              el-form-item(label="流程id: ")
                div {{ scope.row.id }}
              el-form-item(label="名称: ")
                div {{ scope.row.name }}
              el-form-item(label="主题类型: ")
                el-tag(
                  :type="$dic.getFieldPropValue('basic.bpm.workflow.subjectType', scope.row.subjectType, 'tag-type', '')"
                ) {{ $dic.getFieldValue("basic.bpm.workflow.subjectType", scope.row.subjectType, "") }}
              el-form-item(label="主题Id: ")
                div {{ scope.row.subjectId }}
              el-form-item(label="节点详情: ")
                el-table(:data="scope.row.records")
                  //- el-table-column(label="ID", prop="id")
                  el-table-column(label="名称", prop="name")
                  el-table-column(prop="createTime", type="date", label="创建时间")
                    template(#default="scope")
                      div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
                  el-table-column(prop="finishTime", type="date", label="完成时间")
                    template(#default="scope")
                      div {{  scope.row.finishTime ? $moment(scope.row.finishTime).format("YYYY-MM-DD HH:mm:ss") : ''  }}
                  el-table-column(prop="difTime", label="耗时")
                    template(#default="scope")
                      div {{ time2dif(scope.row) }}
                  el-table-column(prop="invalid", type="date", label="有效性")
                    template(#default="scope")
                      el-tag(:type="!scope.row.invalid ? 'success' : 'danger'") {{ !scope.row.invalid ? "有效" : "无效" }}
                  el-table-column(prop="processNodeType", label="节点类型")
                    template(#default="scope")
                      el-tag(
                        :type="$dic.getFieldPropValue('basic.bpm.process.node.type', scope.row.processNodeType, 'tag-type', '')"
                      ) {{ $dic.getFieldValue("basic.bpm.process.node.type", scope.row.processNodeType, "") }}
                  el-table-column(label="状态", prop="state")
                    template(#default="scope")
                      template(
                        v-if="scope.row.processNodeType == 'AUDIT' && scope.row.state == 'FINISH'"
                      )
                        el-tag(
                          :type="$dic.getFieldPropValue('basic.bpm.workflow.record.astate', scope.row.auditResult, 'tag-type', '')"
                        ) {{ $dic.getFieldValue("basic.bpm.workflow.record.astate", scope.row.auditResult, "") }}
                      template(v-else)
                        el-tag(
                          :type="$dic.getFieldPropValue('basic.bpm.workflow.record.state', scope.row.state, 'tag-type', '')"
                        ) {{ $dic.getFieldValue("basic.bpm.workflow.record.state", scope.row.state, "") }}
                  el-table-column(label="操作", prop="operate")
                    template(#default="scope2")
                      el-button.mgl10(
                        v-if="scope2.row.todoDetailUrl",
                        type="text",
                        @click="handleToDetailClick(scope2.row)"
                      ) 去查看
                      template(v-if="scope2.row.state == 'AUDIT'")
                        el-button.mgl10(
                          v-if="scope2.row.todoAuditUrl",
                          type="text",
                          @click="handleToAuditClick(scope2.row)"
                        ) 去审核
                        el-button.mgl10(
                          @click="() => handleAuditClick(scope.row, scope2.row, 'PASS')",
                          type="text"
                        ) 强制审核通过
                        el-button.mgl10(
                          @click="() => handleAuditClick(scope.row, scope2.row, 'REJECT')",
                          type="text"
                        ) 强制审核驳回
                      template(v-if="scope2.row.state == 'PAUSE'")
                        el-popconfirm(
                          title="确认要强制执行此任务吗？",
                          @confirm="handlePauseClick(scope.row, scope2.row)"
                        )
                          el-button.mgl10(slot="reference", type="text") 强制执行

      el-table-column(prop="id", label="流程id")
      el-table-column(prop="name", label="名称")
      el-table-column(prop="subjectType", label="类型")
        template(#default="scope")
          el-tag(
            :type="$dic.getFieldPropValue('basic.bpm.workflow.subjectType', scope.row.subjectType, 'tag-type', '')"
          ) {{ $dic.getFieldValue("basic.bpm.workflow.subjectType", scope.row.subjectType, "") }}
      el-table-column(prop="subjectId", label="主题Id")
      el-table-column(prop="state", label="状态")
        template(#default="scope")
          el-tag(
            :type="$dic.getFieldPropValue('basic.bpm.workflow.state', scope.row.state, 'tag-type', '')"
          ) {{ $dic.getFieldValue("basic.bpm.workflow.state", scope.row.state, "") }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          template(
            v-if="scope.row.state == 'INIT' || scope.row.state == 'RUNNING'"
          )
            //- el-popover(placement="right", width="400", trigger="click")
            //-   el-table(:data="scope.row.auditList")
            //-     el-table-column(width="150", property="name", label="名称")
            //-     el-table-column(width="150", property="processNodeCode", label="节点Code")
            //-     el-table-column(width="150", property="processNodeCode", label="节点类型")
            //-     el-table-column(width="150", property="date", label="日期")
            //-   el-button(slot="reference") 审核节点
            el-popconfirm(
              title="确认要取消此任务吗？",
              @confirm="handleCancelClick(scope.row)"
            )
              el-button.mgl10(slot="reference", type="text") 强制取消
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import contentApi, {
  BasicBpmWorkflowGetListCountQueryParam,
  BasicBpmWorkflowGetListQueryParam,
  BpmWorkflow,
  BpmWorkflowAuditParamBo,
} from "~/api/basic/bpm/workflowApi";
import recordApi, {
  BpmWorkflowRecord,
} from "~/api/basic/bpm/workflow/recordApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";
import timeUtils from "~/utils/timeUtils";

type TableContent = BpmWorkflow & {
  records?: BpmWorkflowRecord[];
};

type GetListCountQueryParam = BasicBpmWorkflowGetListCountQueryParam;

type GetListQueryParam = BasicBpmWorkflowGetListQueryParam;

const urlPre = "/sysbase/bpm";

@Component({
  name: "role-sysbase-bpm-workflow",
})
export default class PageSysbaseBpmWorkflow extends mixins(BaseVue) {
  loading = 0;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    processCode: "",
    states: <string[]>[],
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    contents: <Array<TableContent>>[],
  };

  time2dif(row: BpmWorkflowRecord) {
    const m2s = (milliseconds: number) => {
      const totalSeconds = Math.floor(milliseconds / 1000);
      const days = Math.floor(totalSeconds / 86400);
      const hours = Math.floor((totalSeconds % 86400) / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      const pad = (n: number) => n.toString().padStart(2, "0");

      let s = "";
      if (days > 0) {
        s = s + `${pad(days)}天`;
      }
      if (hours > 0) {
        s = s + `${pad(hours)}时`;
      }
      if (minutes > 0) {
        s = s + `${pad(minutes)}分`;
      }
      if (seconds > 0) {
        s = s + `${pad(seconds)}秒`;
      }
      s = s ? s : "0秒";
      return s;
    };

    if (!row.finishTime) {
      let x = timeUtils.serverTime.getTime() - row.createTime!;
      return "已到达 " + m2s(x);
    }
    let x = row.finishTime! - row.createTime!;
    return m2s(x);
  }

  async created() {
    await this.initQuery();
  }

  async initQuery() {
    this.query.processCode = compUtils.getQueryValue(this, "process_code", "");
    this.query.keyword = compUtils.getQueryValue(this, "keyword", "");
    this.query.states = compUtils.getQueryValue(this, "states", []);
    this.query.page = +compUtils.getQueryValue(this, "page", "1");
    this.query.rows = +compUtils.getQueryValue(this, "rows", "20");
  }

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async mounted() {
    await this.init();
  }

  async loadContentCount() {
    let param: GetListCountQueryParam = {};

    param.process_code = this.query.processCode;
    param.states = this.query.states;
    param.keyword = this.query.keyword;
    routerUtils.putQueryValue(this, param);

    this.loading++;
    try {
      this.query.total = await contentApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async loadContent() {
    let param: GetListQueryParam = {};
    param.process_code = this.query.processCode;
    param.states = this.query.states;
    param.keyword = this.query.keyword;
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, param);

    param.page = param.page - 1;

    this.loading++;
    try {
      let contents: TableContent[] = await contentApi.getList(param);
      contents.map((x) => (x.records = []));
      // contents.map((x) => (x.pauseList = []));
      this.bizData.contents = contents;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  // 展开列表详情时触发
  async handleProductsExpandChange(row: TableContent) {
    if (row.records && row.records.length > 0) {
      return;
    }
    let records = await recordApi.getALLList({ workflowId: row.id! });
    this.$set(row, "records", [...records]);
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }

  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleAddClick() {
    let role = compUtils.getQueryValue(this, "role", "");
    let url = `/${role}${urlPre}/edit`;
    compUtils.toPage(this, "edit", url, {});
  }

  async handleCancelClick(record: TableContent, edit: boolean = true) {
    this.loading++;
    try {
      await contentApi.cancel(record.id!);
      this.$notify.success("操作成功");
      await this.loadContent();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleAuditClick(
    workflow: TableContent,
    record: BpmWorkflowRecord,
    audit: "PASS" | "REJECT"
  ) {
    let param: BpmWorkflowAuditParamBo = { audit };

    param.remark = await new Promise((r, j) => {
      let title = audit == "PASS" ? "请输入通过理由" : "请输入驳回理由";
      this.$prompt(title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /^.+$/,
        inputErrorMessage: "不能为空",
      })
        .then((res: any) => {
          r(res.value);
        })
        .catch(() => {
          r("");
        });
    });
    if (!param.remark) {
      return;
    }

    this.loading++;
    try {
      await contentApi.audit(record.id!, param);
      this.$notify.success("操作成功");
      await this.loadContent();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handlePauseClick(workflow: TableContent, record: BpmWorkflowRecord) {
    this.loading++;
    try {
      await contentApi.proceed(record.id!);
      this.$notify.success("操作成功");
      await this.loadContent();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleToDetailClick(item: BpmWorkflowRecord) {
    this.$router.push(item.todoDetailUrl!);
  }
  async handleToAuditClick(item: BpmWorkflowRecord) {
    this.$router.push(item.todoAuditUrl!);
  }

  async goBack() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${urlPre}`);
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.workflow-detail {
  padding: 10px;
}
</style>

