<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.mall-brand") }}
      el-breadcrumb-item {{ this.query.code ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="150px",
        size="small"
      )
        el-form-item(
          label="业务编号",
          prop="code",
          :rules="[$rule.required(null, '业务编号不能为空')]",
          required
        )
          el-input(
            v-model="formData.code",
            :disabled="!query.edit || formData.processRecordId ? true : false"
          )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name", :disabled="!query.edit")
        el-form-item(label="流程生效", prop="ban")
          el-switch(
            v-model="formData.ban",
            active-text="禁用",
            inactive-text="启用",
            active-color="red",
            inactive-color="green",
            :disabled="!query.edit"
          )
        el-form-item(label="业务通过回调类", prop="execPassClsName")
          el-input(
            v-model="formData.execPassClsName",
            :disabled="!query.edit",
            placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecuteProvider"
          )
        el-form-item(label="业务驳回回调类", prop="execRejectClsName")
          el-input(
            v-model="formData.execRejectClsName",
            :disabled="!query.edit",
            placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecuteProvider"
          )
        el-form-item(label="node执行前回调类", prop="execNodePreClsName")
          el-input(
            v-model="formData.execNodePreClsName",
            :disabled="!query.edit",
            placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecutePreProvider"
          )
        el-form-item(label="node执行后回调类", prop="execNodeFinishClsName")
          el-input(
            v-model="formData.execNodeFinishClsName",
            :disabled="!query.edit",
            placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecuteProvider"
          )
        el-form-item(label="详情页地址", :prop="`todoDetailUrl`")
          el-input(
            v-model="formData.todoDetailUrl",
            placeholder="/def/product/edit?id=${workflow.SubjectId}",
            :disabled="!query.edit"
          )
        el-form-item(label="审核页地址", :prop="`todoAuditUrl`")
          el-input(
            v-model="formData.todoAuditUrl",
            placeholder="/def/product/audit?id=${workflow.SubjectId}",
            :disabled="!query.edit"
          )
        el-form-item(label="")
          el-alert(title="详情与审核地址参数说明（节点中地址同理）", type="success")
            p workflow: 当前任务
            p record: 当前节点信息
            p param: 当前节点参数
        el-form-item(label="属性字段")
          .display-flex
            y-tag(
              v-model="formData.propCodes",
              type="array",
              :disabled="!query.edit"
            )
            el-popover(
              placement="bottom",
              title="约定字段",
              width="350",
              trigger="hover"
            )
              el-descriptions(:column="1", border)
                el-descriptions-item(label="BPM节点执行前状态(bpmPreState)")
                  el-button(
                    type="text",
                    @click="() => handleAddPropCodeClick('bpmPreState')"
                  ) 添加
                el-descriptions-item(label="BPM节点执行后状态(bpmState)")
                  el-button(
                    type="text",
                    @click="() => handleAddPropCodeClick('bpmState')"
                  ) 添加
                el-descriptions-item(label="BPM节点是否隐藏显示(hiddenNode)")
                  el-button(
                    type="text",
                    @click="() => handleAddPropCodeClick('hiddenNode')"
                  ) 添加
              el-button(slot="reference", type="text") +约定字段
        el-form-item(label-width="0")
          .node-window(ref="propertyDiv")
            el-tabs(tab-position="left")
              el-tab-pane(
                v-for="(node, idx) in formData.nodes",
                :key="idx",
                type="card"
              )
                span.item-label(slot="label")
                  span.mgr10 {{ node.name }} ({{ node.code }})
                  span.y-sorthandle.mgr10
                    i.el-icon-rank
                  el-popconfirm(
                    title="确认要删除数据吗？",
                    @confirm="handleNodeTagRemove(idx)"
                  )
                    span(slot="reference")
                      i.el-icon-delete
                .item-content
                  el-form-item(
                    label="节点code",
                    :prop="`nodes.${idx}.code`",
                    required,
                    :rules="[$rule.required(null, '节点code不能为空')]"
                  )
                    el-input(v-model="node.code", :disabled="!query.edit")
                  el-form-item(
                    label="节点名称",
                    :prop="`nodes.${idx}.name`",
                    required,
                    :rules="[$rule.required(null, '节点名称不能为空')]"
                  )
                    el-input(v-model="node.name", :disabled="!query.edit")
                  el-form-item(
                    label="类型",
                    :prop="`nodes.${idx}.type`",
                    required,
                    :rules="[$rule.required(null, '类型不能为空')]"
                  )
                    el-select(
                      v-model="node.type",
                      :disabled="!query.edit",
                      style="width: 100%"
                    )
                      el-option(
                        v-for="option in $dic.getOptions('basic.bpm.process.node.type')",
                        :key="option.value",
                        :label="option.label",
                        :value="option.value"
                      )
                  el-form-item(
                    v-for="(propCode, idx) in formData.propCodes",
                    :key="idx",
                    :label="propCode"
                  )
                    el-input(
                      v-model="node[`prop.${propCode}`]",
                      :disabled="!query.edit"
                    )

                  template(v-if="node.type != 'START' && node.type != 'END'")
                    el-form-item(
                      label="执行前调用接口",
                      :prop="`nodes.${idx}.skipNodeClsNamePre`"
                    )
                      el-input(
                        v-model="node.skipNodeClsNamePre",
                        placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecutePreProvider",
                        :disabled="!query.edit"
                      )
                    el-form-item(
                      label="跳过节点表达式",
                      :prop="`nodes.${idx}.skipNodeExpn`"
                    )
                      el-input(
                        v-model="node.skipNodeExpn",
                        type="textarea",
                        placeholder="跳过节点执行器表达式(js:返回值:boolean 是否跳过此节点)",
                        :disabled="!query.edit"
                      )

                    template(v-if="node.type == 'AUDIT'")
                      el-form-item(
                        label="审核角色",
                        :prop="`nodes.${idx}.auditRoleCode`",
                        required,
                        :rules="[$rule.required(null, '审核角色不能为空')]"
                      )
                        el-select(
                          v-model="node.auditRoleCode",
                          :disabled="!query.edit",
                          style="width: 100%"
                        )
                          el-option(
                            v-for="option in roleData",
                            :key="option.code",
                            :label="`${option.name}(${option.code})`",
                            :value="option.code"
                          )
                      el-form-item(
                        label="强制审核",
                        :prop="`nodes.${idx}.auditMust`",
                        required,
                        :rules="[$rule.required(null, '强制审核不能为空')]"
                      )
                        el-switch(
                          v-model="node.auditMust",
                          active-color="#13ce66",
                          active-text="是",
                          inactive-color="#cccccc",
                          inactive-text="否",
                          :disabled="!query.edit"
                        )
                      el-form-item(
                        label="待办列表不显示",
                        :prop="`nodes.${idx}.hiddenTodo`",
                        required,
                        :rules="[$rule.required(null, '待办列表不显示不能为空')]"
                      )
                        el-switch(
                          v-model="node.hiddenTodo",
                          active-color="#ff4949",
                          active-text="不显示",
                          inactive-color="#13ce66",
                          inactive-text="显示",
                          :disabled="!query.edit"
                        )
                      template(v-if="!node.hiddenTodo")
                        el-form-item(
                          label="详情页地址",
                          :prop="`nodes.${idx}.todoDetailUrl`"
                        )
                          el-input(
                            v-model="node.todoDetailUrl",
                            placeholder="/def/product/edit?id=${workflow.SubjectId}",
                            :disabled="!query.edit"
                          )
                        el-form-item(
                          label="审核页地址",
                          :prop="`nodes.${idx}.todoAuditUrl`"
                        )
                          el-input(
                            v-model="node.todoAuditUrl",
                            placeholder="/def/product/audit?id=${workflow.SubjectId}",
                            :disabled="!query.edit"
                          )
                      el-form-item(
                        label="驳回类型",
                        :prop="`nodes.${idx}.auditRejectType`",
                        required,
                        :rules="[$rule.required(null, '驳回类型不能为空')]"
                      )
                        el-select(
                          v-model="node.auditRejectType",
                          :disabled="!query.edit",
                          style="width: 100%"
                        )
                          el-option(
                            v-for="option in $dic.getOptions('basic.bpm.process.node.artype')",
                            :key="option.value",
                            :label="option.label",
                            :value="option.value"
                          )
                      template(v-if="node.auditRejectType == 'NODE_BACK'")
                        el-form-item(
                          label="驳回回退节点",
                          :prop="`nodes.${idx}.auditRejectBackNodeCode`",
                          required,
                          :rules="[$rule.required(null, '执驳回回退节点不能为空')]"
                        )
                          el-select(
                            v-model="node.auditRejectBackNodeCode",
                            :disabled="!query.edit",
                            style="width: 100%"
                          )
                            el-option(
                              v-for="option in formData.nodes",
                              :key="option.code",
                              :label="`${option.name}(${option.code})`",
                              :value="option.code"
                            )
                      el-form-item(
                        label="驳回回调",
                        :prop="`nodes.${idx}.auditRejectExecClsName`"
                      )
                        el-input(
                          v-model="node.auditRejectExecClsName",
                          placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecuteProvider",
                          :disabled="!query.edit"
                        )

                    template(v-if="node.type == 'ORDINAR'")
                      el-form-item(
                        label="执行器",
                        :prop="`nodes.${idx}.execClsName`"
                      )
                        el-input(
                          v-model="node.execClsName",
                          placeholder="需实现接口：com.yhert.project.cloud.module.basic.server.helper.bpm.provider.BpmExecuteProvider",
                          :disabled="!query.edit"
                        )
            el-button.mgb10(v-if="query.edit", @click="handlePropAddClick") 添加节点

        el-form-item(label="备注")
          el-input(
            v-model="formData.remark",
            type="textarea",
            :rows="2",
            :disabled="!query.edit"
          )
        el-form-item
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import contentApi, {
  BpmProcessAddVo,
  BpmProcessModifyVo,
  BpmProcessNodeBo,
  BpmProcessInfoBo,
  BpmProcessNodeLinkBo,
} from "~/api/basic/bpm/processApi";
import authRoleApi, { SysRole } from "~/api/auth/power/roleApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import sortableUtils from "~/utils/sortableUtils";

type Node = BpmProcessNodeBo & {
  [key in `prop.${string}`]?: string;
};

type FormData = BpmProcessAddVo & {
  propCodes?: string[];
  nodes?: Node[];
  nodeLinks?: Array<BpmProcessNodeLinkBo>;
};

const urlPre = "/sysbase/bpm";

let defNodes: Node[] = [
  {
    code: "START",
    type: "START",
    name: "开始",
    skipNodeClsNamePre: "",
    skipNodeExpn: "",
    auditRoleCode: "",
    auditMust: false,
    auditRejectType: undefined,
    auditRejectBackNodeCode: undefined,
    todoAuditUrl: "",
    todoDetailUrl: "",
    execClsName: "",
  },
  {
    code: "NODE",
    type: "ORDINAR",
    name: "开始",
    skipNodeClsNamePre: "",
    skipNodeExpn: "",
    auditRoleCode: "",
    auditMust: false,
    auditRejectType: undefined,
    auditRejectBackNodeCode: undefined,
    todoAuditUrl: "",
    todoDetailUrl: "",
    execClsName: "",
  },
  {
    code: "END",
    type: "END",
    name: "结束",
    skipNodeClsNamePre: "",
    skipNodeExpn: "",
    auditRoleCode: "",
    auditMust: false,
    auditRejectType: undefined,
    auditRejectBackNodeCode: undefined,
    todoAuditUrl: "",
    todoDetailUrl: "",
    execClsName: "",
  },
];

@Component({
  name: "role-sysbase-bpm-edit",
})
export default class SysbaseBpmEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    code: "",
    cpcode: "",
    edit: true,
  };
  formData: FormData = {
    propCodes: [],
    nodes: defNodes.map((x) => ({ ...x })),
    nodeLinks: [],
    ban: false,
  };

  roleData: Array<SysRole> = [];

  oneLoadParam = {
    inited: false,
    // ...param
  };

  async mounted() {
    await this.init();
    if (this.query.edit) {
      await this.initSortable();
    }
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
      await this.initParam();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initParam() {
    this.roleData = await authRoleApi.getList();
  }

  async initDetail() {
    this.query.code = compUtils.getQueryValue(this, "code", "");
    this.query.cpcode = compUtils.getQueryValue(this, "cpcode", "");
    this.query.edit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "edit", "true")
    );
    if (this.query.code || this.query.cpcode) {
      let data: BpmProcessInfoBo | null = null;
      if (this.query.code) {
        data = await contentApi.get(this.query.code);
      } else {
        data = await contentApi.get(this.query.cpcode);
      }
      let ndata: FormData = { ...data, propCodes: [], nodes: [] };
      let propCodeSet = new Set<string>();
      for (let node of data.nodes!) {
        let nf: Node = { ...node };
        nf.hiddenTodo = !!nf.hiddenTodo;
        nf.props = [];
        if (node.props) {
          for (let prop of node.props) {
            (<any>nf)[`prop.${prop.code}`] = prop.value;
            propCodeSet.add(prop.code!);
          }
        }
        ndata.propCodes = [...propCodeSet];
        nf.auditRejectType = nf.auditRejectType
          ? nf.auditRejectType
          : "PROCESS_REJECT";
        ndata.nodes!.push(nf);
      }
      this.formData = ndata;
    }
  }

  async handleAddPropCodeClick(code: string) {
    if (!this.formData.propCodes) {
      return;
    }
    if (this.formData.propCodes.indexOf(code) >= 0) {
      return;
    }
    this.formData.propCodes?.push(code);
  }

  async handlePropAddClick() {
    let nprop: BpmProcessNodeBo = {};
    nprop.code = "NODE_" + this.formData.nodes!.length;
    nprop.name = "新建节点" + this.formData.nodes?.length;
    nprop.type = "ORDINAR";
    nprop.auditRoleCode = "";
    nprop.auditMust = false;
    nprop.skipNodeExpn = "";
    nprop.skipNodeClsNamePre = "";
    nprop.execClsName = "";
    nprop.auditRejectType = "PROCESS_REJECT";
    nprop.auditRejectBackNodeCode = "";
    nprop.hiddenTodo = false;
    nprop.todoAuditUrl = "";
    nprop.todoDetailUrl = "";
    this.formData.nodes?.push(nprop);
  }

  async handlePropDeleteClick(idx: number) {
    this.formData.nodes?.splice(idx, 1);
  }

  async handleLinkAddClick() {
    let nprop: BpmProcessNodeLinkBo = {};
    nprop.processNodeCode = "";
    nprop.nextProcessNodeCode = "";
    this.formData.nodeLinks?.push(nprop);
  }

  async handleLinkDeleteClick(idx: number) {
    this.formData.nodeLinks?.splice(idx, 1);
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: FormData = { ...data };
      delete (<any>body).propCodes;
      body.nodes = [];
      for (let node of data.nodes!) {
        let nnode = { ...node };
        if ("START" == nnode.type || "END" == nnode.type) {
          nnode.auditMust = false;
          nnode.skipNodeClsNamePre = "";
          nnode.skipNodeExpn = "";
        }
        nnode.props = [];
        for (let propCode of data.propCodes!) {
          let v = (<any>nnode)[`prop.${propCode}`];
          v = v == undefined || v == null ? "" : v;
          nnode.props!.push({
            code: propCode,
            value: v,
          });
        }
        body.nodes.push(nnode);
      }
      {
        let nodes = body.nodes;
        body.nodeFirstCode = nodes.length > 0 ? nodes[0].code : "";
        let nodeLinks: BpmProcessNodeLinkBo[] = [];
        let lastNode: Node | null = null;
        for (let node of nodes) {
          if (lastNode == null) {
            lastNode = node;
            continue;
          }
          let link: BpmProcessNodeLinkBo = {};
          link.processNodeCode = lastNode.code;
          link.nextProcessNodeCode = node.code;
          nodeLinks.push(link);

          lastNode = node;
        }
        body.nodeLinks = nodeLinks;
      }
      if (this.query.code) {
        // 更新
        await contentApi.modify(this.query.code, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await contentApi.add(body);
        this.query.code = detail.code!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.code) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${urlPre}`);
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async handleNodeTagRemove(idx: number) {
    this.formData.nodes!.splice(idx, 1);
  }

  async initSortable() {
    let el: any = this.$refs.propertyDiv;
    let tbody = el.querySelector(".el-tabs__nav");
    sortableUtils.sortable(tbody, this, "formData.nodes", {
      handle: ".y-sorthandle",
      offset: 1,
    });
  }
}
</script>

<style lang="scss" scoped>
.node-window {
  padding: 30px;
  border: solid 1px #ddd;
  // min-height: 500px;

  .item-content {
    width: calc(100% - 80px);
  }
}
.display-flex {
  display: flex;
}
</style>
