<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 报告查询
      el-breadcrumb-item {{ this.id ? $t("pages.power.role.edit.title_edit") : $t("pages.power.role.edit.title_add") }}
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="reportQuery",
        label-width="80px",
        width="900px",
        :rules="rules",
        v-loading="loading"
      )
        el-form-item(
          label="Code",
          prop="code",
          required,
          :rules="$rule.required(null, 'Code不能为空')"
        )
          el-input(v-model="reportQuery.code")
        el-form-item(
          label="名称",
          prop="name",
          required,
          :rules="$rule.required(null, '名称不能为空')"
        )
          el-input(v-model="reportQuery.name")
        el-form-item(label="启用状态", prop="valid", required)
          el-switch(v-model="reportQuery.valid")
        el-form-item(label="执行总数查询", prop="execTotalQuery", required)
          el-switch(v-model="reportQuery.execTotalQuery")
        el-form-item(label="查询语句", prop="queryCmd", required)
          el-input(
            v-model="reportQuery.queryCmd",
            prop="queryCmd",
            type="textarea",
            :rows="10"
          )
          //- el-alert(type="info") 推荐格式：{服务}.{类型}.{字段}[.{功能}]，例如：auth.user.type、auth.user.type.orderList
        el-form-item(label="备注")
          el-input(
            v-model="reportQuery.remark",
            prop="remark",
            type="textarea",
            :rows="2"
          )
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, Vue } from "nuxt-property-decorator";
import reportQueryApi, {
  ReportQuery,
  ReportQueryVo,
} from "~/api/sys/report/queryApi";
import compUtils from "~/utils/compUtils";
import ruleUtils from "~/utils/ruleUtils";

@Component({
  name: "role-sysbase-report-query-edit",
})
export default class SysbaseReportQueryEdit extends Vue {
  loading = true;
  id: string = "";
  reportQuery: ReportQueryVo = {
    code: "",
    valid: true,
    execTotalQuery: false,
    name: "",
    remark: "",
  };
  get rules() {
    return {
      code: [
        ruleUtils.required(null, "Code不能为空"),
        {
          trigger: ["blur"],
          validator: async (rule: any, value: any, cb: any) => {
            let that = this;
            if (value) {
              let rqs = await reportQueryApi.getList({ code: value, rows: 1 });
              if (rqs.length > 0) {
                let rq = rqs[0];
                if (that.id && rq.id == that.id) {
                  cb();
                } else {
                  cb(new Error("Code与系统中现有Code冲突，请更换其它Code"));
                }
              } else {
                cb();
              }
            } else {
              cb();
            }
          },
        },
      ],
      name: [ruleUtils.required("名称不能为空")],
      queryCmd: [ruleUtils.required("查询语句不能为空")],
    };
  }

  async created() {
    this.id = compUtils.getQueryValue(this, "id", "");
    if (this.id) {
      this.reportQuery = await reportQueryApi.get(this.id);
    }
    this.loading = false;
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await ruleUtils.formCheck(this, "form"))) {
        return;
      }
      if (this.id) {
        // 更新
        await reportQueryApi.modify(this.id, this.reportQuery);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let reportQuery = await reportQueryApi.add(this.reportQuery);
        this.id = reportQuery.id!;
        this.reportQuery = reportQuery;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    if (window.history.length <= 1) {
      this.$router.push(
        `/${compUtils.getQueryValue(this, "role", "")}/sysbase/report/query`
      );
    } else {
      this.$router.back();
    }
  }
}
</script>
