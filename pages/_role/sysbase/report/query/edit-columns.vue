<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 报告查询
      el-breadcrumb-item 编辑{{ reportQuery.name }}({{ reportQuery.code }})的字段
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="reportQuery",
        label-width="80px",
        width="100%",
        v-loading="loading"
      )
        //- 表头
        .mgb20
          el-button(@click="() => handleInitColumnClick()") 初始化字段
        //- 表单内容
        div(ref="filedstable")
          el-table.table(
            :data="reportQuery.columns",
            border,
            ref="table",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(width="35", label="排序")
              template(#default="scope")
                .y-sorthandle
                  i.el-icon-rank
            el-table-column(prop="code", label="Code", width="150px")
              template(#default="scope")
                el-form-item(
                  label-width="0",
                  size="mini",
                  :prop="`columns.${scope.$index}.code`",
                  :rules="$rule.required(null, 'Code不能为空')"
                )
                  el-input(v-model="reportQuery.columns[scope.$index].code")
            el-table-column(prop="name", label="名称", width="200px")
              template(#default="scope")
                el-form-item(
                  label-width="0",
                  size="mini",
                  :prop="`columns.${scope.$index}.name`",
                  :rules="$rule.required(null, '名称不能为空')"
                )
                  el-input(v-model="reportQuery.columns[scope.$index].name")
            el-table-column(prop="type", label="类型", width="150px")
              template(#default="scope")
                el-form-item(
                  label-width="0",
                  size="mini",
                  :prop="`columns.${scope.$index}.type`",
                  :rules="$rule.required(null, '类型不能为空')"
                )
                  el-select(v-model="reportQuery.columns[scope.$index].type")
                    el-option(
                      v-for="item in $dic.getOptions('sys.report.column.type')",
                      :key="item.value",
                      :label="item.label",
                      :value="item.value"
                    )
            el-table-column(label="附带数据")
              template(#default="scope")
                el-form-item(
                  v-show="reportQuery.columns[scope.$index].type == 'DIC'",
                  size="mini",
                  :prop="`columns.${scope.$index}.dicCode`",
                  label="数据字典"
                )
                  el-select(
                    v-model="reportQuery.columns[scope.$index].dicCode",
                    style="width: 100%",
                    filterable
                  )
                    el-option(key="", label="(空)", value="")
                    el-option(
                      v-for="item in $dic.getList()",
                      :key="item.code",
                      :label="`${item.name}(${item.code})`",
                      :value="item.code"
                    )
                el-form-item(
                  v-show="reportQuery.columns[scope.$index].type == 'DATE'",
                  size="mini",
                  :prop="`columns.${scope.$index}.dateFormat`",
                  label="时间格式"
                )
                  el-input(
                    v-model="reportQuery.columns[scope.$index].dateFormat"
                  )
                div(
                  v-show="reportQuery.columns[scope.$index].type == 'BOOLEAN'"
                )
                  el-form-item(
                    size="mini",
                    :prop="`columns.${scope.$index}.trueValue`",
                    label="为真时值"
                  )
                    el-input(
                      v-model="reportQuery.columns[scope.$index].trueValue"
                    )
                  el-form-item(
                    size="mini",
                    :prop="`columns.${scope.$index}.falseValue`",
                    label="为假时值"
                  )
                    el-input(
                      v-model="reportQuery.columns[scope.$index].falseValue"
                    )
            el-table-column(prop="operate", label="操作", width="100px")
              template(#default="scope")
                el-popconfirm(
                  title="确认要删除此字段吗？",
                  @confirm="handleDeleteColumnClick(scope.$index)"
                )
                  el-button.red(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
          .mgb20
            el-button(@click="handleAddColumnClick") 添加字段

        el-form-item.mt10
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import reportQueryApi, {
  ReportQueryColumnBo,
  ReportQueryVo,
} from "~/api/sys/report/queryApi";
import { BaseVue } from "~/model/vue";
import sortableUtils from "~/utils/sortableUtils";

@Component({
  name: "role-sysbase-report-query-edit-columns",
})
export default class SysbaseReportQueryEditColumns extends mixins(BaseVue) {
  loading = true;
  id: string = "";

  reportQuery: ReportQueryVo = {
    code: "",
    valid: true,
    name: "",
    remark: "",
    columns: [],
  };

  async created() {
    this.id = <string>this.$route.query.id;
    if (this.id) {
      this.reportQuery = await reportQueryApi.get(this.id);
    }
    this.loading = false;
  }

  async mounted() {
    this.initSortable();
  }

  initSortable() {
    let el: any = this.$refs.filedstable;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "reportQuery.columns", {
      handle: ".y-sorthandle",
    });
  }

  async handleInitColumnClick() {
    try {
      await this.$confirm("现有字段数据将被清空，确认要初始化所有字段吗？", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      });
    } catch (e) {
      return;
    }

    this.loading = true;
    try {
      let defColumns = await reportQueryApi.getDefFields(this.id);
      this.reportQuery.columns = defColumns;
    } finally {
      this.loading = false;
    }
  }

  async handleAddColumnClick() {
    this.reportQuery.columns!.push({
      code: "",
      name: "",
      type: "STRING",
      dicCode: "",
      dateFormat: "",
    });
  }

  async handleDeleteColumnClick(index: number) {
    this.reportQuery.columns!.splice(index, 1);
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      // 更新
      await reportQueryApi.modifyColumns(this.id, {
        columns: this.reportQuery.columns,
      });
      let msg: any = this.$t("common.success.modify");
      this.$notify.success(msg);
      this.goBack();
    } catch (e) {
      if (this.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    if (window.history.length <= 1) {
      this.$router.push("/sysbase/report/query");
    } else {
      this.$router.back();
    }
  }
}
</script>
