<template lang="pug">
div
  client-only
    .crumbs
      el-breadcrumb(separator="/")
        el-breadcrumb-item
          i.el-icon-lx-cascades 报告查询
    .container
      .y-page-table
        //- 表头
        .y-header
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        //- 表单内容
        .y-body
          el-table(
            :data="reportQuerys",
            border,
            ref="table",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(
              prop="code",
              label="Code",
              align="center",
              width="200"
            )
            el-table-column(prop="name", label="名称", width="200")
            el-table-column(label="生效")
              template(#default="scope")
                el-tag(:type="scope.row.valid ? 'success' : 'danger'") {{ scope.row.valid ? "启用" : "停用" }}
            el-table-column(label="总数查询")
              template(#default="scope")
                el-tag(:type="scope.row.execTotalQuery ? 'success' : 'danger'") {{ scope.row.execTotalQuery ? "查询" : "不查询" }}
            el-table-column(label="创建时间")
              template(#default="scope")
                div {{ dateFormat(scope.row.createTime) }}
            el-table-column(prop="remark", label="备注")
            el-table-column(label="操作", width="230", align="center")
              template(#default="scope")
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleExport(scope.row)"
                ) {{ $t("common.base.export") }}
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleEdit(scope.row)"
                ) {{ $t("common.base.modify") }}
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleEditColumns(scope.row)"
                ) 字段
                el-popconfirm.mgl10(
                  title="确认要删除数据吗？",
                  @confirm="handleDelete(scope.row)"
                )
                  el-button.red(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
</template>


<script lang="ts">
import moment from "moment";
import { Component, mixins } from "nuxt-property-decorator";
import reportQueryApi, { ReportQuery } from "~/api/sys/report/queryApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import fileexportUtils from "~/utils/fileexportUtils";

@Component({
  name: "role-sysbase-report-query",
})
export default class SysbaseReportQueryIndex extends mixins(BaseVue) {
  loading = true;
  reportQuerys: Array<ReportQuery> = [];

  dateFormat(time: any) {
    return moment(time).format("yyyy-MM-DD hh:mm:ss");
  }

  async activated() {
    this.loading = true;
    this.refreshTable()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        console.error(e);
        this.loading = false;
        let msg: any = this.$t("common.error.reload");
        this.$notify.error({
          title: msg,
          message: "",
        });
      });
  }

  async refreshTable() {
    // 加载批量数据
    this.reportQuerys = await reportQueryApi.getList({});
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/sysbase/report/query/edit`
    );
  }

  async handleEdit(val: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/sysbase/report/query/edit?id=${val.id}`
    );
  }

  async handleEditColumns(val: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/sysbase/report/query/edit-columns?id=${val.id}`
    );
  }

  async handleExport(val: any) {
    if (!val.valid) {
      this.$notify.error("测试前请先使开关生效");
      return;
    }
    let that = this;
    that.$message.info({
      message: `开始进行 ${val.name} 相关数据导出，请稍后`,
      duration: 3000,
    });
    let ep = fileexportUtils.buildFileExport(val.code, {}, (pg) => {
      // console.log(JSON.stringify(pg));
      that.$message.info({
        message: `正在导出 ${val.name} 相关数据，请稍后，目前已导出量：${
          pg?.progress
        }${pg?.total && pg?.total > 0 ? "/" + pg?.total : ""}`,
        duration: 1000,
      });
    });
    try {
      let url = await ep.submitAndGetUrl();
      that.$message.info({
        message: `导出 ${val.name} 相关数据完成，请下载`,
        duration: 5000,
      });
      window.location.href = url;
    } catch (error: any) {
      this.$message.error(`导出数据出现异常：${JSON.stringify(error.result)}`);
    }
  }

  async handleDelete(val: any) {
    try {
      await reportQueryApi.delete(val.id);
      let msg: any = this.$t("common.success.delete");
      this.$message.success(msg);
      await this.refreshTable();
    } catch (error) {
      let msg: any = this.$t("common.error.delete");
      this.$message.error(msg);
      throw error;
    }
  }
}
</script>
