<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar 站内信
      el-breadcrumb-item 发送器
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="form",
        label-width="100px",
        v-loading="loading",
        :rules="rules"
      )
        el-form-item(label="用户ID", prop="userId", required)
          y-select(
            v-model="form.userId",
            filterable,
            remote,
            :remote-method-page="handleUserIdRemoteMethod",
            style="width: 100%"
          )
            el-option(
              v-for="(item, idx) in bizData.userList",
              :key="idx",
              :value="item.id",
              :label="`${item.name}(${item.username})`"
            )
        el-form-item(label="业务编号", prop="businessCode", required)
          el-input(v-model="form.businessCode", autocomplete="off")
        el-form-item(label="类型", prop="type", required)
          el-input(v-model="form.type", autocomplete="off")
        el-form-item(label="标题", prop="title", required)
          el-input(v-model="form.title", autocomplete="off")
        el-form-item(label="消息类型", prop="msgType", required)
          el-select(v-model="form.msgType")
            el-option(value="TEXT", label="文本")
            el-option(value="HTML", label="HTML")
        el-form-item(label="内容", prop="msg", required)
          el-input(
            v-if="form.msgType == 'TEXT'",
            v-model="form.msg",
            type="textarea",
            :rows="10",
            autocomplete="off"
          )
          ym-richtext(v-if="form.msgType == 'HTML'", v-model="form.msg")
        el-form-item(label="跳转数据集合", prop="toData")
          el-input(v-model="form.toData", autocomplete="off")
        el-form-item(label="跳转目标编号", prop="toPage")
          el-input(v-model="form.toPage", autocomplete="off")
        el-form-item
          el-button(type="primary", @click="submit") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue } from "nuxt-property-decorator";
import YmRichtext from "~/components/ym/richtext.vue";
import msgInboxApi, { MsgInboxSendBo } from "~/api/sys/msgInboxApi";
import { BaseVue } from "~/model/vue";
import ruleUtils from "~/utils/ruleUtils";
import { RemoteMethodParam } from "~/components/y/select.vue";
import authUserInfoApi, {
  AuthUserInfoGetListQueryParam,
  UserListVo,
} from "~/api/auth/user/infoApi";

@Component({
  name: "role-sysbase-msg-inbox-sender",
  components: {
    YmRichtext,
  },
})
export default class SysbaseMsgInboxSender extends mixins(BaseVue) {
  loading = false;
  form: MsgInboxSendBo = {
    // 用户id
    userId: "adminid",
    // 业务编号
    businessCode: "sys",
    // 类型
    type: "msg",
    msgType: "TEXT",
    // 标题
    title: "测试消息",
    // 内容
    msg: "这是测试消息正文",
    // 跳转数据集合
    toData: "",
    // 跳转目标编号
    toPage: "",
  };
  rules = {
    userId: [ruleUtils.required(null, "用户ID不能为空")],
    businessCode: [ruleUtils.required(null, "业务编号不能为空")],
    type: [ruleUtils.required(null, "类型不能为空")],
    title: [ruleUtils.required(null, "标题不能为空")],
    msg: [ruleUtils.required(null, "内容不能为空")],
  };
  bizData = {
    userList: <UserListVo[]>[],
  };

  async handleUserIdRemoteMethod(e: RemoteMethodParam) {
    let param: AuthUserInfoGetListQueryParam = {};
    param.keyword = e.query;
    param.rows = 20;
    param.page = e.page;
    this.bizData.userList = await authUserInfoApi.getList(param);
    if (this.bizData.userList.length < param.rows) {
      e.end();
    }
  }

  async submit() {
    let that = this;
    this.loading = true;
    try {
      if (!(await ruleUtils.formCheck(this, "form"))) {
        return;
      }
      let body: MsgInboxSendBo = { ...this.form };
      await msgInboxApi.send(body);
      this.$notify.success("发送消息成功");
      // this.goBack();
    } finally {
      this.loading = false;
    }
  }

  goBack() {
    this.$router.back();
  }
}
</script>
