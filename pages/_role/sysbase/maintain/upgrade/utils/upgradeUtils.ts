import timeUtils from "~/utils/timeUtils";
import upgradeExecApi from "~/api/sys/maintain/upgrade/execApi";
import arrayUtils from "~/utils/arrayUtils";
import moment from "moment";

export type BakDataTableField = {
  code?: string;
  type?: string;
};

export type BakDataTable = {
  code?: string;
  fields?: BakDataTableField[];
  data?: string;
};

export type BakData = {
  version?: string;
  createTime?: Date;
  tables?: BakDataTable[];
};

export type DifData = {
  select?: boolean;
  type?: "up" | "add" | "del";
  key?: string;
  dataOld?: any;
  dataNew?: any;
};

const encodeBase64 = (input: string): string => {
  return Buffer.from(input, 'utf-8').toString('base64');
};

async function requestBak(tables: string[], pgrcb: (value: number, total: number) => void): Promise<BakData> {
  if (!tables || tables.length <= 0) {
    throw new Error('参数不能为空');
  }
  let bak: BakData = {};
  bak.version = "1.0";
  bak.createTime = timeUtils.serverTime;
  bak.tables = [];
  let total = tables.length;
  let i = 0;
  pgrcb(i, total);
  for (let table of tables) {
    let tb: BakDataTable = {};
    tb.code = table;

    let sql = `SELECT * FROM ${table} `;
    let sqlBase64 = encodeBase64(sql);
    let rs = await upgradeExecApi.getQuery({ sql: sqlBase64 });

    tb.fields = rs.fields;
    tb.data = JSON.stringify(rs.data);

    bak.tables.push(tb);

    i++;
    pgrcb(i, total);
  }
  return bak;
}

function __getValueByField(d?: any, f?: BakDataTableField) {
  if (!f || !d) {
    throw Error("导出出错，字段类型异常");
  }
  let val = d[f.code!];
  if (val === undefined) {
    throw Error("导出出错，字段类型异常undefined：" + JSON.stringify(f) + ", 数据：" + JSON.stringify(d));
  }
  if (val == null) {
    return 'null';
  }

  // 类型适配
  if (f.type == 'varchar' || f.type == 'text') {
    let a: string = val;
    a = a.replaceAll('\'', '\'\'');
    return `'${a}'`;
  } else if (f.type == 'bool') {
    if (typeof val == 'boolean') {
      return val ? 'true' : 'false';
    } else if (val instanceof Boolean) {
      return val ? 'true' : 'false';
    } else if (val instanceof Number) {
      return val == 0 ? 'false' : 'true';
    } else {
      throw Error("导出出错，值类型不被支持：" + JSON.stringify(f) + ", 数据：" + JSON.stringify(d));
    }
  } else if (f.type == 'int' || f.type == 'int4' || f.type == 'int8') {
    if (typeof val == 'number') {
      return val;
    } else if (val instanceof Number) {
      return val;
    } else {
      throw Error("导出出错，值类型不被支持：" + JSON.stringify(f) + ", 数据：" + JSON.stringify(d));
    }
  } else if (f.type == 'timestamp') {
    if (typeof val == 'number') {
      return "'" + moment(val).format('yyyy-MM-DD HH:mm:ss') + "'";
    } else {
      throw Error("导出出错，值类型不被支持：" + JSON.stringify(f) + ", 数据：" + JSON.stringify(d));
    }
  } else {
    throw Error("导出出错，字段类型不被支持：" + JSON.stringify(f) + ", 数据：" + JSON.stringify(d));
  }

  return '';
}

async function margin(code: string, data: DifData, fields: BakDataTableField[], pks: string[], excludeFields: string[]): Promise<number> {
  // console.log('===执行>>>' + data.type + "|" + data.dataOld)
  if (!fields || fields.length <= 0 || !pks || pks.length <= 0) {
    return 0;
  }
  if (!pks || pks.length <= 0) {
    throw new Error(`数据表${code}未配置pks核心字段，请检查配置`);
  }
  let code2field = arrayUtils.array2map(fields, 'code');

  let sql = '';
  if (!data || !data.type) {
    return 0;
  } else if (data.type == 'del') {
    let d = data.dataNew;
    if (!d) {
      return 0;
    }
    sql = `DELETE FROM ${code} WHERE 1=1`;
    for (let pk of pks) {
      let field = code2field.get(pk);
      sql = sql + ` AND ${field?.code}=${__getValueByField(d, field)}`;
    }
    console.log('执行SQL：' + sql);
    upgradeExecApi.delete({ sql: encodeBase64(sql) });
  } else if (data.type == 'add') {
    let d = data.dataOld;
    if (!d) {
      return 0;
    }
    let fstr = '';
    let vstr = '';
    for (let field of fields) {
      if (fstr) {
        fstr = fstr + ', ';
        vstr = vstr + ', ';
      }
      fstr = fstr + field.code;
      vstr = vstr + __getValueByField(d, field);
    }
    sql = `INSERT INTO ${code}(${fstr}) VALUES (${vstr})`;
    console.log('执行SQL：' + sql);
    return (await upgradeExecApi.insert({ sql: encodeBase64(sql) })).count!;
  } else if (data.type == 'up') {
    let d = data.dataOld;
    if (!d) {
      return 0;
    }
    let setStr = '';
    let whereStr = '';
    for (let field of fields) {
      if (pks.indexOf(field.code!) >= 0) {
        if (whereStr) {
          whereStr = whereStr + ' AND ';
        }
        whereStr = whereStr + `${field!.code}=${__getValueByField(d, field)}`;
      }
      if (excludeFields.indexOf(field.code!) < 0) {
        if (setStr) {
          setStr = setStr + ', ';
        }
        setStr = setStr + `${field?.code}=${__getValueByField(d, field)}`;
      }
    }
    if (setStr.length <= 0) {
      return 0;
    }
    sql = `UPDATE ${code} SET ${setStr} WHERE ${whereStr}`;
    console.log('执行SQL：' + sql);
    return (await upgradeExecApi.update({ sql: encodeBase64(sql) })).count!;
  } else {
    let a: never = data.type;
  }
  return 0;
}


export default {
  requestBak,
  margin,
}


