<template lang="pug">
.win-load
  .form
    el-form(
      ref="form",
      :model="formData",
      label-width="100",
      width="100%",
      v-loading="loading > 0"
    )
      el-form-item(
        label="加载数据",
        required,
        :rules="$rule.required(null, '加载数据不能为空')"
      )
        input(
          type="file",
          @input="handleFileChange",
          accept="application/json",
          ref="uploadFile"
        )
        el-button(@click="handleReloadClick") 重选文件
  .pgr(v-if="bizData.readyPgrValue != bizData.readyPgrTotal")
    h3 执行中，请稍后...
    el-progress(
      :text-inside="true",
      :stroke-width="26",
      :percentage="Math.floor((bizData.readyPgrValue / bizData.readyPgrTotal) * 100)",
      :status="bizData.readyPgrValue == bizData.readyPgrTotal ? 'success' : null"
    )
  .win(v-if="bizData.readyPgrValue == bizData.readyPgrTotal")
    .data-step1(v-if="bizData.step == 2")
      el-form(
        ref="step2",
        :model="bizData.step2",
        label-width="100",
        width="100%",
        v-loading="loading > 0"
      )
        el-form-item(
          label="需要分析的内容",
          prop="tables",
          required,
          :rules="$rule.required(null, '需要分析的内容不能为空')"
        )
          el-checkbox-group(v-model="bizData.step2.tables")
            el-checkbox(
              v-for="item in bizData.step2.selectTables",
              :label="item.code",
              :key="item.code"
            ) {{ item.label }}({{ item.code }})
        el-form-item
          el-button(type="primary", @click="step2submit()") 开始分析
    .data-step2(v-if="bizData.step == 3")
      div
        el-button(@click="() => { bizData.step--; }") 上一步
        el-button(type="primary", @click="() => submit()") 执行修改
      el-tabs(v-model="bizData.step3.tab")
        el-tab-pane(
          v-for="item in bizData.step3.data",
          :label="`${item.dicfield.label}(${item.code})`",
          :name="item.code",
          :key="item.code"
        )
          template(v-if="item.error")
            span {{ item.error }}
          template(v-else)
            el-table(
              :data="item.data",
              border,
              header-cell-class-name="table-header",
              @selection-change="(list) => handleSelectionChange(item, list)",
              :row-class-name="step3RowClassName"
            )
              el-table-column(type="selection", width="55")
              el-table-column(label="类型", prop="type", width="50")
                template(#default="scope")
                  span(v-if="scope.row.type == 'add'") 新增
                  span(v-if="scope.row.type == 'up'") 更新
                  span(v-if="scope.row.type == 'del'") 删除
              el-table-column(
                v-for="field in item.fields",
                :label="field.code",
                :prop="field.code",
                :key="field.code"
              )
                template(#default="scope")
                  div(
                    v-if="scope.row.type == 'up' && !fieldEqual(field.code, scope.row.dataOld, scope.row.dataNew)",
                    :style="{ color: 'red' }"
                  )
                    p 当前内容：{{ scope.row.dataNew ? scope.row.dataNew[field.code] : "" }}
                    p
                    p 目标内容：{{ scope.row.dataOld ? scope.row.dataOld[field.code] : "" }}
                  div(v-else)
                    p {{ scope.row.dataOld ? scope.row.dataOld[field.code] : scope.row.dataNew[field.code] }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import { SysDicFieldBo } from "~/api/auth/dic/dicApi";
import upgradeUtils, {
  BakData,
  BakDataTable,
  BakDataTableField,
  DifData,
} from "./utils/upgradeUtils";
import arrayUtils from "~/utils/arrayUtils";

type Step3Data = {
  code?: string;
  pks?: string[];
  excludeFields?: string[];
  error?: string;
  fields?: BakDataTableField[];
  dicfield?: SysDicFieldBo;
  dataNew?: BakDataTable;
  dataOld?: BakDataTable;
  data?: DifData[];

  selected?: DifData[];
};

const DIC_UPGRADE_CODE = "sys.maintain.upgrade.tables";

@Component({
  name: "role-sysbase-maintain-upgrade-load",
})
export default class SysbaseMaintainUpgradeLoad extends mixins(BaseVue) {
  loading = 0;
  query = {};
  formData = {};
  oneLoadParam = {
    inited: false,
    initCount: 0,
    // ...param
    dicFields: <SysDicFieldBo[]>[],
  };
  bizData = {
    readyPgrTotal: 0,
    readyPgrValue: 0,
    step: 0,
    step1: {
      bakJson: "",
      bak: <BakData | null>null,
    },
    step2: {
      selectTables: <SysDicFieldBo[]>[],
      tables: <string[]>[],
      newData: <BakData | null>null,
    },
    step3: {
      tab: "",
      data: <Step3Data[]>[],
    },
  };

  step3RowClassName(scope: { row: DifData; rowIndex: number }) {
    if (!scope.row.type) {
      return;
    } else if (scope.row.type == "up") {
      return "warning-row";
    } else if (scope.row.type == "add") {
      return "success-row";
    } else if (scope.row.type == "del") {
      return "danger-row";
    } else {
      let a: never = scope.row.type;
    }
  }

  async handleSelectionChange(item: Step3Data, list: DifData[]) {
    item.selected = list;
  }

  async mounted() {
    await this.init();
  }

  async init() {
    this.loading++;
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.oneLoadParam.initCount++;
    try {
      await this.initDetailBefor();
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initDetailBefor() {}

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...

    this.oneLoadParam.dicFields = this.$dic.getFields(DIC_UPGRADE_CODE);
  }

  async initDetail() {
    let fields = this.$dic.getFields(DIC_UPGRADE_CODE);
  }

  async handleReloadClick() {
    if (!(await this.confirm("确认重新选择文件开始导入吗?"))) {
      return;
    }

    (<any>this.$refs.uploadFile).value = "";
    this.clear();
  }

  clear() {
    this.bizData.step = 1;
    this.bizData.step1 = { bakJson: "", bak: null };
    this.bizData.step2 = { tables: [], newData: null, selectTables: [] };
    this.bizData.step3 = { tab: "", data: [] };
  }

  confirm(content: string) {
    let that = this;
    return new Promise((r, j) => {
      that
        .$confirm(content, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => r(true))
        .catch(() => r(false));
    });
  }

  async readByFile(file: File) {
    return new Promise<string>((r, j) => {
      // 使用 FileReader 读取文件内容
      const reader = new FileReader();

      reader.onload = (e: ProgressEvent<FileReader>) => {
        const content = e.target?.result as string; // 获取文件内容
        try {
          r(content);
        } catch (err) {}
      };

      reader.onerror = () => {
        j("加载文件出错");
      };

      reader.readAsText(file); // 读取文件为文本
    });
  }

  async handleFileChange(event: Event) {
    let files = (<HTMLInputElement>event.target!).files;
    if (files && files.length <= 0) {
      return;
    }
    let file = files![0];
    this.clear();
    let json = await this.readByFile(file);
    await this.loadJson(json);
  }

  canSelectTable(item: SysDicFieldBo, tables?: BakDataTable[]): boolean {
    if (!item || !item.code) {
      return false;
    }
    if (!tables) {
      return false;
    }
    for (let t of tables) {
      if (t.code == item.code) {
        return true;
      }
    }
    return false;
  }

  loadJson(content: string) {
    this.bizData.step1 = { bak: null, bakJson: "" };
    this.bizData.step1.bakJson = content;
    this.bizData.step1.bak = JSON.parse(content); // 解析 JSON 内容
    this.bizData.step2.tables = [];
    {
      let selectTables: SysDicFieldBo[] = [];
      let tables = this.bizData.step1.bak!.tables;
      for (let field of this.oneLoadParam.dicFields) {
        if (this.canSelectTable(field, tables)) {
          selectTables.push(field);
        }
      }
      this.bizData.step2.selectTables = selectTables;
      this.bizData.step2.tables = selectTables.map((x) => x.code!);
    }
    this.bizData.step++;
  }

  async step2submit(skipValid: boolean) {
    if (!skipValid && !(await this.$rule.formCheck(this, "step2"))) {
      return;
    }
    this.bizData.step = 2;
    this.bizData.readyPgrTotal = 1;
    this.bizData.readyPgrValue = 0;

    // 执行查询
    try {
      let that = this;
      let tables = this.bizData.step2.tables;
      let bak: BakData = await upgradeUtils.requestBak(tables, (v, t) => {
        that.bizData.readyPgrTotal = t;
        that.bizData.readyPgrValue = v;
      });
      this.bizData.step2.newData = bak;

      this.analysisDeal();

      this.bizData.step3.tab = tables[0];
      this.bizData.step++;
    } finally {
      this.bizData.readyPgrTotal = 0;
      this.bizData.readyPgrValue = 0;
    }
  }

  analysisDeal() {
    let tables = this.bizData.step2.tables;
    this.bizData.step3 = { tab: tables[0], data: [] };

    let table2nd = arrayUtils.array2map(
      this.bizData.step2.newData!.tables!,
      "code"
    );
    let table2od = arrayUtils.array2map(
      this.bizData.step1.bak!.tables!,
      "code"
    );

    let data: Step3Data[] = [];
    for (const code of tables) {
      let d: Step3Data = { error: "" };
      d.code = code;
      let f = this.$dic.getField(DIC_UPGRADE_CODE, code);
      d.dicfield = f;

      let ef = this.$dic.getFieldPropValue(
        DIC_UPGRADE_CODE,
        code,
        "exclude-field"
      );
      d.excludeFields = ef ? ef.split(",") : [];
      let fk = this.$dic.getFieldPropValue(DIC_UPGRADE_CODE, code, "field-key");
      d.pks = fk ? fk.split(",") : [];
      d.selected = [];
      d.dataNew = table2nd.get(code);
      d.dataOld = table2od.get(code);
      {
        let nfields = d.dataNew!.fields!;
        let nid2field = arrayUtils.array2map(nfields, "code");
        let ofields = d.dataOld!.fields!;
        let oid2field = arrayUtils.array2map(ofields, "code");
        let fields: BakDataTableField[] = [];
        let ok = true;
        for (let nfd of nfields) {
          let ofd = oid2field.get(nfd.code!);
          if (!ofd) {
            ok = false;
            break;
          } else {
            if (ofd.type != nfd.type) {
              ok = false;
              break;
            }
          }
          fields.push(nfd);
        }
        if (ok) {
          for (let ofd of ofields) {
            let nfd = nid2field.get(ofd.code!);
            if (!nfd) {
              ok = false;
              break;
            } else {
              if (ofd.type != nfd.type) {
                ok = false;
                break;
              }
            }
          }
        }
        if (!ok) {
          d.error = "数据表字段不一致，必须优先同步表结构！";
          fields = [];
        }
        d.fields = fields;
      }
      if (!d.error) {
        this.analysisData(d);
      }
      data.push(d);
    }
    this.bizData.step3.data = data;
  }

  array2map<T>(list: T[], cb: (x: T) => string): Map<string, T> {
    let pk2oldData: Map<string, T> = new Map();
    for (let item of list) {
      let pk = cb(item);
      pk2oldData.set(pk, item);
    }
    return pk2oldData;
  }

  fieldEqual(code: string, dataOld: any, dataNew: any): boolean {
    if (!dataOld && dataNew) {
      return false;
    }
    if (!dataNew && dataOld) {
      return false;
    }
    return dataOld[code] == dataNew[code];
  }

  dataEqual(d: Step3Data, dataOld: any, dataNew: any): boolean {
    let fds = d.fields;
    if (!fds || fds.length <= 0) {
      return false;
    }
    for (let fd of fds) {
      if (d.excludeFields!.indexOf(fd.code!) >= 0) {
        continue;
      }
      if (!this.fieldEqual(fd.code!, dataOld, dataNew)) {
        return false;
      }
    }
    return true;
  }

  analysisData(d: Step3Data) {
    let data: DifData[] = [];
    let oldList: any[] = JSON.parse(d.dataOld!.data!);
    let newList: any[] = JSON.parse(d.dataNew!.data!);
    let data2pk = (x: any) => {
      let pk = "";
      for (let p of d.pks!) {
        if (pk) {
          pk = pk + "||";
        }
        pk = pk + x[p];
      }
      return pk;
    };
    let oldPk2data = this.array2map(oldList, data2pk);
    let newPk2data = this.array2map(newList, data2pk);

    // 新增更新
    for (let dataOld of oldList) {
      let difd: DifData = {};
      difd.select = false;
      let key = data2pk(dataOld);
      difd.key = key;
      difd.dataOld = dataOld;

      let dataNew = newPk2data.get(key);
      difd.dataNew = dataNew;
      if (dataNew) {
        if (!this.dataEqual(d, dataOld, dataNew)) {
          difd.type = "up";
          data.push(difd);
        }
      } else {
        difd.type = "add";
        data.push(difd);
      }
    }
    // 删除
    for (let dataNew of newList) {
      let difd: DifData = {};
      difd.select = false;
      let key = data2pk(dataNew);
      difd.key = key;
      difd.dataNew = dataNew;

      let dataOld = oldPk2data.get(key);
      difd.dataOld = dataOld;
      if (!dataOld) {
        difd.type = "del";
        data.push(difd);
      }
    }

    d.data = data;
  }

  async submit() {
    if (!(await this.confirm("确认要按照当前选项执行更新吗？"))) {
      return;
    }

    try {
      let ds = this.bizData.step3.data;
      if (!ds || ds.length <= 0) {
        return;
      }

      let total = 0;
      for (let table of ds) {
        if (table.selected) {
          total = total + table.selected.length;
        }
      }
      this.bizData.readyPgrTotal = total;
      this.bizData.readyPgrValue = 0;

      let successRow = 0;

      for (let table of ds) {
        let selected = table.selected;
        if (!selected) {
          continue;
        }
        for (let sdata of selected) {
          successRow += await upgradeUtils.margin(
            table.code!,
            sdata,
            table.fields!,
            table.pks!,
            table.excludeFields!
          );
          this.bizData.readyPgrValue = this.bizData.readyPgrValue + 1;
        }
      }

      this.$notify.success(`数据操作成功，一共操作了${successRow}条数据。`);

      await this.step2submit(true);
    } finally {
      this.bizData.readyPgrTotal = 0;
      this.bizData.readyPgrValue = 0;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table .danger-row {
  background: #f4999957;
}

::v-deep .el-table .warning-row {
  background: #fbdb9fdb;
}

::v-deep .el-table .success-row {
  background: #a9f87da3;
}
</style>
