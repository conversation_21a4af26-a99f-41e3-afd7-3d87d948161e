<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a
          i.el-icon-lx-calendar {{ $t("menu.sysbase-maintain-upgrade") }}
  .container
    el-tabs(v-model="query.tab")
      el-tab-pane(label="导出配置参数", name="bak")
        SysbaseMaintainUpgradeBak(v-if="query.tab == 'bak'")
      el-tab-pane(label="导入配置参数", name="load")
        SysbaseMaintainUpgradeLoad(v-if="query.tab == 'load'")
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import SysbaseMaintainUpgradeBak from "./bak.vue";
import SysbaseMaintainUpgradeLoad from "./load.vue";

@Component({
  name: "role-sysbase-maintain-upgrade-index",
  components: {
    SysbaseMaintainUpgradeBak,
    SysbaseMaintainUpgradeLoad,
  },
})
export default class SysbaseMaintainUpgradeIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    tab: "bak",
  };
  formData = {};
  oneLoadParam = {
    inited: false,
    initCount: 0,
    // ...param
  };
  bizData = {
    data: [],
  };

  async mounted() {
    await this.init();
  }

  async init() {
    this.loading++;
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.oneLoadParam.initCount++;
    try {
      await this.initDetailBefor();
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initDetailBefor() {
    this.query.tab = compUtils.getQueryValue(this, "tab", "bak");
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {}

  @Watch("query.tab")
  async handleTabClick(tabNew: any, tabOld: any) {
    console.log(tabOld, tabNew);
    compUtils.putQueryValue(this, { tab: tabNew });
  }
}
</script>

<style lang="scss" scoped>
</style>
