<template lang="pug">
.win-bak
  .form
    el-form(
      ref="form",
      :model="formData",
      label-width="100",
      width="100%",
      v-loading="loading > 0"
    )
      el-form-item(
        label="备份范围",
        prop="tables",
        required,
        :rules="$rule.required(null, '备份范围不能为空')"
      )
        el-checkbox-group(v-model="formData.tables")
          el-checkbox(
            v-for="item in $dic.getFields('sys.maintain.upgrade.tables')",
            :label="item.code",
            :key="item.code"
          ) {{ item.label }}({{ item.code }})
      el-form-item
        el-button(v-if="ready <= 0", type="primary", @click="submit()") 创建并下载备份
  .data(v-if="ready > 0")
    h3 合成中，请稍后...
    el-progress(
      :text-inside="true",
      :stroke-width="26",
      :percentage="Math.floor((bizData.readyPgrValue / bizData.readyPgrTotal) * 100)",
      :status="bizData.readyPgrValue == bizData.readyPgrTotal ? 'success' : null"
    )
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import upgradeUtils, { BakData } from "./utils/upgradeUtils";
import moment from "moment";

type FormData = {
  tables: string[];
};

@Component({
  name: "role-sysbase-maintain-upgrade-bak",
})
export default class SysbaseMaintainUpgradeBak extends mixins(BaseVue) {
  loading = 0;
  ready = 0;
  query = {};
  formData = <FormData>{
    tables: [],
  };
  oneLoadParam = {
    inited: false,
    initCount: 0,
    // ...param
  };
  bizData = {
    readyPgrTotal: 0,
    readyPgrValue: 1,
  };

  async mounted() {
    await this.init();
  }

  async init() {
    this.loading++;
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.oneLoadParam.initCount++;
    try {
      await this.initDetailBefor();
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initDetailBefor() {}

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    let fields = this.$dic.getFields("sys.maintain.upgrade.tables");
    this.formData.tables = fields.map((x) => x.code!);
  }

  confirm(content: string) {
    return new Promise((r, j) => {
      this.$confirm(content, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => r(true))
        .catch(() => r(false));
    });
  }

  async download(content: string) {
    // 创建要下载的内容
    const filename = "备份文件_" + moment().format("YYYYMMDDHHmmss") + ".json";

    // 创建一个 Blob 对象
    const blob = new Blob([content], { type: "text/plain" });

    // 创建一个临时的 a 标签
    const a = document.createElement("a");
    a.href = URL.createObjectURL(blob);
    a.download = filename; // 指定文件名

    // 触发下载
    a.style.display = "none";
    document.body.appendChild(a);
    a.click();

    // 清理 DOM
    document.body.removeChild(a);
    URL.revokeObjectURL(a.href); // 释放 URL 对象
  }

  async submit() {
    this.bizData.readyPgrTotal = 1;
    this.bizData.readyPgrValue = 0;
    if (!(await this.$rule.formCheck(this, "form"))) {
      return;
    }
    if (!(await this.confirm("确认要开始按照此配置进行备份吗?"))) {
      return;
    }

    // 执行查询
    this.ready++;

    try {
      let tables = this.formData.tables;
      let that = this;
      let bak: BakData = await upgradeUtils.requestBak(tables, (v, t) => {
        that.bizData.readyPgrTotal = t;
        that.bizData.readyPgrValue = v;
      });
      let resultJson = JSON.stringify(bak);
      this.download(resultJson);
    } finally {
      this.ready--;
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
