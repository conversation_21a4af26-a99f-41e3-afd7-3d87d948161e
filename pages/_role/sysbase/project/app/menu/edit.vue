<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.sysbase-project-app-menu") }}
      el-breadcrumb-item {{ this.query.id ? $t("pages.cms.subject.edit.title_edit") : $t("pages.cms.subject.edit.title_add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="150px",
        size="small"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name")
        el-form-item(
          label="编号",
          prop="code",
          :rules="[$rule.required(null, '编号不能为空')]",
          required
        )
          el-input(v-model="formData.code")
        el-form-item(label="标签名称", prop="nameTag")
          el-input(v-model="formData.nameTag")
        el-form-item(label="图标", prop="icon")
          el-input(v-model="formData.icon", width="100px")
          span.form-item-icon
            el-button(
              type="text",
              @click="() => { bizData.iconDisplay = true; }"
            )
              i.form-item-icon(v-if="formData.icon", :class="formData.icon")
              span(v-else) 选择
        el-form-item(label="应用APP", prop="projectAppId")
          span {{ $dic.getFieldValue("project.app.info", formData.projectAppId, formData.projectAppId) }}
        el-form-item(
          label="启用",
          prop="valid",
          :rules="[$rule.required(null, '启用不能为空')]",
          required
        )
          el-switch(v-model="formData.valid")
        el-form-item(
          label="所属上一级菜单",
          prop="idParent",
          :rules="[$rule.required(null, '上级菜单节点不能为空')]",
          required
        )
          el-cascader(
            v-model="formData.idParent",
            :options="bizData.menuTree",
            :props="{ checkStrictly: true, label: 'label', value: 'id' }",
            clearable,
            style="width: 100%",
            @change="() => idParentChange()"
          )
        el-form-item(
          label="排序位置",
          prop="ord",
          :rules="[$rule.required(null, '排序位置不能为空')]",
          required
        )
          el-select(v-model="formData.ord")
            el-option(
              v-for="item in bizData.ordList",
              :key="item.ord",
              :value="item.ord",
              :label="`${item.label}`"
            )
          //- el-input-number(v-model="formData.ord")
        el-form-item(
          label="菜单展示",
          prop="show",
          :rules="[$rule.required(null, '菜单展示不能为空')]",
          required
        )
          el-switch(v-model="formData.show")
        el-form-item(label="URL", prop="url")
          el-input(v-model="formData.url")
        el-form-item(label="URL匹配规则", prop="urlExp")
          y-tag(v-model="formData.urlExp", type="json")
          | 用于确定哪些url路径应该匹配到这个菜单，正则表达式
        el-form-item(label="缓存页面", prop="keepalive")
          el-switch(v-model="formData.keepalive")
        el-form-item(label="角色信息", prop="role")
          y-tag(v-model="formData.role", type="json")
          | 当前用户需要拥有哪个角色才会显示此菜单（也可以是权限）
        el-form-item(label="页面角色", prop="pageRole")
          y-tag(v-model="formData.pageRole", type="json")
          | 确认当前是哪个页面角色才显示此菜单
        el-form-item(label="目标页面角色", prop="toPageRole")
          el-input(v-model="formData.toPageRole")
          | 确认点击进入此菜单后页面角色切换为哪个角色

        el-form-item(label="备注")
          el-input(v-model="formData.remark", type="textarea", :rows="4")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}

  el-dialog(
    title="选着icon",
    :visible.sync="bizData.iconDisplay",
    width="80%",
    :before-close="() => { bizData.iconDisplay = false; }"
  )
    .icon-win
      .icon-list
        .item(
          v-for="icon in bizData.icons",
          :key="icon",
          :class="formData.icon == icon ? 'activity' : ''",
          @click="() => { formData.icon = icon; bizData.iconDisplay = false; }"
        )
          p.icon
            i(:class="icon")
          p.txt {{ icon }}
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";

import contentApi, {
  BasicProjectAppMenuGetListQueryParam,
  ProjectAppMenu,
  ProjectAppMenuAddVo,
} from "~/api/basic/project/app/menuApi";

import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import finals from "~/utils/finals";

type FormData = Omit<ProjectAppMenu, "idParent"> & {
  idParent: string[];
};

type ContainerTreeNode = ProjectAppMenu & {
  label?: string;
  children?: ContainerTreeNode[];
};

type OrdData = {
  id?: string;
  ord?: number;
  label?: string;
};

@Component({
  name: "role-wms-container-edit",
})
export default class WmsContainerEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    cpid: "",
    idParent: "0",
    projectAppId: "",
  };
  formData: FormData = {
    id: "",
    code: "",
    name: "",
    nameTag: "",
    icon: "",
    valid: true,
    show: true,
    url: "",
    urlExp: "",
    keepalive: true,
    role: "",
    pageRole: "",
    toPageRole: "",
    projectAppId: "",
    idParent: ["0"],
    ord: 0,
    remark: "",
  };
  defMenu = {
    id: "0",
    name: "（根菜单）",
    label: "（根菜单）",
  };
  defOrd = {
    id: "0",
    ord: 0,
    label: "开始位置",
  };
  oneLoadParam = {
    inited: false,
    // ...param
    id2menu: <Map<string, ContainerTreeNode> | null>null,
    menus: <ContainerTreeNode[] | null>null,
  };
  bizData = {
    icons: finals.icons,
    menuTree: <ContainerTreeNode[]>[{ ...this.defMenu }],
    ordList: <OrdData[]>[],

    iconDisplay: false,
  };

  async created() {
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.cpid = compUtils.getQueryValue(this, "cpid", "");
    this.query.idParent = compUtils.getQueryValue(this, "id_parent", "0");
    this.query.projectAppId = compUtils.getQueryValue(
      this,
      "project_app_id",
      ""
    );
    this.init();
  }

  async mounted() {
    // this.initSortable();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    {
      let params: BasicProjectAppMenuGetListQueryParam = {
        project_app_id: this.query.projectAppId,
      };
      let menus: ContainerTreeNode[] = await contentApi.getList(params);
      menus.map((x) => (x.label = `${x.name}(${x.code})`));
      this.oneLoadParam.id2menu = arrayUtils.array2map(menus, "id");
      this.oneLoadParam.menus = menus;
    }

    this.oneLoadParam.inited = true;
  }

  async initDetail() {
    let form = this.formData;
    if (this.query.id || this.query.cpid) {
      let detail = await contentApi.get(
        this.query.id ? this.query.id : this.query.cpid
      );
      detail.id = "";
      form = { ...(<any>detail) };
      this.formData = form;
      await this.initParam();
      let ids = arrayUtils
        .deepFindByMap(this.oneLoadParam.id2menu!, "idParent", detail.idParent)
        .reverse()
        .map((x) => x.id!);
      form.idParent = ids.length <= 0 ? ["0"] : ids;

      this.formData.projectAppId = form.projectAppId!;
      await this.initParam();
    } else {
      this.formData.projectAppId = this.query.projectAppId;
      let ids = arrayUtils
        .deepFindByMap(
          this.oneLoadParam.id2menu!,
          "idParent",
          this.query.idParent
        )
        .reverse()
        .map((x) => x.id!);
      form.idParent = ids.length <= 0 ? ["0"] : ids;
      await this.initParam();
    }

    this.formData = form;
  }

  async initParam() {
    let menus = this.oneLoadParam.menus!;
    menus = menus.map((x) => ({ ...x }));
    let menuTree = arrayUtils.array2tree(menus, "id", "idParent", "children");
    this.bizData.menuTree = [this.defMenu, ...menuTree];

    await this.initOrdList();
  }

  async initOrdList() {
    let menus = this.oneLoadParam.menus;
    if (!menus) {
      this.bizData.ordList = [this.defOrd];
      return;
    }
    let idParent =
      this.formData.idParent && this.formData.idParent.length > 0
        ? this.formData.idParent[this.formData.idParent.length - 1]
        : "0";
    menus = menus.map((x) => ({ ...x }));
    menus = menus.filter((x) => x.idParent == idParent);
    let ordList = [
      this.defOrd,
      ...menus.map((x) => ({ id: x.id, ord: x.ord, label: x.name })),
    ];
    this.bizData.ordList = ordList;
    let ord = this.formData.ord!;
    if (ordList.filter((x) => x.ord == ord).length > 0) {
      if (this.formData.id) {
        let selectOrds = ordList.filter((x) => x.id! == this.formData.id);
        if (selectOrds.length > 0) {
          this.formData.ord = selectOrds[0].ord;
        }
      }
      return;
    } else {
      this.formData.ord = 0;
    }
  }

  async idParentChange() {
    await this.initOrdList();
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: ProjectAppMenuAddVo = {
        ...(<Omit<FormData, "idParent">>data),
      };
      body.ord = body.ord == 0 ? 1 : body.ord;
      body.idParent = data.idParent[data.idParent.length - 1];
      if (this.query.id) {
        // 更新
        await contentApi.modify(this.query.id, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await contentApi.add(body);
        this.query.id = detail.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.back();
    // this.$router.push(
    //   `/${compUtils.getQueryValue(this, "role", "")}/wms/container`
    // );
  }
}
</script>

<style lang="scss" scoped>
.property-structs-table::v-deep table tbody .cell {
  min-height: 60px;
}
.form-item-icon {
  font-size: 40px;
}
.icon-win {
  .icon-list {
    display: flex;
    flex-wrap: wrap;
    // justify-content: flex-start;
    justify-content: center;

    .item {
      // width: 100px;
      // height: 100px;
      width: 7.6vw;
      height: 7.6vw;
      display: flex;
      align-items: center;
      border: solid 1px #aaa;
      justify-content: center;
      flex-direction: column;

      color: #555;

      cursor: pointer;

      .icon {
        font-size: 40px;
      }
      .txt {
        height: 24px;
        font-size: 10px;
        display: inline-block;
        text-align: center;
        margin: 0 3px;
        color: #999;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
    .item.activity {
      color: #1989fa;
      .txt {
        color: #1989fa;
      }
    }
  }
}
</style>
