<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.sysbase-project-app-menu") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        el-form-item(prop="projectAppId", label="工程APP")
          el-select(v-model="query.projectAppId", @change="handleSearchClick")
            el-option(
              v-for="option in $dic.getFields('project.app.info')",
              :key="option.code",
              :label="`${option.label}(${option.code})`",
              :value="option.code"
            )
    //- 表单内容 default-expand-all,
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      row-key="id",
      header-cell-class-name="table-header",
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }",
      v-loading="loading > 0"
    )
      //- el-table-column(type="selection", width="55", align="center")
      el-table-column(prop="name", label="名称")
        template(#default="scope")
          i(v-if="scope.row.icon", :class="scope.row.icon")
          span.mgl10 {{ scope.row.name }}
      el-table-column(prop="code", label="编号")
      el-table-column(prop="valid", label="启用", width="66")
        template(#default="scope")
          el-tag(:type="scope.row.valid ? 'success' : 'danger'") {{ scope.row.valid ? "启用" : "停用" }}
      el-table-column(prop="show", label="菜单", width="66")
        template(#default="scope")
          el-tag(:type="scope.row.show ? 'success' : 'danger'") {{ scope.row.show ? "启用" : "隐藏" }}
      el-table-column(prop="url", label="URL")
      el-table-column(prop="ord", label="排序", width="50")
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")
      el-table-column(label="操作", width="220", align="center")
        template(#default="scope")
          el-button(
            type="text",
            @click="handleAddChildrenClick(scope.row)",
            v-if="!scope.row.storage"
          ) 添加子菜单
          el-button(type="text", @click="handleModifyClick(scope.row, true)") 复制
          el-button(type="text", @click="handleModifyClick(scope.row)") {{ $t("common.base.modify") }}
          el-popconfirm.mgl10(
            title="确认要删除数据吗？",
            @confirm="handleDeleteClick(scope.row)"
          )
            el-button.red(slot="reference", type="text") {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";

import contentApi, {
  BasicProjectAppMenuGetListQueryParam,
  ProjectAppMenu,
} from "~/api/basic/project/app/menuApi";
import arrayUtils from "~/utils/arrayUtils";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

type Content = ProjectAppMenu & {
  children?: Array<Content>;
};

const urlPre = "/sysbase/project/app/menu";

@Component({
  name: "role-wms-container",
})
export default class WmsContainerIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    projectAppId: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    contents: <Content[]>[],
  };

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...

    {
      let projectApps = this.$dic.getFields("project.app.info");
      if (projectApps && projectApps.length > 0) {
        this.query.projectAppId = projectApps[0].code!;
      }
    }
  }

  async initDetail() {
    this.query.projectAppId = compUtils.getQueryValue(
      this,
      "project_app_id",
      this.query.projectAppId
    );

    await this.loadContentCount();
  }

  // async activated() {
  //   await this.init();
  // }
  async mounted() {
    await this.init();
  }

  async loadContentCount() {
    await this.loadContent();
  }

  async loadContent() {
    routerUtils.putQueryValue(this, {
      project_app_id: this.query.projectAppId,
    });

    let param: BasicProjectAppMenuGetListQueryParam = {
      project_app_id: this.query.projectAppId,
    };
    this.loading++;
    try {
      let res = await contentApi.getList(param);
      let root = arrayUtils.array2tree(res, "id", "idParent", "children");
      this.bizData.contents = root;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleSearchClick() {
    await this.loadContent();
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/edit?project_app_id=${this.query.projectAppId}`
    );
  }

  async handleModifyClick(record: Content, cp = false) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/edit?project_app_id=${this.query.projectAppId}&${
        cp ? "cpid" : "id"
      }=${record.id}`
    );
  }

  async handleAddChildrenClick(record: Content) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/edit?project_app_id=${this.query.projectAppId}&id_parent=${
        record.id
      }`
    );
  }

  async handleDeleteClick(record: Content) {
    this.loading++;
    try {
      await contentApi.delete(record.id!);
      this.$notify.success("删除数据成功过");
      await this.loadContent();
    } finally {
      this.loading--;
    }
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}
</style>
