<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 系统配置
      el-breadcrumb-item {{ this.id ? $t("pages.power.role.edit.title_edit") : $t("pages.power.role.edit.title_add") }}
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="sysconfig",
        label-width="80px",
        width="900px",
        v-loading="loading"
      )
        el-form-item(label="键")
          el-input(v-model="sysconfig.code")
          el-alert(type="info") 推荐格式：{服务}_{类型}_{字段}[_{功能}]，例如：MALL_PRODUCT_PRODUCT_INFO_PROPERTIS
        el-form-item(label="值")
          el-input(v-model="sysconfig.value", type="textarea", :rows="4")
        el-form-item(label="启用状态")
          el-switch(v-model="sysconfig.valid")
        el-form-item(label="数据公开")
          el-switch(v-model="sysconfig.pub")
        el-form-item(label="备注")
          el-input(v-model="sysconfig.remark", type="textarea", :rows="6")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, Vue } from "nuxt-property-decorator";
import authSysconfigApi, { SysConfig } from "~/api/auth/sys/configApi";
import { config } from "~/config/global.config";
import compUtils from "~/utils/compUtils";
import dicUtils from "~/utils/dicUtils";

@Component({
  name: "role-sysbase-sysconfig-edit",
})
export default class SysbaseSysconfigEdit extends Vue {
  languages = config.i18n.locales;

  loading = true;
  id: string = "";
  sysconfig: SysConfig = {
    valid: true,
    pub: false,
  };

  async created() {
    this.id = compUtils.getQueryValue(this, "id", "");
    if (this.id) {
      this.sysconfig = await authSysconfigApi.get(this.id);
    }
    this.loading = false;
  }

  async submit() {
    this.loading = true;
    try {
      if (this.id) {
        // 更新
        await authSysconfigApi.modify(this.id, this.sysconfig);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let dic = await authSysconfigApi.add(this.sysconfig);
        this.id = dic.id!;
        this.sysconfig = dic;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      await dicUtils.reload();
      this.goBack();
    } catch (e) {
      if (this.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    if (window.history.length <= 1) {
      this.$router.push(
        `/${compUtils.getQueryValue(this, "role", "")}/sysbase/dic`
      );
    } else {
      this.$router.back();
    }
  }
}
</script>
