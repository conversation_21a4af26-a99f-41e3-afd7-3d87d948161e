<template lang="pug">
div
  client-only
    .crumbs
      el-breadcrumb(separator="/")
        el-breadcrumb-item
          i.el-icon-lx-cascades 系统配置
    .container
      .y-page-table
        //- 表头
        .y-header
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        .y-body
          //- 表单内容
          el-table.table(
            :data="sysconfigs",
            border,
            ref="multipleTable",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(
              prop="code",
              label="键",
              align="center",
              width="200"
            )
            el-table-column(prop="value", label="值")
            el-table-column(label="启用", width="100")
              template(#default="scope")
                el-tag(:type="scope.row.valid ? 'success' : 'danger'") {{ scope.row.valid ? "启用" : "停用" }}
            el-table-column(label="公开", width="100")
              template(#default="scope")
                el-tag(:type="scope.row.pub ? 'success' : 'danger'") {{ scope.row.pub ? "公开" : "私有" }}
            el-table-column(prop="remark", label="备注")
            el-table-column(label="操作", width="160", align="center")
              template(#default="scope")
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleEdit(scope.row)"
                ) {{ $t("common.base.modify") }}
                el-popconfirm.mgl10(
                  title="确认要删除数据吗？",
                  @confirm="handleDelete(scope.row)"
                )
                  el-button.red(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
</template>


<script lang="ts">
import { Component, Vue } from "nuxt-property-decorator";
import authSysconfigApi, { SysConfig } from "~/api/auth/sys/configApi";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-sysbase-sysconfig",
})
export default class SysbaseSysconfigIndex extends Vue {
  loading = true;
  sysconfigs: Array<SysConfig> = [];

  async activated() {
    this.loading = true;
    this.refreshTable()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        console.error(e);
        this.loading = false;
        let msg: any = this.$t("common.error.reload");
        this.$notify.error({
          title: msg,
          message: "",
        });
      });
  }

  async refreshTable() {
    // 加载批量数据
    this.sysconfigs = await authSysconfigApi.getList();
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/sysbase/sysconfig/edit`
    );
  }

  async handleEdit(val: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/sysbase/sysconfig/edit?id=${val.id}`
    );
  }

  async handleDelete(val: any) {
    try {
      await authSysconfigApi.delete(val.id);
      let msg: any = this.$t("common.success.delete");
      this.$notify.success(msg);
      await this.refreshTable();
    } catch (error) {
      let msg: any = this.$t("common.error.delete");
      this.$notify.error(msg);
      throw error;
    }
  }
}
</script>

