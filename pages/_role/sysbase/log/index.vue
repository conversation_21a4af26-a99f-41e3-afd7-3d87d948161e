<template lang="pug">
.page-sysbase-log-index
  .log-head
    .mgr10
      el-checkbox(v-model="writeLog", @change="handleWriteLogChange") 加载写入
    .mgr10
      el-checkbox(v-model="scollRun") 跟随滚动
    .mgr10
      el-button(type="primary", size="mini", @click="handleClearClick") 清空控制台
  .log-win(ref="logWin")
    .log-content(v-html="log")
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { WsMsgSendBodyBo } from "~/model/common";
import { BaseVue, VueWs } from "~/model/vue";

@Component({
  name: "role-sysbase-log-index",
})
export default class SysbaseLogIndex extends mixins(BaseVue) {
  public static VALUE_TYPE_LOG_SUB = "LOG_SUB";
  log = "";
  scollRun = true;
  writeLog = true;
  logs: string[][] = [];

  async mounted() {
    await this.$ws.send({
      type: "LOG_SUB",
      data: JSON.stringify({ sub: true }),
    });
  }

  beforeDestroy() {
    this.$ws.send({ type: "LOG_SUB", data: JSON.stringify({ sub: false }) });
  }

  handleClearClick() {
    this.log = "";
  }

  handleWriteLogChange() {
    if (!this.writeLog) {
      return;
    }
    if (this.logs.length <= 0) {
      return;
    }
    let logs = this.logs;
    this.logs = [];
    logs.map((ls) =>
      ls.map((x: string) => {
        let y = x;
        y = y.replaceAll("<", "&lt;");
        y = y.replaceAll(">", "&gt;");
        y = y.replaceAll("\n", "<br/>");
        this.log = this.log + y + "<br/>";
      })
    );
    if (this.scollRun) {
      let logWin = <any>this.$refs.logWin;
      this.$nextTick(() => {
        logWin.scrollTop = 1000000;
      });
    }
  }

  @Watch(VueWs.WATCH_WS_CONNECTED)
  async wsMsgConnected() {
    await this.$ws.send({
      type: "LOG_SUB",
      data: JSON.stringify({ sub: true }),
    });
  }

  @Watch(VueWs.WATCH_WS_MSG)
  async wsMsg(msg: WsMsgSendBodyBo) {
    if (msg.bc != "sys") {
      return;
    }
    if (msg.fc != "log") {
      return;
    }
    if (msg.ec != "add") {
      return;
    }
    if (!msg.d) {
      return;
    }
    let list: string[] = JSON.parse(msg.d);
    if (!this.writeLog) {
      this.logs.push(list);
      return;
    }
    list.map((x: string) => {
      let y = x;
      y = y.replaceAll("<", "&lt;");
      y = y.replaceAll(">", "&gt;");
      y = y.replaceAll("\n", "<br/>");
      this.log = this.log + y + "<br/>";
    });
    if (this.scollRun) {
      let logWin = <any>this.$refs.logWin;
      this.$nextTick(() => {
        logWin.scrollTop = 1000000;
      });
    }
  }
}
</script>
<style lang="scss">
.layout-content:has(.page-sysbase-log-index) {
  padding: 0;
  position: relative;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
}
</style>
<style lang="scss" scoped>
h2 {
  margin-bottom: 10px;
}
.page-sysbase-log-index {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;

  .log-head {
    height: 30px;
    padding: 0 30px;
    display: flex;
    align-items: center;
  }
  .log-win {
    height: calc(100% - 30px);
    border: solid 1px #888;
    overflow-y: auto;
    background-color: #ccc;

    .log-content {
      word-wrap: break-word;
      padding: 14px;
    }
  }
}
</style>

