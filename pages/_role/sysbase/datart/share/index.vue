<template lang="pug">
.main-win
  y-datartshare(v-show="shareCode", :id="shareId", :code="shareCode")
  .msg(v-show="!shareCode") 参数异常
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import datarShareApi from "~/api/basic/datart/share/codeApi";
import routerUtils from "~/utils/routerUtils";

@Component({
  name: "role-sysbase-datart-share-index",
  components: {},
})
export default class PageSysbaseDatartShareIndex extends mixins(BaseVue) {
  shareConfCode = "";
  shareId = "";
  shareKey = "";
  shareCode = "";

  created() {
    this.shareConfCode = routerUtils.getQueryValue(this, "confcode", "");
    this.shareId = routerUtils.getQueryValue(this, "code", "");
    this.shareKey = routerUtils.getQueryValue(this, "key", "");
  }

  async mounted() {
    if (this.shareId && this.shareKey) {
      const r = await datarShareApi.buildCode({
        id: this.shareId,
        key: this.shareKey,
      });
      this.shareId = r.id!;
      this.shareCode = r.code!;
    } else if (this.shareConfCode) {
      const r = await datarShareApi.buildCodeByConfCode({
        code: this.shareConfCode,
      });
      this.shareId = r.id!;
      this.shareCode = r.code!;
    }
  }
}
</script>

<style lang="scss" scoped>
.main-win {
  .msg {
    font-size: 20px;
  }
}
</style>
