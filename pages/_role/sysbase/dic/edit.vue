<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.sysbase-dic") }}
      el-breadcrumb-item {{ this.id ? $t("pages.power.role.edit.title_edit") : $t("pages.power.role.edit.title_add") }}
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="dic",
        label-width="80px",
        v-loading="loading"
      )
        el-form-item(
          label="Code",
          prop="code",
          :rules="[$rule.required(null, 'Code不能为空')]"
        )
          el-input(v-model="dic.code")
          el-alert(type="info") 推荐格式：{服务}.{类型}.{字段}[.{功能}]，例如：auth.user.type、auth.user.type.orderList
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]"
        )
          el-input(v-model="dic.name")
        el-form-item(label="启用状态")
          el-switch(v-model="dic.valid")
        el-form-item(label="属性字段")
          y-tag(v-model="dic.propCodes", type="array")
        el-form-item(label="字段数据")
          .fields-table(ref="fieldsdiv")
            el-table(
              :data="dic.fields",
              border,
              ref="multipleTable",
              header-cell-class-name="table-header"
            )
              el-table-column(width="35", label="排序")
                template(#default="scope")
                  .y-sorthandle
                    i.el-icon-rank
              el-table-column(label="code", prop="code", width="200")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`fields.${scope.$index}.code`",
                    :rules="$rule.required(null, 'Code不能为空')"
                  )
                    el-input(v-model="scope.row.code")
              el-table-column(label="名称", prop="label", width="200")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`fields.${scope.$index}.label`",
                    :rules="$rule.required(null, '名称不能为空')"
                  )
                    el-input(v-model="scope.row.label")
              el-table-column(label="国际化", width="400")
                template(#default="scope")
                  .df.mgt10(v-for="(lb, idx) in scope.row.labels")
                    el-form-item(
                      label-width="0",
                      size="mini",
                      :prop="`fields.${scope.$index}.labels.${idx}.language`",
                      :rules="$rule.required(null, '语言不能为空')"
                    )
                      el-select.mgr10(v-model="lb.language")
                        el-option(
                          v-for="(lg, idx) in languages",
                          :value="idx",
                          :key="idx",
                          :label="languages[idx].title + '(' + idx + ')'"
                        )
                    el-form-item(
                      label-width="0",
                      size="mini",
                      :prop="`fields.${scope.$index}.labels.${idx}.label`",
                      :rules="$rule.required(null, '值不能为空')"
                    )
                      el-input.mgr10(v-model="lb.label")
                    el-button.btn-close(
                      @click="() => { scope.row.labels.splice(idx, 1); }",
                      type="text"
                    ) X
                  el-button(
                    @click="() => { scope.row.labels.push({ language: 'zh', label: '' }); }",
                    type="text"
                  ) 增加国际化标签
              el-table-column(
                v-for="(propCode, idx) in dic.propCodes",
                :key="idx",
                :label="propCode",
                :prop="propCode",
                width="200"
              )
                template(#default="scope")
                  el-input(v-model="scope.row[`prop.${propCode}`]")
              el-table-column(label="备注", prop="remark")
                template(#default="scope")
                  el-input(v-model="scope.row.remark", type="textarea")
              el-table-column(label="操作", width="80")
                template(#default="scope")
                  el-popconfirm(
                    title="确认要删除数据吗？",
                    @confirm="dic.fields.splice(scope.$index, 1)"
                  )
                    el-button.red(
                      slot="reference",
                      type="text",
                      icon="el-icon-delete"
                    ) {{ $t("common.base.delete") }}
            el-button.mgt10(@click="handleAddClick") 添加行
        el-form-item(label="备注")
          el-input(v-model="dic.remark", type="textarea", :rows="2")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue } from "nuxt-property-decorator";
import authDicApi, { SysDicBo, SysDicFieldBo } from "~/api/auth/dic/dicApi";
import { config } from "~/config/global.config";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import dicUtils from "~/utils/dicUtils";
import sortableUtils from "~/utils/sortableUtils";

type TableSysDicFieldBo = SysDicFieldBo & {
  [key in `prop.${string}`]?: string;
};

type TypeSysDicBo = SysDicBo & {
  propCodes?: string[];
  fields?: TableSysDicFieldBo[];
};

@Component({
  name: "role-sysbase-dic-edit",
})
export default class SysbaseDicEdit extends mixins(BaseVue) {
  languages = config.i18n.locales;

  loading = true;
  id: string = "";
  dic: TypeSysDicBo = {
    propCodes: [],
    valid: true,
    fields: [],
  };

  async created() {
    this.id = <string>this.$route.query.id;
    if (this.id) {
      let dic = await authDicApi.get(this.id);
      let ndic: TypeSysDicBo = { ...dic, propCodes: [], fields: [] };
      let propCodeSet = new Set<string>();
      for (let field of dic.fields!) {
        let nf: TableSysDicFieldBo = { ...field };
        nf.labels = field.labels ? [...field.labels] : [];
        nf.props = [];
        if (field.props) {
          for (let prop of field.props) {
            (<any>nf)[`prop.${prop.code}`] = prop.value;
            propCodeSet.add(prop.code!);
          }
        }
        ndic.propCodes = [...propCodeSet];
        ndic.fields!.push(nf);
      }
      this.dic = ndic;
    }
    this.initSortable();
    this.loading = false;
  }

  async initSortable() {
    let el: any = this.$refs.fieldsdiv;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "dic.fields", {
      handle: ".y-sorthandle",
    });
  }

  async handleAddClick() {
    this.dic.fields!.push({
      code: "",
      label: "",
      labels: [],
      props: [],
      remark: "",
    });
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let body: SysDicBo = { ...this.dic };
      delete (<any>body).propCodes;
      let fields: SysDicFieldBo[] = [];
      for (let field of this.dic.fields!) {
        let f: TableSysDicFieldBo = { ...field };
        f.props = [];
        if (this.dic.propCodes) {
          for (let propCode of this.dic.propCodes!) {
            let v = (<any>f)[`prop.${propCode}`];
            v = v == undefined || v == null ? "" : v;
            f.props.push({
              code: propCode,
              value: v,
            });
          }
        }
        for (let key in f) {
          if (key.startsWith("prop.")) {
            delete (<any>f)[key];
          }
        }
        fields.push(f);
      }
      body.fields = fields;
      if (this.id) {
        // 更新
        await authDicApi.modify(this.id, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let dic = await authDicApi.add(body);
        this.id = dic.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      await dicUtils.reload();
      this.goBack();
    } catch (e) {
      if (this.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    if (window.history.length <= 1) {
      let role = compUtils.getQueryValue(this, "role", "");
      this.$router.push(`/${role}/sysbase/dic`);
    } else {
      this.$router.back();
    }
  }
}
</script>

<style lang="scss" scoped>
.df {
  display: flex;
}
.btn-close {
  padding: 0;
  height: 30px;
  margin-left: 4px;
}

.fields-table::v-deep table tbody .cell {
  min-height: 60px;
}
</style>
