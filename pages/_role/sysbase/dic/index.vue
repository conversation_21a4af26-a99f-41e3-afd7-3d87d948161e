<template lang="pug">
div
  client-only
    .crumbs
      el-breadcrumb(separator="/")
        el-breadcrumb-item
          i.el-icon-lx-cascades {{ $t("menu.sysbase-dic") }}
    .container
      .y-page-table
        //- 表头
        .y-header
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        .y-body
          //- 表单内容
          el-table(
            :data="dics",
            border,
            ref="multipleTable",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(
              prop="code",
              label="Code",
              align="center",
              width="220"
            )
            el-table-column(prop="name", label="名称", width="200")
            el-table-column(label="字段信息")
              template(#default="scope")
                div
                  div(v-for="(field, idx) in scope.row.fields") {{ field.code }} ： {{ field.label }}
            el-table-column(prop="remark", label="备注")
            el-table-column(label="操作", width="160", align="center")
              template(#default="scope")
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleEdit(scope.row)"
                ) {{ $t("common.base.modify") }}
                el-popconfirm.mgl10(
                  title="确认要删除数据吗？",
                  @confirm="handleDelete(scope.row)"
                )
                  el-button(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
</template>


<script lang="ts">
import { Component, Vue } from "nuxt-property-decorator";
import authDicApi, { SysDicBo } from "~/api/auth/dic/dicApi";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-sysbase-dic",
})
export default class SysbaseDicIndex extends Vue {
  loading = true;
  dics: Array<SysDicBo> = [];

  async activated() {
    this.loading = true;
    this.refreshTable()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        console.error(e);
        this.loading = false;
        let msg: any = this.$t("common.error.reload");
        this.$notify.error({
          title: msg,
          message: "",
        });
      });
  }

  async refreshTable() {
    // 加载批量数据
    this.dics = await authDicApi.getList();
  }

  async handleAddClick() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}/sysbase/dic/edit`);
  }

  async handleEdit(val: any) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}/sysbase/dic/edit?id=${val.id}`);
  }

  async handleDelete(val: any) {
    try {
      await authDicApi.delete(val.id);
      let msg: any = this.$t("common.success.delete");
      this.$notify.success(msg);
      await this.refreshTable();
    } catch (error) {
      let msg: any = this.$t("common.error.delete");
      this.$notify.error(msg);
      throw error;
    }
  }
}
</script>
