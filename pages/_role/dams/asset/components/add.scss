.add-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 0 48px;
    background: rgba(23, 22, 24, 1);

    @media (max-width: 1024px) {
        padding: 0 32px;
    }

    @media (max-width: 768px) {
        padding: 0 16px;
    }

    @media (max-width: 480px) {
        padding: 0 12px;
    }
}

.header {
    width: 100%;
    height: 108px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
        height: 80px;
    }

    .logo {
        img {
            width: 60px;
            height: 60px;

            @media (max-width: 768px) {
                width: 40px;
                height: 40px;
            }
        }
    }

    .header-right {
        display: flex;
        align-items: center;
    }

    // 使用自定义按钮组件替代

    .close-icon {
        margin-left: 24px;
        cursor: pointer;

        svg {
            width: 20px;
            height: 20px;
        }
    }
}

.content {
    display: flex;
    flex-direction: column;
    position: relative;

    .step-bar {
        .step-line {
            height: 6px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 3px;
            position: relative;

            .step-active {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: rgba(255, 255, 255, 1);
                border-radius: 3px;
                transition: width 0.3s;
            }
        }
    }

    .main-content {
        margin-top: 48px;
        display: flex;
        position: relative;
        gap: 144px;

        @media (max-width: 1200px) {
            gap: 72px;
        }

        @media (max-width: 1024px) {
            gap: 36px;
        }

        @media (max-width: 768px) {
            margin-top: 24px;
            flex-direction: column;
            gap: 0;
        }

        .sidebar {
            width: 216px;
            flex-shrink: 0;

            @media (max-width: 1024px) {
                width: 180px;
            }

            @media (max-width: 768px) {
                width: 100%;
                margin-bottom: 24px;
            }
        }

        .form-content {
            width: 702px;
            padding:0 6px;
            box-sizing: border-box;
            max-height: calc(100vh - 180px);
            overflow: auto;
            overflow-x: hidden;

            /* 自定义滚动条样式 */
            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(23, 22, 24, 0.8);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;

                &:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            }

            @media (max-width: 768px) {
                max-width: 100%;
                max-height: calc(100vh - 240px);
            }


            .form-title {
                font-size: 18px;
                font-weight: 600;
                color: #fff;
                margin-bottom: 24px;

                @media (max-width: 768px) {
                    font-size: 16px;
                    margin-bottom: 16px;
                }
            }
        }
    }
}

.footer {
    height: 80px;
    margin-top: 48px;

    @media (max-width: 768px) {
        height: 60px;
        margin-top: 24px;
    }

    .footer-btn {
        position: fixed;
        bottom: 24px;
        z-index: 1000;

        @media (max-width: 1024px) {
            bottom: 20px;
        }

        @media (max-width: 768px) {
            bottom: 16px;
        }

        @media (max-width: 480px) {
            bottom: 12px;
        }
    }

    .footer-btn-left {
        left: 48px;

        @media (max-width: 1024px) {
            left: 32px;
        }

        @media (max-width: 768px) {
            left: 16px;
        }

        @media (max-width: 480px) {
            left: 12px;
        }
    }

    .footer-btn-right {
        right: 48px;

        @media (max-width: 1024px) {
            right: 32px;
        }

        @media (max-width: 768px) {
            right: 16px;
        }

        @media (max-width: 480px) {
            right: 12px;
        }
    }
}

// 通用样式类
.mt-32 {
    margin-top: 32px;

    @media (max-width: 768px) {
        margin-top: 24px;
    }
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 24px;

    @media (max-width: 768px) {
        font-size: 16px;
        margin-bottom: 16px;
    }
}

// 第三步：案例类型和产品细节样式
.case-details-container {
    width: 100%;

    .mt-32 {
        margin-top: 48px;

        @media (max-width: 768px) {
            margin-top: 32px;
        }
    }

    // 服务表单样式
    .service-form {
        @media (max-width: 768px) {
            margin-bottom: 24px;
        }
    }

    // 单选按钮容器样式
    .case-type-selection,
    .product-type-selection {
        margin-bottom: 32px;

        @media (max-width: 768px) {
            margin-bottom: 16px;
        }

        .radio-selection {
            width: 100%;
        }
    }

    // 产品详细信息表单样式
    .product-details-form {
        @media (max-width: 768px) {
            margin-top: 16px;
        }
    }

    // 材质选择器已封装为组件，样式在组件内部定义
}

// 第四步：标签和源文件样式
.tag-files-container {
    .mt-32 {
        margin-top: 48px;

        @media (max-width: 768px) {
            margin-top: 32px;
        }
    }

    .tag-selector-container {
        display: flex;
        align-items: center;
        gap: 16px;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
    }

    .tag-title-container {
        display: flex;
        align-items: center;
        margin-top: 32px;

        .section-title-v2 {
            margin-right: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
        }

        @media (max-width: 768px) {
            margin-bottom: 12px;
        }
    }

    .refresh-btn {
        margin-left: 16px;

        @media (max-width: 768px) {
            margin-left: 8px;
        }
    }

    .selected-tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 16px;

        @media (max-width: 768px) {
            margin-top: 12px;
        }
    }
}

// 单选按钮描述文字样式
.radio-description {
    color: rgba(235, 235, 245, 0.7);
}

// Element UI 样式覆盖
::v-deep {
    .el-form-item {
        margin-bottom: 32px;

        @media (max-width: 768px) {
            margin-bottom: 16px;
        }

        &__label {
            color: #ffffff !important;
            font-size: 12px;
            line-height: 22px;
            padding: 0;

            @media (max-width: 768px) {
                padding-bottom: 6px;
                font-size: 14px;
            }

            &:before {
                content: none !important;
            }
        }

        &.is-required .el-form-item__label {
            &:before {
                content: none;
            }

            &:after {
                content: "*";
                color: #f56c6c;
                margin-left: 4px;
                font-weight: bold;
            }
        }

        &.is-error .el-form-item__error {
            color: #f56c6c;
            font-size: 12px;
            padding-top: 4px;
        }
    }

    .el-form-item__label {
        @media (max-width: 768px) {
            padding-bottom: 6px;
            font-size: 14px;
        }
    }

    .el-input__inner {
        @media (max-width: 768px) {
            height: 36px;
            line-height: 36px;
        }
    }

    .el-textarea__inner {
        @media (max-width: 768px) {
            min-height: 80px;
        }
    }

    // 响应式行间距
    .el-row {
        @media (max-width: 768px) {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
    }

    // 响应式列间距
    .el-col {
        @media (max-width: 768px) {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
    }
}
