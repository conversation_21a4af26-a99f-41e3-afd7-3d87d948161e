<template>
    <div class="add-container">
        <header class="header">
            <div class="logo">
                <img src="~/static/img/logo.svg" />
            </div>
            <div class="header-right">
                <y-button type="secondary" @click="saveDraft">保存草稿</y-button>
                <div class="close-icon" @click="onClose">
                    <y-svg-icon name="close" color="#fff" />
                </div>
            </div>
        </header>

        <div class="content">
            <div class="step-bar">
                <div class="step-line">
                    <div class="step-active" :style="{ width: stepWidth + '%' }"></div>
                </div>
            </div>

            <div class="main-content">
                <div class="sidebar">
                    <y-sidebar>
                        <y-sidebar-item v-for="(step, index) in steps" :key="index" :active="currentStep === index" :icon="step.icon" :activeIcon="step.activeIcon" @click="goToStep(index)">
                            {{ step.name }}
                        </y-sidebar-item>
                    </y-sidebar>
                </div>

                <div class="form-content">
                    <div class="form-title">{{ stepsTitle[currentStep] }}</div>

                    <template v-if="currentStep === 0">
                        <y-form ref="formRef" :form-items="basicFormItems" :form-data="formData" @field-focus="handleFieldFocus" @field-blur="handleFieldBlur" />
                    </template>

                    <template v-if="currentStep === 1">
                        <file-upload type="image" :uploadAction="uploadAction" :files="imageFiles" @update:files="updateImageFiles" />
                    </template>
                    <template v-if="currentStep === 2">
                        <div class="case-details-container">
                            <div class="case-type-selection">
                                <y-radio-group class="radio-selection" v-model="formData.serviceType">
                                    <y-radio label="PRODUCTION">做货 <span class="radio-description">（礼品/赠品/企业文创实物等）</span></y-radio>
                                    <y-radio label="NON_PRODUCTION">非做货 <span class="radio-description">（设计报告/分析报告等）</span></y-radio>
                                </y-radio-group>
                            </div>

                            <template v-if="formData.caseType === 'PRODUCT'">
                                <div class="section-title">添加产品细节</div>
                                <template v-if="formData.serviceType === 'PRODUCTION'">
                                    <div class="product-type-selection">
                                        <y-radio-group class="radio-selection" v-model="formData.productType" :horizontal="true">
                                            <y-radio label="COMMODITY">商品</y-radio>
                                            <y-radio label="GIFT">赠品</y-radio>
                                        </y-radio-group>
                                    </div>

                                    <y-form ref="productFormRef" :form-items="productFormItems" :form-data="formData" @field-change="handleProductFieldChange" />
                                </template>

                                <template v-if="formData.serviceType === 'NON_PRODUCTION'">
                                    <y-form ref="serviceFormRef" :form-items="serviceFormItems" :form-data="formData" />

                                    <div class="section-title">添加交付文件</div>
                                    <file-upload type="document" :uploadAction="uploadAction" :files="documentFiles" @update:files="updateDocumentFiles" />
                                </template>
                            </template>
                        </div>
                    </template>

                    <template v-if="currentStep === 3">
                        <div class="tag-files-container">
                            <el-row>
                                <el-col :span="12">
                                    <div class="tag-selector-container">
                                        <TagSelector v-model="formData.tagIds" :options="tagOptions" :categories="tagCategories" placeholder="从标签库选择标签" dialogTitle="案例标签" :showTags="false" @input="updateTagsValue" />
                                    </div>
                                </el-col>
                            </el-row>

                            <div class="tag-title-container">
                                <span class="section-title-v2">案例标签</span>
                                <y-button icon="star" class="refresh-btn" @click="regenerateTags">
                                    <span>重新生成</span>
                                </y-button>
                            </div>

                            <div v-if="formData.tagIds.length > 0" class="selected-tags-container">
                                <y-tag v-for="(tag, index) in selectedTagLabels" :key="index" closable @close="removeTag(tag)">
                                    {{ tag }}
                                </y-tag>
                            </div>

                            <div class="section-title mt-32">添加源文件</div>
                            <file-upload type="document" :uploadAction="uploadAction" :files="formData.sourceFiles" @update:files="updateSourceFiles" />
                        </div>
                    </template>
                </div>
            </div>

            <div class="footer">
                <y-button type="secondary" :disabled="!canGoBack" @click="goBack" class="footer-btn footer-btn-left">
                    返回
                </y-button>
                <y-button type="inverse" @click="nextStep" :disabled="!isFormValid" class="footer-btn footer-btn-right">
                    {{ currentStep === 3 ? "发布案例" : "下一步" }}
                </y-button>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import toolsUtils from "~/utils/toolsUtils";
import YTag from "~/components/y/y-tag.vue";
import YForm from "~/components/y/y-form.vue";
import FileUpload from "~/components/ybiz/file-upload.vue";
import TagSelector from "~/components/ybiz/tag-selector.vue";
import damsAssetInfoApi from "~/api/dams/asset/infoApi";
import damsTagGroupApi from "~/api/dams/tag/groupApi";
// 导入类型和常量
import type { FormData, FileListItem, StepValidationResult, TagGroup, SelectOption } from '../types/asset-form';
import type { FormItemConfig } from '~/components/y/types/form';
import {
  STEP_CONFIG,
  STEP_TITLES,
  CASE_TYPE_OPTIONS,
  SERVICE_TYPE_OPTIONS,
  PRODUCT_TYPE_OPTIONS,
  TAG_GROUP_TYPES,
  VALIDATION_MESSAGES,
  DEBOUNCE_DELAY
} from '../constants/asset-form';

// 导入工具类
import { FormValidationUtils } from '../utils/form-validation';
import { DataProcessor } from '../utils/data-processor';
import { createDraftUtils, DraftUtils, DraftData } from '../utils/draftUtils';


@Component({
    name: "GoodCase",
    components: {
        YTag,
        YForm,
        FileUpload,
        TagSelector
    }
})
export default class GoodCase extends Vue {
    // 步骤管理
    currentStep = 0;
    readonly steps = STEP_CONFIG;
    readonly stepsTitle = STEP_TITLES;

    // 表单状态
    formValid = false;
    initialValidation = false;
    formValidityState = 0;

    // 加载状态
    loading = false;
    submitting = false;

    // 表单数据
    formData: FormData = {
        name: "",
        projectNo: "",
        releaseTime: "",
        customerId: [],
        description: "",
        caseType: CASE_TYPE_OPTIONS.PRODUCT,
        serviceType: SERVICE_TYPE_OPTIONS.PRODUCTION,
        productType: PRODUCT_TYPE_OPTIONS.COMMODITY,
        productSpec: "",
        productMaterial: "",
        materialIds: [],
        productPrice: "",
        productNum: "",
        dueDesc: "",
        serviceFee: "",
        tagIds: [],
        sourceFiles: [],
        mainImage: "",
        sourceFile: "",
        deliverableFileId: ""
    };

    // 选项数据
    brandOptions: SelectOption[] = [];
    materialCategories: SelectOption[] = [];
    materialOptions: Array<SelectOption & { category: string }> = [];
    tagCategories: SelectOption[] = [];
    tagOptions: Array<SelectOption & { category: string }> = [];

    // 文件列表
    imageFiles: FileListItem[] = [];
    documentFiles: FileListItem[] = [];
    uploadAction = "";

    // 草稿相关常量
    private readonly DRAFT_STORAGE_KEY = 'good_case_draft';
    private readonly DRAFT_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000; // 7天过期

    // 草稿工具实例
    private draftUtils!: DraftUtils;

    async mounted() {
        // 初始化草稿工具
        this.draftUtils = createDraftUtils(this, {
            storageKey: this.DRAFT_STORAGE_KEY,
            expireTime: this.DRAFT_EXPIRE_TIME,
            confirmRestore: true
        });

        await this.initializeComponent();
        this.draftUtils.autoLoadAndRestore(this.restoreDraftCallback.bind(this));
    }

    // 草稿恢复回调
    private restoreDraftCallback(draftData: DraftData): void {
        if (draftData.formData) {
            this.formData = { ...this.formData, ...draftData.formData };
        }
        if (draftData.imageFiles) {
            this.imageFiles = DraftUtils.deserializeFileList(draftData.imageFiles);
        }
        if (draftData.documentFiles) {
            this.documentFiles = DraftUtils.deserializeFileList(draftData.documentFiles);
        }
        if (typeof draftData.currentStep === 'number') {
            this.currentStep = draftData.currentStep;
        }
    }

    // 表单配置
    get basicFormItems(): FormItemConfig[] {
        return [
            {
                label: '案例名称',
                field: 'name',
                component: 'y-input',
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '项目号',
                field: 'projectNo',
                component: 'y-input',
                componentProps: {
                    placeholder: '请输入案例项目号'
                },
                required: true,
                colProps: { span: 12 }
            },
            {
                label: '上市日期',
                field: 'releaseTime',
                component: 'y-date-picker',
                componentProps: {
                    type: 'date',
                    placeholder: 'YYYY/MM/DD',
                    format: 'yyyy/MM/dd',
                    valueFormat: 'yyyy-MM-dd'
                },
                required: true,
                colProps: { span: 12 }
            },
            {
                label: '品牌',
                field: 'customerId',
                component: 'y-multi-select-dropdown',
                componentProps: {
                    options: this.brandOptions,
                    placeholder: '添加品牌/客户',
                    dropdownTitle: '相关品牌'
                },
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '案例简介',
                field: 'description',
                component: 'y-input',
                componentProps: {
                    type: 'textarea',
                    rows: 6
                },
                colProps: { span: 24 }
            }
        ];
    }

    // 产品详情表单配置
    get productFormItems(): FormItemConfig[] {
        return [
            {
                label: '产品规格',
                field: 'productSpec',
                component: 'y-input',
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '产品材质',
                field: 'materialIds',
                component: 'tag-selector',
                componentProps: {
                    options: this.materialOptions,
                    categories: this.materialCategories,
                    placeholder: '请选择产品材质',
                    dialogTitle: '产品材质'
                },
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '采购价格',
                field: 'productPrice',
                component: 'y-input',
                required: true,
                componentProps: {
                    type: 'number',
                    placeholder: '请输入产品采购价格'
                },
                colProps: { xs: 24, sm: 12 }
            },
            {
                label: '采购数量',
                field: 'productNum',
                component: 'y-input',
                required: true,
                componentProps: {
                    type: 'number',
                    placeholder: '请输入产品采购数量'
                },
                colProps: { xs: 24, sm: 12 }
            },
            {
                label: '大货工期',
                field: 'dueDesc',
                component: 'y-input',
                required: true,
                colProps: { xs: 24, sm: 12 }
            }
        ];
    }

    // 服务表单配置
    get serviceFormItems(): FormItemConfig[] {
        return [
            {
                label: '服务费用',
                field: 'serviceFee',
                component: 'y-input',
                required: true,
                componentProps: {
                    type: 'number',
                    placeholder: '请输入服务费用'
                },
                colProps: { xs: 24, sm: 12 }
            }
        ];
    }

    // 计算属性
    get stepWidth(): number {
        return ((this.currentStep + 1) / this.steps.length) * 100;
    }

    get isFormValid(): boolean {
        // 使用formValidityState触发重新计算
        const _ = this.formValidityState;
        return this.formValid;
    }

    get canGoBack(): boolean {
        return this.currentStep > 0;
    }

    get selectedTagLabels(): string[] {
        return DataProcessor.getLabelsFromValues(this.formData.tagIds, this.tagOptions);
    }

    get isProductCase(): boolean {
        return this.formData.caseType === CASE_TYPE_OPTIONS.PRODUCT;
    }

    get isProductionService(): boolean {
        return this.formData.serviceType === SERVICE_TYPE_OPTIONS.PRODUCTION;
    }

    get isNonProductionService(): boolean {
        return this.formData.serviceType === SERVICE_TYPE_OPTIONS.NON_PRODUCTION;
    }



    beforeDestroy() {
        // 清理防抖函数
        if (this.debouncedValidate) {
            this.debouncedValidate.cancel();
        }
    }

    // 组件初始化
    private async initializeComponent(): Promise<void> {
        try {
            this.loading = true;
            this.initialValidation = false;
            this.updateServiceType();
            this.formValid = true;
            this.formValidityState++;

            await this.loadTagGroupData();
            this.validateCurrentStep();
        } catch (error) {
            console.error("组件初始化失败:", error);
            this.handleError("初始化失败，请刷新页面重试");

            // 设置默认数据以防止页面崩溃
            this.setDefaultData();
        } finally {
            this.loading = false;
        }
    }

    // 设置默认数据
    private setDefaultData(): void {
        const defaultOptions = DataProcessor.getDefaultOptions();
        if (this.brandOptions.length === 0) {
            this.brandOptions = defaultOptions.brandOptions;
        }
        if (this.materialOptions.length === 0) {
            this.materialOptions = defaultOptions.materialOptions;
        }
        if (this.tagOptions.length === 0) {
            this.tagOptions = defaultOptions.tagOptions;
        }
    }

    // 更新服务类型（当案例类型改变时重置服务类型）
    private updateServiceType(): void {
        if (this.isProductCase) {
            this.formData.serviceType = SERVICE_TYPE_OPTIONS.PRODUCTION;
        } else {
            this.formData.serviceType = SERVICE_TYPE_OPTIONS.NON_PRODUCTION;
        }
    }

    // 加载标签组数据
    private async loadTagGroupData(): Promise<void> {
        try {
            const allGroups = await damsTagGroupApi.getGroupList({
                tagGroupTypes: [TAG_GROUP_TYPES.MATERIAL, TAG_GROUP_TYPES.GENERAL, TAG_GROUP_TYPES.BRAND]
            });

            this.processTagGroups(allGroups);
        } catch (error) {
            console.error("加载标签组数据失败:", error);
            throw new Error("加载数据失败");
        }
    }

    // 处理标签组数据
    private processTagGroups(allGroups: any[]): void {
        // 类型转换和过滤
        const validGroups: TagGroup[] = allGroups.filter(group => group && group.id && group.name);

        const brandGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.BRAND);
        const materialGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.MATERIAL);
        const tagGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.GENERAL);

        this.brandOptions = DataProcessor.convertTagGroupsToOptions(brandGroups);
        this.materialCategories = DataProcessor.convertTagGroupsToCategories(materialGroups);
        this.materialOptions = DataProcessor.convertTagGroupsToOptionsWithCategory(materialGroups);
        this.tagCategories = DataProcessor.convertTagGroupsToCategories(tagGroups);
        this.tagOptions = DataProcessor.convertTagGroupsToOptionsWithCategory(tagGroups);
    }

    // 表单验证方法
    private validateCurrentStep(): void {
        const validation = this.getStepValidation(this.currentStep);
        this.formValid = validation.isValid;
        this.formValidityState++;
    }

    private getStepValidation(step: number): StepValidationResult {
        switch (step) {
            case 0:
                return FormValidationUtils.validateBasicInfo(this.formData);
            case 1:
                return FormValidationUtils.validateMediaFiles(this.imageFiles);
            case 2:
                return this.validateCaseDetails();
            case 3:
                return FormValidationUtils.validateTagsAndFiles(this.formData);
            default:
                return { isValid: false, message: "未知步骤" };
        }
    }

    private validateCaseDetails(): StepValidationResult {
        if (this.isProductCase) {
            if (this.isProductionService) {
                return FormValidationUtils.validateProductDetails(this.formData);
            } else if (this.isNonProductionService) {
                return FormValidationUtils.validateServiceDetails(this.formData, this.documentFiles);
            }
            return { isValid: false, message: "请选择服务类型" };
        }
        return { isValid: true }; // 其他案例类型暂时不需要额外验证
    }

    private validateField(fieldName: string): void {
        try {
            if (this.currentStep === 0 && this.$refs.formRef) {
                const formRef = this.$refs.formRef as any;
                if (formRef.validateField) {
                    formRef.validateField(fieldName);
                }
            } else if (this.currentStep === 2) {
                if (this.isProductCase && this.isProductionService && this.$refs.productFormRef) {
                    const formRef = this.$refs.productFormRef as any;
                    if (formRef.validateField) {
                        formRef.validateField(fieldName);
                    }
                } else if (this.isProductCase && this.isNonProductionService && this.$refs.serviceFormRef) {
                    const formRef = this.$refs.serviceFormRef as any;
                    if (formRef.validateField) {
                        formRef.validateField(fieldName);
                    }
                }
            }
        } catch (error) {
            console.error("字段验证失败:", error);
        }
    }

    // 防抖验证方法
    private debouncedValidate = toolsUtils.debounce(() => {
        this.validateCurrentStep();
    }, DEBOUNCE_DELAY);

    // 监听器
    @Watch("formData", { deep: true })
    private onFormDataChange(): void {
        this.debouncedValidate();
    }

    @Watch("currentStep")
    private onStepChange(): void {
        this.$nextTick(() => {
            this.validateCurrentStep();
        });
    }

    @Watch("imageFiles")
    @Watch("documentFiles")
    private onFilesChange(): void {
        this.validateCurrentStep();
    }

    // 表单验证
    private validateForm(): void {
        this.initialValidation = true;
        if (this.currentStep === 0 && this.$refs.formRef) {
            (this.$refs.formRef as any).validate((valid: boolean) => {
                this.formValid = valid;
                this.formValidityState++;
            });
        } else if (this.currentStep === 2) {
            if (this.isProductCase && this.isProductionService && this.$refs.productFormRef) {
                (this.$refs.productFormRef as any).validate((valid: boolean) => {
                    this.formValid = valid;
                    this.formValidityState++;
                });
            } else if (this.isProductCase && this.isNonProductionService && this.$refs.serviceFormRef) {
                (this.$refs.serviceFormRef as any).validate((valid: boolean) => {
                    this.formValid = valid;
                    this.formValidityState++;
                });
            } else {
                this.formValid = true;
                this.formValidityState++;
            }
        } else {
            this.formValid = true;
            this.formValidityState++;
        }
    }

    // 步骤导航
    private goBack(): void {
        if (this.canGoBack) {
            this.currentStep--;
        }
    }

    private async nextStep(): Promise<void> {
        console.log(this.formData);
        this.initialValidation = true;

        const validation = this.getStepValidation(this.currentStep);

        if (!validation.isValid) {
            this.showValidationError(validation.message || "请完善当前步骤信息");
            this.formValid = false;
            this.formValidityState++;
            return;
        }

        // 特殊处理需要表单验证的步骤
        if (this.currentStep === 0) {
            await this.validateFirstStep();
            return;
        }

        if (this.currentStep === 2) {
            await this.validateThirdStep();
            return;
        }

        // 最后一步提交表单
        if (this.currentStep === 3) {
            await this.submitForm();
            return;
        }

        // 其他步骤直接进入下一步
        this.proceedToNextStep();
    }

    private async validateFirstStep(): Promise<void> {
        if (!this.$refs.formRef) {
            console.error("表单引用不存在");
            return;
        }

        try {
            const formRef = this.$refs.formRef as any;

            if (typeof formRef.validate !== 'function') {
                console.error("validate方法不存在");
                this.showValidationError("表单验证方法不可用");
                return;
            }

            const valid = await formRef.validate();

            if (valid) {
                this.proceedToNextStep();
            } else {
                this.showValidationError("请完善必填信息");
                this.formValid = false;
                this.formValidityState++;
            }
        } catch (error) {
            console.error("表单验证失败:", error);
            this.showValidationError("表单验证失败");
            this.formValid = false;
            this.formValidityState++;
        }
    }

    private async validateThirdStep(): Promise<void> {
        if (this.isProductCase) {
            if (this.isProductionService) {
                await this.validateProductForm();
            } else if (this.isNonProductionService) {
                await this.validateServiceForm();
            } else {
                this.showValidationError("请选择服务类型");
                this.formValid = false;
                this.formValidityState++;
            }
        } else {
            // 其他案例类型直接进入下一步
            this.proceedToNextStep();
        }
    }

    private async validateProductForm(): Promise<void> {
        if (!this.$refs.productFormRef) {
            console.error("产品表单引用不存在");
            return;
        }

        try {
            const formRef = this.$refs.productFormRef as any;
            const valid = await formRef.validate();

            if (valid) {
                this.proceedToNextStep();
            } else {
                this.showValidationError("请完善产品详情信息");
                this.formValid = false;
                this.formValidityState++;
            }
        } catch (error) {
            console.error("产品表单验证失败:", error);
            this.showValidationError("产品表单验证失败");
            this.formValid = false;
            this.formValidityState++;
        }
    }

    private async validateServiceForm(): Promise<void> {
        if (!this.$refs.serviceFormRef) {
            console.error("服务表单引用不存在");
            return;
        }

        try {
            const formRef = this.$refs.serviceFormRef as any;
            const valid = await formRef.validate();

            if (valid) {
                // 还需要验证交付文件
                if (this.documentFiles.length === 0) {
                    this.showValidationError("请上传交付文件");
                    this.formValid = false;
                    this.formValidityState++;
                } else {
                    this.proceedToNextStep();
                }
            } else {
                this.showValidationError("请完善服务详情信息");
                this.formValid = false;
                this.formValidityState++;
            }
        } catch (error) {
            console.error("服务表单验证失败:", error);
            this.showValidationError("服务表单验证失败");
            this.formValid = false;
            this.formValidityState++;
        }
    }

    private proceedToNextStep(): void {
        this.currentStep++;
        this.formValid = true;
        this.formValidityState++;
    }

    private showValidationError(message: string): void {
        this.$notify.warning({
            title: "提示",
            message
        });
    }

    // 表单字段验证方法
    private clearBrandValidationError(): void {
        try {
            if (this.$refs.formRef) {
                const form = this.$refs.formRef as any;
                if (form.clearValidate) {
                    form.clearValidate("customerId");
                }
            }
        } catch (error) {
            console.error("清除验证错误失败:", error);
        }
    }

    private validateBrandField(): void {
        this.validateField("customerId");
    }

    // 字段事件处理
    private handleFieldFocus(data: { field: string; event: Event }): void {
        if (data.field === 'customerId') {
            this.clearBrandValidationError();
        }
    }

    private handleFieldBlur(data: { field: string; event: Event }): void {
        if (data.field === 'customerId') {
            this.validateBrandField();
        }
    }

    // 产品字段变化处理
    private handleProductFieldChange(data: { field: string; value: any }): void {
        if (data.field === 'materialIds') {
            this.updateMaterialValue(data.value);
        }
    }

    // 文件更新方法
    private updateImageFiles(files: FileListItem[]): void {
        this.imageFiles = files;
    }

    private updateDocumentFiles(files: FileListItem[]): void {
        this.documentFiles = files;
    }

    private updateSourceFiles(files: FileListItem[]): void {
        console.log(this.formData.sourceFiles);
        this.formData.sourceFiles = files;
    }

    private getAllUploadedFiles(): FileListItem[] {
        return [...this.imageFiles, ...this.documentFiles, ...this.formData.sourceFiles];
    }

    // 步骤导航
    private goToStep(index: number): void {
        // 检查是否可以跳转到目标步骤
        if (this.canJumpToStep(index)) {
            this.currentStep = index;
        }
    }

    // 检查是否可以跳转到指定步骤
    private canJumpToStep(targetStep: number): boolean {
        // 如果是第一步，直接允许
        if (targetStep === 0) {
            return true;
        }

        // 检查目标步骤之前的所有步骤是否都已验证通过
        for (let step = 0; step < targetStep; step++) {
            const validation = this.getStepValidation(step);
            if (!validation.isValid) {
                return false;
            }
        }

        return true;
    }

    // 案例类型变化监听（已合并到formData的深度监听中）
    @Watch("formData.caseType")
    private onCaseTypeChange(): void {
        this.updateServiceType();
        this.validateCurrentStep();
    }

    // 数据更新方法
    private updateMaterialValue(selectedMaterials: string[]): void {
        this.formData.materialIds = selectedMaterials;
        this.formData.productMaterial = DataProcessor.getLabelsFromValues(selectedMaterials, this.materialOptions).join(", ");
        this.validateCurrentStep();
    }

    private updateTagsValue(selectedTags: string[]): void {
        this.formData.tagIds = selectedTags;
        this.validateCurrentStep();
    }

    // 标签操作方法
    private regenerateTags(): void {
        if (this.tagOptions.length === 0) {
            this.showValidationError("暂无可用标签");
            return;
        }

        this.formData.tagIds = DataProcessor.generateRandomTags(this.tagOptions);
        this.validateCurrentStep();
    }

    private removeTag(tag: string): void {
        const tagOption = this.tagOptions.find(t => t.label === tag);
        if (tagOption) {
            this.formData.tagIds = this.formData.tagIds.filter(t => t !== tagOption.value);
            this.validateCurrentStep();
        }
    }

    // 草稿保存
    private saveDraft(): void {
        this.draftUtils.saveDraft({
            formData: this.formData,
            imageFiles: DraftUtils.serializeFileList(this.imageFiles),
            documentFiles: DraftUtils.serializeFileList(this.documentFiles),
            currentStep: this.currentStep
        });
    }

    // 提交表单
    private async submitForm(): Promise<void> {
        if (this.submitting) return;

        try {
            this.submitting = true;
            const submitData = DataProcessor.buildSubmitData({
                formData: this.formData,
                imageFiles: this.imageFiles,
                documentFiles: this.documentFiles,
                type: "GOOD_CASE"
            });

            console.log("提交数据:", submitData);

            const result = await damsAssetInfoApi.add(submitData);
            console.log("提交结果:", result);

            this.$notify.success({
                title: "成功",
                message: "案例提交成功"
            });

            // 提交成功后清除草稿
            this.draftUtils.clearDraft();

            this.$router.push("/");
        } catch (error) {
            console.error("提交失败:", error);
            const errorMessage = error instanceof Error ? error.message : "提交失败，请重试";
            this.$notify.error({
                title: "发布失败",
                message: errorMessage
            });
        } finally {
            this.submitting = false;
        }
    }

    // 错误处理
    private handleError(message: string): void {
        this.$notify.error({
            title: "错误",
            message
        });
    }

    // 关闭页面
    private onClose(): void {
        this.$router.push("/");
    }
}
</script>
<style lang="scss" scoped>
@import "./add.scss";
</style>
