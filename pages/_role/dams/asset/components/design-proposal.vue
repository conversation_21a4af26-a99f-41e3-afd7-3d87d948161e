<template>
    <div class="add-container">
        <header class="header">
            <div class="logo">
                <img src="~/static/img/logo.svg" />
            </div>
            <div class="header-right">
                <y-button type="secondary" @click="saveDraft">保存草稿</y-button>
                <div class="close-icon" @click="onClose">
                    <y-svg-icon name="close" color="#fff" />
                </div>
            </div>
        </header>

        <div class="content">
            <div class="step-bar">
                <div class="step-line">
                    <div class="step-active" :style="{ width: stepWidth + '%' }"></div>
                </div>
            </div>

            <div class="main-content">
                <div class="sidebar">
                    <y-sidebar>
                        <y-sidebar-item v-for="(step, index) in steps" :key="index" :active="currentStep === index" :icon="step.icon" :activeIcon="step.activeIcon" @click="goToStep(index)">
                            {{ step.name }}
                        </y-sidebar-item>
                    </y-sidebar>
                </div>

                <div class="form-content">
                    <div class="form-title">{{ stepsTitle[currentStep] }}</div>

                    <!-- 步骤1：基础信息 -->
                    <template v-if="currentStep === 0">
                        <y-form ref="formRef" :form-items="basicFormItems" :form-data="formData" @field-focus="handleFieldFocus" @field-blur="handleFieldBlur" />
                    </template>

                    <!-- 步骤2：媒体资源 -->
                    <template v-if="currentStep === 1">
                        <file-upload type="image" :uploadAction="uploadAction" :files="imageFiles" @update:files="updateImageFiles" />

                        <div class="section-title mt-32">添加设计提案文件</div>
                        <file-upload type="document" :uploadAction="uploadAction" :files="designFile" @update:files="updateDesignFile" />
                    </template>

                    <!-- 步骤3：标签和源文件 -->
                    <template v-if="currentStep === 2">
                        <div class="tag-files-container">
                            <el-row>
                                <el-col :span="12">
                                    <div class="tag-selector-container">
                                        <TagSelector v-model="formData.tagIds" :options="tagOptions" :categories="tagCategories" placeholder="从标签库选择标签" dialogTitle="提案标签" :showTags="false" @input="updateTagsValue" />
                                    </div>
                                </el-col>
                            </el-row>

                            <div class="tag-title-container">
                                <span class="section-title-v2">提案标签</span>
                                <y-button icon="star" class="refresh-btn" @click="regenerateTags">
                                    <span>重新生成</span>
                                </y-button>
                            </div>

                            <div v-if="formData.tagIds.length > 0" class="selected-tags-container">
                                <y-tag v-for="(tag, index) in selectedTagLabels" :key="index" closable @close="removeTag(tag)">
                                    {{ tag }}
                                </y-tag>
                            </div>

                            <div class="section-title mt-32">添加源文件</div>
                            <file-upload type="document" :uploadAction="uploadAction" :files="formData.sourceFiles" @update:files="updateSourceFiles" />
                        </div>
                    </template>
                </div>
            </div>

            <div class="footer">
                <y-button type="secondary" :disabled="!canGoBack" @click="goBack" class="footer-btn footer-btn-left">
                    返回
                </y-button>
                <y-button type="inverse" @click="nextStep" :disabled="!isFormValid" class="footer-btn footer-btn-right">
                    {{ isLastStep ? "发布提案" : "下一步" }}
                </y-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import toolsUtils from "~/utils/toolsUtils";
import localStorageUtils from "~/utils/localStorageUtils";
import YTag from "~/components/y/y-tag.vue";
import YForm from "~/components/y/y-form.vue";
import FileUpload from "~/components/ybiz/file-upload.vue";
import TagSelector from "~/components/ybiz/tag-selector.vue";
import damsAssetInfoApi from "~/api/dams/asset/infoApi";
import damsTagGroupApi from "~/api/dams/tag/groupApi";
// 导入类型和常量
import type { FormData, FileListItem, StepValidationResult, TagGroup, SelectOption } from '../types/asset-form';
import type { FormItemConfig } from '~/components/y/types/form';
import {
    TAG_GROUP_TYPES,
    DEBOUNCE_DELAY,
    PROPOSAL_STEP_CONFIG,
    PROPOSAL_STEP_TITLES,
    PROPOSAL_TOTAL_STEPS,
    ASSET_TYPES,
    VALIDATION_MESSAGES
} from '../constants/asset-form';

// 导入工具类
import { FormValidationUtils } from '../utils/form-validation';
import { DataProcessor } from '../utils/data-processor';
import { createDraftUtils, DraftUtils, DraftData } from '../utils/draftUtils';

@Component({
    name: "DesignProposal",
    components: {
        YTag,
        YForm,
        FileUpload,
        TagSelector
    }
})
export default class DesignProposal extends Vue {
    // 草稿工具实例
    private draftUtils!: DraftUtils;

    // 步骤管理
    currentStep = 0;
    readonly steps = PROPOSAL_STEP_CONFIG;
    readonly stepsTitle = PROPOSAL_STEP_TITLES;
    readonly totalSteps = PROPOSAL_TOTAL_STEPS;

    // 表单状态
    formValid = false;
    initialValidation = false;
    formValidityState = 0;

    // 加载状态
    loading = false;
    submitting = false;

    // 表单数据
    formData: FormData = this.getInitialFormData();

    // 选项数据
    brandOptions: SelectOption[] = [];
    tagCategories: SelectOption[] = [];
    tagOptions: Array<SelectOption & { category: string }> = [];

    // 文件列表
    imageFiles: FileListItem[] = [];
    uploadAction = "";
    designFile: FileListItem[] = [];

    // 获取初始表单数据
    private getInitialFormData(): FormData {
        return {
            name: "",
            projectNo: "",
            releaseTime: "",
            customerId: [],
            description: "",
            caseType: "",
            serviceType: "",
            productType: "",
            productSpec: "",
            productMaterial: "",
            materialIds: [],
            productPrice: "",
            productNum: "",
            dueDesc: "",
            serviceFee: "",
            tagIds: [],
            sourceFiles: [],
            mainImage: "",
            sourceFile: "",
            deliverableFileId: ""
        };
    }

    // 表单配置
    get basicFormItems(): FormItemConfig[] {
        return [
            {
                label: '提案名称',
                field: 'name',
                component: 'y-input',
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '项目号',
                field: 'projectNo',
                component: 'y-input',
                componentProps: {
                    placeholder: '请输入提案项目号'
                },
                required: true,
                colProps: { span: 12 }
            },
            {
                label: '提交日期',
                field: 'releaseTime',
                component: 'y-date-picker',
                componentProps: {
                    type: 'date',
                    placeholder: 'YYYY/MM/DD',
                    format: 'yyyy/MM/dd',
                    valueFormat: 'yyyy-MM-dd'
                },
                required: true,
                colProps: { span: 12 }
            },
            {
                label: '品牌',
                field: 'customerId',
                component: 'y-multi-select-dropdown',
                componentProps: {
                    options: this.brandOptions,
                    placeholder: '添加品牌/客户',
                    dropdownTitle: '相关品牌'
                },
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '提案简介',
                field: 'description',
                component: 'y-input',
                componentProps: {
                    type: 'textarea',
                    rows: 6
                },
                colProps: { span: 24 }
            }
        ];
    }

    // 计算属性
    get stepWidth(): number {
        return ((this.currentStep + 1) / this.totalSteps) * 100;
    }

    get isFormValid(): boolean {
        // 使用formValidityState触发重新计算
        const _ = this.formValidityState;
        return this.formValid;
    }

    get canGoBack(): boolean {
        return this.currentStep > 0;
    }

    get isLastStep(): boolean {
        return this.currentStep === this.totalSteps - 1;
    }

    get selectedTagLabels(): string[] {
        return DataProcessor.getLabelsFromValues(this.formData.tagIds, this.tagOptions);
    }

    async mounted() {
        // 初始化草稿工具
        this.draftUtils = createDraftUtils(this, {
            storageKey: 'design_proposal_draft',
            expireTime: 7 * 24 * 60 * 60 * 1000
        });
        
        await this.initializeComponent();
        
        // 自动加载和恢复草稿数据
        await this.draftUtils.autoLoadAndRestore(this.restoreDraftCallback);
    }

    // 草稿数据恢复回调
    private restoreDraftCallback = (draftData: DraftData) => {
        if (draftData.formData) {
            this.formData = { ...this.formData, ...draftData.formData };
        }
        if (draftData.imageFiles) {
            this.imageFiles = draftData.imageFiles;
        }
        if (draftData.extraImages) {
            this.designFile = draftData.extraImages;
        }
        if (draftData.currentStep !== undefined) {
            this.currentStep = draftData.currentStep;
        }
        
        // 触发表单验证
        this.$nextTick(() => {
            this.validateCurrentStep();
        });
    };
    beforeDestroy() {
        // 清理防抖函数
        if (this.debouncedValidate) {
            this.debouncedValidate.cancel();
        }
    }

    // 组件初始化
    private async initializeComponent(): Promise<void> {
        try {
            this.loading = true;
            this.formValid = true; // 初始状态设为有效，避免按钮禁用

            await this.loadTagGroupData();
            this.validateCurrentStep();
        } catch (error) {
            console.error("组件初始化失败:", error);
            this.handleError(VALIDATION_MESSAGES.FORM_DATA_MISSING);
            this.setDefaultData();
        } finally {
            this.loading = false;
        }
    }

    // 设置默认数据
    private setDefaultData(): void {
        const defaultOptions = DataProcessor.getDefaultOptions();
        this.brandOptions = this.brandOptions.length === 0 ? defaultOptions.brandOptions : this.brandOptions;
        this.tagOptions = this.tagOptions.length === 0 ? defaultOptions.tagOptions : this.tagOptions;
    }

    // 加载标签组数据
    private async loadTagGroupData(): Promise<void> {
        try {
            const allGroups = await damsTagGroupApi.getGroupList({
                tagGroupTypes: [TAG_GROUP_TYPES.GENERAL, TAG_GROUP_TYPES.BRAND]
            });

            this.processTagGroups(allGroups);
        } catch (error) {
            console.error("加载标签组数据失败:", error);
            throw new Error(VALIDATION_MESSAGES.FORM_DATA_MISSING);
        }
    }

    // 处理标签组数据
    private processTagGroups(allGroups: any[]): void {
        if (!Array.isArray(allGroups)) {
            this.setDefaultData();
            return;
        }

        // 类型转换和过滤
        const validGroups: TagGroup[] = allGroups.filter(group => group && group.id && group.name);

        const brandGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.BRAND);
        const tagGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.GENERAL);

        this.brandOptions = DataProcessor.convertTagGroupsToOptions(brandGroups);
        this.tagCategories = DataProcessor.convertTagGroupsToCategories(tagGroups);
        this.tagOptions = DataProcessor.convertTagGroupsToOptionsWithCategory(tagGroups);
    }

    // 表单验证方法
    private validateCurrentStep(): void {
        const validation = this.getStepValidation(this.currentStep);
        this.formValid = validation.isValid;
        this.formValidityState++;
    }

    private getStepValidation(step: number): StepValidationResult {
        switch (step) {
            case 0:
                return FormValidationUtils.validateBasicInfo(this.formData);
            case 1:
                // 验证媒体文件和设计提案文件
                const mediaFilesValidation = FormValidationUtils.validateMediaFiles(this.imageFiles);
                if (!mediaFilesValidation.isValid) {
                    return mediaFilesValidation;
                }

                return FormValidationUtils.validateDesignFiles(this.designFile);
            case 2:
                return FormValidationUtils.validateTagsAndFiles(this.formData);
            default:
                return { isValid: false, message: "未知步骤" };
        }
    }

    // 防抖验证方法
    private debouncedValidate = toolsUtils.debounce(() => {
        this.validateCurrentStep();
    }, DEBOUNCE_DELAY);

    // 监听器
    @Watch("formData", { deep: true })
    private onFormDataChange(): void {
        this.debouncedValidate();
    }

    @Watch("currentStep")
    private onStepChange(): void {
        this.$nextTick(() => {
            this.validateCurrentStep();
        });
    }

    @Watch("imageFiles")
    private onFilesChange(): void {
        this.validateCurrentStep();
    }

    @Watch("designFile")
    private onDesignFileChange(): void {
        this.validateCurrentStep();
    }

    // 步骤导航
    private goBack(): void {
        if (this.canGoBack) {
            this.currentStep--;
        }
    }

    private async nextStep(): Promise<void> {
        this.initialValidation = true;

        const validation = this.getStepValidation(this.currentStep);
        if (!validation.isValid) {
            this.showValidationError(validation.message || VALIDATION_MESSAGES.FORM_DATA_MISSING);
            this.formValid = false;
            this.formValidityState++;
            return;
        }

        // 特殊处理需要表单验证的步骤
        if (this.currentStep === 0) {
            await this.validateFirstStep();
            return;
        }

        // 最后一步提交表单
        if (this.isLastStep) {
            await this.submitForm();
            return;
        }

        // 其他步骤直接进入下一步
        this.proceedToNextStep();
    }

    private async validateFirstStep(): Promise<void> {
        if (!this.$refs.formRef) {
            console.error("表单引用不存在");
            return;
        }

        try {
            const formRef = this.$refs.formRef as any;
            const valid = await formRef.validate();

            if (valid) {
                this.proceedToNextStep();
            } else {
                this.showValidationError(VALIDATION_MESSAGES.FORM_DATA_MISSING);
                this.formValid = false;
                this.formValidityState++;
            }
        } catch (error) {
            console.error("表单验证失败:", error);
            this.showValidationError(VALIDATION_MESSAGES.FORM_DATA_MISSING);
            this.formValid = false;
            this.formValidityState++;
        }
    }

    private proceedToNextStep(): void {
        this.currentStep++;
        this.formValid = true;
        this.formValidityState++;
    }

    private showValidationError(message: string): void {
        this.$notify.warning({
            title: "提示",
            message
        });
    }

    // 字段事件处理
    private handleFieldFocus(data: { field: string; event: Event }): void {
        if (data.field === 'customerId') {
            this.clearFieldValidation('customerId');
        }
    }

    private handleFieldBlur(data: { field: string; event: Event }): void {
        if (data.field === 'customerId') {
            this.validateField('customerId');
        }
    }

    // 表单字段验证辅助方法
    private clearFieldValidation(fieldName: string): void {
        try {
            const formRef = this.$refs.formRef as any;
            if (formRef?.clearValidate) {
                formRef.clearValidate(fieldName);
            }
        } catch (error) {
            console.error(`清除${fieldName}验证错误失败:`, error);
        }
    }

    private validateField(fieldName: string): void {
        try {
            const formRef = this.$refs.formRef as any;
            if (formRef?.validateField) {
                formRef.validateField(fieldName);
            }
        } catch (error) {
            console.error(`验证${fieldName}失败:`, error);
        }
    }

    // 文件更新方法
    private updateImageFiles(files: FileListItem[]): void {
        this.imageFiles = files;
    }

    private updateSourceFiles(files: FileListItem[]): void {
        this.formData.sourceFiles = files;
    }

    private updateDesignFile(files: FileListItem[]): void {
        this.designFile = files;
    }

    // 步骤导航
    private goToStep(index: number): void {
        if (this.canJumpToStep(index)) {
            this.currentStep = index;
        } else {
            this.showValidationError("请先完成前面的步骤");
        }
    }

    // 检查是否可以跳转到指定步骤
    private canJumpToStep(targetStep: number): boolean {
        // 如果是第一步，直接允许
        if (targetStep === 0) return true;

        // 检查目标步骤之前的所有步骤是否都已验证通过
        for (let step = 0; step < targetStep; step++) {
            if (!this.getStepValidation(step).isValid) {
                return false;
            }
        }

        return true;
    }

    // 标签操作方法
    private updateTagsValue(selectedTags: string[]): void {
        this.formData.tagIds = selectedTags;
        this.validateCurrentStep();
    }

    private regenerateTags(): void {
        if (this.tagOptions.length === 0) {
            this.showValidationError("暂无可用标签");
            return;
        }

        this.formData.tagIds = DataProcessor.generateRandomTags(this.tagOptions);
        this.validateCurrentStep();
    }

    private removeTag(tag: string): void {
        const tagOption = this.tagOptions.find(t => t.label === tag);
        if (tagOption) {
            this.formData.tagIds = this.formData.tagIds.filter(t => t !== tagOption.value);
            this.validateCurrentStep();
        }
    }

    // 草稿保存
    private saveDraft(): void {
        try {
            const draftData = {
                formData: this.formData,
                imageFiles: this.imageFiles,
                extraImages: this.designFile,
                currentStep: this.currentStep
            };

            this.draftUtils.saveDraft(draftData);

            this.$notify.success({
                title: "成功",
                message: "提案草稿已保存"
            });
        } catch (error) {
            console.error("保存草稿失败:", error);
            this.$notify.error({
                title: "错误",
                message: "保存草稿失败，请重试"
            });
        }
    }



    // 提交表单
    private async submitForm(): Promise<void> {
        if (this.submitting) return;

        try {
            this.submitting = true;
            const submitData = DataProcessor.buildSubmitData({
                formData: this.formData,
                imageFiles: this.imageFiles,
                documentFiles: [],
                type: ASSET_TYPES.DESIGN_PROPOSAL,
                designFile: this.designFile
            });

            const result = await damsAssetInfoApi.add(submitData);

            this.$notify.success({
                title: "成功",
                message: "提案提交成功"
            });
            
            // 提交成功后清除草稿
            this.draftUtils.clearDraft();
            
            this.$router.push("/");
        } catch (error) {
            console.error("提交失败:", error);
            const errorMessage = error instanceof Error ? error.message : "提交失败，请重试";
            this.$notify.error({
                title: "发布失败",
                message: errorMessage
            });
        } finally {
            this.submitting = false;
        }
    }

    // 错误处理
    private handleError(message: string): void {
        this.$notify.error({
            title: "错误",
            message
        });
    }

    // 关闭页面
    private onClose(): void {
        this.$router.push("/");
    }
}
</script>

<style lang="scss" scoped>
@import "./add.scss";
</style>
