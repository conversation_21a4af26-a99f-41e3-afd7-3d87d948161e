<template>
    <div class="add-container">
        <header class="header">
            <div class="logo">
                <img src="~/static/img/logo.svg" />
            </div>
            <div class="header-right">
                <y-button type="secondary" @click="saveDraft">保存草稿</y-button>
                <div class="close-icon" @click="onClose">
                    <y-svg-icon name="close" color="#fff" />
                </div>
            </div>
        </header>

        <div class="content">
            <div class="main-content">
                <div class="form-content centered-form">
                    <!-- 3D模型上传（对应步骤一：基础信息） -->
                    <div class="form-section">
                        <div class="section-title">3D模型上传</div>
                        <y-form ref="formRef" :form-items="basicFormItems" :form-data="formData" @field-focus="handleFieldFocus" @field-blur="handleFieldBlur" />

                        <!-- 古德原创开关 -->
                        <div class="original-switch-container">
                            <div class="original-switch-label">古德原创<span class="required-mark">*</span></div>
                            <el-switch v-model="formData.original" @change="handleOriginalChange"> </el-switch>
                        </div>
                    </div>

                    <!-- 分隔线 -->
                    <div class="section-divider"></div>

                    <!-- 添加作品封面（对应步骤二：媒体资源） -->
                    <div class="form-section">
                        <div class="section-title">添加作品封面</div>
                        <file-upload type="image" :uploadAction="uploadAction" :files="imageFiles" @update:files="updateImageFiles" />
                    </div>

                    <!-- 分隔线 -->
                    <div class="section-divider"></div>

                    <div class="form-section">
                        <div class="section-title">素材补充</div>
                        <file-upload type="image" :uploadAction="uploadAction" :extraImages="extraImages" @update:extraImages="updateExtraImageFiles" enableTabs />
                    </div>

                    <!-- 分隔线 -->
                    <div class="section-divider"></div>

                    <!-- 添加标签（对应步骤三：标签和源文件） -->
                    <div class="form-section">
                        <div class="section-title">添加标签</div>
                        <div class="tag-files-container">
                            <el-row>
                                <el-col :span="12">
                                    <div class="tag-selector-container">
                                        <TagSelector v-model="formData.tagIds" :options="tagOptions" :categories="tagCategories" placeholder="从标签库选择标签" dialogTitle="作品标签" :showTags="false" @input="updateTagsValue" />
                                    </div>
                                </el-col>
                            </el-row>

                            <div class="tag-title-container">
                                <span class="section-title-v2">作品标签</span>
                                <y-button icon="star" class="refresh-btn" @click="regenerateTags">
                                    <span>重新生成</span>
                                </y-button>
                            </div>

                            <div v-if="formData.tagIds.length > 0" class="selected-tags-container">
                                <y-tag v-for="(tag, index) in selectedTagLabels" :key="index" closable @close="removeTag(tag)">
                                    {{ tag }}
                                </y-tag>
                            </div>

                            <div class="section-title mt-32">添加源文件</div>
                            <file-upload type="document" :uploadAction="uploadAction" :files="formData.sourceFiles" @update:files="updateSourceFiles" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer">
                <y-button type="secondary" @click="onClose" class="footer-btn footer-btn-left">
                    取消
                </y-button>
                <y-button type="inverse" @click="submitForm" :disabled="!isFormValid" class="footer-btn footer-btn-right">
                    提交
                </y-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import toolsUtils from "~/utils/toolsUtils";
import YTag from "~/components/y/y-tag.vue";
import YForm from "~/components/y/y-form.vue";
import FileUpload from "~/components/ybiz/file-upload.vue";
import TagSelector from "~/components/ybiz/tag-selector.vue";
import damsAssetInfoApi from "~/api/dams/asset/infoApi";
import damsTagGroupApi from "~/api/dams/tag/groupApi";
// 导入类型和常量
import type { FormData, FileListItem, StepValidationResult, TagGroup, SelectOption } from '../types/asset-form';
import type { FormItemConfig } from '~/components/y/types/form';
import { TAG_GROUP_TYPES, DEBOUNCE_DELAY } from '../constants/asset-form';

// 导入工具类
import { FormValidationUtils } from '../utils/form-validation';
import { DataProcessor } from '../utils/data-processor';
import { createDraftUtils, DraftUtils, DraftData } from "../utils/draftUtils";

@Component({
    name: "Model3D",
    components: {
        YTag,
        YForm,
        FileUpload,
        TagSelector
    }
})
export default class Model3D extends Vue {
    // draftUtils实例
    private draftUtils!: DraftUtils;

    // 表单状态
    formValid = false;
    initialValidation = false;
    formValidityState = 0;

    // 加载状态
    loading = false;
    submitting = false;

    // 表单数据
    formData: FormData = {
        name: "",
        projectNo: "",
        releaseTime: "",
        customerId: [],
        description: "",
        caseType: "",
        serviceType: "",
        productType: "",
        productSpec: "",
        productMaterial: "",
        materialIds: [],
        productPrice: "",
        productNum: "",
        dueDesc: "",
        serviceFee: "",
        tagIds: [],
        sourceFiles: [],
        mainImage: "",
        sourceFile: "",
        deliverableFileId: "",
        original: true
    };

    // 选项数据
    brandOptions: SelectOption[] = [];
    tagCategories: SelectOption[] = [];
    tagOptions: Array<SelectOption & { category: string }> = [];

    // 文件列表
    imageFiles: FileListItem[] = [];
    extraImages: FileListItem[] = [];
    uploadAction = "";

    // 表单配置
    get basicFormItems(): FormItemConfig[] {
        return [
            {
                label: '作品名称',
                field: 'name',
                component: 'y-input',
                required: true,
                colProps: { span: 12 }
            },
            {
                label: '项目号',
                field: 'projectNo',
                component: 'y-input',
                componentProps: {
                    placeholder: '请输入作品项目号'
                },
                required: true,
                colProps: { span: 12 }
            },
            {
                label: '品牌',
                field: 'customerId',
                component: 'y-multi-select-dropdown',
                componentProps: {
                    options: this.brandOptions,
                    placeholder: '添加品牌/客户',
                    dropdownTitle: '相关品牌'
                },
                required: true,
                colProps: { span: 24 }
            },
            {
                label: '作品简介',
                field: 'description',
                component: 'y-input',
                componentProps: {
                    type: 'textarea',
                    rows: 6
                },
                colProps: { span: 24 }
            }
        ];
    }

    // 计算属性
    get isFormValid(): boolean {
        // 使用formValidityState触发重新计算
        const _ = this.formValidityState;
        return this.formValid;
    }

    get selectedTagLabels(): string[] {
        return DataProcessor.getLabelsFromValues(this.formData.tagIds, this.tagOptions);
    }

    async mounted() {
        // 初始化draftUtils
        this.draftUtils = createDraftUtils(this, {
            storageKey: 'model_3d_draft',
            expireTime: 7 * 24 * 60 * 60 * 1000
        });

        await this.initializeComponent();
        // 初始化完成后尝试恢复草稿
        this.draftUtils.autoLoadAndRestore(this.restoreDraftCallback);
    }

    // 草稿恢复回调
    private restoreDraftCallback = (draftData: DraftData): void => {
        if (draftData.formData) {
            this.formData = { ...this.formData, ...draftData.formData };
        }
        if (draftData.imageFiles) {
            this.imageFiles = DraftUtils.deserializeFileList(draftData.imageFiles);
        }
        if (draftData.extraImages) {
            this.extraImages = DraftUtils.deserializeFileList(draftData.extraImages);
        }
    };
    beforeDestroy() {
        // 清理防抖函数
        if (this.debouncedValidate) {
            this.debouncedValidate.cancel();
        }
    }

    // 组件初始化
    private async initializeComponent(): Promise<void> {
        try {
            this.loading = true;
            this.initialValidation = false;
            this.formValid = true;
            this.formValidityState++;

            await this.loadTagGroupData();
            this.validateForm();
        } catch (error) {
            console.error("组件初始化失败:", error);
            this.handleError("初始化失败，请刷新页面重试");

            // 设置默认数据以防止页面崩溃
            this.setDefaultData();
        } finally {
            this.loading = false;
        }
    }

    // 设置默认数据
    private setDefaultData(): void {
        const defaultOptions = DataProcessor.getDefaultOptions();
        if (this.brandOptions.length === 0) {
            this.brandOptions = defaultOptions.brandOptions;
        }
        if (this.tagOptions.length === 0) {
            this.tagOptions = defaultOptions.tagOptions;
        }
    }

    // 加载标签组数据
    private async loadTagGroupData(): Promise<void> {
        try {
            const allGroups = await damsTagGroupApi.getGroupList({
                tagGroupTypes: [TAG_GROUP_TYPES.GENERAL, TAG_GROUP_TYPES.BRAND]
            });

            this.processTagGroups(allGroups);
        } catch (error) {
            console.error("加载标签组数据失败:", error);
            throw new Error("加载数据失败");
        }
    }

    // 处理标签组数据
    private processTagGroups(allGroups: any[]): void {
        // 类型转换和过滤
        const validGroups: TagGroup[] = allGroups.filter(group => group && group.id && group.name);

        const brandGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.BRAND);
        const tagGroups = validGroups.filter(group => group.type === TAG_GROUP_TYPES.GENERAL);

        this.brandOptions = DataProcessor.convertTagGroupsToOptions(brandGroups);
        this.tagCategories = DataProcessor.convertTagGroupsToCategories(tagGroups);
        this.tagOptions = DataProcessor.convertTagGroupsToOptionsWithCategory(tagGroups);
    }

    // 表单验证方法
    private validateForm(): void {
        // 验证基本信息（名称、项目号、日期、品牌）
        const basicInfoValid = this.validateBasicInfo();

        // 验证媒体文件
        const mediaFilesValid = this.imageFiles.length > 0;

        // 验证标签（不强制要求源文件）
        const tagsValid = this.formData.tagIds.length > 0;

        // 所有验证都通过才算有效
        this.formValid = basicInfoValid && mediaFilesValid && tagsValid;
        this.formValidityState++;
    }

    // 验证基本信息
    private validateBasicInfo(): boolean {
        const { name, projectNo, releaseTime, customerId } = this.formData;
        return !!name?.trim() &&
               !!projectNo?.trim() &&
               !!(customerId && customerId.length > 0);
    }

    private validateField(fieldName: string): void {
        try {
            if (this.$refs.formRef) {
                const formRef = this.$refs.formRef as any;
                if (formRef.validateField) {
                    formRef.validateField(fieldName);
                }
            }
        } catch (error) {
            console.error("字段验证失败:", error);
        }
    }

    // 防抖验证方法
    private debouncedValidate = toolsUtils.debounce(() => {
        this.validateForm();
    }, DEBOUNCE_DELAY);

    // 监听器
    @Watch("formData", { deep: true })
    private onFormDataChange(): void {
        this.debouncedValidate();
    }

    @Watch("imageFiles")
    private onFilesChange(): void {
        this.debouncedValidate();
    }

    // 表单字段验证方法
    private clearBrandValidationError(): void {
        try {
            if (this.$refs.formRef) {
                const form = this.$refs.formRef as any;
                if (form.clearValidate) {
                    form.clearValidate("customerId");
                }
            }
        } catch (error) {
            console.error("清除验证错误失败:", error);
        }
    }

    private validateBrandField(): void {
        this.validateField("customerId");
    }

    // 字段事件处理
    private handleFieldFocus(data: { field: string; event: Event }): void {
        if (data.field === 'customerId') {
            this.clearBrandValidationError();
        }
    }

    private handleFieldBlur(data: { field: string; event: Event }): void {
        if (data.field === 'customerId') {
            this.validateBrandField();
        }
    }

    // 古德原创开关变化处理
    private handleOriginalChange(value: boolean): void {
        this.formData.original = value;
        this.validateForm();
    }

    // 文件更新方法
    private updateImageFiles(files: FileListItem[]): void {
        console.log("updateImageFiles", files);
        this.imageFiles = files;
    }

    private updateExtraImageFiles(files: FileListItem[]): void {
        this.extraImages = files;
    }

    private updateSourceFiles(files: FileListItem[]): void {
        this.formData.sourceFiles = files;
    }

    // 标签操作方法
    private updateTagsValue(selectedTags: string[]): void {
        this.formData.tagIds = selectedTags;
        this.validateForm();
    }

    private regenerateTags(): void {
        if (this.tagOptions.length === 0) {
            this.showValidationError("暂无可用标签");
            return;
        }

        this.formData.tagIds = DataProcessor.generateRandomTags(this.tagOptions);
        this.validateForm();
    }

    private removeTag(tag: string): void {
        const tagOption = this.tagOptions.find(t => t.label === tag);
        if (tagOption) {
            this.formData.tagIds = this.formData.tagIds.filter(t => t !== tagOption.value);
            this.validateForm();
        }
    }

    private showValidationError(message: string): void {
        this.$notify.warning({
            title: "提示",
            message
        });
    }

    // 草稿保存
    private saveDraft(): void {
        this.draftUtils.saveDraft({
            formData: this.formData,
            imageFiles: DraftUtils.serializeFileList(this.imageFiles),
            extraImages: DraftUtils.serializeFileList(this.extraImages),
        });
    }

    // 提交表单
    private async submitForm(): Promise<void> {
        if (this.submitting) return;

        try {
            this.submitting = true;

            // 验证表单
            if (this.$refs.formRef) {
                const formRef = this.$refs.formRef as any;
                const valid = await formRef.validate();

                if (!valid) {
                    this.showValidationError("请完善必填信息");
                    this.submitting = false;
                    return;
                }
            }

            // 验证图片
            if (this.imageFiles.length === 0) {
                this.showValidationError("请上传作品封面");
                this.submitting = false;
                return;
            }

            // 验证标签
            if (this.formData.tagIds.length === 0) {
                this.showValidationError("请选择作品标签");
                this.submitting = false;
                return;
            }

            const submitData = DataProcessor.buildSubmitData({
                formData: this.formData,
                imageFiles: this.imageFiles,
                documentFiles: [],
                type: "MODEL_3D",
                extraImages: this.extraImages,
            });

            console.log("提交数据:", submitData);

            const result = await damsAssetInfoApi.add(submitData);
            console.log("提交结果:", result);

            this.$notify.success({
                title: "成功",
                message: "3D模型提交成功"
            });

            // 提交成功后清除草稿
            this.draftUtils.clearDraft();

            this.$router.push("/");
        } catch (error) {
            console.error("提交失败:", error);
            const errorMessage = error instanceof Error ? error.message : "提交失败，请重试";
            this.$notify.error({
                title: "发布失败",
                message: errorMessage
            });
        } finally {
            this.submitting = false;
        }
    }

    // 错误处理
    private handleError(message: string): void {
        this.$notify.error({
            title: "错误",
            message
        });
    }

    // 关闭页面
    private onClose(): void {
        this.$router.push("/");
    }
}
</script>

<style lang="scss" scoped>
@import "./add.scss";

.centered-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: 40px;
}

.section-divider {
    width: 100%;
    height: 1px;
    background: rgba(255, 255, 255, 0.15);
    margin-bottom: 48px;
    position: relative;

    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-50%);
    }
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 24px;

    @media (max-width: 768px) {
        font-size: 16px;
        margin-bottom: 16px;
    }
}

.original-switch-container {
    display: flex;
    align-items: center;
    align-items: center;

    .original-switch-label {
        font-size: 12px;
        line-height: 22px;
        color: #fff;
        display: flex;
        margin-right: 9px;

        .required-mark {
            color: #f56c6c;
            margin-left: 4px;
        }
    }

    @media (max-width: 768px) {
        margin-top: 16px;
        padding: 12px 16px;

        .original-switch-label {
            font-size: 13px;
        }
    }
}

.tag-files-container {
    .tag-selector-container {
        display: flex;
        align-items: center;
        gap: 16px;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
    }

    .tag-title-container {
        display: flex;
        align-items: center;
        margin-top: 32px;

        .section-title-v2 {
            margin-right: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
        }

        @media (max-width: 768px) {
            margin-bottom: 12px;
        }
    }

    .refresh-btn {
        margin-left: 16px;

        @media (max-width: 768px) {
            margin-left: 8px;
        }
    }

    .selected-tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 16px;

        @media (max-width: 768px) {
            margin-top: 12px;
        }
    }
}

.mt-32 {
    margin-top: 32px;

    @media (max-width: 768px) {
        margin-top: 24px;
    }
}

.footer-btn {
    margin-right: 10px;
}

.footer-btn-left {
    left: 48px;

    @media (max-width: 1024px) {
        left: 32px;
    }

    @media (max-width: 768px) {
        left: 16px;
    }

    @media (max-width: 480px) {
        left: 12px;
    }
}

.footer-btn-right {
    right: 48px;

    @media (max-width: 1024px) {
        right: 32px;
    }

    @media (max-width: 768px) {
        right: 16px;
    }

    @media (max-width: 480px) {
        right: 12px;
    }
}
</style>
