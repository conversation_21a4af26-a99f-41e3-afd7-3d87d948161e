import { FormItemConfig } from "~/components/y/types/form";
import { FileListItem, FormData, SelectOption, StepConfig } from "./asset-form";

/**
 * 资产表单策略接口
 * 定义不同资产类型的表单配置和行为
 */
export interface AssetFormStrategy {
    /**
     * 资产类型标识
     */
    assetType: string;

    /**
     * 步骤配置
     */
    stepConfig: StepConfig[];

    /**
     * 步骤标题
     */
    stepTitles: string[];

    /**
     * 获取基础信息表单项
     */
    getBasicFormItems(options: SelectOption[]): FormItemConfig[];

    /**
     * 获取详情表单项
     */
    getDetailFormItems(options: SelectOption[]): FormItemConfig[];

    /**
     * 验证步骤是否有效
     */
    validateStep(
        step: number,
        formData: FormData,
        files: {
            imageFiles: FileListItem[];
            documentFiles: FileListItem[];
            sourceFiles: FileListItem[];
            designFiles?: FileListItem[];
        }
    ): { isValid: boolean; message?: string };

    /**
     * 构建提交数据
     */
    buildSubmitData(
        formData: FormData,
        files: {
            imageFiles: FileListItem[];
            documentFiles: FileListItem[];
            sourceFiles: FileListItem[];
            designFiles?: FileListItem[];
        }
    ): any;

    /**
     * 初始化表单数据
     */
    initFormData(): Partial<FormData>;

    /**
     * 获取草稿存储键
     */
    getDraftStorageKey(): string;
}

/**
 * 资产表单策略工厂接口
 */
export interface AssetFormStrategyFactory {
    /**
     * 获取策略实例
     * @param type 资产类型
     */
    getStrategy(type: string): AssetFormStrategy;
}
