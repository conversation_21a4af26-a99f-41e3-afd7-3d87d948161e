// 资产表单相关类型定义

export interface FormData {
    name: string;
    projectNo: string;
    releaseTime: string;
    customerId: string[];
    description: string;
    caseType: string;
    serviceType: string;
    productType: string;
    productSpec: string;
    productMaterial: string;
    materialIds: string[];
    productPrice: string;
    productNum: string;
    dueDesc: string;
    serviceFee: string;
    tagIds: string[];
    sourceFiles: FileListItem[];
    mainImage: string;
    sourceFile: string;
    deliverableFileId: string;
    designFile?: string;
    extraImages?: ExtraImage[]; // 新增：素材补充图片数据
    original?: boolean; // 新增：古德原创标识
}

// 素材补充图片数据结构
export interface ExtraImage {
    type: string; // 图片类型：ELEMENT、EXTEND、PRODUCT、SCENE、THREE_VIEW
    fileId: string[]; // 文件ID数组
}

export interface FileListItem {
    name: string;
    url: string;
    size?: number;
    uid?: number;
    status?: string;
    uploading?: boolean;
    progress?: number;
    file?: File;
    mimeType?: string;
    uploadedDoc?: any;
    type?: string;
}

export interface StepValidationResult {
    isValid: boolean;
    message?: string;
}

export interface TagGroup {
    id?: string;
    name?: string;
    type?: string;
    tagInfos?: Array<{
        id?: string;
        name?: string;
        groupId?: string;
        assetRelNum?: number;
    }>;
}

export interface SelectOption {
    value: string;
    label: string;
    category?: string;
    assetRelNum?: number;
}

export interface StepConfig {
    name: string;
    icon: string;
    activeIcon: string;
}
