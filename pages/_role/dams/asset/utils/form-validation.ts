// 表单验证工具类
import { VALIDATION_MESSAGES } from '../constants/asset-form';
import type { FormData, StepValidationResult, FileListItem } from '../types/asset-form';

export class FormValidationUtils {
  /**
   * 验证基础信息步骤
   */
  static validateBasicInfo(formData: FormData): StepValidationResult {
    if (!formData) {
      return { isValid: false, message: "表单数据不存在" };
    }

    const requiredFields = [
      { field: 'name', message: VALIDATION_MESSAGES.REQUIRED_NAME },
      { field: 'projectNo', message: VALIDATION_MESSAGES.REQUIRED_PROJECT_NO },
      { field: 'releaseTime', message: VALIDATION_MESSAGES.REQUIRED_RELEASE_TIME }
    ];

    // 检查必填字符串字段
    for (const { field, message } of requiredFields) {
      if (!formData[field as keyof FormData]?.toString().trim()) {
        return { isValid: false, message };
      }
    }

    // 检查数组字段
    if (!Array.isArray(formData.customerId) || formData.customerId.length === 0) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_BRAND };
    }

    return { isValid: true };
  }

  /**
   * 验证媒体文件步骤
   */
  static validateMediaFiles(imageFiles: FileListItem[]): StepValidationResult {
    if (!Array.isArray(imageFiles)) {
      return { isValid: false, message: "图片列表无效" };
    }

    const isValid = imageFiles.length > 0;
    return {
      isValid,
      message: isValid ? undefined : VALIDATION_MESSAGES.REQUIRED_IMAGES
    };
  }

  /**
   * 验证产品详情
   */
  static validateProductDetails(formData: FormData): StepValidationResult {
    if (!formData) {
      return { isValid: false, message: "表单数据不存在" };
    }

    const { productSpec, productPrice, productNum, dueDesc } = formData;

    // 检查必填字符串字段
    if (!productSpec?.trim()) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_PRODUCT_SPEC };
    }

    if (!dueDesc?.trim()) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_DUE_DESC };
    }

    // 检查数组字段
    if (!Array.isArray(formData.materialIds) || formData.materialIds.length === 0) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_MATERIAL };
    }

    // 检查数值字段
    if (!this.isValidNumber(productPrice)) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_PRICE };
    }

    if (!this.isValidNumber(productNum)) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_NUM };
    }

    return { isValid: true };
  }

  /**
   * 验证服务详情
   */
  static validateServiceDetails(formData: FormData, documentFiles: FileListItem[]): StepValidationResult {
    if (!formData) {
      return { isValid: false, message: "表单数据不存在" };
    }

    // 检查服务费用
    if (!this.isValidNumber(formData.serviceFee)) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_SERVICE_FEE };
    }

    // 检查文档文件
    if (!Array.isArray(documentFiles) || documentFiles.length === 0) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_DOCUMENTS };
    }

    return { isValid: true };
  }

  /**
   * 验证标签和源文件步骤
   */
  static validateTagsAndFiles(formData: FormData): StepValidationResult {
    if (!formData) {
      return { isValid: false, message: "表单数据不存在" };
    }

    // 检查标签
    if (!Array.isArray(formData.tagIds) || formData.tagIds.length === 0) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_TAGS };
    }

    // 检查源文件
    if (!Array.isArray(formData.sourceFiles) || formData.sourceFiles.length === 0) {
      return { isValid: false, message: VALIDATION_MESSAGES.REQUIRED_SOURCE_FILES };
    }

    return { isValid: true };
  }

  /**
   * 验证设计提案文件
   */
  static validateDesignFiles(designFiles: FileListItem[]): StepValidationResult {
    if (!Array.isArray(designFiles)) {
      return { isValid: false, message: VALIDATION_MESSAGES.INVALID_FILE_LIST };
    }

    const isValid = designFiles.length > 0;
    return {
      isValid,
      message: isValid ? undefined : VALIDATION_MESSAGES.REQUIRED_DESIGN_FILES
    };
  }

  /**
   * 验证数值是否有效
   */
  private static isValidNumber(value: string | number | undefined): boolean {
    if (value === undefined || value === null || value === '') return false;
    const num = Number(value);
    return !isNaN(num) && num > 0;
  }

  /**
   * 创建数字验证器
   */
  static createNumberValidator(fieldName: string, isRequired: boolean = true) {
    return {
      required: isRequired,
      message: `请输入${fieldName}`,
      validator: (rule: any, value: any, callback: Function) => {
        if (!isRequired && (value === "" || value === null || value === undefined)) {
          callback();
          return;
        }

        if (value === "" || value === null || value === undefined) {
          callback(new Error(`请输入${fieldName}`));
        } else if (Number(value) <= 0) {
          callback(new Error(`${fieldName}必须大于0`));
        } else {
          callback();
        }
      },
      trigger: "blur"
    };
  }

  /**
   * 创建数组验证器
   */
  static createArrayValidator(fieldName: string) {
    return {
      required: true,
      message: `请选择${fieldName}`,
      validator: (rule: any, value: string[], callback: Function) => {
        if (Array.isArray(value) && value.length > 0) {
          callback();
        } else {
          callback(new Error(`请选择${fieldName}`));
        }
      },
      trigger: "blur"
    };
  }
}
