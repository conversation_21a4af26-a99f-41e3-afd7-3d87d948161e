// 数据处理工具类
import type { FormData, FileListItem, TagGroup, SelectOption, ExtraImage } from '../types/asset-form';

export class DataProcessor {
  /**
   * 转换标签组为选项数组
   */
  static convertTagGroupsToOptions(groups: TagGroup[]): SelectOption[] {
    if (!Array.isArray(groups)) return [];

    return groups.flatMap(
      group =>
        group.tagInfos?.filter(tag => tag.id && tag.name).map(tag => ({
          value: tag.id as string,
          label: tag.name as string
        })) || []
    );
  }

  /**
   * 转换标签组为带分类的选项数组
   */
  static convertTagGroupsToOptionsWithCategory(groups: TagGroup[]): Array<SelectOption & { category: string }> {
    if (!Array.isArray(groups)) return [];

    return groups.flatMap(
      group => {
        if (!group.id || !group.tagInfos) return [];
        return group.tagInfos.filter(tag => tag.id && tag.name).map(tag => ({
          value: tag.id as string,
          label: tag.name as string,
          category: group.id as string,
          assetRelNum: tag.assetRelNum || 0
        }));
      }
    );
  }

  /**
   * 转换标签组为分类数组
   */
  static convertTagGroupsToCategories(groups: TagGroup[]): SelectOption[] {
    if (!Array.isArray(groups)) return [];

    return groups
      .filter(group => group.id && group.name)
      .map(group => ({
        value: group.id as string,
        label: group.name as string
      }));
  }

  /**
   * 根据选中的值获取标签文本
   */
  static getLabelsFromValues(selectedValues: string[], options: SelectOption[]): string[] {
    if (!Array.isArray(selectedValues) || !Array.isArray(options)) return [];

    return selectedValues.map(value => {
      const option = options.find(opt => opt.value === value);
      return option?.label || value;
    });
  }

  /**
   * 构建提交数据
   */
  static buildSubmitData({
    formData,
    imageFiles,
    documentFiles,
    type = "GOOD_CASE",
    extraImages,
    designFile
  }: {
    formData: FormData;
    imageFiles: FileListItem[];
    documentFiles: FileListItem[];
    type?: string;
    extraImages?: FileListItem[];
    designFile?: FileListItem[];
  }): any {
    if (!formData) return {};

    // 基础数据
    const submitData = {
      type,
      name: formData.name,
      projectNo: formData.projectNo,
      description: formData.description,
      customerId: this.joinValues(formData.customerId),
      releaseTime: formData.releaseTime,
      caseType: formData.caseType,
      serviceType: formData.serviceType,
      productType: formData.productType,
      productSpec: formData.productSpec,
      materialId: this.joinValues(formData.materialIds),
      tagId: this.joinValues(formData.tagIds)
    };

    // 数值类型字段处理
    const numericFields = {
      productPrice: formData.productPrice,
      productNum: formData.productNum,
      serviceFee: formData.serviceFee
    };

    // 文件相关字段
    const fileFields = {
      headImage: this.getHeadImageId(imageFiles),
      mainImage: this.getMainImageIds(imageFiles),
      sourceFile: this.getFileIds(formData.sourceFiles),
      deliverableFileId: this.getFileIds(documentFiles),
      extraImages: this.formatExtraImages(extraImages || []),
      designFile: this.getFileIds(designFile || [])
    };

    // 合并所有数据
    return {
      ...submitData,
      ...this.convertNumericFields(numericFields),
      ...fileFields
    };
  }

  /**
   * 转换数值类型字段
   */
  private static convertNumericFields(fields: Record<string, string>): Record<string, number | undefined> {
    const result: Record<string, number | undefined> = {};

    Object.entries(fields).forEach(([key, value]) => {
      const numValue = Number(value);
      result[key] = !isNaN(numValue) ? numValue : undefined;
    });

    return result;
  }

  /**
   * 连接值数组为字符串
   */
  private static joinValues(values: string[] | undefined): string {
    if (!values || !Array.isArray(values)) return '';
    return values.filter(Boolean).join(',');
  }

  /**
   * 获取头图ID
   */
  private static getHeadImageId(imageFiles: FileListItem[]): string {
    if (!Array.isArray(imageFiles) || imageFiles.length === 0) return '';
    return imageFiles[0].uploadedDoc?.id || '';
  }

  /**
   * 获取主图片IDs
   */
  private static getMainImageIds(imageFiles: FileListItem[]): string {
    if (!Array.isArray(imageFiles) || imageFiles.length <= 1) return '';
    return this.getFileIds(imageFiles.slice(1));
  }

  /**
   * 获取文件IDs
   */
  private static getFileIds(files: FileListItem[]): string {
    if (!Array.isArray(files)) return '';
    return files
      .map(file => file.uploadedDoc?.id || '')
      .filter(Boolean)
      .join(',');
  }

  /**
   * 格式化额外图片数据
   */
  private static formatExtraImages(extraImages: FileListItem[]): ExtraImage[] {
    if (!Array.isArray(extraImages)) return [];

    // 按类型分组
    const groupedByType: Record<string, string[]> = {};

    extraImages.forEach(file => {
      if (!file.type || !file.uploadedDoc?.id) return;

      if (!groupedByType[file.type]) {
        groupedByType[file.type] = [];
      }

      groupedByType[file.type].push(file.uploadedDoc.id);
    });

    // 转换为所需格式
    return Object.entries(groupedByType).map(([type, fileIds]) => ({
      type,
      fileId: fileIds
    }));
  }

  /**
   * 生成随机标签
   */
  static generateRandomTags(tagOptions: SelectOption[], count: number = 3): string[] {
    if (!Array.isArray(tagOptions) || tagOptions.length === 0) return [];

    const shuffled = [...tagOptions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, tagOptions.length)).map(tag => tag.value);
  }

  /**
   * 设置默认选项数据
   */
  static getDefaultOptions(): {
    brandOptions: SelectOption[];
    materialOptions: Array<SelectOption & { category: string }>;
    tagOptions: Array<SelectOption & { category: string }>;
  } {
    return {
      brandOptions: [{ value: "default", label: "默认品牌" }],
      materialOptions: [{ value: "default", label: "默认材质", category: "default", assetRelNum: 0 }],
      tagOptions: [{ value: "default", label: "默认标签", category: "default", assetRelNum: 0 }]
    };
  }
}
