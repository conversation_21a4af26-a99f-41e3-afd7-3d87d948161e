import localStorageUtils from "~/utils/localStorageUtils";
import { FileListItem, FormData } from "../types/asset-form";

/**
 * 草稿数据接口
 */
export interface DraftData {
    formData: FormData;
    imageFiles?: FileListItem[];
    documentFiles?: FileListItem[];
    extraImages?: FileListItem[];
    currentStep?: number;
    timestamp: number;
}

/**
 * 草稿配置接口
 */
export interface DraftConfig {
    storageKey: string;
    expireTime?: number; // 过期时间（毫秒），默认7天
    confirmRestore?: boolean; // 是否需要确认恢复，默认true
}

/**
 * 草稿保存工具类
 * 提供通用的草稿保存、加载、恢复、清除功能
 */
export class DraftUtils {
    private config: Required<DraftConfig>;
    private vueInstance: any; // Vue组件实例

    constructor(vueInstance: any, config: DraftConfig) {
        this.vueInstance = vueInstance;
        this.config = {
            expireTime: 7 * 24 * 60 * 60 * 1000, // 默认7天
            confirmRestore: true,
            ...config
        };
    }

    /**
     * 保存草稿
     */
    saveDraft(data: Partial<DraftData>): void {
        try {
            const draftData: DraftData = {
                timestamp: Date.now(),
                ...data
            } as DraftData;

            localStorageUtils.set(
                this.config.storageKey, 
                JSON.stringify(draftData), 
                this.config.expireTime
            );

            this.vueInstance.$notify.success({
                title: "成功",
                message: "草稿已保存"
            });
        } catch (error) {
            console.error("保存草稿失败:", error);
            this.vueInstance.$notify.error({
                title: "错误",
                message: "保存草稿失败，请重试"
            });
        }
    }

    /**
     * 加载草稿数据
     */
    loadDraftData(): DraftData | null {
        try {
            const draftDataStr = localStorageUtils.get(this.config.storageKey);
            if (draftDataStr) {
                const draftData = JSON.parse(draftDataStr) as DraftData;
                
                // 检查是否过期
                if (this.isDraftExpired(draftData)) {
                    this.clearDraft();
                    return null;
                }
                
                return draftData;
            }
        } catch (error) {
            console.error("加载草稿失败:", error);
        }
        return null;
    }

    /**
     * 恢复草稿数据
     */
    restoreDraftData(
        draftData: DraftData,
        restoreCallback: (data: DraftData) => void
    ): void {
        if (this.config.confirmRestore) {
            this.vueInstance.$confirm('检测到未完成的草稿，是否恢复？', '提示', {
                confirmButtonText: '恢复',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                restoreCallback(draftData);
                this.vueInstance.$notify.success({
                    title: "成功",
                    message: "草稿已恢复"
                });
            }).catch(() => {
                // 用户取消恢复，清除草稿
                this.clearDraft();
            });
        } else {
            restoreCallback(draftData);
        }
    }

    /**
     * 自动加载并恢复草稿
     */
    autoLoadAndRestore(restoreCallback: (data: DraftData) => void): void {
        const draftData = this.loadDraftData();
        if (draftData) {
            this.restoreDraftData(draftData, restoreCallback);
        }
    }

    /**
     * 序列化文件列表
     */
    static serializeFileList(fileList: FileListItem[]): any[] {
        return fileList.map(item => ({
            name: item.name,
            url: item.url,
            size: item.size,
            uid: item.uid,
            status: item.status,
            mimeType: item.mimeType,
            type: item.type,
            uploadedDoc: item.uploadedDoc,
            // 不保存实际的File对象
        }));
    }

    /**
     * 反序列化文件列表
     */
    static deserializeFileList(serializedList: any[]): FileListItem[] {
        return serializedList.map(item => ({
            name: item.name,
            url: item.url,
            size: item.size,
            uid: item.uid,
            status: item.status,
            mimeType: item.mimeType,
            type: item.type,
            uploadedDoc: item.uploadedDoc,
            file: undefined, // 文件对象需要用户重新选择
        }));
    }

    /**
     * 清除草稿
     */
    clearDraft(): void {
        localStorageUtils.clear(this.config.storageKey);
    }

    /**
     * 检查草稿是否过期
     */
    private isDraftExpired(draftData: DraftData): boolean {
        const now = Date.now();
        return (now - draftData.timestamp) > this.config.expireTime;
    }
}

/**
 * 创建草稿工具实例的工厂函数
 */
export function createDraftUtils(vueInstance: any, config: DraftConfig): DraftUtils {
    return new DraftUtils(vueInstance, config);
}