// 资产表单相关常量定义
import type { StepConfig } from '../types/asset-form';

// 通用步骤配置
export const STEP_CONFIG: readonly StepConfig[] = [
  { name: "基础信息", icon: "document", activeIcon: "documentActive" },
  { name: "媒体资源", icon: "picture", activeIcon: "pictureActive" },
  { name: "案例细节", icon: "tickets", activeIcon: "ticketsActive" },
  { name: "标签和源文件", icon: "tag", activeIcon: "tagActive" }
] as const;

// 通用步骤标题
export const STEP_TITLES: readonly string[] = [
  "添加案例基础信息",
  "添加案例图片",
  "选择案例类型",
  "添加案例标签"
] as const;

// 提案步骤配置
export const PROPOSAL_STEP_CONFIG: readonly StepConfig[] = [
  { name: "基础信息", icon: "document", activeIcon: "documentActive" },
  { name: "媒体资源", icon: "picture", activeIcon: "pictureActive" },
  { name: "标签和源文件", icon: "tag", activeIcon: "tagActive" }
] as const;

// 提案步骤标题
export const PROPOSAL_STEP_TITLES: readonly string[] = [
  "添加提案基础信息",
  "添加提案图片",
  "添加提案标签"
] as const;

// 资产类型
export const ASSET_TYPES = {
  GOOD_CASE: "GOOD_CASE",
  DESIGN_PROPOSAL: "DESIGN_PROPOSAL",
  KV_DESIGN: "KV_DESIGN",
  MODEL_3D: "MODEL_3D",
  MARKET_INSPIRATION: "MARKET_INSPIRATION"
} as const;

// 案例类型选项
export const CASE_TYPE_OPTIONS = {
  PRODUCT: "PRODUCT",
  REPORT: "REPORT",
  IP_RESOURCE: "IP_RESOURCE",
  OTHER: "OTHER"
} as const;

// 服务类型选项
export const SERVICE_TYPE_OPTIONS = {
  PRODUCTION: "PRODUCTION",
  NON_PRODUCTION: "NON_PRODUCTION"
} as const;

// 产品类型选项
export const PRODUCT_TYPE_OPTIONS = {
  COMMODITY: "COMMODITY",
  GIFT: "GIFT"
} as const;

// 标签组类型
export const TAG_GROUP_TYPES = {
  MATERIAL: "MATERIAL",
  GENERAL: "GENERAL",
  BRAND: "BRAND"
} as const;

// 图片类型
export const IMAGE_TYPES = {
  ELEMENT: "ELEMENT",
  EXTEND: "EXTEND",
  PRODUCT: "PRODUCT",
  SCENE: "SCENE",
  THREE_VIEW: "THREE_VIEW"
} as const;

// 表单验证消息
export const VALIDATION_MESSAGES = {
  // 通用验证消息
  FORM_DATA_MISSING: "表单数据不存在",
  INVALID_FILE_LIST: "文件列表无效",

  // 基础信息验证消息
  REQUIRED_NAME: "请输入名称",
  REQUIRED_PROJECT_NO: "请输入项目号",
  REQUIRED_RELEASE_TIME: "请选择上市日期",
  REQUIRED_BRAND: "请选择品牌",

  // 产品详情验证消息
  REQUIRED_PRODUCT_SPEC: "请输入产品规格",
  REQUIRED_MATERIAL: "请选择产品材质",
  REQUIRED_PRICE: "请输入采购价格",
  REQUIRED_NUM: "请输入采购数量",
  REQUIRED_DUE_DESC: "请输入大货工期",
  REQUIRED_SERVICE_FEE: "请输入服务费用",
  INVALID_PRICE: "采购价格必须大于0",
  INVALID_NUM: "采购数量必须大于0",
  INVALID_SERVICE_FEE: "服务费用必须大于0",

  // 文件和标签验证消息
  REQUIRED_IMAGES: "请上传至少一张图片",
  REQUIRED_DOCUMENTS: "请上传交付文件",
  REQUIRED_TAGS: "请选择标签",
  REQUIRED_SOURCE_FILES: "请上传源文件",
  REQUIRED_DESIGN_FILES: "请上传设计提案文件"
} as const;

// 防抖延迟时间
export const DEBOUNCE_DELAY = 300;

// 步骤数量
export const TOTAL_STEPS = STEP_CONFIG.length;
export const PROPOSAL_TOTAL_STEPS = PROPOSAL_STEP_CONFIG.length;
