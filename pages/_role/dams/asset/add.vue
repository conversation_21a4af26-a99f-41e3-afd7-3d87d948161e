<template>
    <div class="asset-add-entry">
        <component :is="currentComponent" />
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import GoodCase from "./components/good-case.vue";
import MarketInspiration from "./components/market-inspiration.vue";
import DesignProposal from "./components/design-proposal.vue";
import KvDesign from "./components/kv-design.vue";
import Model3D from "./components/model-3d.vue";

// 资产类型枚举
enum AssetType {
    MARKET_INSPIRATION = 'MARKET_INSPIRATION',
    GOOD_CASE = 'GOOD_CASE',
    DESIGN_PROPOSAL = 'DESIGN_PROPOSAL',
    KV_DESIGN = 'KV_DESIGN',
    MODEL_3D = 'MODEL_3D'
}

// 组件映射
const COMPONENT_MAP = {
    [AssetType.MARKET_INSPIRATION]: MarketInspiration,
    [AssetType.GOOD_CASE]: GoodCase,
    [AssetType.DESIGN_PROPOSAL]: DesignProposal,
    [AssetType.KV_DESIGN]: KvDesign,
    [AssetType.MODEL_3D]: Model3D
};

@Component({
    name: "AssetAdd",
    layout: "empty",
    components: {
        GoodCase,
        MarketInspiration,
        DesignProposal,
        KvDesign,
        Model3D
    }
})
export default class AssetAdd extends Vue {
    // 当前组件类型
    assetType: string = '';

    // 计算当前应该显示的组件
    get currentComponent() {
        // 如果没有指定类型或类型无效，默认显示古德案例
        if (!this.assetType || !COMPONENT_MAP[this.assetType as AssetType]) {
            return COMPONENT_MAP[AssetType.GOOD_CASE];
        }

        return COMPONENT_MAP[this.assetType as AssetType];
    }

    // 组件挂载时获取路由参数
    mounted() {
        this.initializeAssetType();
    }

    // 初始化资产类型
    private initializeAssetType(): void {
        // 从路由查询参数中获取 type
        const type = this.$route.query.type as string;

        if (type && this.isValidAssetType(type)) {
            this.assetType = type;
        } else {
            // 如果没有指定类型或类型无效，默认为古德案例
            this.assetType = AssetType.GOOD_CASE;
            console.warn(`无效的资产类型: ${type}，已默认设置为 GOOD_CASE`);
        }

        console.log(`当前资产类型: ${this.assetType}`);
    }

    // 验证资产类型是否有效
    private isValidAssetType(type: string): boolean {
        return Object.values(AssetType).includes(type as AssetType);
    }

    // 监听路由变化
    beforeRouteUpdate(to: any, from: any, next: any) {
        const newType = to.query.type as string;

        if (newType && this.isValidAssetType(newType)) {
            this.assetType = newType;
        } else {
            this.assetType = AssetType.GOOD_CASE;
        }

        next();
    }
}
</script>

<style lang="scss" scoped>
.asset-add-entry {
    width: 100%;
    height: 100vh;
    overflow: hidden;
}
</style>
