<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("pages.cms.subject.index.title") }}
  .container
    //- 表头
    .handle-box
      el-button.mr10(
        type="primary",
        icon="el-icon-plus",
        @click="handleAddClick"
      ) {{ $t("common.base.add") }}
      el-input.handle-input.mr10(v-model="query.keyword", placeholder="关键词")
      el-button.mr10(
        type="primary",
        icon="el-icon-search",
        @click="handleSearchClick"
      ) 搜索
      //- a.downswagger(:href="swaggerUrl", target="_blank") 下载Swagger文档
    //- 表单内容
    el-table.table(
      :data="subjects",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      //- el-table-column(type="selection", width="55", align="center")
      el-table-column(prop="id", label="Id", align="center")
      el-table-column(prop="name", label="名称")
      el-table-column(prop="path", label="路径")
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="ord", label="排序")
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-document",
            @click="handleDocumentClick(scope.row)"
          ) 内容管理
          el-button(
            type="text",
            icon="el-icon-document",
            @click="handleCategoryClick(scope.row)"
          ) 分类管理
          el-button(
            type="text",
            icon="el-icon-document",
            @click="handleContentListUrl(scope.row)"
          ) 开放地址
          el-button(
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-popconfirm(
            title="确认要删除数据吗？",
            @confirm="handleDeleteClick(scope.row)"
          )
            el-button.red(slot="reference", type="text", icon="el-icon-delete") {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, Vue, VuexModule } from "nuxt-property-decorator";
import cmsSubjectApi from "~/api/cms/mg/subjectApi";
import { config } from "~/config/global.config";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-cms-subject",
})
export default class CmsSubjectIndex extends Vue {
  loading: boolean = true;
  query = { keyword: "" };
  subjects: any = [];

  get swaggerUrl() {
    return config.baseUrl.apiUrl + `/cms/mg/subject/swagger`;
  }

  async mounted() {
    await this.refreshPage();
  }

  async refreshPage() {
    this.loading = true;
    let param: any = {};
    param.keywrod = this.query.keyword;
    try {
      this.subjects = await cmsSubjectApi.getList(param);
    } catch (error) {
      this.$notify.error({ title: "加载出错，请稍后重试", message: "" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  // async handleTableChange(pagination: any, filters: any, sorter: any) {
  //     let page = Math.round(pagination.current / pagination.pageSize);
  //     this.refreshPage({ page, size: pagination.pageSize });
  // }

  async handleSearchClick() {
    await this.refreshPage();
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/edit`
    );
  }

  async handleModifyClick(record: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/edit?id=${
        record.id
      }`
    );
  }

  async handleDocumentClick(record: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        record.id
      }/content`
    );
  }

  async handleCategoryClick(record: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        record.id
      }/category`
    );
  }

  async handleContentListUrl(record: any) {
    let url = config.baseUrl.apiUrl + `/cms/open/s/${record.path}/_0`;
    window.open(url);
  }

  async handleDeleteClick(record: any) {
    await cmsSubjectApi.delete(record.id);
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}
.mr10 {
  margin-right: 10px;
}
</style>

