<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar CMS主题管理
      el-breadcrumb-item {{ this.id ? $t("pages.cms.subject.edit.title_edit") : $t("pages.cms.subject.edit.title_add") }}
  .container
    .form-box
      el-form(
        ref="form",
        :model="subject",
        label-width="80px",
        width="100%",
        v-loading="loading"
      )
        el-form-item(
          label="名称",
          prop="name",
          required,
          :rule="[$rule.required('')]"
        )
          el-input(v-model="subject.name")
        //- el-form-item(label="idParent")
        //-     el-input(v-model="subject.idParent")
        el-form-item(
          v-if="!id",
          prop="path",
          label="路径",
          required,
          :rule="[$rule.required('')]"
        )
          el-input(v-model="subject.path")
        el-form-item(label="分页数量")
          el-input-number(v-model="subject.rowsDef", :min="1", :max="1000")
        el-form-item(label="排序")
          el-input-number(v-model="subject.ord", :min="1", :max="1000")
        el-form-item.struct(label="结构信息")
        structs-edit(v-model="subject.contentStructs")
        br

        el-form-item(label="备注", prop="remark")
          el-input(
            type="textarea",
            v-model="subject.remark",
            :autosize="{ minRows: 2, maxRows: 4 }"
          )
        el-form-item
          el-button(type="primary", @click="handelSubmit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, Vue, VuexModule } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import cmsSubjectApi from "~/api/cms/mg/subjectApi";
import structsEdit from "./structs-edit.vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-cms-subject-edit",
  components: { "structs-edit": structsEdit },
})
export default class CmsSubjectEdit extends BaseVue {
  id = "";
  subject: any = {
    contentStructs: [],
    path: "",
    name: "",
    ord: 1000,
    rowsDef: 20,
  };
  loading: boolean = false;

  mounted() {
    this.id = <string>this.$route.query.id;
    if (!this.id) {
      return;
    }
    cmsSubjectApi
      .get(this.id)
      .then((res: any) => {
        res.idParent = res.idParent == "0" ? null : res.idParent;
        this.subject = res;
      })
      .catch((e: any) => {
        this.$notify.error({ title: "加载数据出错，请重试", message: "" });
      });
  }

  async handelSubmit() {
    if (!(await this.$rule.formCheck(this, "form"))) {
      return;
    }
    let param = JSON.parse(JSON.stringify(this.subject));
    this.loading = true;
    // let id: string = <any>this.$route.params.id;
    param.idParent = !!param.idParent ? param.idParent : "0";
    try {
      if (!this.id) {
        let res: any = await cmsSubjectApi.insert(param);
        this.id = res.id;
        this.$router.push(
          `/${compUtils.getQueryValue(this, "role", "")}/cms/subject`
        );
      } else {
        await cmsSubjectApi.update(this.id, param);
        this.$router.push(
          `/${compUtils.getQueryValue(this, "role", "")}/cms/subject`
        );
      }
      this.$notify.success({ title: "保存成功", message: "" });
    } catch (error) {
      console.error(error);
      this.$notify.error({ title: "保存失败：" + error, message: "" });
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject`
    );
  }
}
</script>
<style lang="scss" scoped>
</style>