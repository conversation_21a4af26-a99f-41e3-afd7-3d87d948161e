<template lang="pug">
div
  div(v-show="false") {{ count }}
  .row(v-for="(struct, idx) in structs")
    .show
      .title 字段信息：
      .model
        span Code：
          el-input(v-model="struct.code", style="width: 100px")
        span 名称：
          el-input(v-model="struct.name", style="width: 150px")
      .model
        span 必须字段：
          el-switch(v-model="struct.require")
        span(v-if="_deep <= 0") 索引：
          el-switch(v-model="struct.index")
        span(v-if="_deep <= 0 && struct.index") 索引Code：
          el-input.width100(v-model="struct.indexCode")
        span(v-if="_deep <= 0") 展示：
          el-switch(v-model="struct.show") 
      .title 类型信息：
      .model
        span 类型：
          el-select(v-model="struct.dataType")
            el-option(key="LIST", label="列表", value="LIST")
            el-option(key="MAP", label="子对象", value="MAP")
            el-option(key="INT", label="整数", value="INT")
            el-option(key="STRING", label="字符串", value="STRING")
            el-option(key="FLOAT", label="浮点", value="FLOAT")
            el-option(key="BOOL", label="布尔", value="BOOL")
            el-option(key="DATE", label="时间", value="DATE")
            el-option(key="ENUM", label="枚举", value="ENUM")
        span(v-if="struct.dataType == 'STRING'") 最小尺寸：
          el-input-number(v-model="struct.minSize")
        span(v-if="struct.dataType == 'STRING'") 最大尺寸：
          el-input-number(v-model="struct.maxSize")
      .model
        span(v-if="struct.dataType == 'ENUM'") 枚举数据：
          el-tag(
            v-for="(tag, tidx) in struct.enumValues",
            :key="tag",
            closable,
            :disable-transitions="false",
            @close="handleEnumClose(tag, idx, tidx)"
          ) {{ tag }}
          el-input.input-new-tag(
            v-if="struct.temp.inputVisible",
            v-model="temp.tag",
            ref="saveTagInput",
            size="small",
            @keyup.enter.native="handleEnumTagInputConfirm(struct, temp.tag, idx)",
            @blur="handleEnumTagInputConfirm(struct, temp.tag, idx)"
          )
          el-button.button-new-tag(
            v-else,
            size="small",
            @click="handleEnumShowInput(struct, idx)"
          ) + New Tag
      template(v-if="struct.dataType == 'ENUM'")
        .title 控件配置：
        .model
          table.table-enum-list
            thead
              tr
                th Code
                th 名称
            tbody
              tr(v-for="(em, idx) in struct.restrict.enumValues")
                td {{ em.code }}
                td
                  input(v-model="em.name")
      template(
        v-if="struct.dataType != 'LIST' && struct.dataType != 'MAP' && struct.dataType != 'ENUM'"
      )
        .title 控件配置：
        .model
          span 控件类型：
            el-select(v-model="struct.labelType")
              template(v-if="struct.dataType == 'DATE'")
                el-option(
                  key="DATE_PICKER",
                  label="日期时间选择器",
                  value="DATE_PICKER"
                )
              template(v-else-if="struct.dataType == 'STRING'")
                el-option(key="INPUT", label="输入框", value="INPUT")
                el-option(key="TEXTAREA", label="文本域", value="TEXTAREA")
                el-option(key="RICHTEXT", label="富文本", value="RICHTEXT")
                el-option(key="FILE_UPLOAD", label="文件上传", value="FILE_UPLOAD")
                el-option(key="IMG_UPLOAD", label="图片上传", value="IMG_UPLOAD")
              template(v-else)
                el-option(key="INPUT", label="输入框", value="INPUT")
          span(
            v-if="struct.labelType == 'FILE_UPLOAD' || struct.labelType == 'IMG_UPLOAD'"
          ) 最大文件数量：
            el-input-number(v-model="struct.restrict.limit")
    .children(v-if="struct.dataType == 'LIST' || struct.dataType == 'MAP'")
      .title 子节点信息：
      structs-edit.content(v-model="struct.childrens", :deep="_deep + 1")
    .remark
      .title 备注：
      el-input(
        v-model="struct.remark",
        type="textarea",
        :autosize="{ minRows: 2, maxRows: 4 }"
      )
    .btn-operate
      el-popconfirm(
        confirm-button-text="删除",
        cancel-button-text="取消",
        icon="el-icon-info",
        icon-color="red",
        :title="`确定要删除字段 ${struct.name}[${struct.code}] 吗？`",
        @confirm="handleDelColumn(idx, struct)"
      )
        el-button(type="primary", slot="reference") 删除字段
      a.mr10(
        v-if="idx != 0",
        href="javascript:void(0)",
        @click="columnUp(idx, struct)"
      ) 上移字段
      a.mr10(
        v-if="idx != structs.length - 1",
        href="javascript:void(0)",
        @click="columnDown(idx, struct)"
      ) 下移字段
  .row
    .btn-add
      el-button(type="primary", @click="handleAddColumn") 添加字段
</template>

<script lang="ts">
import {
  Component,
  Prop,
  Vue,
  VuexModule,
  Watch,
} from "nuxt-property-decorator";
// import { config } from "~/config/global.config";

@Component({
  name: "structs-edit",
  props: ["value", "deep"],
})
export default class StructsEdit extends Vue {
  structs: Array<any> = [];
  temp = { tag: "" };

  count = 0;

  mounted() {
    this.valueEven();
  }

  get _deep() {
    return this.$props.deep ? this.$props.deep : 0;
  }

  initValue() {
    this.structs.forEach((x) => {
      x.childrens = x.childrens ? x.childrens : [];
      x.enumValues = x.enumValues ? x.enumValues : [];
      x.restrict = x.restrict ? x.restrict : {};
      x.restrict.enumValues = x.restrict.enumValues
        ? x.restrict.enumValues
        : [];
      x.temp = x.temp ? x.temp : {};
      x.temp.tag = x.temp.tag ? x.temp.tag : {};
      x.temp.inputVisible = x.temp.inputVisible ? x.temp.inputVisible : false;
    });
  }

  async handleAddColumn() {
    this.structs = this.structs ? this.structs : [];
    this.structs.push({ restrict: {} });
    this.initValue();
  }

  async handleDelColumn(idx: number, struct: any) {
    this.structs.splice(idx, 1);
  }

  async columnUp(idx: number, struct: any) {
    this.structs.splice(idx, 1);
    this.structs.splice(idx - 1, 0, struct);
  }

  async columnDown(idx: number, struct: any) {
    this.structs.splice(idx, 1);
    this.structs.splice(idx + 1, 0, struct);
  }

  async handleEnumShowInput(struct: any, idx: number) {
    this.structs[idx].temp.inputVisible = true;
    this.count = this.count + 1;
  }

  async handleEnumTagInputConfirm(struct: any, tag: string, idx: number) {
    if (!tag) {
      return;
    }
    if (this.structs[idx].enumValues.indexOf(tag) < 0) {
      this.structs[idx].enumValues.push(tag);
      this.structs[idx].restrict.enumValues.push({
        code: tag,
        name: tag,
      });
      this.structs[idx].temp.inputVisible = false;
    }
    this.temp.tag = "";
  }

  async handleEnumClose(tag: string, idx: number, tidx: number) {
    this.structs[idx].enumValues.splice(tidx, 1);
    this.structs[idx].restrict.enumValues.splice(tidx, 1);
  }

  @Watch("$props.value")
  async valueEven() {
    this.structs = this.$props.value;
    this.initValue();
  }

  @Watch("structs")
  contentChange(structs: any, oldVal: any) {
    this.$emit("input", structs);
  }
}
</script>
<style lang="scss" scoped>
.row {
  border: 1px solid #888;
  padding: 10px 20px;
  margin-top: -1px;

  .show {
    margin-top: 10px;

    & > div {
      padding: 5px 10px;
    }
    .model > span {
      padding: 0px 20px;
      border-right: 1px solid #888;
      &:last-child {
        border-right: 0px solid #888;
      }
    }
  }

  .children {
    border-top: 1px dashed #ccc;

    .content {
      margin-left: 10px;
    }
  }

  .remark {
    border-top: 1px dashed #ccc;

    span {
      padding: 0px 20px;
    }
  }

  .btn-operate {
    border-top: 1px dashed #ccc;
    padding: 10px 20px;
  }
}
.width100 {
  width: 100px;
}
.mr10 {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.table-enum-list {
  border-collapse: collapse;
  text-align: center;
  min-width: 500px;
  border: 1px solid #ccc;
  input {
    width: 98%;
    height: 22px;
    font-size: 16px;
  }
}
.table-enum-list th {
  border: 1px solid #ccc;
}
.table-enum-list td {
  border: 1px solid #ccc;
}
</style>
