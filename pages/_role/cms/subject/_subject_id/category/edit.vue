<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar CMS主题管理
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ subject.name }}
      el-breadcrumb-item {{ this.id ? $t("pages.cms.subject.edit.title_edit") : $t("pages.cms.subject.edit.title_add") }}
  .container
    .form-box
      el-form(
        ref="form",
        :model="category",
        label-width="80px",
        width="900px",
        v-loading="loading"
      )
        el-form-item(label="名称", prop="name")
          el-input(v-model="category.name")
        el-form-item(v-if="!id", label="路径")
          el-input(v-model="category.path")
          | 为空时将自动生成
        el-form-item(v-if="id", label="路径")
          template(#default="scope")
            | {{ category.path }}
        //- el-form-item(label="父一级分类", prop="idParent")
        //-   el-select(v-model="category.idParent")
        //-     el-option(label="（根节点）", value="0")
        //-     el-option(
        //-       v-for="cat in categories",
        //-       v-if="!(id && cat.id == id)",
        //-       :label="cat.name",
        //-       :value="cat.id"
        //-     )
        el-form-item(label="备注", prop="remark")
          el-input(v-model="category.remark")
        el-form-item(label="排序", prop="ord")
          el-input-number(
            v-model="category.ord",
            :min="1",
            :max="10000",
            label="排序"
          )
        el-form-item
          el-button(type="primary", @click="handelSubmit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import cmsSubjectApi from "~/api/cms/mg/subjectApi";
import cmsCategoryApi from "~/api/cms/categoryApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-cms-subject-subject_id-category-edit",
})
export default class CmsContentEdit extends mixins(BaseVue) {
  loading: boolean = false;

  // 主题信息
  subjectId = "";
  subject: any = {};

  categories = [];

  // 内容
  id = "";
  category = {
    name: "",
    ord: 1000,
    remark: "",
    idParent: "0",
    path: "",
  };

  async mounted() {
    try {
      this.loading = true;
      this.subjectId = compUtils.getQueryValue(this, "subject_id", "");
      (async () => {
        this.subject = await cmsSubjectApi.get(this.subjectId);
      })();
      (async () => {
        this.categories = await cmsCategoryApi.getList(this.subjectId, {});
      })();

      this.id = <string>this.$route.query.id;
      if (!this.id) {
        return;
      }
      (async () => {
        this.category = await cmsCategoryApi.get(this.subjectId, this.id);
      })();
    } finally {
      this.loading = false;
    }
  }

  async handelSubmit() {
    let that = this;

    if (!(await this.$rule.formCheck(that, "form"))) {
      return;
    }
    that.loading = true;
    // let id: string = <any>this.$route.params.id;
    // param.idParent = !!param.idParent ? param.idParent : "0";
    let param = { ...this.category };
    try {
      if (!that.id) {
        let res: any = await cmsCategoryApi.post(that.subjectId, param);
        that.id = res.id;
        await that.goBack();
      } else {
        await cmsCategoryApi.put(that.subjectId, that.id, param);
        await that.goBack();
      }
      that.$notify.success({ title: "保存成功", message: "" });
    } catch (error) {
      console.error(error);
    } finally {
      that.loading = false;
    }
  }

  async goBack() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        this.subjectId
      }/category`
    );
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  width: 100%;
  //     .el-form-item {
  //         width: 600px;
  //     }
  //     .el-form-item.struct {
  //         width: 100%;
  //     }
}
</style>
