<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar CMS主题管理
      el-breadcrumb-item {{ subject.name }}
  .container
    h2.mgb20
      | {{ subject.name }}（{{ subject.path }}）分类管理
    .y-page-table
      .y-header
        //- 表头
        el-button.mgr10(
          type="primary",
          icon="el-icon-plus",
          @click="handleAddClick"
        ) {{ $t("common.base.add") }}
        el-input.handle-input.mgr10(v-model="query.keyword", placeholder="关键词")
        el-button.mgr10(
          type="primary",
          icon="el-icon-search",
          @click="handleSearchClick"
        ) {{ $t("common.base.search") }}
      .y-body
        //- 表单内容
        el-table.table(
          :data="categories",
          row-key="id",
          border,
          ref="multipleTable",
          default-expand-all,
          v-loading="loading",
          :tree-props="{ children: 'children' }"
        )
          el-table-column(prop="name", label="名称")
          el-table-column(prop="path", label="路径")
          el-table-column(prop="remark", label="备注")
          el-table-column(label="操作", width="150", align="center")
            template(#default="scope")
              el-button(
                type="text",
                icon="el-icon-edit",
                @click="handleModifyClick(scope.row)"
              ) {{ $t("common.base.modify") }}
              el-popconfirm.mgl10(
                title="确认要删除数据吗？",
                @confirm="handleDeleteClick(scope.row)"
              )
                el-button.red(
                  slot="reference",
                  type="text",
                  icon="el-icon-delete"
                ) {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, Vue, VuexModule } from "nuxt-property-decorator";
import cmsSubjectApi from "~/api/cms/mg/subjectApi";
import cmsCategoryApi from "~/api/cms/categoryApi";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-cms-subject-subject_id-category",
})
export default class CmsContentList extends Vue {
  loading: boolean = true;

  // 主题信息
  subjectId = "";
  subject: any = {};

  // 当前数据
  query = { keyword: "" };
  categories: any = [];

  async mounted() {
    this.subjectId = compUtils.getQueryValue(this, "subject_id", "");
    this.subject = await cmsSubjectApi.get(this.subjectId);

    await this.loadContents();
  }

  async loadContents() {
    this.loading = true;
    let param: any = {};
    try {
      let categories = await cmsCategoryApi.getList(this.subjectId, param);
      let rcat: Array<any> = [];
      let id2cat: any = {};
      categories.map((x: any) => (id2cat[x.id] = x));
      for (let cat of categories) {
        let pcat = id2cat[cat.idParent];
        if (pcat) {
          pcat.children = pcat.children ? pcat.children : [];
          pcat.hasChildren = true;
          pcat.children.push(cat);
        } else {
          rcat.push(cat);
        }
      }
      console.log("categories=>", rcat);
      this.categories = rcat;
    } catch (error) {
      this.$notify.error({ title: "加载出错，请稍后重试", message: "" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handleSearchClick() {
    await this.loadContents();
  }

  async handlePageChange() {}

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        this.subjectId
      }/category/edit`
    );
  }

  async handleModifyClick(record: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        this.subjectId
      }/category/edit?id=${record.id}`
    );
  }

  async handleDeleteClick(record: any) {
    await cmsCategoryApi.del(this.subjectId, record.id);
    this.loadContents();
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.table-column-img {
  max-width: 60px;
  max-height: 60px;
}
</style>

