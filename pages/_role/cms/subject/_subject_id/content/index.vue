<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar CMS主题管理
      el-breadcrumb-item {{ subject.name }}
  .container
    h2.mgb20
      | {{ subject.name }}（{{ subject.path }}）文档管理
    .y-page-table
      .y-header
        //- 表头
        el-button.mgr10(
          type="primary",
          icon="el-icon-plus",
          @click="handleAddClick"
        ) {{ $t("common.base.add") }}
        el-input.handle-input.mgr10(v-model="query.keyword", placeholder="关键词")
        el-button.mgr10(
          type="primary",
          icon="el-icon-search",
          @click="handleSearchClick"
        ) {{ $t("common.base.search") }}
      .y-body
        //- 表单内容
        el-table.table(
          :data="contents",
          border,
          ref="multipleTable",
          header-cell-class-name="table-header",
          v-loading="loading"
        )
          el-table-column(label="文档内容")
            template(v-for="struct in contentStructs")
              el-table-column(
                v-if="struct.show",
                :label="struct.name",
                :type="struct.dataType == 'DATE' ? 'date' : ''",
                align="center"
              )
                template(#default="scope")
                  template(v-if="struct.dataType == 'ENUM'")
                    | {{ enum2value(scope.row[struct.code], struct) }}
                  template(v-else-if="struct.dataType == 'DATE'")
                    | {{ scope.row[struct.code] ? $moment(scope.row[struct.code]).format("YYYY-MM-DD HH:mm:ss") : "" }}
                  template(v-else-if="struct.dataType == 'STRING'")
                    template(v-if="struct.labelType == 'IMG_UPLOAD'")
                      template(v-for="val in strSplit(scope.row[struct.code])")
                        img.table-column-img(:src="getFileUrl(fileUrl, 3)")
                    template(v-else)
                      | {{ scope.row[struct.code] }}
                  template(v-else)
                    | {{ scope.row[struct.code] }}

          //- el-table-column(type="selection", width="55", align="center")
          //- el-table-column(prop="id", label="Id", align="center")
          el-table-column(label="文档信息")
            el-table-column(prop="_path", label="访问地址")
            el-table-column(label="展示")
              template(#default="scope")
                | {{ scope.row._open ? "是" : "否" }}
            el-table-column(label="置顶")
              template(#default="scope")
                | {{ scope.row._top ? "是" : "否" }}
            el-table-column(prop="_pv", label="PV")
            el-table-column(
              prop="_createTime",
              type="date",
              label="创建时间",
              width="110"
            )
              template(#default="scope")
                div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            el-table-column(prop="_ord", label="排序", width="60")
            el-table-column(prop="_remark", label="备注", width="150")

            el-table-column(label="操作", width="150", align="center")
              template(#default="scope")
                el-button(
                  type="text",
                  icon="el-icon-document",
                  @click="handleContentListUrlClick(scope.row)"
                ) 开放地址
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleModifyClick(scope.row)"
                ) {{ $t("common.base.modify") }}
                el-popconfirm.mgl10(
                  title="确认要删除数据吗？",
                  @confirm="handleDeleteClick(scope.row)"
                )
                  el-button.red(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
      .y-footer
        //- 分页信息
        .pagination
          el-pagination(
            background,
            layout="total, prev, pager, next",
            :current-page="query.page",
            :page-size="query.rows",
            :total="query.total",
            @current-change="handlePageChange"
          )
</template>

<script lang="ts">
import { Component, Vue, VuexModule } from "nuxt-property-decorator";
import cmsSubjectApi from "~/api/cms/mg/subjectApi";
import cmsContentApi from "~/api/cms/contentApi";
import { config } from "~/config/global.config";
import urlUtils from "~/utils/urlUtils";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-cms-subject-subject_id-content",
})
export default class CmsContentList extends Vue {
  loading: boolean = true;

  // 主题信息
  subjectId = "";
  subject: any = {};
  contentStructs = [];

  // 当前数据
  query = { page: 0, rows: 20, total: 0, keyword: "" };
  contents: any = [];

  getFileUrl(url: string, s: number) {
    return urlUtils.file2url(url, { s });
  }

  async mounted() {
    this.subjectId = compUtils.getQueryValue(this, "subject_id", "");
    this.subject = await cmsSubjectApi.get(this.subjectId);
    this.contentStructs = this.subject.contentStructs;

    await this.loadContentCount();
    if (this.query.total > 0) {
      await this.loadContents();
    }
  }

  strSplit(val: string) {
    return val ? val.split(",") : [];
  }

  enum2value(val: string, struct: any) {
    if (!val) {
      return "";
    }
    if (struct.restrict && struct.restrict.enumValues) {
      for (let i in struct.restrict.enumValues) {
        let ev = struct.restrict.enumValues[i];
        if (ev.code == val) {
          return `${ev.name}(${val})`;
        }
      }
    }
    return val;
  }

  async loadContentCount() {
    this.loading = true;
    let param: any = {};
    param._category_id = "";
    param._page = this.query.page;
    param._rows = this.query.rows;
    try {
      this.query.total = await cmsContentApi.getListCount(
        this.subjectId,
        param
      );
    } catch (error) {
      this.$notify.error("加载出错，请稍后重试");
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContents() {
    this.loading = true;
    let param: any = {};
    param._category_id = "";
    param._page = this.query.page;
    param._rows = this.query.rows;
    try {
      this.contents = await cmsContentApi.getList(this.subjectId, param);
    } catch (error) {
      this.$notify.error("加载出错，请稍后重试");
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handleSearchClick() {
    await this.loadContents();
  }

  async handlePageChange() {}

  async handleContentListUrlClick(record: any) {
    let url =
      config.baseUrl.apiUrl +
      `/cms/open/c/${this.subject.path}/${record._path}`;
    window.open(url);
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        this.subjectId
      }/content/edit`
    );
  }

  async handleModifyClick(record: any) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        this.subjectId
      }/content/edit?id=${record._id}`
    );
  }

  async handleDeleteClick(record: any) {
    await cmsSubjectApi.delete(record.id);
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.table-column-img {
  max-width: 60px;
  max-height: 60px;
}
</style>

