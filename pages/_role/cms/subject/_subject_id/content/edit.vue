<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar CMS主题管理
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ subject.name }}
      el-breadcrumb-item {{ this.id ? $t("pages.cms.subject.edit.title_edit") : $t("pages.cms.subject.edit.title_add") }}
  .container
    .form-box
      el-form(
        ref="form",
        :model="content",
        label-width="80px",
        width="900px",
        v-loading="loading"
      )
        structs-edit(:structs="contentStructs", v-model="content")

        el-divider

        el-form-item(label="所属分类")
          el-select(v-model="content._categoryId")
            el-option(
              v-for="cat in categories",
              :key="cat.id",
              :label="cat.name",
              :value="cat.id"
            )
        el-form-item(v-if="!id", label="路径")
          el-input(v-model="content._path")
          | 为空时将自动生成
        el-form-item(v-if="id", label="路径")
          template(#default="scope")
            | {{ content._path }}
        el-form-item(label="置顶")
          el-switch(v-model="content._top")
        el-form-item(label="是否展示")
          el-switch(v-model="content._open")
        el-form-item(label="开始时间")
          el-date-picker(
            v-model="content._startTime",
            type="datetime",
            placeholder="选择日期时间"
          )
        el-form-item(label="结束时间")
          el-date-picker(
            v-model="content._endTime",
            type="datetime",
            placeholder="选择日期时间"
          )
        el-form-item(label="排序")
          el-input-number(
            v-model="content._ord",
            :min="1",
            :max="10000",
            label="排序"
          )
        el-form-item
          el-button(type="primary", @click="handelSubmit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import cmsSubjectApi from "~/api/cms/mg/subjectApi";
import cmsContentApi from "~/api/cms/contentApi";
import cmsCategoryApi from "~/api/cms/categoryApi";
import structsEdit from "./structs-edit.vue";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-cms-subject-subject_id-content-edit",
  components: { "structs-edit": structsEdit },
})
export default class CmsContentEdit extends mixins(BaseVue) {
  loading: boolean = false;

  // 主题信息
  subjectId = "";
  subject: any = {};
  contentStructs = [];
  categories = [];

  // 内容
  id = "";
  content = {
    _categoryId: null,
    _ord: 1000,
    _top: false,
    _path: "",
    _open: true,
    _startTime: null,
    _endTime: null,
  };

  async mounted() {
    this.subjectId = compUtils.getQueryValue(this, "subject_id", "");
    this.subject = await cmsSubjectApi.get(this.subjectId);
    this.contentStructs = this.subject.contentStructs;
    this.categories = await cmsCategoryApi.getList(this.subjectId, {});

    this.id = <string>this.$route.query.id;
    if (!this.id) {
      return;
    }
    let content = await cmsContentApi.get(this.subjectId, this.id);
    content._startTime = content._startTime
      ? new Date(content._startTime)
      : null;
    content._endTime = content._endTime ? new Date(content._endTime) : null;
    this.content = content;
  }

  async handelSubmit() {
    let that = this;

    if (!(await this.$rule.formCheck(that, "form"))) {
      return;
    }
    let param = JSON.parse(JSON.stringify(that.content));
    param._startTime = that.content._startTime
      ? (<any>that.content._startTime).getTime()
      : null;
    param._endTime = that.content._endTime
      ? (<any>that.content._endTime).getTime()
      : null;
    that.loading = true;
    // let id: string = <any>this.$route.params.id;
    // param.idParent = !!param.idParent ? param.idParent : "0";
    try {
      if (!that.id) {
        let res: any = await cmsContentApi.post(that.subjectId, param);
        that.id = res.id;
        await that.goBack();
      } else {
        await cmsContentApi.put(that.subjectId, that.id, param);
        await that.goBack();
      }
      that.$notify.success({ title: "保存成功", message: "" });
    } catch (error) {
      console.error(error);
    } finally {
      that.loading = false;
    }
  }

  async goBack() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/cms/subject/${
        this.subjectId
      }/content`
    );
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  width: 100%;
  //     .el-form-item {
  //         width: 600px;
  //     }
  //     .el-form-item.struct {
  //         width: 100%;
  //     }
}
</style>
