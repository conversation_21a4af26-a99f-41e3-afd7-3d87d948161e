<template lang="pug">
div
  div(v-show="false") {{ count }}
  template(v-for="struct in contentStructs")
    //- el-form-item(:label="struct.name")
    .el-form-item
      .el-form-item__label(style="width: 80px")
        span.color-red(v-if="struct.require") *
        | {{ struct.name }}
      .el-form-item__content(style="margin-left: 80px")
        template(v-if="struct.dataType == 'DATE'")
          el-date-picker(
            type="datetime",
            value-format="timestamp",
            v-model="content[struct.code]"
          )
        template(v-if="struct.dataType == 'ENUM'")
          el-select(v-model="content[struct.code]")
            el-option(
              v-for="(ev, eidx) in struct.restrict.enumValues",
              :key="ev.code",
              :label="`${ev.name}(${ev.code})`",
              :value="ev.code"
            )
        template(v-else-if="struct.dataType == 'LIST'")
          .list-items.bb-bottom(v-for="(ctn, idx) in content[struct.code]")
            structs-edit(
              :structs="struct.childrens",
              v-model="content[struct.code][idx]",
              :deep="_deep + 1"
            )
            el-button.ml10.mt10(
              type="primary",
              @click="handleDelRow(idx, struct, content[struct.code][idx])"
            ) 删除数据
            a.ml10(
              v-if="idx != 0",
              href="javascript:void(0)",
              @click="rowUp(idx, struct, content[struct.code][idx])"
            ) 上移字段
            a.ml10(
              v-if="idx != content[struct.code].length - 1",
              href="javascript:void(0)",
              @click="rowDown(idx, struct, content[struct.code][idx])"
            ) 下移字段
          el-button.btn-add-rows(type="primary", @click="handleAddRow(struct)") 添加新数据
        template(v-else-if="struct.dataType == 'MAP'")
          structs-edit(
            :structs="struct.childrens",
            v-model="content[struct.code]",
            :deep="_deep + 1"
          )
        template(v-else)
          ym-richtext(
            v-if="struct.labelType == 'RICHTEXT'",
            v-model="content[struct.code]",
            placeholder=""
          )
          y-upload(
            v-else-if="struct.labelType == 'FILE_UPLOAD' || struct.labelType == 'IMG_UPLOAD'",
            :type="struct.labelType == 'IMG_UPLOAD' ? 'pic' : ''",
            :limit="!!struct.restrict && !!struct.restrict.limit && struct.restrict.limit > 0 ? struct.restrict.limit : null",
            v-model="content[struct.code]"
          )
          el-input(
            v-else-if="struct.labelType == 'TEXTAREA'",
            v-model="content[struct.code]",
            type="textarea",
            :autosize="{ minRows: 2, maxRows: 4 }"
          )
          el-input(v-else, v-model="content[struct.code]")
</template>

<script lang="ts">
import {
  Component,
  Prop,
  Vue,
  VuexModule,
  Watch,
} from "nuxt-property-decorator";
import YmRichtext from "~/components/ym/richtext.vue";
// import { config } from "~/config/global.config";

@Component({
  name: "structs-edit",
  props: ["structs", "value", "deep"],
  components: {
    YmRichtext,
  },
})
export default class StructsEdit extends Vue {
  contentStructs: Array<any> = [];

  content: any = {};

  count: number = 1;

  mounted() {
    this.structsEven();
    this.valueEven();
  }

  get _deep() {
    return this.$props.deep ? this.$props.deep : 0;
  }

  async handleAddRow(struct: any) {
    this.count = this.count + 1;

    let val: any = {};
    struct.childrens.forEach((x: any) => {
      val[x.code] = null;
    });
    this.content[struct.code].push(val);
  }

  async handleDelRow(idx: number, struct: any, ctx: any) {
    this.count = this.count + 1;
    this.content[struct.code].splice(idx, 1);
  }

  async rowUp(idx: number, struct: any, ctx: any) {
    this.content[struct.code].splice(idx, 1);
    this.content[struct.code].splice(idx - 1, 0, ctx);
  }

  async rowDown(idx: number, struct: any, ctx: any) {
    this.content[struct.code].splice(idx, 1);
    this.content[struct.code].splice(idx + 1, 0, ctx);
  }

  initContent() {
    if (!this.content || !this.contentStructs) {
      return;
    }
    this.contentStructs.forEach((x) => {
      if (x.dataType == "LIST") {
        this.content[x.code] = this.content[x.code] ? this.content[x.code] : [];
        x.childrens = x.childrens ? x.childrens : [];
      }
      if (x.dataType == "MAP") {
        this.content[x.code] = this.content[x.code] ? this.content[x.code] : {};
        x.childrens = x.childrens ? x.childrens : [];
      }
    });
  }

  @Watch("$props.value")
  async valueEven() {
    this.content = this.$props.value;
    this.initContent();
    this.count = this.count + 1;
  }

  @Watch("content")
  contentChange(content: any, oldVal: any) {
    this.$emit("input", content);
    this.count = this.count + 1;
  }

  @Watch("$props.structs")
  async structsEven() {
    this.contentStructs = this.$props.structs;
    this.initContent();
    this.count = this.count + 1;
  }

  @Watch("contentStructs")
  structsChange(contentStructs: any, oldVal: any) {
    this.$emit("structs", contentStructs);
    this.count = this.count + 1;
  }
}
</script>
<style lang="scss" scoped>
.mt10 {
  margin-bottom: 20px;
}
.ml10 {
  margin-left: 10px;
}
.el-form-item .el-form-item {
  margin-bottom: 22px;
}
.list-items.bb-bottom {
  margin-bottom: 22px;
  border-bottom: 1px solid #888;
  &:last-child {
    border-bottom: 0px solid #888;
  }
}
.color-red {
  color: red;
}
.btn-add-rows {
  margin-bottom: 20px;
}
</style>
