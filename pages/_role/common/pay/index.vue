<template>
  <div class="common-pay-index">
  </div>
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import routerUtils from "~/utils/routerUtils";
import payApi, { PayUrlQuery } from "~/api/pay/payApi";

import urlUtils from "~/utils/urlUtils";
import fieldUtils from "~/utils/fieldUtils";
import { BaseVue } from "~/model/vue";

// 跳转地址参数return_url：回跳地址，id: 拉起的支付订单id
@Component({
  name: "role-common-pay",
  layout: "empty",
})
export default class CommonPay extends mixins(BaseVue) {
  paying = false;
  paySuccess = false;

  id = "";
  query = {};

  async mounted() {
    let query = routerUtils.getQueryByVue(this);
    this.id = fieldUtils.getFieldValue(query, "id", "");
    this.query = query;

    if (!this.id) {
      this.$router.back();
    }
    this.init();
  }

  async init() {
    if (!this.query || !this.id) {
      this.$router.back();
    }

    try {
      let result = await payApi.payOrdersResult(this.id);
      let query: PayUrlQuery = { ...this.query };
      if (result.pay) {
        let return_url = query.return_url;
        if (return_url) {
          let nr =
            return_url.startsWith("http://") ||
            return_url.startsWith("https://");
          if (!nr) {
            let wwwUrl = routerUtils.getLocalUrl(this);
            return_url = `${wwwUrl}${return_url}`;
          }
        }
        if (return_url) {
          window.location.replace(return_url);
        } else {
          this.$router.back();
        }
      } else {
        let ua = navigator.userAgent;
        console.log("user agent: ", ua);
        if (/(micromessenger)/i.test(ua)) {
          // window.alert("微信");
          query.device_code = "H5";
        } else {
          // window.alert("浏览器");
          let pci =
            /(Android|webOS|iPhone|ipad|iPod|BlackBerry|IEMobile|Opera Mini)/i;
          if (pci.test(ua)) {
            // window.alert("H5");
            query.device_code = "H5";
          } else {
            // window.alert("PC");
            query.device_code = "COMPUTER";
          }
        }
        let url = `${payApi.payUrl}/pay/${this.id}`;
        delete (<any>query).id;
        let return_url = query.return_url;
        // console.log(return_url);
        if (return_url) {
          let nr =
            return_url.startsWith("http://") ||
            return_url.startsWith("https://");
          if (!nr) {
            let wwwUrl = routerUtils.getLocalUrl(this);
            return_url = `${wwwUrl}${return_url}`;
          }
        }
        query.return_url = return_url;
        url = urlUtils.getUrl(url, query);
        window.location.replace(url);
      }
    } catch (error) {
      this.$notify.error(<any>{
        title: "拉起支付异常",
        message: "拉起支付出现异常，请稍后再试。",
      });
      this.$router.back();
    }
  }
}
</script>

<style lang="sass" scoped>
@import './index.scss'
</style>
