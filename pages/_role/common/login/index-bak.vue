<template>
  <div class="login-wrap">
    <div class="ms-login">
      <div class="ms-title">{{ $t('pages.common.login.index.title') }}</div>
      <div class="login-switch-win" v-show="suportLoginTypes.length > 1">
        <div class="w50p" :class="{ active:loginType=='ACCOUNT' }"
             @click="switchLoignType('ACCOUNT')">
          <div class="login-text">账户密码登录</div>
          <div></div>
        </div>
        <div class="w50p" :class="{ active:loginType=='WX_MP_LOGIN' }"
             @click="switchLoignType('WX_MP_LOGIN')">
          <div class="login-text">
            扫码登录
          </div>
          <div></div>
        </div>
      </div>
      <!-- 账户密码登录-->
      <div v-if="loginType=='ACCOUNT'" class="login-window">
        <el-form :model="loginParam" :rules="rules" ref="login" label-width="0px"
                 class="ms-content">
          <el-form-item prop="username">
            <el-input v-model="loginParam.username"
                      :placeholder="$t('pages.common.login.index.login_form_username_placeholder')"
                      tabindex="10" @keyup.enter.native="submit()">
              <template #prepend>
                <el-button icon="el-icon-user"></el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input type="password"
                      :placeholder="$t('pages.common.login.index.login_form_password_placeholder')"
                      v-model="loginParam.password" tabindex="11" @keyup.enter.native="submit()">
              <template #prepend>
                <el-button icon="el-icon-lock"></el-button>
              </template>
            </el-input>
          </el-form-item>
          <div class="login-btn">
            <el-button type="primary" tabindex="12" :loading="loading"
                       @click="submit()">{{ $t('pages.common.login.index.login') }}</el-button>
          </div>
          <p class="login-tips"></p>
        </el-form>
      </div>
      <!-- 扫码登录-->
      <div v-if="loginType=='WX_MP_LOGIN'" class="login-window wxlogin">
        <div class="content">
          <div style="width: 180px; height: 180px; margin: auto;">
            <y-wxmpscanbridge type="WX_MP_LOGIN" :sys-app-id="sysAppId"
                              @success="wxMpScanLoginSuccess" @scan="wxMpScanLoginScan" />
            <p>请使用微信扫码</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import loginApi, { LoginParamVo } from "~/api/auth/auth/loginApi";
import { BadRequestException } from "~/utils/request";
import { config } from "~/config/global.config";
import ruleUtils from "~/utils/ruleUtils";
import { BaseVue } from "~/model/vue";
// import idUtils from "@/utils/idUtils";
// import authWxMpAuthBridgeApi from "@/api/auth/wxMpAuthBridgeApi";

@Component({
  name: "role-common-login",
  layout: "empty",
})
export default class Login extends mixins(BaseVue) {
  loading = false;
  loginParam = { username: "", password: "" };

  info = "info";

  // suportLoginTypes = ["ACCOUNT"];
  suportLoginTypes = ["ACCOUNT", "WX_MP_LOGIN"];

  loginType = "ACCOUNT";

  wxmpUuid = "";
  wxmpImgUrl = "/_nuxt/static/img/login-bg.jpg";

  get sysAppId() {
    return config.sysInfo.wxMpSysAppId;
  }

  get rules() {
    let that = this;
    let a = <string>(
      that.$t("pages.common.login.index.login_form_username_fail")
    );
    return {
      username: [
        ruleUtils.required(
          null,
          <string>that.$t("pages.common.login.index.login_form_username_fail")
        ),
      ],
      password: [
        ruleUtils.required(
          null,
          <string>that.$t("pages.common.login.index.login_form_password_fail")
        ),
      ],
    };
  }

  created() {
    let type = this.$sysconfig.getValue("AUTH_MG_LOGIN_TYPE", false);
    type = type ? type : "ACCOUNT";
    this.suportLoginTypes = type.split(",");
    this.switchLoignType(this.suportLoginTypes[0]);
  }

  mounted() {}

  /**
   * 切换登录方式
   */
  async switchLoignType(type: string) {
    if (this.loginType == type) {
      return;
    }
    this.loginType = type;
  }

  async wxMpScanLoginScan() {
    console.log("扫码完成。");
  }
  async wxMpScanLoginSuccess(token: any) {
    await this.$store.dispatch("auth/setToken", token);
    console.log("扫码登录成功。");
    await this.loginSuccess();
  }

  async submit() {
    this.loading = true;
    let loginForm = <any>this.$refs["login"];
    loginForm.validate(async (valid: boolean) => {
      if (valid) {
        let username = this.loginParam.username;
        let password = this.loginParam.password;
        await this.login({ username, password });
      }
    });
  }
  async login(param: LoginParamVo) {
    try {
      let result = await loginApi.login(param);
      this.$store.dispatch("auth/setToken", result);
      await this.loginSuccess();
    } catch (e) {
      await this.loginError(e);
    } finally {
      this.loading = false;
    }
  }
  private async loginSuccess() {
    let url = <string>this.$route.query.return_url;
    url = url ? url : `/`;
    this.$router.push(url);
  }
  private async loginError(e: any) {
    if (e instanceof BadRequestException) {
      let be = <BadRequestException>e;
      if (be.code == "USERNAME_OR_PASSWORD_FAIL") {
        this.$notify.error(<any>{
          title: "登录失败",
          message: this.$i18n.t("pages.common.login.index.login_form_fail_msg"),
        });
        return;
      } else if (be.code == "MAN_MATCHINE_FAIL") {
        this.$notify.error(<any>{
          title: "账户锁定，请稍后再试",
          message: this.$i18n.t("pages.common.login.index.login_form_fail_msg"),
        });
        return;
      }
    }
    this.$notify.error({
      title: e.message,
      message: "",
    });
  }
}
</script>

<style lang="sass" scoped>
@import './index.scss'
</style>
