<template lang='pug'>
.loginwindow
    //- 登录表单内容
    .loginform
        h1.title {{ $t('pages.common.login.index.welcome') }}{{ $t('pages.common.login.index.login') }}
        div
            a-form-model(
                @submit="submit",
                :model="loginParam",
                :rules="rules",
                ref="loginForm"
            )
                a-form-model-item(
                    :label="$t('pages.common.login.index.login_form_username_label')",
                    prop="username"
                )
                    a-input(
                        v-model="loginParam.username",
                        :placeholder="$t('pages.common.login.index.login_form_username_tip')"
                    )
                a-form-model-item(
                    :label="$t('pages.common.login.index.login_form_password_label')",
                    prop="password"
                )
                    a-input(
                        v-model="loginParam.password",
                        type="password",
                        :placeholder="$t('pages.common.login.index.login_form_password_tip')",
                        @keydown="passwordKeyDown"
                    )
                a-form-model-item
                    a-button(
                        type="primary",
                        @click="submit",
                        :loading="loading",
                        block
                    ) {{ $t('pages.common.login.index.login') }}
</template>

<script lang="ts">
import { Component, Prop, Vue, VuexModule } from "nuxt-property-decorator";
import authApi from "@/api/authApi";
import { LoginParam } from "@/api/authApi";
import { notification } from "ant-design-vue";
import { BadRequestException } from "~/utils/request";
const md5 = require("md5");

const Cookie = process.client ? require("js-cookie") : undefined;

import { ValidUtils } from "~/utils/validUtils";

@Component({
    name: "login",
    layout: "empty",
})
export default class Login extends Vue {
    loading = false;
    loginParam = { username: "", password: "" };

    info = "info";

    get rules() {
        return {
            username: [
                ValidUtils.buildEmptyValidator(
                    this.$t("pages.common.login.index.login_form_username_fail")
                ),
            ],
            password: [
                ValidUtils.buildEmptyValidator(
                    this.$t("pages.common.login.index.login_form_password_fail")
                ),
            ],
        };
    }

    mounted() {}

    async passwordKeyDown(e: KeyboardEvent) {
        if ("Enter" == e.key) {
            await this.submit();
        }
    }
    async submit() {
        this.loading = false;
        let loginForm = <any>this.$refs["loginForm"];
        loginForm.validate(async (valid: boolean) => {
            if (valid) {
                let param: LoginParam = new LoginParam();
                param.username = this.loginParam.username;
                param.password = md5(this.loginParam.password);
                await this.login(param);
            }
        });
    }
    async login(param: LoginParam) {
        try {
            let result = await authApi.login(param);
            await this.loginSuccess(result);
        } catch (e) {
            await this.loginError(e);
        } finally {
            this.loading = false;
        }
    }
    private async loginSuccess(result: any) {
        this.$store.commit("setToken", result);

        let url = <string>this.$route.query.return_url;
        url = url ? url : "/";
        this.$router.push(url);
    }
    private async loginError(e: any) {
        if (e instanceof BadRequestException) {
            let be = <BadRequestException>e;
            if (be.code == "USERNAME_OR_PASSWORD_FAIL") {
                notification.error(<any>{
                    message: this.$i18n.t(
                        "pages.common.login.index.login_form_fail_msg"
                    ),
                });
                return;
            }
        }
        notification.error(<any>{
            message: e.message,
        });
    }
}
</script>

<style lang="sass" scoped>
@import url('./index.scss')
</style>
