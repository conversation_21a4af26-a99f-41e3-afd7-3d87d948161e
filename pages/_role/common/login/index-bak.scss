@import '@/static/css/_var.scss';

.login-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  background-image: url(~/static/img/login-bg.jpg);
  background-size: cover;
}

.ms-title {
  width: 100%;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  color: #ffffff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  font-weight: 800;
}

.ms-login {
  position: absolute;
  left: 50%;
  top: 40%;
  width: 350px;
  margin: -190px 0 0 -175px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ms-content {
  padding: 30px 30px;
}

.login-btn {
  text-align: center;
}

.login-btn button {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
}

.login-tips {
  font-size: 12px;
  line-height: 30px;
  color: #fff;
}

/* 切换登录方式 */
.login-switch-win {
  display: flex;
  height: 33px;
  width: 100%;

  font-size: 18px;
  color: #555;
  text-align: center;

}

.login-switch-win .w50p {
  width: 50%;
  height: 100%;
  border: solid #dddddd 1px;
  border-bottom-width: 2px;
  border-bottom-color: #dddddd76;
  border-top: 0px;
  border-right: 0px;
  cursor: pointer;
}

.login-switch-win .w50p .login-text {
  padding: 6px;
  color: rgba(255, 255, 255, 0.9);
}

.login-switch-win .w50p:hover .login-text {
  color: rgba(255, 255, 255, 0.5);
}

.login-switch-win .w50p.active {
  border-bottom-color: $--color-primary;
}

/* 登录窗口 */
.login-window {
  min-height: 240px;
}

.login-window.wxlogin .content {
  padding: 20px;
  text-align: center;
}

.login-window.wxlogin .content img {
  height: 180px;
  width: 180px;
}