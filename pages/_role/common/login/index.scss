@import '@/static/css/_var.scss';

.login-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  background: url(~/static/img/login/bg.png);
  background-repeat: no-repeat;
  background-size: contain;
  background-color: black;
}

.ms-title {
  width: 100%;
  line-height: 1.5;
  text-align: center;
  color: #ffffff;
  font-family: 'PingFangSC';
  font-size: 24px;
  font-weight: 500;
  margin: 32px 0;
}

.ms-login {
  width: 500px;
  // height: 576px;
  position: absolute;
  top: 20%;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 20px;
  background-blend-mode: overlay;
  border: 1px solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
  // background-color:rgba(255, 255, 255, 0.05) ;
}
.logo-wrap{
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 48px;
}
.logo-icon{
  width: 60px;
  height: 60px;
}
.ms-content {
  padding: 30px 30px;
}

.login-btn {
  text-align: center;
  margin-bottom: 20px;
}

.login-btn button {
  width: 100%;
  height: 45px;
  font-family: 'PingFangSC';
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  // margin-bottom: 10px;
  border-radius: 28px;
  border: 0;
  background-image: linear-gradient(84deg, #4f0293 0%, #5899ff 100%);
}

.login-tips {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  p {
    font-size: 12px;
    line-height: 30px;
    color: #fff;
    font-family: 'PingFangSC';
    // padding-left: 8px;
  }
  span {
    color: #448ef4;
  }
  img {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  .checkbox{
    width: 16px;
    height: 16px;
    border-radius: 4px;
    background-color: #ffffff;
    cursor: pointer;
  }
}

/* 切换登录方式 */
.login-switch-win {
  display: flex;
  width: 100%;
  justify-content: center;
  font-family: 'PingFangSC';
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.login-switch-win .w50p {
  height: 29px;
  line-height: 29px;
  cursor: pointer;
  margin: 0 12px;
  border-bottom: solid 2px rgba(255, 255, 255, 0);
}

.login-switch-win .w50p .login-text {
  color: rgba(235, 235, 245, 0.7);
}

// .login-switch-win .w50p:hover .login-text {
//   color: rgba(255, 255, 255, 0.5);
// }

.login-switch-win .w50p.active {
  border-bottom-color: #fff;
}
.login-switch-win .w50p.active .login-text{
  color: #fff;
}

/* 登录窗口 */
.login-window {
  min-height: 240px;
}

.login-window.wxlogin .content {
  padding: 20px;
  text-align: center;
}

.login-window.wxlogin .content img {
  height: 180px;
  width: 180px;
}

/* 输入框样式篡改 */

::v-deep {
  .el-input__inner,
  .el-input-group__prepend,
  .el-input-group__append {
    background-color: transparent;
  }
  .el-input-group__prepend {
    padding-left: 12px;
    padding-right: 0;
  }

  .el-input__inner {
    color: #fff;
    font-family: 'PingFangSC';
    font-size: 14px;

    &::placeholder {
      font-family: 'PingFangSC';
      font-size: 14px;
      color: rgba(235, 235, 245, 0.7);
    }
  }

  .el-input-group--prepend .el-input__inner,
  .el-input-group--append .el-input-group__append {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border: solid 1px rgba(255, 255, 255, 0.15);
    border-left: 0;  
  }

  .el-input-group--prepend .el-input-group__prepend,
  .el-input-group--append .el-input__inner{
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border: solid 1px rgba(255, 255, 255, 0.15);
    border-right: 0; 
  }
  .el-form-item__label {
    padding: 0;
    color: #fff;
    line-height: 18px;
    padding-bottom: 4px;
    font-size: 12px;
    font-family: 'PingFangSC';
  }

}
.phone-btn{
  width: 34px;
  font-family: 'PingFangSC';
  font-size: 14px;
  color: #fff;
  border-right: solid 1px rgba(255, 255, 255, 0.15);
}
.captcha-btn{
  font-size: 12px;
  color: #448ef4;
  cursor: pointer;
  letter-spacing: normal;
  font-family: 'PingFangSC';
}
.captcha-txt{
  font-size: 12px;
  color: #448ef4;
  letter-spacing: normal;
  font-family: 'PingFangSC';
}
.unmask-icon{
  width: 18px;
  height: 18px;
  cursor: pointer;
  padding-top: 4px;
}

// .common-checkbox{
//   width: 16px;
//   height: 16px;
//   border-radius: 4px;
//   background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
//   border: solid 1px rgba(255, 255, 255, 0.15);
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   cursor: pointer;
//   box-sizing: border-box;
//   span {
//       display: none;
//     }
//   &.active{
//     background-color: rgba(255, 255, 255);
//     color: rgba(23, 22, 24);

//     span {
//       font-size: 8px;
//         display: inline-block;
//         color: rgba(23, 22, 24);
//       }
//   }
// }

