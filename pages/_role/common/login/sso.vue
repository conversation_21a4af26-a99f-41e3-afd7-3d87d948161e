<template lang="pug">
.storelogin
  h1.info
    i.el-icon-loading
    span.msg 跳转中，请稍后...
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import authBridgeApi from "~/api/auth/auth/bridgeApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-common-login-sso",
  layout: "empty",
})
export default class CommonLoginSso extends mixins(BaseVue) {
  query = {
    id: "",
    return_url: "",
  };

  async mounted() {
    await this.initParam();
  }

  async initParam() {
    this.query.id = compUtils.getQueryValue(this, "code", "");
    this.query.return_url = compUtils.getQueryValue(this, "return_url", "");

    if (!this.query.id) {
      window.alert("当前用户信息未找到，请重试。");
      this.goBack();
      return;
    }
    await this.initToken();
  }

  async initToken() {
    let id = this.query.id;
    try {
      let token = await authBridgeApi.getTokenById({ id });
      this.$store.dispatch("auth/setToken", token);

      await this.goToPage();
    } catch (error) {
      window.alert("当前用户信息未找到，请重试。");
      this.goBack();
    }
  }

  async goToPage() {
    let rurl = this.query.return_url;
    if (!rurl) {
      rurl = "/";
    }
    if (rurl.startsWith("http://") || rurl.startsWith("https://")) {
      window.location.href = rurl;
    } else {
      this.$router.replace(rurl);
    }
  }

  goBack() {
    setTimeout(() => {
      window.history.go(-2);
    }, 100);
  }
}
</script>

<style lang="scss" scoped>
.storelogin {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  // background: linear-gradient(45deg, #4caf50, #2196f3); /* 初始背景渐变 */
  // transition: background-color 5s ease-in-out; /* 平滑过渡背景 */

  .info {
    color: rgba(0, 0, 0, 0.8); /* 初始字体颜色 */
    height: 30%;

    .msg {
      margin-left: 10px;
    }
  }
}
</style>


