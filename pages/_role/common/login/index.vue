<template>
  <div class="login-wrap">
    <div class="ms-login">
      <div class="logo-wrap">
        <img src="/img/logo.svg" class="logo-icon" alt="">
      </div>
      <div class="ms-title">欢迎登录</div>
      <div class="login-switch-win" v-show="suportLoginTypes.length > 1">
        <div class="w50p" :class="{ active:loginType=='CAPTCHA' }" @click="switchLoignType('CAPTCHA')">
          <div class="login-text">验证码登录</div>
          <div class="text-line"></div>
        </div>
        <div class="w50p" :class="{ active:loginType=='ACCOUNT' }" @click="switchLoignType('ACCOUNT')">
          <div class="login-text">密码登录</div>
          <div class="text-line"></div>
        </div>
      </div>
      <!-- 账户密码登录-->
      <div v-if="loginType=='ACCOUNT'" class="login-window">
        <el-form :model="accountLoginParam" :rules="rules" ref="login" label-width="0px" class="ms-content"
          label-position="top">
          <el-form-item prop="username" label="手机号码">
            <el-input v-model="accountLoginParam.username" placeholder="请输入手机号码" tabindex="10"
              @keyup.enter.native="submit()">
              <template #prepend>
                <div class="phone-btn">+86</div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password" label="登录密码">
            <el-input type="password" placeholder="请输入登录密码" v-model="accountLoginParam.password" tabindex="11"
              @keyup.enter.native="submit()">
              <template #append>
                <!-- <el-button icon="el-icon-view"></el-button> -->
                <img src="/img/login/unmask.png" class="unmask-icon" alt="">
              </template>
            </el-input>
          </el-form-item>
          <div class="login-tips">
            <y-checkbox v-model="checked"></y-checkbox>
            <p>我已仔细阅读并同意接受古德的
              <span>使用条款</span>
              和
              <span>隐私政策</span>
            </p>
          </div>
          <div class="login-btn">
            <el-button type="primary" tabindex="12" :loading="loading" @click="submit()">立即登录</el-button>
          </div>
          <!-- <p class="login-tips"></p> -->
        </el-form>
      </div>
      <!-- 验证码登录-->
      <div v-if="loginType=='CAPTCHA'" class="login-window">
        <el-form :model="captchaLoginParam" :rules="rules" ref="login" label-width="0px" class="ms-content"
          label-position="top">
          <el-form-item prop="phone" label="手机号码">
            <el-input v-model="captchaLoginParam.phone" placeholder="请输入手机号码" tabindex="10"
              @keyup.enter.native="submit()">
              <template #prepend>
                <div class="phone-btn">+86</div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code" label="验证码">
            <el-input placeholder="请输入手机验证码" v-model="captchaLoginParam.code" tabindex="11"
              @keyup.enter.native="submit()">
              <template #append>
                <div :class="isCounting ? 'captcha-txt' : 'captcha-btn'" @click="startCountdown"> {{ isCounting ?
                  `${count}s 后可重新获取` : '获取验证码' }}</div>
              </template>
            </el-input>
          </el-form-item>
          <div class="login-tips">
            <y-checkbox v-model="checked"></y-checkbox>
            <p>我已仔细阅读并同意接受古德的
              <span>使用条款</span>
              和
              <span>隐私政策</span>
            </p>
          </div>
          <div class="login-btn">
            <el-button type="primary" tabindex="12" :loading="loading" @click="submit()">立即登录</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import loginApi, { LoginParamVo } from "~/api/auth/auth/loginApi";
import { BadRequestException } from "~/utils/request";
import { config } from "~/config/global.config";
import ruleUtils from "~/utils/ruleUtils";
import { BaseVue } from "~/model/vue";
// import idUtils from "@/utils/idUtils";
// import authWxMpAuthBridgeApi from "@/api/auth/wxMpAuthBridgeApi";

@Component({
  name: "role-common-login",
  layout: "empty",
})
export default class Login extends mixins(BaseVue) {
  loading = false;
  checked = false;
  count: number = 60;
  timer: any = null;
  isCounting = false;
  captchaLoginParam = { phone: "", code: "" };
  accountLoginParam = { username: "", password: "" };

  info = "info";

  // suportLoginTypes = ["ACCOUNT"];
  suportLoginTypes = ["CAPTCHA", "ACCOUNT"];

  loginType = "CAPTCHA";

  wxmpUuid = "";
  wxmpImgUrl = "/_nuxt/static/img/login-bg.jpg";

  get sysAppId() {
    return config.sysInfo.wxMpSysAppId;
  }

  get rules() {
    let that = this;
    let a = <string>(
      that.$t("pages.common.login.index.login_form_username_fail")
    );
    return {
      username: [
        ruleUtils.required(
          null,
          <string>that.$t("pages.common.login.index.login_form_username_fail")
        ),
      ],
      password: [
        ruleUtils.required(
          null,
          <string>that.$t("pages.common.login.index.login_form_password_fail")
        ),
      ],
    };
  }

  created() {
    // let type = this.$sysconfig.getValue("AUTH_MG_LOGIN_TYPE", false);
    // type = type ? type : "CAPTCHA";
    // this.suportLoginTypes = type.split(",");
    // this.switchLoignType(this.suportLoginTypes[0]);
    this.switchLoignType("CAPTCHA");
  }

  mounted() {}

  /**
   * 切换登录方式
   */
  async switchLoignType(type: string) {
    if (this.loginType == type) {
      return;
    }
    this.loginType = type;
  }

  async wxMpScanLoginScan() {
    console.log("扫码完成。");
  }
  async wxMpScanLoginSuccess(token: any) {
    await this.$store.dispatch("auth/setToken", token);
    console.log("扫码登录成功。");
    await this.loginSuccess();
  }
  
  checkedChange(val:any){
    this.checked = val;
  }
  //获取验证码
  startCountdown() {
    if (this.isCounting) return

    this.isCounting = true
    this.count = 60

    // 启动定时器
    this.timer = setInterval(() => {
      this.count--
      if (this.count <= 0) {
        clearInterval(this.timer)
        this.isCounting = false
      }
    }, 1000)

    // TODO: 在这里发起验证码请求
    // this.sendVerificationCode()
  }

  async submit() {
    this.loading = true;
    let loginForm = <any>this.$refs["login"];
    loginForm.validate(async (valid: boolean) => {
      if (valid) {
        let username = this.accountLoginParam.username;
        let password = this.accountLoginParam.password;
        await this.login({ username, password });
      }
    });
  }
  async login(param: LoginParamVo) {
    try {
      let result = await loginApi.login(param);
      this.$store.dispatch("auth/setToken", result);
      await this.loginSuccess();
    } catch (e) {
      await this.loginError(e);
    } finally {
      this.loading = false;
    }
  }
  private async loginSuccess() {
    let url = <string>this.$route.query.return_url;
    url = url ? url : `/`;
    this.$router.push(url);
  }
  private async loginError(e: any) {
    if (e instanceof BadRequestException) {
      let be = <BadRequestException>e;
      if (be.code == "USERNAME_OR_PASSWORD_FAIL") {
        this.$notify.error(<any>{
          title: "登录失败",
          message: this.$i18n.t("pages.common.login.index.login_form_fail_msg"),
        });
        return;
      } else if (be.code == "MAN_MATCHINE_FAIL") {
        this.$notify.error(<any>{
          title: "账户锁定，请稍后再试",
          message: this.$i18n.t("pages.common.login.index.login_form_fail_msg"),
        });
        return;
      }
    }
    this.$notify.error({
      title: e.message,
      message: "",
    });
  }
}
</script>

<style lang="sass" scoped>
@import './index.scss'
</style>
