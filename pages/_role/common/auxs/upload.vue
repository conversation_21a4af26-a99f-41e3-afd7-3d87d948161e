<template lang="pug">
.main-win
  .up-comp(:style="{ display: upData.uploading ? '' : 'none' }")
    .up-comp-text 上传中，请稍候...
    y-upload(
      ref="upload",
      v-model="bizData.file",
      :type="upData.type",
      :limit="upData.limit",
      :multiple="upData.multiple",
      :accept="upData.accept",
      :secrecy="upData.secrecy",
      :watermark="upData.watermark",
      :exp="upData.exp",
      :uid="upData.uid",
      :ticket="bizData.ticket",
      :disabled="upData.uploading",
      @change="handleUploadChange",
      @uploading="handleUploading"
    )
  .up-win(
    :style="{ display: upData.uploading ? 'none' : '' }",
    @click="handleUploadClick"
  )
    .up-text 点击上传
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import fsUpSyncApi from "~/api/sys/fs/upSyncApi";
import routerUtils from "~/utils/routerUtils";
import { FileUploadingEvent } from "~/components/y/upload.vue";

@Component({
  name: "role-common-aux-upload",
  layout: "empty",
})
export default class PageRoleCommonAuxUpload extends mixins(BaseVue) {
  loading = 0;
  query = {
    id: "",
  };
  oneLoadParam = {
    inited: false,
    initCount: 0,
    // ...param
  };
  bizData = {
    file: "",

    ticket: "",
    upTimer: <any>null,
  };
  upData = {
    limit: 1,
    aceept: "image/*",
    type: "pic",
    multiple: false,
    secrecy: false,
    watermark: false,
    exp: null,
    uid: null,
    uploading: false,
  };

  async mounted() {
    await this.init();
  }

  async destroyed() {
    if (this.bizData.upTimer) {
      clearInterval(this.bizData.upTimer);
      this.bizData.upTimer = null;
    }
  }

  async init() {
    this.loading++;
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.oneLoadParam.initCount++;
    try {
      await this.initDetailBefor();
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initDetailBefor() {
    this.query.id = routerUtils.getQueryValue(this, "id", "");
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    const result = await fsUpSyncApi.putValue(this.query.id, {});
    if (!result || !result.valid) {
      this.closeMsg();
      return;
    }
    this.bizData.ticket = result.ticket!;

    if (!result.baseMsg) {
      return;
    }

    const data = JSON.parse(result.baseMsg);
    this.upData.limit = data.limit;
    this.upData.type = data.type;
    this.upData.aceept = data.aceept;
    this.upData.multiple = data.multiple;
    this.upData.secrecy = data.secrecy;
    this.upData.exp = data.exp;
    this.upData.uid = data.uid;
    this.upData.watermark = !!data.watermark;

    this.bizData.upTimer = setInterval(async () => {
      await this.checkServer();
    }, 1000);
  }

  async checkServer() {
    const result = await fsUpSyncApi.putValue(this.query.id, {});
    if (!result || !result.valid) {
      this.closeMsg();
      return;
    }
  }

  async closeMsg() {
    if (this.bizData.upTimer) {
      clearInterval(this.bizData.upTimer);
      this.bizData.upTimer = null;
    }
    window.alert("上传授权已退出，请重新扫码。");
    this.$router.push("/");
    window.close();
  }

  async handleUploadClick() {
    (<any>this.$refs).upload.handleUploadInputClick();
  }

  async handleUploadChange() {
    const fid = this.bizData.file;
    if (!fid) {
      return;
    }
    await fsUpSyncApi.putValue(this.query.id, { value: fid });
    this.bizData.file = "";

    this.$message.success("已有文件同步到PC端，请在PC端端查看。");
  }

  async handleUploading(e: FileUploadingEvent) {
    this.upData.uploading = e.uploadingList && e.uploadingList.length > 0;
  }
}
</script>

<style lang="scss" scoped>
.main-win {
  padding: 0px;

  width: 100vw;
  height: 50vw;

  .up-comp {
    .up-comp-text {
      font-size: 20px;
      color: #ccc;
    }
  }

  .up-win {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    margin: 10px;
    border: 2px solid #ccc;
    border-radius: 10px;

    font-size: 30px;
    color: #ccc;

    height: 80%;

    cursor: pointer;
  }
}
</style>

