<template lang="pug">
.page-power-power-index
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("pages.power.power.index.title") }}
  el-row.rows
    el-col.col-win.left(:span="11")
      .container
        .mgb10
          el-button(type="primary", @click="() => handleAddClick()") {{ $t("common.base.add") }}
        el-input(placeholder="输入关键字进行过滤", v-model="filterText")
        el-tree(
          :loading="loading",
          :data="powersTree",
          ref="ptree",
          node-key="code",
          :props="tpowerDefaultProps",
          draggable,
          @node-drag-end="handlePowerTreeDragEnd",
          @node-click="handleTreeNodeClick",
          :filter-node-method="filterNode"
        )
          span.custom-tree-node(slot-scope="{ node, data }")
            span.tree-label
              el-tooltip(
                :content="node.label",
                placement="right",
                :open-delay="500"
              )
                div {{ node.label }}
            span.tree-operate(@click.stop="() => {}")
              el-button(
                type="text",
                size="mini",
                @click="handleAddClick(data.code)"
              ) 添加下一级
              el-button(
                type="text",
                size="mini",
                @click="handleEditClick(data)"
              ) {{ $t("common.base.modify") }}
              el-popconfirm.mgl10(
                :title="$t('common.base.q_delete')",
                :confirm-button-text="$t('common.base.confirm')",
                :cancel-button-text="$t('common.base.cancel')",
                @confirm="handleDeleteClick(data)"
              )
                el-button.danger(slot="reference", type="text", size="mini") {{ $t("common.base.delete") }}

    el-col.col-win.right(:span="12", :offset="1")
      .container
        h2 详情：
        el-form(:model="power", label-width="80px", width="900px")
          el-form-item(label="Code")
            el-input(v-model="power.code", :disabled="true")
          el-form-item(label="名称")
            el-input(v-model="power.name", :disabled="true")
          el-form-item(label="请求方法")
            el-input(v-model="power.httpMethod", :disabled="true")
          el-form-item(label="URL地址")
            el-input(v-model="power.url", :disabled="true")
          el-form-item(label="所属APP")
            el-input(v-model="power.appCode", :disabled="true")
          el-form-item(label="所属权限")
            el-input(v-model="power.parentCode", :disabled="true")
          el-form-item(label="树形依赖")
            //- el-input(v-model="power.depCode", :disabled="true")
            el-tree(
              :loading="loading",
              :data="showPowerTree",
              node-key="code",
              default-expand-all,
              :props="showPowerDefaultProps"
            )
          el-form-item(label="类")
            el-input(v-model="power.clsName", :disabled="true")
          el-form-item(label="方法")
            el-input(v-model="power.clsMethod", :disabled="true")
          el-form-item(label="备注")
            el-input(v-model="power.remark", :disabled="true")

        h2 被依赖的角色：
          el-table(:data="rolePowers")
            el-table-column(label="Code")
              template(#default="scope")
                | {{ getRoleByCode(scope.row.roleCode).code }}
            el-table-column(label="名称")
              template(#default="scope")
                | {{ getRoleByCode(scope.row.roleCode).name }}
            el-table-column(
              label="操作",
              v-if="$auth.v_hasPower('POWER_ROLE_MANAGE')"
            )
              template(#default="scope")
                el-popconfirm(
                  :title="$t('common.base.q_delete')",
                  :confirm-button-text="$t('common.base.confirm')",
                  :cancel-button-text="$t('common.base.cancel')",
                  @confirm="handleRoleRevokePowerClick(scope.$index, scope.row)"
                )
                  el-button.danger(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
</template>


<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import authPowerApi, { SysPower } from "~/api/auth/power/powerApi";
import authRoleApi, { SysRole } from "~/api/auth/power/roleApi";
import authRolePowerApi, { SysRolePower } from "~/api/auth/power/rolePowerApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

type SysPowerTree = SysPower & {
  children?: Array<SysPowerTree>;
  label?: string;
  depCodes?: Array<string>;
};

@Component({
  name: "role-power-power",
})
export default class PowerPowerIndex extends mixins(BaseVue) {
  loading = true;
  filterText: string = "";
  rolePowers: Array<SysRolePower> = new Array();
  powersTree: Array<SysPowerTree> = new Array();
  tpowerDefaultProps = {
    children: "children",
    label: (pr: any) =>
      (pr.name ? pr.name + "(" + pr.code + ")" : pr.code) +
      (pr.httpMethod ? `[${pr.httpMethod} ${pr.url}]` : ""),
  };

  // 展示数据
  power: SysPowerTree = {};
  showPowerDefaultProps = { ...this.tpowerDefaultProps, disabled: true };
  showPowerTree: Array<SysPowerTree> = new Array();

  roleCode2role: Map<string, SysRole> | null = null;

  code2power: Map<string, SysPowerTree> | null = null;

  async activated() {
    this.init();
  }

  async init() {
    this.loading = true;
    try {
      await this.refreshTable();
      await this.initDetail();
    } catch (e) {
      console.error(e);
      this.loading = false;
      let msg: any = this.$t("common.error.reload");
      this.$notify.error({
        title: msg,
        message: "",
      });
    } finally {
      this.loading = false;
    }
  }

  async initDetail() {
    let code = compUtils.getQueryValue(this, "code", "");
    if (code) {
      let power = this.code2power?.get(code);
      if (power) {
        this.handleTreeNodeClick(power);
      }
    }
  }

  getRoleByCode(code: string) {
    if (!this.rolePowers) {
      return {};
    }
    return this.roleCode2role?.get(code) ?? {};
  }

  filterNode(value: string, data: SysPowerTree) {
    if (!value) return true;
    const filter = (key: string) => {
      if (!key) {
        return true;
      }
      if (data.name && data.name.indexOf(key) != -1) {
        return true;
      }
      if (data.url && data.url.indexOf(key) != -1) {
        return true;
      }
      if (data.clsName && data.clsName.indexOf(key) != -1) {
        return true;
      }
      if (data.clsMethod && data.clsMethod.indexOf(key) != -1) {
        return true;
      }
      if (data.code && data.code.indexOf(key) != -1) {
        return true;
      }
      return false;
    };
    let vs = value.split("*");
    let ok = true;
    for (let key of vs) {
      if (!filter(key)) {
        ok = false;
        break;
      }
    }
    return ok;
  }

  async refreshTable() {
    // 加载批量数据
    let powers: Array<SysPowerTree> = await authPowerApi.getList();
    powers.forEach((pr) => (pr.children = new Array()));
    powers.forEach((pr) => (pr.label = this.tpowerDefaultProps.label(pr)));
    let code2power: Map<string, SysPowerTree> = arrayUtils.array2map(
      powers,
      "code"
    );
    this.code2power = code2power;

    // 构建树结构
    let powersTree = arrayUtils.array2tree(
      powers,
      "code",
      "parentCode",
      "children"
    );
    this.powersTree = powersTree;
  }

  async handleAddClick(codeParent: string) {
    let url = `/${compUtils.getQueryValue(this, "role", "")}/power/power/edit${
      codeParent ? "?code_parent=" + codeParent : ""
    }`;
    this.$router.push(url);
  }

  async handleEditClick(val: SysPowerTree) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/power/power/edit?code=${
        val.code
      }`
    );
  }

  async handleDeleteClick(val: SysPowerTree) {
    try {
      await authPowerApi.delete(val.code!);
      let msg: any = this.$t("common.success.delete");
      this.$notify.success(msg);
      this.power = {};
      await this.refreshTable();
    } catch (error) {
      let msg: any = this.$t("common.error.delete");
      this.$notify.error(msg);
      throw error;
    }
  }

  async handleTreeNodeClick(data: SysPowerTree) {
    routerUtils.putQueryValue(this, { code: data.code! });

    this.power = data;
    this.showPowerTree = new Array();
    this.power.depCodes = this.power.depCode
      ? this.power.depCode.split(",")
      : new Array();
    this.selectTreeNode(
      this.powersTree,
      this.power.depCodes,
      this.showPowerTree
    );

    if (this.roleCode2role == null) {
      let roles = await authRoleApi.getList();
      let roleCode2role = new Map();
      roles.forEach((role) => roleCode2role.set(role.code, role));
      this.roleCode2role = roleCode2role;
    }
    this.rolePowers = await authRolePowerApi.getList({ power_code: data.code });
  }

  async handleRoleRevokePowerClick(idx: number, rolePower: SysRolePower) {
    await authRolePowerApi.delete(rolePower);
    this.rolePowers.splice(idx, 1);
  }

  // 组装展示数据
  selectTreeNode(
    pts: Array<SysPowerTree>,
    codes: Array<string>,
    spts: Array<SysPowerTree>
  ) {
    for (let k in pts) {
      let pt = pts[k];
      let children = new Array();
      if (pt.children && pt.children.length > 0) {
        this.selectTreeNode(pt.children, codes, children);
      }
      if (children.length > 0 || codes.indexOf(pt.code!) >= 0) {
        let npt = { ...pt };
        npt.children = children;
        spts.push(npt);
      }
    }
  }

  async handlePowerTreeDragEnd(node: any, afterNode: any, inner: any, aa: any) {
    let pr = node.data;
    let ppr = node.store.nodesMap[pr.code].parent;
    let mparam = {
      code: pr.code,
      parentCode: ppr ? ppr.data.code : null,
    };
    if (mparam.parentCode != pr.parentCode) {
      try {
        this.loading = true;
        await authPowerApi.putParentCode([mparam]);
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      } catch (error) {
        console.log(error);
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      } finally {
        this.loading = false;
      }
      pr.parentCode = mparam.parentCode;
      await this.refreshTable();
    }
  }

  @Watch("filterText")
  async filterTextChange() {
    (<any>this.$refs).ptree?.filter(this.filterText);
  }
}
</script>
<style lang="scss" scoped>
@import "@/static/css/_var.scss";

.page-power-power-index {
  height: 100%;
  width: 100%;

  .crumbs {
    height: 16px;
    margin: 0;
    padding: 10px;
  }

  .rows {
    width: 100%;
    height: calc(100% - 36px);

    .col-win {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}
.table {
  width: 100%;
  font-size: 14px;
}
.danger {
  color: $--color-danger;
}
.mr10 {
  margin-right: 10px;
}
.table-td-thumb {
  display: block;
  margin: auto;
  width: 40px;
  height: 40px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  max-width: 100%;

  .tree-label {
    max-width: 430px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  // .tree-operate {
  //   width: 12%;
  // }
}
</style>

