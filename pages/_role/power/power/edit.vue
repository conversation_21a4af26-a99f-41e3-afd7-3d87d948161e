<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar 权限
      el-breadcrumb-item {{ query.code ? $t("pages.power.power.edit.title_edit") : $t("pages.power.power.edit.title_add") }}
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="power",
        label-width="80px",
        width="900px",
        v-loading="loading"
      )
        el-form-item(
          label="Code",
          prop="code",
          :required="true",
          :rules="[$rule.required(null, 'Code不能为空')]"
        )
          el-input(v-model="power.code")
        el-form-item(
          label="名称",
          prop="name",
          :required="true",
          :rules="[$rule.required(null, '名称不能为空')]"
        )
          el-input(v-model="power.name")
        el-form-item(label="请求方法")
          el-input(v-model="power.httpMethod")
        el-form-item(label="URL地址")
          el-input(v-model="power.url")
        el-form-item(v-if="$auth.v_hasPower('ROLE_ADMIN')", label="是否隐藏")
          el-switch(v-model="power.hidden")
        el-form-item(label="所属APP")
          el-input(v-model="power.appCode")
        el-form-item(label="所属权限")
          el-cascader(
            v-model="power.parentCodes",
            :options="powersTree",
            :props="{ checkStrictly: true, label: 'label', value: 'code' }",
            clearable,
            style="width: 100%"
          )
        el-form-item(label="过滤")
          el-input(placeholder="输入关键字进行过滤", v-model="query.filterText")
        el-form-item(label="树形依赖")
          el-tree(
            ref="ptree",
            :data="powersTree",
            show-checkbox,
            node-key="code",
            :default-expanded-keys="[]",
            :default-checked-keys="power.depCodes",
            :props="tpowerDefaultProps",
            :filter-node-method="filterNode"
          )
        el-form-item(label="类")
          el-input(v-model="power.clsName")
        el-form-item(label="方法")
          el-input(v-model="power.clsMethod")
        el-form-item(label="备注")
          el-input(v-model="power.remark")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>
  
<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import authPowerApi, {
  SysPower,
  SysPowerModifyVo,
} from "~/api/auth/power/powerApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import routerUtils from "~/utils/routerUtils";

type PowerTree = SysPower & {
  children?: PowerTree[];
  label?: string;
};
type PowerDetail = SysPower & {
  depCodes?: string[];
  parentCodes?: string[];
};

@Component({
  name: "role-power-power-edit",
})
export default class PowerPowerEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    code: "",
    codeParent: "",
    filterText: "",
  };
  tpowerDefaultProps = {
    children: "children",
    label: (pr: any) =>
      (pr.name ? pr.name + "(" + pr.code + ")" : pr.code) +
      (pr.httpMethod ? `[${pr.httpMethod} ${pr.url}]` : ""),
  };
  power: PowerDetail = {};
  code2power: Map<string, PowerTree> | null = null;
  powersTree: PowerTree[] = [];

  async created() {
    this.query.code = routerUtils.getQueryValue(this, "code", "");
    this.query.codeParent = routerUtils.getQueryValue(this, "code_parent", "");

    await this.reloadAllPower();
    await this.reloadNowPower();

    this.loading = false;
  }

  async reloadAllPower() {
    // 加载批量数据
    let powers: PowerTree[] = await authPowerApi.getList();
    powers.forEach((p) => (p.label = this.tpowerDefaultProps.label(p)));

    let code2power = arrayUtils.array2map(powers, "code");
    this.code2power = code2power;

    let powersTree = arrayUtils.array2tree(
      powers,
      "code",
      "parentCode",
      "children"
    );
    this.powersTree = powersTree;
  }

  async reloadNowPower() {
    // 准备当前数据
    if (this.query.code) {
      let power = await authPowerApi.get(this.query.code);
      let powerDetail: PowerDetail = { ...power };
      powerDetail.depCodes = power.depCode ? power.depCode.split(",") : [];
      powerDetail.parentCodes = arrayUtils
        .deepFindByMap(this.code2power!, "parentCode", power.parentCode)
        .reverse()
        .map((x) => x.code!);
      this.power = powerDetail;
    } else if (this.query.codeParent) {
      this.power.parentCodes = arrayUtils
        .deepFindByMap(this.code2power!, "parentCode", this.query.codeParent)
        .reverse()
        .map((x) => x.code!);
    }
  }

  async submit() {
    if (!(await this.$rule.formCheck(this, "form"))) {
      return;
    }

    this.loading = true;
    this.power.depCodes = (<any>this.$refs.ptree).getCheckedKeys();
    let modifyVo: SysPowerModifyVo = { ...this.power };
    modifyVo.depCode = this.power.depCodes
      ? arrayUtils.join(this.power.depCodes, ",")!
      : ""!;
    if (this.power.parentCodes && this.power.parentCodes.length > 0) {
      modifyVo.parentCode = this.power.parentCodes[0];
    }
    try {
      if (this.query.code) {
        // 更新
        await authPowerApi.modify(this.query.code, modifyVo);

        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let power = await authPowerApi.add(modifyVo);
        this.query.code = power.code!;

        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.code) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  filterNode(value: string, data: PowerTree) {
    if (!value) return true;
    const filter = (key: string) => {
      if (!key) {
        return true;
      }
      if (data.name && data.name.indexOf(key) != -1) {
        return true;
      }
      if (data.url && data.url.indexOf(key) != -1) {
        return true;
      }
      if (data.clsName && data.clsName.indexOf(key) != -1) {
        return true;
      }
      if (data.clsMethod && data.clsMethod.indexOf(key) != -1) {
        return true;
      }
      if (data.code && data.code.indexOf(key) != -1) {
        return true;
      }
      return false;
    };
    let vs = value.split("*");
    let ok = true;
    for (let key of vs) {
      if (!filter(key)) {
        ok = false;
        break;
      }
    }
    return ok;
  }

  async goBack() {
    this.$router.back();
  }

  @Watch("query.filterText")
  async filterTextChange() {
    (<any>this.$refs).ptree?.filter(this.query.filterText);
  }
}
</script>
