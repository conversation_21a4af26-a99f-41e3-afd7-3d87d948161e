<template lang="pug">
.page-power-rule-edit
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar {{ $t("menu." + title) }}
      el-breadcrumb-item {{ $t("pages.power.power.edit.title_add") }}
  .container
    .y-page-form.hw100
      el-form.hw100(
        ref="form",
        :model="formData",
        label-width="80px",
        v-loading="loading"
      )
        .form-body
          .body-col
            el-form-item.hw100(
              label-width="0",
              prop="owners",
              :required="true",
              :rules="[$rule.required(null, (queryPrincipal.owner ? queryPrincipal.owner.name : '') + '不能为空')]"
            )
              .body-main.hw100
                .title {{ queryPrincipal.owner ? queryPrincipal.owner.name : "" }}信息:
                .select-body
                  el-select(
                    ref="ownerSelect",
                    v-model="selectowner.select",
                    filterable,
                    remote,
                    reserve-keyword,
                    placeholder="请输入关键词",
                    :remote-method="(q) => handleSelectRemoteMethod(q, 'owner')",
                    style="width: 100%",
                    @change="handleSelectChange('owner')",
                    :loading="selectowner.loading"
                  )
                    el-option(
                      v-for="item in selectowner.list",
                      :key="item.id",
                      :label="(item.name ? item.name : '') + (item.no ? '(' + item.no + ')' : '')",
                      :value="item"
                    )
                .selected-body
                  .item(v-for="(item, idx) in formData.owners")
                    .ctx {{ (item.name ? item.name : "") + (item.no ? "(" + item.no + ")" : "") }}
                    .btn
                      el-button(
                        type="text",
                        @click="() => formData.owners.splice(idx, 1)"
                      ) 删除
          .body-col
            el-form-item.hw100(
              label-width="0",
              prop="roles",
              :required="true",
              :rules="[$rule.required(null, (queryPrincipal.role ? queryPrincipal.role.name : '') + '名称不能为空')]"
            )
              .body-main.hw100
                .title {{ queryPrincipal.role ? queryPrincipal.role.name : "" }}信息:
                .tree-body
                  el-tree(
                    :loading="loading",
                    :data="roleTreeData",
                    default-expand-all,
                    show-checkbox,
                    node-key="id",
                    :props="tpowerDefaultProps",
                    @check-change="handleRoleCheckChange"
                  )
          .body-col
            el-form-item.hw100(
              label-width="0",
              prop="subjects",
              :required="true",
              :rules="[$rule.required(null, (queryPrincipal.subject ? queryPrincipal.subject.name : '') + '名称不能为空')]"
            )
              .body-main.hw100(v-if="query.subjectModel == 'tree'")
                .title {{ queryPrincipal.subject ? queryPrincipal.subject.name : "" }}信息:
                .tree-body
                  el-tree(
                    :loading="loading",
                    :data="subjectTreeData",
                    default-expand-all,
                    show-checkbox,
                    node-key="id",
                    :props="tpowerDefaultProps",
                    @check-change="handleSubjectCheckChange"
                  )
              .body-main.hw100(v-if="query.subjectModel == 'select'")
                .title {{ queryPrincipal.subject ? queryPrincipal.subject.name : "" }}信息:
                .select-body
                  el-select(
                    ref="ownerSelect",
                    v-model="selectsubject.select",
                    filterable,
                    remote,
                    reserve-keyword,
                    placeholder="请输入关键词",
                    :remote-method="(q) => handleSelectRemoteMethod(q, 'subject')",
                    style="width: 100%",
                    @change="handleSelectChange('subject')",
                    :loading="selectsubject.loading"
                  )
                    el-option(
                      v-for="item in selectsubject.list",
                      :key="item.id",
                      :label="(item.name ? item.name : '') + (item.no ? '(' + item.no + ')' : '')",
                      :value="item"
                    )
                .selected-body
                  .item(v-for="(item, idx) in formData.subjects")
                    .ctx {{ (item.name ? item.name : "") + (item.no ? "(" + item.no + ")" : "") }}
                    .btn
                      el-button(
                        type="text",
                        @click="() => formData.subjects.splice(idx, 1)"
                      ) 删除
        .footer-button
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>
  
<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import authRoleApi, { SysRole } from "~/api/auth/power/roleApi";
import authPowerPrincipalBusinessApi, {
  AuthPowerPrincipalBusinessGetListQueryParam,
  SysPrincipalBusinessDetailBo,
} from "~/api/auth/power/principal/businessApi";
import principalRelationApi, {
  SysPrincipalRelation,
} from "~/api/auth/power/principalRelationApi";
import authPrincipalApi, {
  SysPrincipalVo,
} from "~/api/auth/power/principalApi";
import authRuelApi, { SysRuleBatchAddVo } from "~/api/auth/power/ruleApi";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";

type Code2principal = {
  [key: string]: SysPrincipalVo;
};

type QueryPrincipal = {
  role: SysPrincipalVo | null;
  owner: SysPrincipalVo | null;
  subject: SysPrincipalVo | null;
};

type QueryData = {
  ownerType: string;
  subjectType: string;
  roleType: string;
  subjectModel: "tree" | "select";
};

type FormDate = {
  roles: Array<SysRole>;
  owners: Array<SysPrincipalBusinessDetailBo>;
  subjects: Array<SysPrincipalBusinessDetailBo>;
};

type SelectData = {
  select: SysPrincipalBusinessDetailBo | SysRole | null;
  list: Array<SysPrincipalBusinessDetailBo | SysRole>;
  loading: boolean;
};

type TreeData<T> = T & {
  children?: Array<TreeData<T>>;
};

const subjectValueAll = {
  id: "ALL",
  no: "ALL",
  name: "（全部）",
  idParent: "0",
};

@Component({
  name: "role-power-rule-owner_type-subject_type-edit",
})
export default class PowerRuleEdit extends mixins(BaseVue) {
  loading = true;
  query: QueryData = {
    ownerType: "",
    subjectType: "",
    roleType: "ROLE",
    // 模式，tree、select
    subjectModel: "tree",
  };

  queryPrincipal: QueryPrincipal = {
    role: null,
    owner: null,
    subject: null,
  };

  oneLoadParam = {
    inited: false,
    code2principal: <Code2principal | null>null,
    relations: <SysPrincipalRelation[] | null>null,
    code2relation: <Map<string, SysPrincipalRelation>>new Map(),
  };

  selectowner: SelectData = {
    select: null,
    list: <Array<SysPrincipalBusinessDetailBo>>[],
    loading: false,
  };
  selectsubject: SelectData = {
    select: null,
    list: <Array<SysPrincipalBusinessDetailBo>>[],
    loading: false,
  };
  subjectTreeData: Array<TreeData<SysPrincipalBusinessDetailBo>> = [];
  roleTreeData: Array<TreeData<SysRole>> = [];

  formData: FormDate = {
    roles: [],
    owners: [],
    subjects: [],
  };

  tpowerDefaultProps = {
    children: "children",
    label: (pr: SysPrincipalBusinessDetailBo & SysRole) =>
      (pr.name ? pr.name : "") +
      (pr.code ? "(" + pr.code + ")" : "") +
      (pr.no ? "(" + pr.no + ")" : "") +
      (pr.hidden != undefined && pr.hidden != null && pr.hidden
        ? "[隐藏]"
        : ""),
  };

  get title() {
    return (
      "power-rule-" +
      compUtils.getQueryValue(this, "owner_type", "") +
      "-" +
      compUtils.getQueryValue(this, "subject_type", "")
    );
  }

  async created() {
    await this.init();
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initParam();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;

    let principals = await authPrincipalApi.getAllList();
    let code2principal: Code2principal = {};
    principals.forEach((x) => (code2principal[x.code!] = x));
    this.oneLoadParam.code2principal = code2principal;

    {
      let list = await authRoleApi.getList();
      let admin = await this.$auth.hasPower("ROLE_ADMIN");
      if (!admin) {
        list = list.filter((x) => !x.hidden);
      }
      this.roleTreeData = <any>list;
    }
  }

  async handleSubjectCheckChange(
    data: TreeData<SysPrincipalBusinessDetailBo>,
    checked: boolean,
    indeterminate: boolean
  ) {
    if (data.children && data.children.length > 0) {
      return;
    }
    let list = this.formData.subjects;
    if (checked) {
      list.push(data);
    } else {
      for (let i = 0; i < list.length; i++) {
        if (data.id == list[i].id) {
          list.splice(i, 1);
          break;
        }
      }
    }
  }

  async handleRoleCheckChange(data: TreeData<SysRole>, checked: boolean) {
    if (data.children && data.children.length > 0) {
      return;
    }
    let list = this.formData.roles;
    if (checked) {
      list.push(data);
    } else {
      for (let i = 0; i < list.length; i++) {
        if (data.code == list[i].code!) {
          list.splice(i, 1);
          break;
        }
      }
    }
  }

  async handleSelectChange(type: "owner" | "subject") {
    let selectObj: SelectData = (<any>this)[`select${type}`];
    if (!selectObj.select) {
      return;
    }
    (<Array<SysPrincipalBusinessDetailBo>>(
      (<any>this.formData)[`${type}s`]
    )).push(selectObj.select!);
    selectObj.select = null;
    selectObj.list = [];

    let ref: any = this.$refs[`${type}Select`];
    if (!ref) {
      return;
    }
    ref.focus();
  }

  async handleSelectRemoteMethod(query: string, type: "owner" | "subject") {
    let selectObj: SelectData = (<any>this)[`select${type}`];
    if (!query) {
      selectObj.list = [];
      return;
    }
    selectObj.loading = true;
    try {
      let body: AuthPowerPrincipalBusinessGetListQueryParam = {
        rows: 10,
        keyword: query,
      };
      let fileterType = (<any>this.query)[`${type}Type`];
      selectObj.list = await authPowerPrincipalBusinessApi.getList(
        fileterType,
        body
      );
      if ("subject" == type) {
        selectObj.list.push(subjectValueAll);
      }
    } finally {
      selectObj.loading = false;
    }
  }

  async initParam() {
    this.query.ownerType = compUtils.getQueryValue(this, "owner_type", "USER");
    this.query.subjectType = compUtils.getQueryValue(this, "subject_type", "");
    this.query.subjectModel = compUtils.getQueryValue(
      this,
      "subject_model",
      "tree"
    );
    if (!this.query.ownerType) {
      this.$alert("参数filterType为空，请联系管理员");
      this.$router.back();
      return;
    }
    {
      let code2principal = this.oneLoadParam.code2principal!;
      let rp = code2principal["ROLE"];
      let op = code2principal[this.query.ownerType];
      let sp = code2principal[this.query.subjectType];
      if (!rp || !op || !sp) {
        this.$alert("参数异常，请联系管理员");
        this.$router.back();
        return;
      }
      this.queryPrincipal.role = rp;
      this.queryPrincipal.owner = op;
      this.queryPrincipal.subject = sp;
    }
    {
      if (this.query.subjectModel == "tree") {
        let body: AuthPowerPrincipalBusinessGetListQueryParam = {
          rows: 2000,
        };
        let list = await authPowerPrincipalBusinessApi.getList(
          this.query.subjectType,
          body
        );
        list.push(subjectValueAll);
        this.subjectTreeData = arrayUtils.array2tree(
          list,
          "id",
          "idParent",
          "children"
        );
      } else {
        this.subjectTreeData = [];
      }
    }
  }

  async submit() {
    if (!(await this.$rule.formCheck(this, "form"))) {
      return;
    }

    this.loading = true;
    try {
      let body: SysRuleBatchAddVo = {};
      body.ownerType = this.query.ownerType;
      body.subjectType = this.query.subjectType;
      body.ownerIds = this.formData.owners.map((x) => x.id!);
      body.roleCodes = this.formData.roles.map((x) => x.code!);
      body.subjectIds = this.formData.subjects.map((x) => x.id!);
      await authRuelApi.batchAdd(body);

      this.$notify.success("添加权限成功");

      this.goBack();
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    // let url = `/power/rule/${this.query.ownerType}/${this.query.subjectType}`;
    // this.$router.push(url);
    this.$router.back();
  }
}
</script>
<style lang="scss">
.layout-content:has(.page-power-rule-edit) {
  overflow: hidden;
}
</style>
<style lang="scss" scoped>
.page-power-rule-edit {
  height: 100%;
  width: 100%;

  .container {
    height: calc(100% - 100px);
    padding: 24px 12px;

    .y-page-form {
      .el-form {
        .form-body {
          height: calc(100% - 70px);
        }

        .footer-button {
          padding: 0 20px;
          height: 40px;
          margin-top: 30px;
        }
      }
    }
  }

  .form-body {
    display: flex;
    justify-content: space-evenly;

    .body-col {
      width: 32%;
      height: 100%;

      .body-main {
        border: solid 0.007353rem #ccc;
        border-radius: 0.029412rem;

        .title {
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          font-size: 16px;
          padding: 0 8px;
          background-color: #eee;
        }
        .tree-body {
          height: calc(100% - 40px);
          overflow-y: auto;
        }
        .selected-body {
          height: calc(100% - 80px);
          overflow-y: auto;
        }
      }
    }
  }
  .form-body::v-deep {
    .el-input__inner {
      border-radius: 0;
    }
    .el-form-item.is-error .body-main {
      border: solid 1px red;

      .el-input__inner {
        border: 0.007353rem solid #dcdfe6;
      }
    }
  }

  .selected-body {
    border-top: solid 1px #ccc;

    .item {
      padding: 2px 12px;
      border-bottom: solid 1px #ccc;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      justify-content: space-between;

      font-size: 14px;
    }
  }

  .hw100 {
    width: 100%;
    height: 100%;
  }

  .el-form-item.hw100::v-deep {
    height: 100%;
    width: 100%;
    .el-form-item__content {
      height: 100%;
      width: 100%;
    }
  }
}
</style>