<template lang="pug">
.page-power-rule-index
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu." + title) }}
  el-row.rows.mgt10
    //- 左边结构
    el-col.col-win.left(:span="5")
      .container
        h2(v-if="queryPrincipal.role") {{ treeTitle }}
          span ：
          span.switch-type(title="切换用户", @click="() => handleSwitchType()")
            i.el-icon-refresh
        //- 树 tree
        template(
          v-if="query.subjectModel == 'select' && query.filterType != 'ROLE'"
        )
          el-select(
            v-model="selectleft.select",
            filterable,
            remote,
            reserve-keyword,
            placeholder="请输入关键词",
            :remote-method="(q) => handleSelectRemoteMethod(q, 'owner')",
            style="width: 100%",
            @change="handleSelectChange()",
            :loading="selectleft.loading"
          )
            el-option(
              v-for="item in selectleft.list",
              :key="item.id",
              :label="(item.name ? item.name : '') + (item.no ? '(' + item.no + ')' : '')",
              :value="item"
            )
        el-tree.mgt10.left-list-tree(
          v-loading="loading > 0",
          :data="treeData",
          default-expand-all,
          node-key="id",
          :props="tpowerDefaultProps",
          @node-click="(data) => handleTreeNodeClick(data)"
        )
          span.custom-tree-node(slot-scope="{ node, data }")
            span.tree-label
              el-tooltip(
                :content="node.label",
                placement="right",
                :open-delay="500"
              )
                div {{ node.label }}
        //- 搜索选择 select

    //- 右边列表
    el-col.col-win.right.mgl10(:span="18")
      .container
        h2(v-if="checkedNode") “{{  checkedNode ? (checkedNode.name ? checkedNode.name : "") + (checkedNode.no ? '('+checkedNode.no + ')' : "") : ""  }}” 下的授权情况：
        .y-page-table.mgt10
          //- 表头
          .y-header
            el-button.mgr10(
              type="primary",
              icon="el-icon-plus",
              @click="handleAddClick"
            ) 前去授权
            el-button.mgr10(
              type="primary",
              icon="el-icon-delete",
              @click="handleBatchDeleteClick"
            ) 全部撤销
            el-input.mgr10(
              v-model="query.keyword",
              placeholder="关键词",
              style="width: 200px",
              @keyup.enter.native="() => refreshTable()"
            )
            el-button.mgr10(
              type="primary",
              icon="el-icon-search",
              @click="() => refreshTable()"
            ) 搜索
          .y-body
            //- 表单内容
            el-table.table(
              :data="rules",
              border,
              ref="multipleTable",
              header-cell-class-name="table-header",
              v-loading="loading > 0"
            )
              el-table-column(
                prop="owner",
                :label="(queryPrincipal.owner ? queryPrincipal.owner.name : '') + '名称'",
                align="center"
              )
                template(#default="scope")
                  span(
                    v-if="oneLoadParam.ownerId2owner.get(scope.row.ownerId)"
                  ) {{ oneLoadParam.ownerId2owner.get(scope.row.ownerId).name }}({{ oneLoadParam.ownerId2owner.get(scope.row.ownerId).no }})
                  span(v-else) {{  (scope.row.ownerName ? scope.row.ownerName : "") + (scope.row.ownerNo ? "(" + scope.row.ownerNo + ")" : "") + (!scope.row.ownerName && !scope.row.ownerNo ? scope.row.ownerId : '')  }}
              el-table-column(prop="role", label="角色名称", align="center")
                template(#default="scope")
                  | {{ `${scope.row.roleName}(${scope.row.roleCode})` }}
              el-table-column(
                prop="subject",
                :label="(queryPrincipal.subject ? queryPrincipal.subject.name : '') + '名称'",
                align="center"
              )
                template(#default="scope")
                  span(
                    v-if="oneLoadParam.subjectId2subject.get(scope.row.subjectId)"
                  ) {{ oneLoadParam.subjectId2subject.get(scope.row.subjectId).name }}({{ oneLoadParam.subjectId2subject.get(scope.row.subjectId).no }})
                  span(v-else) {{  (scope.row.subjectName ? scope.row.subjectName : "") + (scope.row.subjectNo ? "(" + scope.row.subjectNo + ")" : "") + (!scope.row.subjectName && !scope.row.subjectNo ? scope.row.subjectId : '')  }}
              el-table-column(label="操作", width="230", align="center")
                template(#default="scope")
                  el-popconfirm(
                    title="是否立刻撤销该权限?",
                    :confirm-button-text="$t('common.base.confirm')",
                    :cancel-button-text="$t('common.base.cancel')",
                    @confirm="handleDelete(scope.$index, scope.row)"
                  )
                    el-button.red(slot="reference", type="text") 撤销权限
                  .y-footer
            //- 分页信息
            .pagination
              el-pagination(
                background,
                :page-sizes="[5, 10, 20, 50, 100]",
                layout="total, sizes, prev, pager, next",
                :current-page="query.page",
                :page-size="query.rows",
                :total="query.total",
                @current-change="handlePageChange",
                @size-change="handleSizeChange"
              )
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import routerUtils from "~/utils/routerUtils";
import authPrincipalApi, {
  SysPrincipalVo,
} from "~/api/auth/power/principalApi";
import authPrincipalBusinessApi, {
  AuthPowerPrincipalBusinessGetListQueryParam,
  SysPrincipalBusinessDetailBo,
} from "~/api/auth/power/principal/businessApi";
import authRuleApi, {
  AuthPowerRuleGetVoListCountQueryParam,
  AuthPowerRuleGetVoListQueryParam,
  SysRuleVo,
} from "~/api/auth/power/ruleApi";
import authRuleStaticApi, {
  AuthPowerRulePrincipalBusinessStaticGetStaticListQueryParam,
} from "~/api/auth/power/rule/principal/business/staticApi";
import stringUtils from "~/utils/stringUtils";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";

type Code2principal = {
  [key: string]: SysPrincipalVo;
};

type QueryPrincipal = {
  role: SysPrincipalVo | null;
  owner: SysPrincipalVo | null;
  subject: SysPrincipalVo | null;
};

type QueryData = {
  ownerType: string;
  subjectType: string;
  filterType: "OWNER" | "ROLE" | "SUBJECT" | "";

  subjectModel: "tree" | "select";

  ownerId: string;
  subjectId: string;
  roleCode: string;

  page: number;
  rows: number;
  total: number;
  keyword: string;
};

type TreeData<T> = T & {
  children?: Array<TreeData<T>> | null;
};

type TreeLeft = Omit<SysPrincipalBusinessDetailBo, "childrenNum"> & {
  num?: number;
};

const subjectValueAll = {
  id: "ALL",
  no: "ALL",
  name: "（全部）",
  idParent: "0",
};
const subjectId2subject: Map<String, SysRuleVo> = new Map();
subjectId2subject.set(subjectValueAll.id, subjectValueAll);

const ownerId2owner: Map<String, SysRuleVo> = new Map();

type SelectData = {
  select: SysPrincipalBusinessDetailBo | null;
  list: Array<SysPrincipalBusinessDetailBo>;
  loading: boolean;
};

@Component({
  name: "role-power-rule-owner_type-subject_type",
})
export default class PowerRuleIndex extends mixins(BaseVue) {
  loading = 0;
  query: QueryData = {
    ownerType: "",
    subjectType: "",
    filterType: "",

    ownerId: "",
    subjectId: "",
    roleCode: "",

    subjectModel: "tree",

    keyword: "",
    page: 1,
    rows: 10,
    total: 0,
  };
  oneLoadParam = {
    inited: false,
    subjectId2subject: subjectId2subject,
    ownerId2owner: ownerId2owner,
    initCount: 0,
    code2principal: <Code2principal | null>null,
  };
  queryPrincipal: QueryPrincipal = {
    role: null,
    owner: null,
    subject: null,
  };

  tpowerDefaultProps = {
    children: "children",
    label: (pr: TreeLeft) =>
      (pr.name ? pr.name : "") +
      (pr.no ? "(" + pr.no + ")" : "") +
      (pr.num != undefined && pr.num != null ? " [" + pr.num + "]" : ""),
  };

  selectleft: SelectData = {
    select: null,
    list: <Array<SysPrincipalBusinessDetailBo>>[],
    loading: false,
  };

  id2treeData: Map<string, TreeData<TreeLeft>> = new Map();
  treeData: Array<TreeData<TreeLeft>> = [];
  checkedNode: TreeLeft | null = null;
  rules: Array<SysRuleVo> = [];

  get title() {
    return (
      "power-rule-" +
      compUtils.getQueryValue(this, "owner_type", "") +
      "-" +
      compUtils.getQueryValue(this, "subject_type", "")
    );
  }

  get treeTitle() {
    return this.query.filterType == "OWNER"
      ? this.queryPrincipal.subject?.name
      : this.query.filterType == "SUBJECT"
      ? this.queryPrincipal.subject?.name
      : this.queryPrincipal.role?.name;
  }

  async created() {
    this.initQuery();
    await this.init();
  }

  async activated() {
    await this.init();
  }

  initQuery() {
    this.query.filterType = compUtils.getQueryValue(
      this,
      "filter_type",
      "SUBJECT"
    );
    this.query.ownerType = compUtils.getQueryValue(this, "owner_type", "USER");
    this.query.subjectType = compUtils.getQueryValue(this, "subject_type", "");
    this.query.subjectModel = compUtils.getQueryValue(
      this,
      "subject_model",
      "tree"
    );
    this.query.roleCode = compUtils.getQueryValue(this, "role_code", "");
    this.query.ownerId = compUtils.getQueryValue(this, "owner_id", "");
    this.query.subjectId = compUtils.getQueryValue(this, "subject_id", "");
    if (!this.query.filterType) {
      this.$alert("参数filterType为空，请联系管理员");
      this.$router.back();
      return;
    }
    this.query.page = +compUtils.getQueryValue(this, "page", "1");
    this.query.rows = +compUtils.getQueryValue(this, "rows", "10");
  }

  async init() {
    if (this.oneLoadParam.initCount > 0) {
      return;
    }
    this.loading++;
    this.oneLoadParam.initCount++;
    try {
      await this.initOneLoad();
      await this.initParam();

      if (!this.query.filterType) {
        return;
      }
      await this.initData();
    } finally {
      this.loading--;
      this.oneLoadParam.initCount--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;

    let principals = await authPrincipalApi.getAllList();
    let code2principal: Code2principal = {};
    principals.forEach((x) => (code2principal[x.code!] = x));
    this.oneLoadParam.code2principal = code2principal;
  }

  async initData() {
    let id = "";
    let type = "";
    if ("SUBJECT" == this.query.filterType) {
      id = this.query.subjectId;
      type = this.query.subjectType;
    } else if ("OWNER" == this.query.filterType) {
      id = this.query.ownerId;
      type = this.query.ownerType;
    } else if ("ROLE" == this.query.filterType) {
      id = this.query.roleCode;
      type = "ROLE";
    }
    if (!id || !type) {
      return;
    }
    let pbs: Array<SysPrincipalBusinessDetailBo> =
      await authPrincipalBusinessApi.getList(type, { id: [id] });
    if (pbs.length <= 0) {
      if (this.query.filterType == "SUBJECT") {
        if (id == subjectValueAll.id) {
          pbs = [subjectValueAll];
        }
      } else {
        return;
      }
    }
    let pd: SysPrincipalBusinessDetailBo = pbs[0];
    await this.handleTreeNodeClick(pd, true);
  }

  async initParam() {
    {
      // 初始化原始信息
      let code2principal = this.oneLoadParam.code2principal!;
      let rp = code2principal["ROLE"];
      let op = code2principal[this.query.ownerType];
      let sp = code2principal[this.query.subjectType];
      if (!rp || !op || !sp) {
        this.$alert("参数异常，请联系管理员");
        this.$router.back();
        return;
      }
      this.queryPrincipal.role = rp;
      this.queryPrincipal.owner = op;
      this.queryPrincipal.subject = sp;
    }

    {
      // 初始化左侧信息
      let filterType = this.query.filterType;
      if (!filterType) {
        return [];
      }
      let type = "";
      let tid = "";
      if ("SUBJECT" == filterType) {
        type = this.query.subjectType;
        tid = this.query.subjectId;
      } else if ("OWNER" == filterType) {
        type = this.query.ownerType;
        tid = this.query.ownerId;
      } else if ("ROLE" == filterType) {
        type = "ROLE";
        tid = this.query.roleCode;
      }

      if (
        this.query.subjectModel == "select" &&
        this.query.filterType != "ROLE"
      ) {
        let list: TreeLeft[] = [];
        if (tid) {
          let param: AuthPowerPrincipalBusinessGetListQueryParam = {};
          param.rows = 1;
          param.id = [tid];
          list = await authPrincipalBusinessApi.getList(
            this.query.subjectType,
            param
          );
        }
        if ("SUBJECT" == filterType) {
          list.push(subjectValueAll);
        }
        list = await this.putStatic(list);
        let treeData = [...list];
        let id2treeData = arrayUtils.array2map(list, "id");
        this.id2treeData = id2treeData;
        this.treeData = treeData;
      } else {
        let param: AuthPowerPrincipalBusinessGetListQueryParam = {};
        param.rows = 2000;
        let list: TreeLeft[] = await authPrincipalBusinessApi.getList(
          type,
          param
        );
        if ("SUBJECT" == filterType) {
          list.push(subjectValueAll);
        }
        list = await this.putStatic(list);
        let treeData = arrayUtils.array2tree(
          list,
          "id",
          "idParent",
          "children"
        );
        let id2treeData = arrayUtils.array2map(list, "id");
        this.id2treeData = id2treeData;
        this.treeData = treeData;
      }
    }
  }

  async putStatic(list: TreeLeft[]) {
    if (list && list.length > 0) {
      let filterType = this.query.filterType;
      let ids = list.map((x) => x.id!);
      let sparam: AuthPowerRulePrincipalBusinessStaticGetStaticListQueryParam =
        {};
      sparam.owner_type = this.query.ownerType;
      sparam.subject_type = this.query.subjectType;
      sparam.owner_id = this.query.ownerId ? [this.query.ownerId] : [];
      sparam.type = filterType;
      if ("SUBJECT" == filterType) {
        sparam.subject_id = ids;
      } else if ("OWNER" == filterType) {
        sparam.owner_id = ids;
      } else if ("ROLE" == filterType) {
        sparam.role_code = ids;
      }
      let statics = await authRuleStaticApi.getStaticList(sparam);
      list = arrayUtils.marginArray(list, "id", statics, "id");
    }
    return list;
  }

  async refreshTable() {
    await this.loadTableCount();
    await this.loadTableData();
  }

  async loadTableCount() {
    let body = stringUtils.objectKeyToSnake(this.query);
    {
      let qp = { ...body };
      delete qp.total;
      routerUtils.putQueryValue(this, qp);
    }
    let query: AuthPowerRuleGetVoListCountQueryParam = { ...body };
    this.loading++;
    try {
      this.query.total = await authRuleApi.getVoListCount(query);
      this.rules = [];
    } finally {
      this.loading--;
    }
  }

  async loadTableData() {
    if (this.query.total > 0) {
      let param: AuthPowerRuleGetVoListQueryParam = {
        ...stringUtils.objectKeyToSnake(this.query),
        sort: "",
        rows: this.query.rows,
        page: this.query.page,
      };
      param.page = param.page! - 1;
      this.loading++;
      try {
        this.rules = await authRuleApi.getVoList(param);
      } finally {
        this.loading--;
      }

      // delete param.rows;
      // delete param.page;
      param.page = this.query.page;
      delete param.sort;
      delete param.keyword;
      delete (<any>param).total;
      routerUtils.putQueryValue(this, param);
    }
  }

  async handleSwitchType() {
    this.query.filterType =
      this.query.filterType == "ROLE" ? "SUBJECT" : "ROLE";
    this.query.subjectId = "";
    this.query.roleCode = "";
    this.query.ownerId = "";
    let q = { ...this.query };
    let qc = stringUtils.objectKeyToSnake(q);
    routerUtils.putQueryValue(this, <any>qc);
    this.checkedNode = null;
    this.rules = [];
    this.oneLoadParam.inited = false;
    this.query.total = 0;
    await this.init();
  }

  async handleSelectRemoteMethod(query: string) {
    let selectObj: SelectData = this.selectleft;
    if (!query) {
      selectObj.list = [];
      return;
    }
    selectObj.loading = true;
    try {
      let body: AuthPowerPrincipalBusinessGetListQueryParam = {
        rows: 10,
        keyword: query,
      };
      let type = "";
      if ("SUBJECT" == this.query.filterType) {
        type = this.query.subjectType;
      } else if ("OWNER" == this.query.filterType) {
        type = this.query.ownerType;
      } else if ("ROLE" == this.query.filterType) {
        type = "ROLE";
      }
      let fileterType = type;
      let list = await authPrincipalBusinessApi.getList(fileterType, body);
      let rs: SysPrincipalBusinessDetailBo[] = [];
      for (let r of list) {
        if (this.id2treeData.get(r.id!)) {
          continue;
        }
        rs.push(r);
      }
      selectObj.list = rs;
    } finally {
      selectObj.loading = false;
    }
  }

  async handleSelectChange() {
    let selectObj: SelectData = this.selectleft;
    if (!selectObj.select) {
      return;
    }
    let data = selectObj.select;
    selectObj.select = null;
    selectObj.list = [];

    if (this.id2treeData.get(data.id!)) {
      return;
    }

    let list = await this.putStatic([data]);
    data = list[0];
    this.id2treeData.set(data.id!, data);
    this.treeData = [data, ...this.treeData];

    this.handleTreeNodeClick(data, false);
  }

  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadTableData();
  }
  async handlePageChange(page: number) {
    this.query.page = page;
    await this.loadTableData();
  }

  async handleTreeNodeClick(data: TreeData<TreeLeft>, init = false) {
    // if (data.children && data.children.length > 0) {
    //   return;
    // }
    this.query.keyword = "";
    this.checkedNode = data;

    let id = data.id!;
    let refreshState = false;
    if ("SUBJECT" == this.query.filterType) {
      if (this.query.subjectId != id) {
        refreshState = true;
      }
      this.query.subjectId = id;
    } else if ("OWNER" == this.query.filterType) {
      if (this.query.ownerId != id) {
        refreshState = true;
      }
      this.query.ownerId = id;
    } else if ("ROLE" == this.query.filterType) {
      if (this.query.roleCode != id) {
        refreshState = true;
      }
      this.query.roleCode = id;
    }
    if (!refreshState && !init) {
      return;
    }
    if (!init) {
      this.query.total = 0;
      this.query.page = 1;
    }
    this.loading++;
    try {
      await this.refreshTable();
    } finally {
      this.loading--;
    }
  }

  async handleAddClick() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(
      `/${role}/power/rule/${this.query.ownerType}/${this.query.subjectType}/edit?subject_model=${this.query.subjectModel}`
    );
  }

  async handleBatchDeleteClick() {
    let ok: boolean = await new Promise((r, j) => {
      this.$confirm("此操作将永久撤销该用户权限, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => r(true))
        .catch(() => r(false));
    });
    if (!ok) {
      return;
    }
    while (this.rules != null && this.rules.length > 0) {
      let rules = this.rules;
      let deletePromises = [];
      for (let i = 0; i < rules.length; i++) {
        deletePromises.push(this.handleDelete(-1, this.rules[i]));
      }
      await Promise.all(deletePromises);
      this.rules = [];

      this.query.page = 1;
      await this.loadTableData();
    }
  }

  async handleDelete(idx: number, val: SysRuleVo) {
    this.loading++;
    try {
      await authRuleApi.delete(val.id!);
      if (idx >= 0) {
        this.rules.splice(idx, 1);
      }
      this.query.total = this.query.total - 1;
      {
        let tid = "";
        if ("SUBJECT" == this.query.filterType) {
          tid = val.subjectId!;
        } else if ("OWNER" == this.query.filterType) {
          tid = val.ownerId!;
        } else if ("ROLE" == this.query.filterType) {
          tid = val.roleCode!;
        }
        if (tid) {
          let treeData = this.id2treeData.get(tid);
          if (treeData && treeData.num != undefined && treeData.num != null) {
            treeData.num = treeData.num - 1;
          }
        }
      }
    } finally {
      this.loading--;
    }
  }

  @Watch("$route.fullPath")
  async urlChange() {
    if (!this.$route.fullPath.startsWith("/power/rule")) {
      return;
    }
    if (
      compUtils.getQueryValue(this, "filter_type", "SUBJECT") !=
      this.query.filterType
    ) {
      this.query.filterType = "";
      this.initQuery();
      await this.init();
    }
  }
}
</script>
<style lang="scss" scoped>
.page-power-rule-index {
  width: 100%;

  .left-list-tree {
    overflow-x: auto;
    padding-bottom: 20px;
  }

  .switch-type {
    font-size: 13px;
    cursor: pointer;
    float: right;
    height: 100%;
    line-height: 100%;
  }

  .crumbs {
    height: 16px;
    margin: 0;
    padding: 10px;
  }

  .rows {
    width: 100%;
    height: calc(100% - 36px);

    .col-win {
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
