<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar 授权
      el-breadcrumb-item 代理切换
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="form",
        label-width="80px",
        v-loading="loading"
      )
        el-form-item(
          label="目标用户",
          prop="userId",
          required,
          :rules="[$rule.required('', '不能为空')]"
        )
          y-select(
            v-model="form.userId",
            filterable,
            remote,
            :remote-method-page="handleUserIdRemoteMethod"
          )
            el-option(
              v-for="(item, idx) in bizData.userList",
              :key="idx",
              :value="item.id",
              :label="`${item.name}(${item.username})`"
            )
        el-form-item
          el-button(type="primary", @click="submit", :loading="loading > 0") {{ $t("common.base.submit") }}
          el-button(@click="goBack()", :loading="loading > 0") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue } from "nuxt-property-decorator";
import authUserInfoApi, {
  AuthUserInfoGetListQueryParam,
  UserListVo,
} from "~/api/auth/user/infoApi";
import autAuthApi from "~/api/auth/auth/authApi";
import { RemoteMethodParam } from "~/components/y/select.vue";
import { BaseVue } from "~/model/vue";

@Component({
  name: "role-base-user-change-password",
})
export default class BaseUserChangePassword extends mixins(BaseVue) {
  loading = 0;
  form: any = {
    userId: "",
  };
  bizData = {
    userList: <UserListVo[]>[],
  };

  async handleUserIdRemoteMethod(e: RemoteMethodParam) {
    let param: AuthUserInfoGetListQueryParam = {};
    param.keyword = e.query;
    param.rows = 20;
    param.page = e.page;
    this.bizData.userList = await authUserInfoApi.getList(param);
    if (this.bizData.userList.length < param.rows) {
      e.end();
    }
  }

  async submit() {
    let that = this;
    this.loading++;
    try {
      let ok = await new Promise((r, j) => {
        (<any>that.$refs.form).validate((success: boolean) => r(success));
      });
      if (!ok) {
        return;
      }

      let result = await autAuthApi.proxySwitch2user({ id: this.form.userId });
      await this.$store.dispatch("auth/setToken", result);

      this.$notify.success("切换成功");

      this.$router.push("/");

      setTimeout(() => {
        window.location.reload();
      }, 10);
    } finally {
      this.loading--;
    }
  }

  goBack() {
    this.$router.back();
  }
}
</script>

