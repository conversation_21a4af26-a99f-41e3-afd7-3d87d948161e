<template lang="pug">
div
  client-only
    .crumbs
      el-breadcrumb(separator="/")
        el-breadcrumb-item
          i.el-icon-lx-cascades {{ $t("pages.power.role.index.title") }}
    .container
      .y-page-table
        //- 表头
        .y-header
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        .y-body
          //- 表单内容
          el-table.table(
            :data="roles",
            border,
            ref="multipleTable",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            //- el-table-column(type="selection", width="55", align="center")
            el-table-column(prop="code", label="Code", align="center")
            el-table-column(prop="name", label="名称")
            el-table-column(prop="remark", label="备注")
            el-table-column(label="操作", width="230", align="center")
              template(#default="scope")
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleEdit(scope.$index, scope.row)"
                ) {{ $t("common.base.modify") }}
                el-button(
                  type="text",
                  icon="el-icon-edit",
                  @click="handleEditPower(scope.$index, scope.row)"
                ) {{ $t("pages.power.role.index.edit_power") }}
                el-popconfirm.mgl10(
                  :title="$t('common.base.q_delete')",
                  :confirm-button-text="$t('common.base.confirm')",
                  :cancel-button-text="$t('common.base.cancel')",
                  @confirm="handleDelete(scope.$index, scope.row)"
                )
                  el-button.red(
                    slot="reference",
                    type="text",
                    icon="el-icon-delete"
                  ) {{ $t("common.base.delete") }}
</template>


<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import authRoleApi from "~/api/auth/power/roleApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-power-role",
})
export default class PowerRoleIndex extends mixins(BaseVue) {
  loading = true;
  roles = new Array();

  async activated() {
    this.loading = true;
    this.refreshTable()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        console.error(e);
        this.loading = false;
        let msg: any = this.$t("common.error.reload");
        this.$notify.error({
          title: msg,
          message: "",
        });
      });
  }

  async refreshTable() {
    let roles = await authRoleApi.getList();
    let admin = await this.$auth.hasPower("ROLE_ADMIN");
    let temps: Array<any> = [];
    roles.forEach((x) => (x.hidden && !admin ? null : temps.push(x)));
    this.roles = temps;
  }

  async handleAddClick() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}/power/role/edit`);
  }

  async handleEdit(idx: number, val: any) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}/power/role/edit?code=${val.code}`);
  }

  async handleEditPower(idx: number, val: any) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}/power/role/rule?code=${val.code}`);
  }

  async handleDelete(idx: number, val: any) {
    try {
      await authRoleApi.delete(val.code);
      let msg: any = this.$t("common.success.delete");
      this.$notify.success(msg);
    } catch (error) {
      let msg: any = this.$t("common.error.delete");
      this.$notify.error(msg);
      throw error;
    }
  }
}
</script>
