<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 角色
      el-breadcrumb-item {{ this.code ? $t("pages.power.role.edit.title_edit") : $t("pages.power.role.edit.title_add") }}
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="role",
        label-width="80px",
        v-loading="loading"
      )
        el-form-item(
          label="Code",
          prop="code",
          :required="true",
          :rules="[$rule.required(null, 'Code不能为空')]"
        )
          el-input(v-model="role.code")
        el-form-item(
          label="名称",
          prop="name",
          :required="true",
          :rules="[$rule.required(null, '名称不能为空')]"
        )
          el-input(v-model="role.name")
        el-form-item(v-if="$auth.v_hasPower('ROLE_ADMIN')", label="是否隐藏")
          el-switch(v-model="role.hidden")
        el-form-item(label="备注")
          el-input(v-model="role.remark")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue } from "nuxt-property-decorator";
import authRoleApi from "~/api/auth/power/roleApi";
import { BaseVue } from "~/model/vue";

@Component({
  name: "role-power-role-edit",
})
export default class PowerRoleEdit extends mixins(BaseVue) {
  loading = true;
  code: string = "";
  role: any = {};

  async mounted() {
    this.code = <string>this.$route.query.code;

    this.reloadNowPower()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        this.$notify.error(e.message);
        this.loading = false;
      });
  }

  async reloadNowPower() {
    // 准备当前数据
    if (this.code) {
      this.role = await authRoleApi.get(this.code);
    }
  }

  async submit() {
    if (!(await this.$rule.formCheck(this, "form"))) {
      return;
    }

    this.loading = true;
    try {
      if (this.code) {
        // 更新
        await authRoleApi.modify(this.code, this.role);

        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        this.role = await authRoleApi.add(this.role);
        this.code = this.role.code;

        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.code) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.back();
    // this.$router.push("/power/role");
  }
}
</script>

