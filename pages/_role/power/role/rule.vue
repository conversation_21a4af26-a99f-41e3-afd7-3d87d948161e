<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 角色
      el-breadcrumb-item {{ this.code ? $t("pages.power.role.edit.title_edit") : $t("pages.power.role.edit.title_add") }}
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="role",
        label-width="80px",
        width="900px",
        v-loading="loading",
        @submit.native.prevent
      )
        el-form-item(label="过滤")
          el-input(placeholder="输入关键字进行过滤", v-model="filterText")
        el-form-item(label="权限")
          el-tree(
            ref="ptree",
            :data="powersTree",
            show-checkbox,
            node-key="code",
            :default-expanded-keys="[]",
            :default-checked-keys="powerCodes",
            :props="tpowerDefaultProps",
            :filter-node-method="filterNode"
          )
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>
    
<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import authRoleApi, { SysRole } from "~/api/auth/power/roleApi";
import authRolePowerApi, { SysRolePower } from "~/api/auth/power/rolePowerApi";
import authPowerApi, { SysPower } from "~/api/auth/power/powerApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";

type TreeSysPower = SysPower & {
  children?: TreeSysPower[];
};

@Component({
  name: "role-power-role-rule",
})
export default class PowerRoleRule extends mixins(BaseVue) {
  loading = true;
  code: string = "";
  filterText: string = "";
  role: SysRole = {};
  rolePowers: SysRolePower[] = new Array();
  powerCodes: string[] = new Array();

  tpowerDefaultProps = {
    children: "children",
    label: (pr: any) =>
      (pr.name ? pr.name + "(" + pr.code + ")" : pr.code) +
      (pr.httpMethod ? `[${pr.httpMethod} ${pr.url}]` : ""),
  };
  powers: TreeSysPower[] = new Array();
  powersTree: TreeSysPower[] = new Array();

  async created() {
    this.code = <string>this.$route.query.code;

    this.reloadAllPower()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        this.$notify.error(e.message);
        this.loading = false;
      });
    this.reloadRolePower()
      .then(() => {
        this.loading = false;
      })
      .catch((e) => {
        this.$notify.error(e.message);
        this.loading = false;
      });
  }

  filterNode(value: string, data: TreeSysPower) {
    if (!value) return true;
    const filter = (key: string) => {
      if (!key) {
        return true;
      }
      if (data.name && data.name.indexOf(key) != -1) {
        return true;
      }
      if (data.url && data.url.indexOf(key) != -1) {
        return true;
      }
      if (data.clsName && data.clsName.indexOf(key) != -1) {
        return true;
      }
      if (data.clsMethod && data.clsMethod.indexOf(key) != -1) {
        return true;
      }
      if (data.code && data.code.indexOf(key) != -1) {
        return true;
      }
      return false;
    };
    let vs = value.split("*");
    let ok = true;
    for (let key of vs) {
      if (!filter(key)) {
        ok = false;
        break;
      }
    }
    return ok;
  }

  async reloadAllPower() {
    // 加载批量数据
    let prs = await authPowerApi.getList();
    // 过滤POWER_头
    let admin = await this.$auth.hasPower("ROLE_ADMIN");
    let powers: SysPower[] = new Array();
    for (let k in prs) {
      let pr = prs[k];
      if (admin || pr.code?.startsWith("POWER_")) {
        powers.push(pr);
      }
    }
    let powersTree = arrayUtils.array2tree(
      powers,
      "code",
      "parentCode",
      "children"
    );
    this.powers = powers;
    this.powersTree = powersTree;
  }

  async reloadRolePower() {
    // 准备当前数据
    if (this.code) {
      authRoleApi.get(this.code).then((data) => (this.role = data));
      this.rolePowers = await authRolePowerApi.getList({
        role_code: this.code,
      });
      this.powerCodes = this.rolePowers.map((x) => x.powerCode!);
    }
  }

  async submit() {
    this.loading = true;
    try {
      if (this.code) {
        // 更新
        let powerCodes = (<any>this.$refs.ptree).getCheckedKeys();
        let modifyParam = { roleCode: this.code, powerCodes };
        await authRolePowerApi.modifyRolePower(modifyParam);

        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
        this.goBack();
      } else {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
        return;
      }
    } catch (e) {
      if (this.code) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.back();
    // this.$router.push(`/power/role${this.admin ? "?admin=true" : ""}`);
  }

  @Watch("filterText")
  async filterTextChange() {
    (<any>this.$refs).ptree?.filter(this.filterText);
  }
}
</script>
