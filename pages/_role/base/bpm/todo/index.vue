<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 流程管理
      el-breadcrumb-item 任务列表
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        @submit.native.prevent="() => {}"
      )
        el-form-item(prop="processCode", label="业务信息")
          el-radio-group(
            v-model="query.processCode",
            @change="handleSearchClick"
          )
            el-radio-button(label="") 全部
            el-radio-button(
              v-for="item in oneLoadParam.processes",
              :key="item.code",
              :label="item.code"
            ) {{ item.name }}
        el-form-item.mgl10(prop="keyword", label="关键词")
          el-input(
            v-model="query.keyword",
            placeholder="关键词",
            @keyup.enter.native="handleSearchClick"
          )
        el-form-item.mgl10
          el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading>0"
    )
      el-table-column(prop="workflowId", label="流程id")
      el-table-column(prop="workflowName", label="任务名称")
      el-table-column(prop="recordName", label="动作")
      el-table-column(prop="recordProcessCode", label="流程")
        template(#default="scope")
          el-tag(type="success") {{ getProcesByCode(scope.row) ? getProcesByCode(scope.row).name : scope.row.recordProcessCode }}
      el-table-column(prop="recordCreateTime", label="到达时间")
        template(#default="scope")
          div {{ time2dif(scope.row) }}前

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(
            v-if="scope.row.recordTodoDetailUrl",
            type="text",
            @click="handleDetailClick(scope.row)"
          ) 去查看
          el-button(
            v-if="scope.row.recordTodoAuditUrl",
            type="text",
            @click="handleAuditClick(scope.row)"
          ) 去审核
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BpmProcessListVo } from "~/api/basic/bpm/processApi";
import contentApi, {
  BasicBpmUserWorkflowGetListCountQueryParam,
  BasicBpmUserWorkflowGetListQueryParam,
  BpmWorkflowTodoListVo,
} from "~/api/basic/bpm/user/workflowApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";
import timeUtils from "~/utils/timeUtils";

type TableContent = BpmWorkflowTodoListVo & {};

type GetListCountQueryParam = BasicBpmUserWorkflowGetListCountQueryParam;

type GetListQueryParam = BasicBpmUserWorkflowGetListQueryParam;

const urlPre = "/base/bpm/todo";

@Component({
  name: "role-base-bpm-todo",
})
export default class BaseBpmTodo extends mixins(BaseVue) {
  loading = 0;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    processCode: "",
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
    processes: <BpmProcessListVo[]>[],
    code2process: <Map<string, BpmProcessListVo> | null>null,
  };
  bizData = {
    contents: <Array<TableContent>>[],
  };

  getProcesByCode(item: TableContent): BpmProcessListVo | null {
    let code2process = this.oneLoadParam.code2process;
    if (!code2process || !item.recordProcessCode) {
      return null;
    }
    let process = code2process.get(item.recordProcessCode!);
    return process ? process : null;
  }

  time2dif(row: TableContent) {
    const m2s = (milliseconds: number) => {
      const totalSeconds = Math.floor(milliseconds / 1000);
      const days = Math.floor(totalSeconds / 86400);
      const hours = Math.floor((totalSeconds % 86400) / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      const pad = (n: number) => n.toString().padStart(2, "0");

      let s = "";
      if (days > 0) {
        s = s + `${pad(days)}天`;
      }
      if (hours > 0) {
        s = s + `${pad(hours)}时`;
      }
      if (minutes > 0) {
        s = s + `${pad(minutes)}分`;
      }
      if (seconds > 0) {
        s = s + `${pad(seconds)}秒`;
      }
      s = s ? s : "0秒";
      return s;
    };

    if (row.recordCreateTime) {
      let x = timeUtils.serverTime.getTime() - row.recordCreateTime!;
      return m2s(x);
    }
    return this.$moment(row.recordCreateTime).format("YYYY-MM-DD HH:mm:ss");
  }

  async created() {
    await this.initQuery();
  }

  async initQuery() {
    this.query.processCode = compUtils.getQueryValue(this, "process_code", "");
    this.query.keyword = compUtils.getQueryValue(this, "keyword", "");
    this.query.page = +compUtils.getQueryValue(this, "page", "1");
    this.query.rows = +compUtils.getQueryValue(this, "rows", "20");
  }

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
    let processes = await contentApi.getProcessList();
    processes = processes.filter((x) => !x.ban);
    let code2process = arrayUtils.array2map(processes, "code");
    this.oneLoadParam.code2process = code2process;
    this.oneLoadParam.processes = processes;
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async mounted() {
    await this.init();
  }

  async loadContentCount() {
    let param: GetListCountQueryParam = {};

    param.process_code = this.query.processCode;
    param.keyword = this.query.keyword;
    routerUtils.putQueryValue(this, param);

    this.loading++;
    try {
      this.query.total = await contentApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async loadContent() {
    let param: GetListQueryParam = {};
    param.process_code = this.query.processCode;
    param.keyword = this.query.keyword;
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, param);

    param.page = param.page - 1;

    this.loading++;
    try {
      let contents: TableContent[] = await contentApi.getList(param);
      this.bizData.contents = contents;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  // 展开列表详情时触发

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }

  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleDetailClick(item: TableContent) {
    this.$router.push(item.recordTodoDetailUrl!);
  }

  async handleAuditClick(item: TableContent) {
    this.$router.push(item.recordTodoAuditUrl!);
  }

  async goBack() {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${urlPre}`);
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

