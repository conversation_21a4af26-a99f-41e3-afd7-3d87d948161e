<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-calendar 账户
      el-breadcrumb-item 修改密码
  .container
    .y-page-form
      el-form(
        ref="form",
        :model="form",
        label-width="80px",
        :rules="rules",
        v-loading="loading"
      )
        el-form-item(label="原始密码", prop="oldPassword")
          el-input(
            v-model="form.oldPassword",
            type="password",
            autocomplete="off"
          )
        el-form-item(label="新密码", prop="password", required)
          el-input(
            v-model="form.password",
            type="password",
            autocomplete="off"
          )
        el-form-item(
          label="确认密码",
          prop="confirmPassword",
          required,
          @keyup.enter.native="submit"
        )
          el-input(
            v-model="form.confirmPassword",
            type="password",
            autocomplete="off"
          )
        el-form-item
          el-button(type="primary", @click="submit") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, Vue } from "nuxt-property-decorator";
import infoApi, {
  UserInfoChangePasswordMeParamVo,
} from "~/api/auth/user/infoApi";

@Component({
  name: "role-base-user-change-password",
})
export default class BaseUserChangePassword extends Vue {
  loading = false;
  form: any = {
    oldPassword: "",
    password: "",
    confirmPassword: "",
  };
  rules = {
    password: [
      {
        trigger: "blur",
        validator: (rule: any, value: any, cb: any) => {
          if (!value) {
            cb(new Error("新密码不能为空"));
          } else if (value.length < 4) {
            cb(new Error("密码不能小于4位"));
          } else if (this.form.oldPassword == this.form.password) {
            cb(new Error("新密码不能与原密码一致"));
          } else {
            cb();
          }
        },
      },
    ],
    confirmPassword: [
      {
        trigger: "blur",
        validator: (rule: any, value: any, cb: any) => {
          if (!value) {
            cb(new Error("确认密码不能为空"));
          } else if (this.form.password != this.form.confirmPassword) {
            cb(new Error("两次输入的密码不一致"));
          } else {
            cb();
          }
        },
      },
    ],
  };

  async submit() {
    let that = this;
    this.loading = true;
    try {
      let ok = await new Promise((r, j) => {
        (<any>that.$refs.form).validate((success: boolean) => r(success));
      });
      if (!ok) {
        return;
      }
      let body: UserInfoChangePasswordMeParamVo = { ...this.form };
      await infoApi.changePasswordByMe(body);
      this.$notify.success("修改密码成功");
      this.goBack();
    } finally {
      this.loading = false;
    }
  }

  goBack() {
    this.$router.back();
  }
}
</script>

