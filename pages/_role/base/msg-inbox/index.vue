<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        .el-icon-lx-copy {{ $t("pages.msg-inbox.title") }}
  .container
    el-tabs(v-model="message", @tab-click="handleTabClick")
      el-tab-pane(
        :label="$t('pages.msg-inbox.unread_msg_label', { count: unreadCount })",
        name="unread"
      )
      el-tab-pane(
        :label="$t('pages.msg-inbox.read_msg_label', { count: readCount })",
        name="read"
      )
      el-tab-pane(
        :label="$t('pages.msg-inbox.delete_msg_label', { count: deletedCount })",
        name="deleted"
      )
      //- 未读表单
      el-table(
        :data="inboxs",
        :show-header="false",
        style="width: 100%",
        v-loading="loading"
      )
        el-table-column
          template(#default="scope")
            span.message-title {{ scope.row.title }}
        el-table-column(width="180")
          template(#default="scope")
            span {{ dateFormate(scope.row.createTime) }}
        el-table-column(width="120")
          template(#default="scope")
            template(v-if="message == 'unread'")
              el-button(
                :loading="scope.row.loading",
                size="small",
                @click="handleRead(scope.$index, scope.row)"
              ) {{ $t("pages.msg-inbox.mark_read") }}
            template(v-if="message == 'read'")
              el-button(
                :loading="scope.row.loading",
                type="danger",
                @click="handleDel(scope.$index, scope.row)"
              ) {{ $t("pages.msg-inbox.delete") }}
            template(v-if="message == 'deleted'")
              el-button(
                :loading="scope.row.loading",
                @click="handleRestore(scope.$index, scope.row)"
              ) {{ $t("pages.msg-inbox.reduction") }}
      .handle-row
        div
          template(v-if="message == 'unread'")
            el-button(type="primary", @click="handlePutAllRead") {{ $t("pages.msg-inbox.mark_read_all") }}
          template(v-if="message == 'read'")
            el-button(type="danger", @click="handleDeleteAllRead") {{ $t("pages.msg-inbox.delete_all") }}
          template(v-if="message == 'deleted'")
            el-button(type="danger", @click="handleClearAllDeleted") {{ $t("pages.msg-inbox.clear_all") }}
        div
          el-pagination(
            :total="page.total",
            :page-size="page.size",
            :current-page="page.cpage",
            :page-sizes="[2, 10, 20, 50, 100, 200, 400, 500]",
            layout="total, sizes, prev, pager, next, jumper",
            @size-change="handlePageSizeChange",
            @current-change="handlePageCurrentChange"
          )
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import sysMsgInboxApi, {
  MsgInbox as MsgInboxVo,
  SysMsgInboxGetListByMeQueryParam,
} from "~/api/sys/msgInboxApi";
import moment from "moment";
import urlUtils from "~/utils/urlUtils";
import { BaseVue } from "~/model/vue";

type TableItemVo = MsgInboxVo & {
  loading?: boolean;
};

@Component({
  name: "role-base-msg-inbox",
})
export default class BaseMsgInbox extends mixins(BaseVue) {
  message = "unread";
  loading = true;

  unreadCount = 0;
  readCount = 0;
  deletedCount = 0;

  page = { total: 1, size: 20, cpage: 1 };
  inboxs: Array<TableItemVo> = [];

  dateFormate(date: any) {
    if (!date) {
      return "";
    }
    return moment(date).format("yyyy-MM-DD HH:mm:ss");
  }

  async created() {
    let query = urlUtils.url2query(this.$route.fullPath);
    if (query.type) {
      this.message = query.type;
    }
  }

  async mounted() {
    this.loading = true;
    try {
      await this.refreshMsg();
    } finally {
      this.loading = false;
    }
  }

  async handleTabClick(tab: any, event: any) {
    this.loading = true;
    try {
      await this.refreshTable();
      this.$router.replace(
        urlUtils.putParam(this.$route.fullPath, { type: this.message })
      );
    } finally {
      this.loading = false;
    }
  }

  async refreshMsg() {
    await Promise.all([
      (async () => {
        this.unreadCount = await sysMsgInboxApi.getListCountByMe({
          state: "NEW",
        });
        this.$store.commit("msg/setUnreadCount", this.unreadCount);
        if (this.message == "unread") {
          this.page.total = this.unreadCount;
        }
      })(),
      (async () => {
        this.readCount = await sysMsgInboxApi.getListCountByMe({
          state: "READED",
        });
        if (this.message == "read") {
          this.page.total = this.readCount;
        }
      })(),
      (async () => {
        this.deletedCount = await sysMsgInboxApi.getListCountByMe({
          state: "DEL",
        });
        if (this.message == "deleted") {
          this.page.total = this.deletedCount;
        }
      })(),
      this.refreshTable(),
    ]);
  }

  async refreshTable() {
    let param: SysMsgInboxGetListByMeQueryParam = {};
    if (this.message == "unread") {
      param.state = "NEW";
    } else if (this.message == "read") {
      param.state = "READED";
    } else if (this.message == "deleted") {
      param.state = "DEL";
    }
    param.page = this.page.cpage - 1;
    param.rows = this.page.size;
    let inboxs: TableItemVo[] = await sysMsgInboxApi.getListByMe(param);
    for (let idx in inboxs) {
      let inbox = inboxs[idx];
      inbox.loading = false;
    }
    this.inboxs = inboxs;
  }

  async handlePageSizeChange(size: number) {
    this.page.cpage =
      Math.floor((this.page.size * (this.page.cpage - 1)) / size) + 1;
    this.page.size = size;

    this.loading = true;
    try {
      await this.refreshTable();
    } finally {
      this.loading = false;
    }
  }

  async handlePageCurrentChange(cpage: number) {
    this.page.cpage = cpage;

    this.loading = true;
    try {
      await this.refreshTable();
    } finally {
      this.loading = false;
    }
  }

  async handleRead(index: number, row: MsgInboxVo) {
    (<any>row).loading = true;
    try {
      await sysMsgInboxApi.putNotifyByMe(row.id!, { notify: true });
      await this.refreshTable();
      this.unreadCount--;
      this.readCount++;
      this.$store.commit("msg/setUnreadCount", this.unreadCount);
    } finally {
      (<any>row).loading = false;
    }
  }

  async handleUnread(index: number, row: MsgInboxVo) {
    (<any>row).loading = true;
    try {
      await sysMsgInboxApi.putNotifyByMe(row.id!, { notify: false });
      await this.refreshTable();
      this.readCount--;
      this.unreadCount++;
      this.$store.commit("msg/setUnreadCount", this.unreadCount);
    } finally {
      (<any>row).loading = false;
    }
  }

  async handleDel(index: number, row: MsgInboxVo) {
    (<any>row).loading = true;
    try {
      await sysMsgInboxApi.putDelByMe(row.id!, { del: true });
      await this.refreshTable();
      this.readCount--;
      this.deletedCount++;
    } finally {
      (<any>row).loading = false;
    }
  }

  async handleRestore(index: number, row: MsgInboxVo) {
    (<any>row).loading = true;
    try {
      await sysMsgInboxApi.putDelByMe(row.id!, { del: false });
      await this.refreshTable();
      this.deletedCount--;
      this.readCount++;
    } finally {
      (<any>row).loading = false;
    }
  }

  async handlePutAllRead() {
    this.loading = true;
    try {
      await sysMsgInboxApi.putAllNotifyByMe({ notify: true });
      await this.refreshMsg();
    } finally {
      this.loading = false;
    }
  }

  async handleDeleteAllRead() {
    this.loading = true;
    try {
      await sysMsgInboxApi.putAllDelByMe({ del: true });
      await this.refreshMsg();
    } finally {
      this.loading = false;
    }
  }

  async handleClearAllDeleted() {
    this.loading = true;
    try {
      await sysMsgInboxApi.deleteAllByMe({ state: "DEL" });
      await this.refreshMsg();
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style>
.message-title {
  cursor: pointer;
}
.handle-row {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

