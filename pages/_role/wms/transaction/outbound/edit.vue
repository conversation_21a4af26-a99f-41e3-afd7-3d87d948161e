<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 出库单
      el-breadcrumb-item {{ this.query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="100px",
        size="small",
        @submit.native.prevent="() => {}"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(
            v-model="formData.name",
            :disabled="!query.edit || query.audit"
          )
        //- el-form-item(label="寄存时间")
        //-   el-date-picker(
        //-     v-model="formData.time",
        //-     type="daterange",
        //-     range-separator="至",
        //-     start-placeholder="开始日期",
        //-     end-placeholder="结束日期",
        //-     placeholder="选择日期时间"
        //-   )
        //- el-form-item(
        //-   label="有效期",
        //-   prop="name",
        //-   :rules="[$rule.required(null, '名称不能为空')]",
        //-   required
        //- )
        //-   el-input(v-model="formData.name", :disabled="!query.edit")
        //- el-form-item(
        //-   label="批次号",
        //-   prop="name",
        //-   :rules="[$rule.required(null, '名称不能为空')]",
        //-   required
        //- )
        //-   el-input(v-model="formData.name", :disabled="!query.edit")
        //- el-form-item(
        //-   label="寄存仓库",
        //-   prop="warehouseId",
        //-   :rules="[$rule.required(null, '寄存仓库不能为空')]",
        //-   required
        //- )
        //-   el-select(v-model="formData.warehouseId", @change="warehouseChange")
        //-     el-option(
        //-       v-for="option in oneLoadParam.warehouses",
        //-       :key="option.id",
        //-       :label="option.name",
        //-       :value="option.id"
        //-     )
        el-form-item(
          label="商品列表：",
          prop="products",
          :rules="[$rule.required(null, '商品不能为空')]",
          required
        )
          el-button.add-product-btn(
            v-if="query.edit && !query.audit",
            type="text",
            @click="handleOpenSelectProductClick"
          ) 选择出库商品
          el-table.table(
            :data="formData.products",
            border,
            ref="multipleTable",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(prop="name", label="名称(sku)")
              template(#default="scope")
                div {{ $fieldUtils.getFieldValue(scope.row, "pd.name", scope.row.productSpaceId) }} ({{ $fieldUtils.getFieldValue(scope.row, "pd.sku", scope.row.productSpaceId) }})
            el-table-column(label="批次号", prop="box.lotNum", width="200")
            el-table-column(
              label="数量",
              prop="num",
              :render-header="addRedStar",
              width="200"
            )
              template(#default="scope")
                el-form-item(
                  label-width="0",
                  :prop="`products.${scope.$index}.num`",
                  :rules="[$rule.required(null, '数量不能为空')]"
                )
                  el-input-number(
                    :step="1",
                    :min="1",
                    :max="scope.row.box ? scope.row.box.numStorage - scope.row.box.numLock : 1",
                    v-model="scope.row.num",
                    :disabled="!query.edit || query.audit"
                  )
                  span.mgl10(v-if="scope.row.box") / {{ scope.row.box ? scope.row.box.numStorage - scope.row.box.numLock : 1 }}
            el-table-column(prop="remark", label="备注")
              template(#default="scope")
                el-input(
                  v-model="scope.row.remark",
                  type="textarea",
                  :rows="2",
                  :disabled="!query.edit || query.audit"
                )
            el-table-column(
              label="操作",
              width="250",
              align="center",
              v-if="query.edit && !query.audit"
            )
              template(#default="scope")
                el-popconfirm(
                  title="确认要删除数据吗？",
                  @confirm="handleProductRemoveClick(scope.row, scope.$index)"
                )
                  el-button(
                    slot="reference",
                    type="text",
                    icon="el-icon-tickets"
                  ) 移除

        el-form-item(label="备注", prop="remark")
          el-input(
            v-model="formData.remark",
            :disabled="!query.edit || query.audit",
            type="textarea",
            :rows="2"
          )
        el-form-item(
          v-if="!query.edit && !query.audit",
          label="审核流程：",
          prop="audit"
        )
          ybiz-audittimeline(:value="formData.bpmWorkflowRecords")
        el-form-item(v-if="!query.audit")
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
        AuditBtn(v-if="query.audit", :id="query.id", @goBack="goBack")

      //- 商品编辑界面
      el-dialog(
        :visible="bizData.selectProduct.visible",
        title="选择商品",
        :before-close="() => (bizData.selectProduct.visible = false)",
        width="80%"
      )
        wms-transaction-comps-select-productbox-window(
          :hasIds="hasProductBoxIds",
          @add="handleSelectProductAdd"
        )
        template(#footer)
          //- el-button(type="primary") 添加
          el-button(@click="() => (bizData.selectProduct.visible = false)") 取消
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import contentApi, {
  WmsTransactionInfoBo,
  WmsTransactionProduct,
} from "~/api/wms/mg/transactionApi";
import outboundApi, {
  WmsTransactionOutboundAddProductVo,
  WmsTransactionOutboundAddVo,
} from "~/api/wms/mg/transaction/outboundApi";
import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import productBoxApi, { WmsProductBox } from "@/api/wms/mg/product/boxApi";

import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import WmsTransactionCompsSelectProductboxWindow from "../comps/select-productbox-window.vue";
import arrayUtils from "~/utils/arrayUtils";
import AuditBtn from "../comps/audit-btn.vue";
import YbizAudittimeline from "~/components/ybiz/audittimeline.vue";

type FormData = Omit<WmsTransactionOutboundAddVo, "products"> & {
  products?: Product[];
};

type Product = WmsTransactionOutboundAddProductVo & {
  pd?: WmsProductSpace;
  box?: WmsProductBox;
};

const urlPre = "/wms/transaction";

@Component({
  name: "role-wms-transaction-outbound-edit",
  components: {
    WmsTransactionCompsSelectProductboxWindow,
    AuditBtn,
    YbizAudittimeline,
  },
})
export default class WmsTransactionOutboundEdit extends mixins(BaseVue) {
  loading = true;
  selectProductVisible = false;
  query = {
    id: "",
    cpid: "",
    edit: true,
    audit: false,
  };
  formData: FormData = {
    name: "",
    products: [],
    remark: "",
    reasonType: "NORMAL",
  };
  bizData = {
    selectProduct: {
      visible: false,
    },
  };

  oneLoadParam = {
    inited: false,
    warehouses: <WmsWarehouse[]>[],
    // ...param
  };

  get hasProductBoxIds() {
    return this.formData.products!.map((x) => x.productBoxId!);
  }

  async created() {
    this.init();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    {
      let param: WmsMgWarehouseGetAllListQueryParam = {};
      param.state = ["OK"];
      let warehouses = await warehouseApi.getAllList(param);
      this.oneLoadParam.warehouses = warehouses;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.cpid = compUtils.getQueryValue(this, "cpid", "");
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.audit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "audit", "false")
    );
    if (this.query.audit) {
      this.query.edit = false;
    } else {
      this.query.edit = commonFunUtils.parseBoolean(
        compUtils.getQueryValue(this, "edit", "true")
      );
    }
    if (this.query.cpid || this.query.id) {
      let content: WmsTransactionInfoBo | null = null;
      if (this.query.cpid) {
        content = await contentApi.get(this.query.cpid);
      } else {
        content = await contentApi.get(this.query.id);
      }
      let nfd: FormData = { ...content };
      nfd = nfd!;

      let pbids = content.products!.map((x) => x.productBoxId!);
      let pdids = content.products!.map((x) => x.productSpaceId!);

      let [pdBoxs, pds] = await Promise.all([
        productBoxApi.getList({ id: pbids }),
        productSpaceApi.getList({ id: pdids }),
      ]);

      // let pdBoxs = await productBoxApi.getList({ id: pbids });
      // let pds = await productSpaceApi.getList({ id: pdids });
      let id2pb = arrayUtils.array2map(pdBoxs, "id");
      let id2pd = arrayUtils.array2map(pds, "id");

      for (let pd of nfd.products!) {
        let pdbox = id2pb.get(pd.productBoxId!)!;
        let p = id2pd.get(pdbox.productSpaceId!);
        pd.pd = p;
        pd.box = pdbox;
      }

      this.formData = nfd;
    }
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: FormData = { ...data };
      // 新增
      let detail = await outboundApi.add(body);
      this.query.id = detail.id!;
      let msg: any = this.$t("common.success.save");
      this.$notify.success(msg);

      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async handleProductRemoveClick(pd: Product, idx: number) {
    this.formData.products!.splice(idx, 1);
  }

  async goBack() {
    // let url = `/${compUtils.getQueryValue(this, "role", "")}${urlPre}`;
    // compUtils.toPage(this, "back", url, {});
    this.$router.back();
  }

  handleOpenSelectProductClick() {
    this.bizData.selectProduct.visible = true;
  }

  async handleSelectProductAdd(
    box: WmsProductBox & {
      pd?: WmsProductSpace;
    }
  ) {
    if (!box) {
      return;
    }
    this.formData.products!.push({
      pd: box.pd,
      box,
      productBoxId: box.id,
      num: 1,
      remark: "",
    });
    this.bizData.selectProduct.visible = false;
  }
}
</script>

<style lang="scss" scoped>
.add-product-btn {
  font-size: 14px;
}
</style>
