<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 移库单
      el-breadcrumb-item {{ this.query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="100px",
        size="small",
        @submit.native.prevent="() => {}"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(
            v-model="formData.name",
            :disabled="!query.edit || query.audit"
          )

        //- 步骤条
        .product-win
          el-tabs(
            v-model="bizData.tabAction",
            v-if="!query.edit",
            @tab-click="handleTabsClick"
          )
            el-tab-pane(label="准备移动的商品", name="1")
            el-tab-pane(label="目标位置设置", name="2")
          el-row(v-else)
            el-col(:span="20", :offset="2")
              el-steps(:active="bizData.stepAction")
                el-step(title="准备移动的商品")
                el-step(title="目标位置设置")
                el-step(title="完成")

          .product-content
            template(
              v-if="(query.edit && bizData.stepAction == 1) || (!query.edit && bizData.tabAction == '1')"
            )
              el-form-item(
                label-width="0",
                prop="outboxs",
                :rules="[$rule.required(null, '商品不能为空')]",
                required
              )
                el-table.table(
                  :data="formData.outboxs",
                  border,
                  header-cell-class-name="table-header",
                  v-loading="loading"
                )
                  el-table-column(prop="name", label="名称(sku)")
                    template(#default="scope")
                      div {{ $fieldUtils.getFieldValue(scope.row, "pd.name", scope.row.productBoxId) }} ({{ $fieldUtils.getFieldValue(scope.row, "pd.sku", scope.row.productBoxId) }})
                  el-table-column(label="批次号", prop="box.lotNum", width="200")
                  el-table-column(prop="containerId", label="存储位置")
                    template(#default="scope")
                      p {{ getContainerById(scope.row) }}
                  el-table-column(
                    label="数量",
                    prop="num",
                    :render-header="addRedStar",
                    width="250"
                  )
                    template(#default="scope")
                      el-form-item(
                        label-width="0",
                        :prop="`outboxs.${scope.$index}.num`",
                        :rules="[$rule.required(null, '数量不能为空')]"
                      )
                        el-input-number(
                          :step="1",
                          :min="1",
                          :max="scope.row.wbox ? scope.row.wbox.numStorage - scope.row.wbox.numLock : 1",
                          v-model="scope.row.num",
                          :disabled="!query.edit"
                        )
                        span.mgl10(v-if="scope.row.wbox") / {{ scope.row.wbox ? scope.row.wbox.numStorage - scope.row.wbox.numLock : 1 }}
                  el-table-column(prop="remark", label="备注")
                    template(#default="scope")
                      el-input(
                        v-model="scope.row.remark",
                        type="textarea",
                        :rows="2",
                        :disabled="!query.edit"
                      )
                  el-table-column(
                    label="操作",
                    width="250",
                    align="center",
                    v-if="query.edit"
                  )
                    template(#default="scope")
                      el-popconfirm(
                        title="确认要删除数据吗？",
                        @confirm="handleWboxRemoveClick('outboxs', scope.row, scope.$index)"
                      )
                        el-button(
                          slot="reference",
                          type="text",
                          icon="el-icon-tickets"
                        ) 移除
                el-button.add-product-btn(
                  v-if="query.edit && !query.audit",
                  @click="handleOpenSelectWarehouseClick"
                ) 选择出库商品

            template(
              v-if="(query.edit && bizData.stepAction == 2) || (!query.edit && bizData.tabAction == '2')"
            )
              el-form-item(
                label-width="0",
                prop="products",
                :rules="[]",
                required
              )
                el-collapse(v-model="bizData.productAction")
                  el-collapse-item.product-box-win(
                    v-for="(pd, idx) in formData.products",
                    :key="pd.productBoxId",
                    :name="pd.productBoxId"
                  )
                    template(slot="title")
                      .box-title
                        span.mgr10 商品名称：{{ pd.pd.name }}
                        span.mgr10 商品SKU：{{ pd.pd.sku }}
                        span.mgr10 批次号：{{ pd.box.lotNum }}
                        span.mgr10 数量：{{ pd.num }}
                    template(#default)
                      el-form-item(
                        width="0",
                        :prop="`products.${idx}.boxs`",
                        :rules="[$rule.required(null, '位置不能为空'), $rule.valid(() => validPdNum(pd))]"
                      )
                        el-table.table(
                          :data="pd.boxs",
                          border,
                          header-cell-class-name="table-header",
                          v-loading="loading"
                        )
                          el-table-column(
                            prop="warehouseContainerId",
                            label="存放存放货架"
                          )
                            template(#default="scope")
                              el-form-item(
                                width="0",
                                :prop="`products.${idx}.boxs.${scope.$index}.warehouseContainerIds`",
                                :rules="[$rule.required(null, '位置不能为空'), $rule.valid(() => validWarehouseContainer(pd, scope.row))]"
                              )
                                el-cascader(
                                  v-model="scope.row.warehouseContainerIds",
                                  :options="pd.containerTree",
                                  :props="{ checkStrictly: false, label: 'name', value: 'id' }",
                                  clearable,
                                  :disabled="!query.edit || query.audit",
                                  style="width: 100%",
                                  @change="() => { refreshStorageNumFree(); checkForm(); }"
                                )
                                  template(slot-scope="{ node, data }")
                                    span {{ data.name }}
                                    span(v-if="data.storage") ({{ data.numFree }})
                          el-table-column(label="数量", prop="num", width="250")
                            template(#default="scope")
                              el-input-number(
                                :step="1",
                                :min="1",
                                :max="stoageFreeNum(scope.row) != null ? Math.min(stoageFreeNum(scope.row) + scope.row.num, pd.num) : pd.num",
                                v-model="scope.row.num",
                                :disabled="!query.edit",
                                @change="() => refreshStorageNumFree()"
                              )
                              span.mgl10(
                                v-if="bizData.id2containerStorage && scope.row.warehouseContainerIds && scope.row.warehouseContainerIds.length > 0"
                              ) / {{ stoageFreeNum(scope.row) }}
                          el-table-column(prop="remark", label="备注")
                            template(#default="scope")
                              el-input(
                                v-model="scope.row.remark",
                                type="textarea",
                                :rows="2",
                                :disabled="!query.edit"
                              )
                          el-table-column(
                            label="操作",
                            width="100",
                            align="center",
                            v-if="query.edit"
                          )
                            template(#default="boxscope")
                              el-popconfirm(
                                title="确认要删除数据吗？",
                                @confirm="handleBoxRemoveClick(pd, boxscope.$index)"
                              )
                                el-button(
                                  slot="reference",
                                  type="text",
                                  icon="el-icon-tickets"
                                ) 移除
                        el-button(
                          v-if="query.edit",
                          type="text",
                          @click="handleAddBoxClick(pd)"
                        ) 添加位置

        .mgt10
        el-form-item(label="备注", prop="remark")
          el-input(
            v-model="formData.remark",
            :disabled="!query.edit",
            type="textarea",
            :rows="2"
          )
        el-form-item(
          v-if="!query.edit && !query.audit",
          label="审核流程：",
          prop="audit"
        )
          ybiz-audittimeline(:value="formData.bpmWorkflowRecords")
        el-form-item
          template
            el-button(
              v-if="bizData.stepAction == 1 && query.edit",
              type="primary",
              @click="handleNextClick()"
            ) 下一步
            el-button(
              v-if="bizData.stepAction == 1 && !query.audit",
              @click="goBack()"
            ) 取消
            el-button(
              v-if="bizData.stepAction == 2 && query.edit",
              type="primary",
              @click="submit()"
            ) {{ $t("common.base.submit") }}
            el-button(
              v-if="bizData.stepAction == 2 && query.edit",
              @click="handleLastClick()"
            ) 上一步
        AuditBtn(v-if="query.audit", :id="query.id", @goBack="goBack")

      //- 商品编辑界面
      el-dialog(
        :visible="bizData.selectWarehouseBox.visible",
        title="选择商品",
        :before-close="() => (bizData.selectWarehouseBox.visible = false)",
        width="80%"
      )
        wms-transaction-comps-select-warehousebox-window(
          :hasIds="hasWarehouseBoxIds",
          @add="handleSelectWarehouseBoxAdd"
        )
        template(#footer)
          //- el-button(type="primary") 添加
          el-button(
            @click="() => (bizData.selectWarehouseBox.visible = false)"
          ) 取消
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import contentApi, {
  WmsTransactionInfoBo,
  WmsTransactionProduct,
} from "~/api/wms/mg/transactionApi";
import outboundApi, {
  WmsTransactionOutboundAddProductVo,
  WmsTransactionOutboundAddVo,
} from "~/api/wms/mg/transaction/outboundApi";
import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import productBoxApi, { WmsProductBox } from "@/api/wms/mg/product/boxApi";

import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import WmsTransactionCompsSelectWarehouseboxWindow from "../comps/select-warehousebox-window.vue";
import arrayUtils from "~/utils/arrayUtils";
import warehouseBoxApi, {
  WmsWarehouseBox,
} from "~/api/wms/mg/warehouse/boxApi";
import moveApi, {
  WmsTransactionMoveAddVo,
  WmsTransactionMoveBoxVo,
} from "~/api/wms/mg/transaction/moveApi";
import containerApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
} from "~/api/wms/mg/warehouse/containerApi";
import AuditBtn from "../comps/audit-btn.vue";
import YbizAudittimeline from "~/components/ybiz/audittimeline.vue";

type FormData = Omit<WmsTransactionMoveAddVo, "products"> & {
  outboxs?: BoxType[];
  products?: Product[];
};

type BoxType = WmsTransactionMoveBoxVo & {
  id?: string;
  wbox?: WmsWarehouseBox;
  pd?: WmsProductSpace;
  box?: WmsProductBox;

  warehouseContainerIds?: string[];
};

type Product = {
  productBoxId?: string;
  num?: number;
  pd?: WmsProductSpace;
  box?: WmsProductBox;
  boxs?: BoxType[];

  containerTree?: ContainerTree[];
};

type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];

    numFree?: number;
  };

const urlPre = "/wms/transaction";

@Component({
  name: "role-wms-transaction-move-edit",
  components: {
    WmsTransactionCompsSelectWarehouseboxWindow,
    AuditBtn,
    YbizAudittimeline,
  },
})
export default class WmsTransactionMoveEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    cpid: "",
    edit: false,
    audit: false,
  };
  formData: FormData = {
    name: "",
    outboxs: [],
    products: [],
    remark: "",
    reasonType: "NORMAL",
  };
  bizData = {
    stepAction: 1,
    tabAction: "1",
    selectWarehouseBox: {
      visible: false,
    },

    productAction: <string[]>[],
    id2containerStorage: <Map<string, ContainerTree> | null>null,
  };

  oneLoadParam = {
    inited: false,
    // ...param

    id2container: <Map<string, ContainerTree> | null>null,
    containers: <ContainerTree[]>[],
  };

  get hasWarehouseBoxIds() {
    return this.formData.outboxs!.map((x) => x.id!);
  }

  async created() {
    this.init();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  getContainerById(wbox: BoxType) {
    if (!this.oneLoadParam.id2container) {
      return "";
    }
    let cs = arrayUtils.deepFindByMap(
      this.oneLoadParam.id2container,
      "idParent",
      wbox.wbox!.containerId!
    );
    return cs
      .reverse()
      .map((x) => x.name)
      .join(" > ");
  }

  async refreshStorageNumFree() {
    if (!this.bizData.id2containerStorage) {
      return;
    }
    for (const [k, storage] of this.bizData.id2containerStorage) {
      storage.numFree = storage.numMax! - storage.numUsed! - storage.numLock!;
    }
    if (this.formData.products) {
      for (let pd of this.formData.products) {
        if (pd.boxs) {
          for (let box of pd.boxs) {
            if (
              box.warehouseContainerIds &&
              box.warehouseContainerIds.length > 0
            ) {
              let wcid =
                box.warehouseContainerIds[box.warehouseContainerIds.length - 1];
              let storage = this.bizData.id2containerStorage.get(wcid)!;
              storage.numFree = storage.numFree! - box.num!;
            }
          }
        }
      }
    }
  }

  async checkForm() {
    this.$rule.formCheck(this, "form");
  }

  validWarehouseContainer(pd: Product, box: BoxType) {
    if (
      !box ||
      !box.warehouseContainerIds ||
      box.warehouseContainerIds.length <= 0
    ) {
      return null;
    }
    let id = box.warehouseContainerIds[box.warehouseContainerIds.length - 1];
    let count = 0;
    for (let b of pd.boxs ?? []) {
      if (b.warehouseContainerIds && b.warehouseContainerIds.length > 0) {
        let nid = b.warehouseContainerIds[b.warehouseContainerIds.length - 1];
        if (nid == id) {
          count++;
          if (count >= 2) {
            return "入库位置信息重复。";
          }
        }
      }
    }
    return null;
  }

  validPdNum(pd: Product) {
    if (!pd || !pd.boxs || pd.boxs.length <= 0) {
      return null;
    }
    let scount = 0;
    for (let box of pd.boxs) {
      scount = scount + box.num!;
    }
    if (pd.num == scount) {
      return null;
    } else {
      return "库位与商品数量不一致";
    }
  }

  stoageFreeNum(box: BoxType) {
    if (!this.bizData.id2containerStorage) {
      return null;
    }
    let cid = box.warehouseContainerIds![box.warehouseContainerIds!.length - 1];
    let storage = this.bizData.id2containerStorage.get(cid);
    if (!storage) {
      return null;
    }
    return storage.numFree!;
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    {
      let warehouseId = "";

      let wparam: WmsMgWarehouseGetAllListQueryParam = {};
      wparam.state = ["OK"];
      let warehouses = await warehouseApi.getAllList(wparam);

      let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
      cparam.state = ["OK"];
      cparam.warehouseId = warehouseId;
      let containers: ContainerTree[] = await containerApi.getAllList(cparam);
      containers.map((x) => {
        if (!x.idParent || x.idParent == "0") {
          x.idParent = x.warehouseId;
        }
      });
      warehouses.forEach((x) => {
        let wc: ContainerTree = {};
        wc.id = x.id;
        wc.idParent = "0";
        wc.name = x.name;
        wc.storage = false;
        wc.no = "";
        wc.state = x.state;
        wc.createTime = x.createTime;
        containers.push(wc);
      });

      containers.forEach((c) => {
        if (!c.storage) {
          return;
        }
        c.numFree = c.numMax! - c.numUsed! - c.numLock!;
      });

      this.bizData.id2containerStorage = arrayUtils.array2map(containers, "id");
      this.oneLoadParam.containers = containers;
      this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");
    }

    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.cpid = compUtils.getQueryValue(this, "cpid", "");
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.audit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "audit", "false")
    );
    if (this.query.audit) {
      this.query.edit = false;
    } else {
      this.query.edit = commonFunUtils.parseBoolean(
        compUtils.getQueryValue(this, "edit", "true")
      );
    }
    if (this.query.cpid || this.query.id) {
      let content: WmsTransactionInfoBo | null = null;
      if (this.query.cpid) {
        content = await contentApi.get(this.query.cpid);
      } else {
        content = await contentApi.get(this.query.id);
      }
      let nfd: FormData = { ...content, products: [], outboxs: [] };
      nfd = nfd!;

      let pbids = content.products!.map((x) => x.productBoxId!);
      let pdids = content.products!.map((x) => x.productSpaceId!);
      let wboxids = content.boxs!.map((x) => x.warehouseBoxId!);

      let [pdBoxs, pds, wboxs] = await Promise.all([
        productBoxApi.getList({ id: pbids }),
        productSpaceApi.getList({ id: pdids }),
        warehouseBoxApi.getList({ id: wboxids }),
      ]);

      let id2pbox = arrayUtils.array2map(pdBoxs, "id");
      let id2pd = arrayUtils.array2map(pds, "id");
      let id2wbox = arrayUtils.array2map(wboxs, "id");

      let nid2product = new Map();
      for (let box of content.boxs!) {
        let pbox = id2pbox.get(box.productBoxId!)!;
        let pdspace = id2pd.get(pbox.productSpaceId!);

        let wbox = undefined;
        if (box.warehouseBoxId) {
          wbox = id2wbox.get(box.warehouseBoxId!);
        }
        let nbox: BoxType = { ...box };
        nbox.box = pbox;
        nbox.wbox = wbox;
        nbox.productBoxId = pbox.id;
        nbox.pd = pdspace;

        {
          if (nbox.warehouseContainerId) {
            let cs = arrayUtils.deepFindByMap(
              this.oneLoadParam.id2container!,
              "idParent",
              nbox.warehouseContainerId!
            );
            nbox.warehouseContainerIds = cs.reverse().map((x) => x.id!);
          } else {
            nbox.warehouseContainerIds = [];
          }
          nbox.warehouseContainerId;
        }
        if ("OUT" == box.type) {
          nfd.outboxs!.push(nbox);
        } else {
          let npd = nid2product.get(nbox.productBoxId!);
          if (!npd) {
            let tnpd: Product = {};
            tnpd.productBoxId = nbox.productBoxId!;
            tnpd.boxs = [];
            tnpd.box = nbox.box;
            tnpd.pd = nbox.pd;

            await this.putPdContainerTree(tnpd);

            nfd.products!.push(tnpd);
            nid2product.set(tnpd.productBoxId, tnpd);
            npd = tnpd;
          }
          npd.boxs!.push(nbox);
        }
      }

      this.formData = nfd;
    }
  }

  async putPdContainerTree(npd: Product) {
    let icontainers = this.oneLoadParam.containers
      .map((x) => ({ ...x }))
      .filter((x) => {
        // 过滤商品环境
        if (!this.query.edit) {
          return true;
        }
        if (!x.storage) {
          return true;
        }
        if (x.archived) {
          return false;
        }
        if (!npd.pd || !npd.pd.environment) {
          return true;
        }
        if (!x.environment) {
          return false;
        }
        let cenvs = x.environment.split(",");
        let pdenvs = npd.pd.environment.split(",");
        let ok = true;
        for (let e of pdenvs) {
          if (cenvs.indexOf(e) < 0) {
            ok = false;
            break;
          }
        }
        return ok;
      });
    let containers: ContainerTree[] = icontainers.map((x) => {
      if (x.storage) {
        delete x.children;
        return x;
      } else {
        return { ...x };
      }
    });
    let containerTree: ContainerTree[] = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    npd.containerTree = containerTree;
  }

  async handleNextClick() {
    if (!(await this.$rule.formCheck(this, "form"))) {
      return;
    }
    // product更新
    let outboxs = this.formData.outboxs!;
    let pboxid2boxs = arrayUtils.groupBy(outboxs, (x) => x.productBoxId!);

    let products = this.formData.products!;
    let id2pd = arrayUtils.array2map(products, "productBoxId");

    let npds: Product[] = [];
    for (const [pboxid, wboxs] of pboxid2boxs) {
      let num = wboxs.reduce((n, c) => n + c.num!, 0);
      let npd = id2pd.get(pboxid);
      if (!npd) {
        let wbox = wboxs[0];
        npd = {};
        npd.boxs = [];
        npd.productBoxId = wbox.box!.id;
        npd.boxs = [];
        npd.box = wbox.box!;
        npd.pd = wbox.pd!;

        await this.putPdContainerTree(npd);
      }
      npd.num = num;
      npds.push(npd);
    }

    this.formData.products = npds;

    this.bizData.stepAction = this.bizData.stepAction + 1;

    if (npds.length > 0) {
      let as = npds.map((x) => x.productBoxId!);
      this.bizData.productAction = as;
    }
  }
  async handleLastClick() {
    this.bizData.stepAction = this.bizData.stepAction - 1;
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: FormData = { ...data };
      body.boxs = [];
      for (let ibox of data.outboxs!) {
        let b = { ...ibox };
        b.type = "OUT";
        b.warehouseContainerId = b.wbox!.containerId;
        delete b.box;
        delete b.pd;
        delete b.wbox;
        body.boxs.push(b);
      }
      for (let pds of data.products!) {
        for (let ibox of pds.boxs!) {
          let b = { ...ibox };
          b.type = "IN";
          b.warehouseContainerId =
            b.warehouseContainerIds![b.warehouseContainerIds!.length - 1];
          delete b.warehouseContainerIds;
          body.boxs.push(b);
        }
      }
      delete body.outboxs;
      delete body.products;

      // 新增
      await moveApi.add(body);
      let msg: any = this.$t("common.success.save");
      this.$notify.success(msg);

      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async handleWboxRemoveClick(key: "outboxs", wpd: Product, idx: number) {
    this.formData[key]!.splice(idx, 1);
  }

  async goBack() {
    // let url = `/${compUtils.getQueryValue(this, "role", "")}${urlPre}`;
    // compUtils.toPage(this, "back", url, {});
    this.$router.back();
  }

  handleOpenSelectWarehouseClick() {
    this.bizData.selectWarehouseBox.visible = true;
  }

  async handleBoxRemoveClick(pd: Product, idx: number) {
    pd.boxs!.splice(idx, 1);
    await this.refreshStorageNumFree();
    this.$rule.formCheck(this, "form");
  }

  async handleAddBoxClick(pd: Product) {
    let num = pd.num! - pd.boxs!.map((x) => x.num!).reduce((x, y) => x + y, 0);
    pd.boxs!.push({
      productBoxId: pd.productBoxId,
      warehouseContainerIds: [],
      num: num,
      remark: "",
    });
    this.$rule.formCheck(this, "form");
  }

  async handleSelectWarehouseBoxAdd(
    wbox: WmsWarehouseBox & {
      pbox?: WmsProductBox;
      pd?: WmsProductSpace;
    }
  ) {
    if (!wbox) {
      return;
    }
    this.formData.outboxs!.push({
      id: wbox.id,
      pd: wbox.pd,
      box: wbox.pbox,
      wbox: wbox,
      productBoxId: wbox.pbox!.id,
      num: 1,
      remark: "",
    });
    this.bizData.selectWarehouseBox.visible = false;
    this.$rule.formCheck(this, "form");
  }
  handleTabsClick() {
    if (this.formData.products!.length > 0) {
      let as = this.formData.products!.map((x) => x.productBoxId!);
      this.bizData.productAction = as;
    }
  }
}
</script>

<style lang="scss" scoped>
.add-product-btn {
  font-size: 14px;
  margin-top: 10px;
}

.product-win {
  margin-top: 40px;
  border: solid 1px #ddd;
  padding: 40px;

  .product-content {
    width: 100%;
    // padding: 20px;
    margin-top: 10px;
  }
}
</style>
