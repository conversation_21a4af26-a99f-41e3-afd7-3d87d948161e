<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.wms-transaction") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small",
        @submit.native.prevent="() => {}"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick('inbound')"
          ) 新建入库单
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick('outbound')"
          ) 新建出库单
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick('move')"
          ) 新建移库单
        //- el-form-item(prop="warehouseId", label="仓库")
        //-   el-select(v-model="query.warehouseId", @change="handleSearchClick")
        //-     el-option(
        //-       v-for="option in oneLoadParam.warehouses",
        //-       :key="option.id",
        //-       :label="option.name",
        //-       :value="option.id"
        //-     )
        el-form-item(prop="keyword", label="关键词")
          el-input(
            v-model="query.keyword",
            placeholder="关键词",
            @keyup.enter.native="handleSearchClick"
          )
        el-form-item
          el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="name", label="名称")
      el-table-column(prop="no", label="申请编号")
      el-table-column(prop="bpmNo", label="流程编号")
      el-table-column(prop="type", label="类型")
        template(#default="scope")
          el-tag(
            :type="$dic.getFieldPropValue('wms.transaction.type', scope.row.type, 'tag-type', '')"
          ) {{ $dic.getFieldValue("wms.transaction.type", scope.row.type, scope.row.type) }}
      el-table-column(prop="state", label="状态")
        template(#default="scope")
          el-tag(
            :type="$dic.getFieldPropValue('wms.transaction.state', scope.row.state, 'tag-type', '')"
          ) {{ $dic.getFieldValue("wms.transaction.state", scope.row.state, "") }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="250", align="center")
        template(#default="scope")
          el-button(type="text", @click="handleCopyClick(scope.row)") 复制
          el-button(type="text", @click="handleTrialAuditClick(scope.row)") 初审
          el-button(
            type="text",
            @click="handleBoxClick(scope.row)",
            v-if="scope.row.type !== 'MOVE'"
          ) 分配库位
          el-button(
            type="text",
            @click="handleFinalAuditClick(scope.row)",
            v-if="scope.row.type !== 'MOVE'"
          ) 终审
          el-button(type="text", @click="handleDetailClick(scope.row)") 详情
          //- el-button(type="text", @click="handleAuditClick(scope.row, true)") 审核通过
          //- el-button(type="text", @click="handleAuditClick(scope.row, false)") 审核驳回
          //- el-button(type="text", @click="handleBoxClick(scope.row)") 设置位置
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import transactionApi, {
  WmsTransactionAuditVo,
} from "~/api/wms/mg/transactionApi";
import contentApi, {
  WmsMgTransactionGetListQueryParam,
  WmsTransaction,
} from "~/api/wms/mg/transactionApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

type TableContent = WmsTransaction;

type GetListCountQueryParam = {};

type GetListQueryParam = WmsMgTransactionGetListQueryParam;

const urlPre = "/wms/transaction";

@Component({
  name: "role-wms-transaction",
})
export default class WmsTransactionIndex extends mixins(BaseVue) {
  loading = true;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    contents: <Array<TableContent>>[],
  };

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.keyword = compUtils.getQueryValue(this, "keyword", "");
    this.query.page = +compUtils.getQueryValue(this, "page", "1");
    this.query.rows = +compUtils.getQueryValue(this, "rows", "20");
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    let param: GetListCountQueryParam = {};
    // param.keyword = this.query.keyword;
    routerUtils.putQueryValue(this, param);

    this.loading = true;
    try {
      this.query.total = await contentApi.getListCount();
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContent() {
    let param: GetListQueryParam = {};
    // param.keyword = this.query.keyword;
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, param);

    param.page = param.page - 1;

    this.loading = true;
    try {
      this.bizData.contents = await contentApi.getList(param);
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }

  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleAddClick(type: "inbound" | "outbound" | "move") {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/${type}/edit`
    );
  }

  async handleCopyClick(transaction: TableContent) {
    let type = transaction.type!.toLowerCase();
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/${type}/edit?cpid=${transaction.id}`
    );
  }

  async handleTrialAuditClick(transaction: TableContent) {
    let type = transaction.type!.toLowerCase();
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/${type}/edit?id=${
        transaction.id
      }&audit=true`
    );
  }

  async handleAuditClick(transaction: TableContent, audit: boolean) {
    let param: WmsTransactionAuditVo = {};
    param.audit = audit;
    param.remark = audit ? "同意此操作" : "不同意此操作";

    this.loading = true;
    try {
      await transactionApi.audit(transaction.id!, param);
      await this.loadContent();
    } finally {
      this.loading = false;
    }
  }

  async handleBoxClick(transaction: TableContent) {
    let type = transaction.type!.toLowerCase();
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/${type}/editbox?id=${transaction.id}`
    );
  }

  async handleDetailClick(transaction: TableContent) {
    let type = transaction.type!.toLowerCase();
    if (type == "move") {
      this.$router.push(
        `/${compUtils.getQueryValue(
          this,
          "role",
          ""
        )}${urlPre}/${type}/edit?id=${transaction.id}&edit=false`
      );
    } else {
      this.$router.push(
        `/${compUtils.getQueryValue(
          this,
          "role",
          ""
        )}${urlPre}/${type}/editbox?id=${transaction.id}&edit=false`
      );
    }
  }

  async handleFinalAuditClick(transaction: TableContent) {
    let type = transaction.type!.toLowerCase();
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/${type}/editbox?id=${transaction.id}&edit=false&audit=true`
    );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

