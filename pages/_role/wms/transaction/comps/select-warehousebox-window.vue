<template lang="pug">
div
  //- 表头
  .handle-box
    el-form(
      ref="queryForm",
      :model="query",
      :inline="true",
      v-loading="!oneLoadParam.inited",
      size="small",
      @submit.native.prevent="() => handleSearchClick()"
    )
      el-form-item(prop="containerIds", label="仓库位置")
        el-cascader(
          v-model="query.containerIds",
          :options="oneLoadParam.containerTree",
          :props="{ checkStrictly: false, label: 'name', value: 'id' }",
          clearable,
          style="min-width: 300px",
          @keyup.enter.native="handleSearchClick"
        )
      el-form-item(prop="keyword", label="关键词")
        el-input(
          v-model="query.keyword",
          placeholder="关键词",
          @keyup.enter.native="handleSearchClick"
        )
      el-form-item
        el-button(type="primary", @click="handleSearchClick") 搜索
  //- 表单内容
  el-table.table(
    :data="bizData.contents",
    border,
    ref="multipleTable",
    header-cell-class-name="table-header",
    v-loading="loading"
  )
    el-table-column(prop="pd.name", label="名称")
    el-table-column(prop="pd.sku", label="SKU")
    el-table-column(prop="pbox.lotNum", label="批次号")
    el-table-column(prop="numUsed", label="数量")
      template(#default="scope")
        p 可出库：{{ scope.row.numStorage - scope.row.numLock }}
        p 总数量：{{ scope.row.numStorage }}
        p 已锁定：{{ scope.row.numLock }}
    el-table-column(prop="containerId", label="存储位置")
      template(#default="scope")
        p {{ getContainerById(scope.row) }}
    //- el-table-column(prop="remark", label="备注")

    el-table-column(label="操作", width="250", align="center")
      template(#default="scope")
        el-button(
          v-if="_hasIds.indexOf(scope.row.id) < 0",
          type="text",
          icon="el-icon-tickets",
          @click="handleAddClick(scope.row)"
        ) 添加到列表
        div(v-else) 已添加
  .y-footer
    //- 分页信息
    .pagination
      el-pagination(
        background,
        :page-sizes="[5, 10, 20, 50, 100]",
        layout="total, sizes, prev, pager, next",
        :current-page="query.page",
        :page-size="query.rows",
        :total="query.total",
        @current-change="handlePageChange",
        @size-change="handleSizeChange"
      )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
// import contentApi, {
//   WmsProductSpace,
//   WmsMgProductSpaceGetListCountQueryParam,
//   WmsMgProductSpaceGetListQueryParam,
//   WmsProductBatchArchivedVo,
// } from "~/api/wms/mg/product/spaceApi";
import contentApi, {
  WmsMgWarehouseBoxGetListCountQueryParam,
  WmsMgWarehouseBoxGetListQueryParam,
  WmsWarehouseBox,
} from "@/api/wms/mg/warehouse/boxApi";
import productBoxApi, { WmsProductBox } from "@/api/wms/mg/product/boxApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import { BaseVue } from "~/model/vue";

import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import containerApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
} from "@/api/wms/mg/warehouse/containerApi";
import arrayUtils from "~/utils/arrayUtils";

import productHelper from "~/helper/wms/productHelper";

type TableContent = WmsWarehouseBox & {
  pbox?: WmsProductBox;
  pd?: WmsProductSpace;
};

type GetListCountQueryParam = WmsMgWarehouseBoxGetListCountQueryParam;

type GetListQueryParam = WmsMgWarehouseBoxGetListQueryParam;

type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];
  };

const urlPre = "/wms/product";

@Component({
  name: "role-wms-transaction-comps-select-warehousebox-window",
  props: ["hasIds"],
})
export default class WmsTransactionCompsSelectWarehouseboxWindow extends mixins(
  BaseVue
) {
  loading = true;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
    containerIds: <string[]>[],
  };
  oneLoadParam = {
    inited: false,
    // ...param

    containerTree: <ContainerTree[]>[],
    id2container: <Map<string, ContainerTree> | null>null,
  };
  bizData = {
    contents: <Array<TableContent>>[],
    id2space: <Map<string, WmsProductSpace>>new Map(),
    id2pbox: <Map<string, WmsProductBox>>new Map(),
  };

  get _hasIds() {
    return this.$props.hasIds ? this.$props.hasIds : [];
  }

  getContainerById(wbox: TableContent) {
    if (!this.oneLoadParam.id2container) {
      return "";
    }
    let cs = arrayUtils.deepFindByMap(
      this.oneLoadParam.id2container,
      "idParent",
      wbox.containerId!
    );
    return cs
      .reverse()
      .map((x) => x.name)
      .join(" > ");
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }

    let warehouseId = "";

    let wparam: WmsMgWarehouseGetAllListQueryParam = {};
    wparam.state = ["OK"];
    let warehouses = await warehouseApi.getAllList(wparam);

    let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
    cparam.state = ["OK"];
    cparam.warehouseId = warehouseId;
    let containers: ContainerTree[] = await containerApi.getAllList(cparam);
    containers.map((x) => {
      if (!x.idParent || x.idParent == "0") {
        x.idParent = x.warehouseId;
      }
    });
    warehouses.forEach((x) => {
      let wc: ContainerTree = {};
      wc.id = x.id;
      wc.idParent = "0";
      wc.name = x.name;
      wc.storage = false;
      wc.no = "";
      wc.state = x.state;
      wc.createTime = x.createTime;
      containers.push(wc);
    });

    this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");

    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    this.oneLoadParam.containerTree = containerTree;

    // ...
    this.oneLoadParam.inited = true;
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async mounted() {
    await this.init();
  }

  async loadContentCount() {
    let containerId =
      this.query.containerIds.length > 0
        ? this.query.containerIds[this.query.containerIds.length - 1]
        : null;

    let param: GetListCountQueryParam = {};

    this.loading = true;
    try {
      this.query.total = await contentApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContent() {
    let containerId =
      this.query.containerIds.length > 0
        ? this.query.containerIds[this.query.containerIds.length - 1]
        : null;

    let param: GetListQueryParam = {};
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    param.page = param.page - 1;

    this.loading = true;
    try {
      type WmsWarehouseBoxExtend = WmsWarehouseBox & {
        productSpaceId?: string;
      };
      let contents: WmsWarehouseBoxExtend[] = await contentApi.getList(param);
      await productHelper.warehouseBoxPutMsg(contents, this.bizData.id2pbox);
      await productHelper.productBoxPutMsg(contents, this.bizData.id2space);

      this.bizData.contents = contents;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleAddClick(pd: TableContent) {
    this.$emit("add", pd);
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

