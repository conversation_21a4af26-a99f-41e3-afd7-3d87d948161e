<template lang="pug">
div
  el-form-item(label="审核原因")
    el-input(v-model="remark", type="textarea", :rows="2")
  //- el-form-item(label="审核状态")
  //-   el-radio-group(v-model="audit")
  //-       el-radio(:label="true") 通过
  //-       el-radio(:label="false") 驳回
  el-form-item
    el-button(
      type="primary",
      @click="handleAuditClick(true)",
      v-loading="loading"
    ) 审核通过
    el-button(
      type="danger",
      @click="handleAuditClick(false)",
      v-loading="loading"
    ) 审核驳回
    el-button(@click="goBack()", v-loading="loading") 取 消
</template>
  
  <script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import transactionApi, {
  WmsTransactionAuditVo,
} from "~/api/wms/mg/transactionApi";

import { BaseVue } from "~/model/vue";

@Component({
  name: "role-wms-transaction-inbound-edit",
  props: ["id"],
})
export default class WmsTransactionInboundEdit extends mixins(BaseVue) {
  loading = false;
  remark = "";

  get _id() {
    return this.$props.id ? this.$props.id : "";
  }

  async handleAuditClick(audit: boolean) {
    let param: WmsTransactionAuditVo = {};
    param.audit = audit;
    param.remark = this.remark;

    this.loading = true;
    try {
      await transactionApi.audit(this._id, param);
      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } finally {
      this.loading = false;
    }
  }

  goBack() {
    this.$emit("goBack");
  }
}
</script>
  
  <style lang="scss" scoped>
.add-product-btn {
  font-size: 14px;
}
</style>
  