<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 入库单
      el-breadcrumb-item {{ this.query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="100px",
        size="small",
        @submit.native.prevent="() => {}"
      )
        el-form-item(label="名称", prop="name")
          el-input(v-model="formData.name", :disabled="true")
        el-form-item(
          label="商品位置：",
          :rules="[$rule.required(null, '批次号不能为空')]",
          required
        )
          el-collapse(v-model="bizData.productAction")
            el-collapse-item.product-box-win(
              v-for="(pd, idx) in formData.products",
              :key="pd.productExtendId",
              :name="pd.productExtendId"
            )
              template(slot="title")
                .box-title
                  span.mgr10 商品名称：{{ pd.pd.name }}
                  span.mgr10 商品SKU：{{ pd.pd.sku }}
                  span.mgr10 批次号：{{ pd.lotNum }}
                  span.mgr10 数量：{{ pd.num }}
                  span.mgr10 备注：{{ pd.remark }}
              template(#default)
                el-form-item(
                  width="0",
                  :prop="`products.${idx}.boxs`",
                  :rules="[$rule.required(null, '位置不能为空'), $rule.valid(() => validPdNum(pd))]"
                )
                  el-table.table(
                    :data="pd.boxs",
                    border,
                    ref="multipleTable",
                    header-cell-class-name="table-header",
                    v-loading="loading"
                  )
                    el-table-column(
                      prop="warehouseContainerId",
                      label="存放存放货架"
                    )
                      template(#default="scope")
                        el-form-item(
                          width="0",
                          :prop="`products.${idx}.boxs.${scope.$index}.warehouseContainerIds`",
                          :rules="[$rule.required(null, '位置不能为空'), $rule.valid(() => validWarehouseContainer(pd, scope.row))]"
                        )
                          el-cascader(
                            v-model="scope.row.warehouseContainerIds",
                            :options="pd.containerTree",
                            :props="{ checkStrictly: false, label: 'name', value: 'id' }",
                            clearable,
                            :disabled="!query.edit",
                            style="width: 100%",
                            @change="() => { refreshStorageNumFree(); checkForm(); }"
                          )
                            template(slot-scope="{ node, data }")
                              span {{ data.name }}
                              span(v-if="data.storage") ({{ data.numFree }})
                    el-table-column(label="数量", prop="num", width="250")
                      template(#default="scope")
                        el-input-number(
                          :step="1",
                          :min="1",
                          :max="stoageFreeNum(scope.row) != null ? Math.min(stoageFreeNum(scope.row) + scope.row.num, pd.num) : pd.num",
                          v-model="scope.row.num",
                          :disabled="!query.edit",
                          @change="() => refreshStorageNumFree()"
                        )
                        span.mgl10(
                          v-if="bizData.id2containerStorage && scope.row.warehouseContainerIds && scope.row.warehouseContainerIds.length > 0"
                        ) / {{ stoageFreeNum(scope.row) }}
                    el-table-column(prop="remark", label="备注")
                      template(#default="scope")
                        el-input(
                          v-model="scope.row.remark",
                          type="textarea",
                          :rows="2",
                          :disabled="!query.edit"
                        )
                    el-table-column(
                      label="操作",
                      width="100",
                      align="center",
                      v-if="query.edit"
                    )
                      template(#default="boxscope")
                        el-popconfirm(
                          title="确认要删除数据吗？",
                          @confirm="handleProductRemoveClick(pd, boxscope.$index)"
                        )
                          el-button(
                            slot="reference",
                            type="text",
                            icon="el-icon-tickets"
                          ) 移除
                  el-button(
                    v-if="query.edit",
                    type="text",
                    @click="handleAddBoxClick(pd)"
                  ) 添加位置

        el-form-item(label="备注", prop="remark")
          el-input(
            v-model="formData.remark",
            :disabled="!query.edit",
            type="textarea",
            :rows="2"
          )
        el-form-item(
          v-if="!query.edit && !query.audit",
          label="审核流程：",
          prop="audit"
        )
          ybiz-audittimeline(:value="formData.bpmWorkflowRecords")
        el-form-item(v-if="!query.audit")
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
        AuditBtn(v-if="query.audit", :id="query.id", @goBack="goBack")
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import contentApi, {
  WmsTransactionInfoBo,
  WmsTransactionProduct,
} from "~/api/wms/mg/transactionApi";
import inboundApi, {
  WmsTransactionInboundPutBoxBoxVo,
  WmsTransactionInboundPutBoxMsgVo,
} from "~/api/wms/mg/transaction/inboundApi";
import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import containerApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
} from "@/api/wms/mg/warehouse/containerApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import arrayUtils from "~/utils/arrayUtils";
import AuditBtn from "../comps/audit-btn.vue";
import YbizAudittimeline from "~/components/ybiz/audittimeline.vue";

type FormData = Omit<WmsTransactionInfoBo, "products"> & {
  products?: Product[];
};

type BoxType = WmsTransactionInboundPutBoxBoxVo & {
  warehouseContainerIds?: string[];
};

type Product = WmsTransactionProduct & {
  pd?: WmsProductSpace;
  boxs?: BoxType[];

  containerTree?: ContainerTree[];
};

type ContainerTree = WmsWarehouseContainer & {
  children?: ContainerTree[];
  numFree?: number;
};

const urlPre = "/wms/transaction";

@Component({
  name: "role-wms-transaction-inbound-edit",
  components: {
    AuditBtn,
    YbizAudittimeline,
  },
  props: [compUtils.VALUE_TAG_PROP],
})
export default class WmsTransactionInboundEdit extends mixins(BaseVue) {
  loading = true;
  selectProductVisible = false;
  query = {
    id: "",
    edit: true,
    audit: false,
  };
  formData: FormData = {
    name: "",
    type: "INBOUND",
    products: [],
    remark: "",
  };
  bizData = {
    productAction: <string[]>[],

    id2containerStorage: <Map<string, ContainerTree> | null>null,
  };

  oneLoadParam = {
    inited: false,
    warehouses: <WmsWarehouse[]>[],
    // ...param

    containers: <ContainerTree[]>[],
  };

  stoageFreeNum(box: BoxType) {
    if (!this.bizData.id2containerStorage) {
      return null;
    }
    let cid = box.warehouseContainerIds![box.warehouseContainerIds!.length - 1];
    let storage = this.bizData.id2containerStorage.get(cid);
    if (!storage) {
      return null;
    }
    return storage.numFree!;
  }

  validWarehouseContainer(pd: Product, box: BoxType) {
    if (
      !box ||
      !box.warehouseContainerIds ||
      box.warehouseContainerIds.length <= 0
    ) {
      return null;
    }
    let id = box.warehouseContainerIds[box.warehouseContainerIds.length - 1];
    let count = 0;
    for (let b of pd.boxs ?? []) {
      if (b.warehouseContainerIds && b.warehouseContainerIds.length > 0) {
        let nid = b.warehouseContainerIds[b.warehouseContainerIds.length - 1];
        if (nid == id) {
          count++;
          if (count >= 2) {
            return "入库位置信息重复。";
          }
        }
      }
    }
    return null;
  }

  validPdNum(pd: Product) {
    if (!pd || !pd.boxs || pd.boxs.length <= 0) {
      return null;
    }
    let scount = 0;
    for (let box of pd.boxs) {
      scount = scount + box.num!;
    }
    if (pd.num == scount) {
      return null;
    } else {
      return "库位与商品数量不一致";
    }
  }

  productShow(pd: Product) {
    if (!pd) {
      return "";
    }
    let name = "";
    if (pd.pd) {
      name = pd.pd.name ?? pd.productSpaceId!;
      if (pd.pd.sku) {
        name = name + `(${pd.pd.sku})`;
      }
    } else {
      name = pd.productSpaceId!;
    }
    return name;
  }

  async created() {
    this.init();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    {
      let warehouseId = "";
      let wparam: WmsMgWarehouseGetAllListQueryParam = {};
      wparam.state = ["OK"];
      let warehouses = await warehouseApi.getAllList(wparam);
      this.oneLoadParam.warehouses = warehouses;

      let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
      cparam.state = ["OK"];
      cparam.warehouseId = warehouseId;
      let containers: ContainerTree[] = await containerApi.getAllList(cparam);
      containers.map((x) => {
        if (!x.idParent || x.idParent == "0") {
          x.idParent = x.warehouseId;
        }
      });
      warehouses.forEach((x) => {
        let wc: ContainerTree = {};
        wc.id = x.id;
        wc.idParent = "0";
        wc.name = x.name;
        wc.storage = false;
        wc.no = "";
        wc.state = x.state;
        wc.createTime = x.createTime;
        containers.push(wc);
      });

      containers.forEach((c) => {
        if (!c.storage) {
          return;
        }
        c.numFree = c.numMax! - c.numUsed! - c.numLock!;
      });

      this.bizData.id2containerStorage = arrayUtils.array2map(containers, "id");
      this.oneLoadParam.containers = containers;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.edit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "edit", "true")
    );
    this.query.audit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "audit", "false")
    );

    if (this.query.id) {
      let content: WmsTransactionInfoBo = await contentApi.get(this.query.id);
      let nfd: FormData = { ...content };

      let psids = nfd.products!.map((x) => x.productSpaceId!);
      let pds = await productSpaceApi.getList({ id: psids });
      let id2pd = arrayUtils.array2map(pds, "id");
      for (let pd of nfd.products!) {
        let p = id2pd.get(pd.productSpaceId!);
        pd.pd = p;
        pd.boxs = [];

        {
          let icontainers = [...this.oneLoadParam.containers];
          let containers = icontainers
            .map((x) => {
              if (x.storage) {
                delete x.children;
                return x;
              } else {
                return { ...x };
              }
            })
            .filter((x) => {
              // 过滤商品环境
              if (!this.query.edit) {
                return true;
              }
              if (!x.storage) {
                return true;
              }
              if (x.archived) {
                return false;
              }
              if (!pd.pd || !pd.pd.environment) {
                return true;
              }
              if (!x.environment) {
                return false;
              }
              let cenvs = x.environment.split(",");
              let pdenvs = pd.pd.environment.split(",");
              let ok = true;
              for (let e of pdenvs) {
                if (cenvs.indexOf(e) < 0) {
                  ok = false;
                  break;
                }
              }
              return ok;
            });
          let containerTree: ContainerTree[] = arrayUtils.array2tree(
            containers,
            "id",
            "idParent",
            "children"
          );
          const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
            let ncs: ContainerTree[] = [];
            for (let ct of cts) {
              let cns: ContainerTree[] = [];
              if (ct.children) {
                cns = clearContainer(ct.children);
              }
              if (ct.storage) {
                ct.children = undefined;
                ncs.push(ct);
              } else if (cns.length > 0) {
                ct.children = cns;
                ncs.push(ct);
              }
            }
            return ncs;
          };
          containerTree = clearContainer(containerTree);
          pd.containerTree = containerTree;
        }
      }

      let id2tpid = arrayUtils.array2map(nfd.products!, "id");
      for (let cbox of content.boxs!) {
        let box: BoxType = { ...cbox };
        let id2containerStorage = this.bizData.id2containerStorage!;
        let cid = box.warehouseContainerId!;
        let cs = arrayUtils.deepFindByMap(id2containerStorage, "idParent", cid);
        box.warehouseContainerIds = cs.reverse().map((x) => x.id!);
        let pd = id2tpid.get(box.transactionProductId!);
        pd!.boxs!.push(box);
      }

      this.formData = nfd;
      if (nfd.products && nfd.products!.length > 0) {
        let as = nfd.products!.map((x) => x.productExtendId!);
        this.bizData.productAction = as;
      }
    }
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      if (!this.formData.products || this.formData.products.length <= 0) {
        return;
      }
      let body: WmsTransactionInboundPutBoxMsgVo = {};
      body.boxs = [];
      for (let pd of this.formData.products) {
        if (!pd.boxs) {
          this.$notify.error("库位与商品数量不一致");
          return;
        }
        for (let box of pd.boxs) {
          let b: WmsTransactionInboundPutBoxBoxVo = { ...box };
          b.num = box.num;
          b.transactionProductId = pd.id;
          b.warehouseContainerId =
            box.warehouseContainerIds![box.warehouseContainerIds!.length - 1];
          delete (<any>b).warehouseContainerIds;
          body.boxs.push(b);
        }
      }

      // 新增
      await inboundApi.putBoxMsg(this.query.id, body);
      let msg: any = this.$t("common.success.save");
      this.$notify.success(msg);

      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async checkForm() {
    this.$rule.formCheck(this, "form");
  }

  async handleAddBoxClick(pd: Product) {
    let num = pd.num! - pd.boxs!.map((x) => x.num!).reduce((x, y) => x + y, 0);
    pd.boxs!.push({
      warehouseContainerIds: [],
      num: num,
      remark: "",
    });
    this.$rule.formCheck(this, "form");
  }

  async refreshStorageNumFree() {
    if (!this.bizData.id2containerStorage) {
      return;
    }
    for (const [k, storage] of this.bizData.id2containerStorage) {
      storage.numFree = storage.numMax! - storage.numUsed! - storage.numLock!;
    }
    if (this.formData.products) {
      for (let pd of this.formData.products) {
        if (pd.boxs) {
          for (let box of pd.boxs) {
            if (
              box.warehouseContainerIds &&
              box.warehouseContainerIds.length > 0
            ) {
              let wcid =
                box.warehouseContainerIds[box.warehouseContainerIds.length - 1];
              let storage = this.bizData.id2containerStorage.get(wcid)!;
              storage.numFree = storage.numFree! - box.num!;
            }
          }
        }
      }
    }
  }

  async handleProductRemoveClick(pd: Product, idx: number) {
    pd.boxs!.splice(idx, 1);
    await this.refreshStorageNumFree();
    this.$rule.formCheck(this, "form");
  }

  async goBack() {
    // this.$router.push(`/${compUtils.getQueryValue(this, "role", "")}${urlPre}`);
    this.$router.back();
  }
}
</script>

<style lang="scss" scoped>
.product-box-win {
  border: solid 1px #f4f4f4;
  // border-radius: 4px;
  padding: 6px 20px 0px;
  margin-bottom: 10px;

  .box-title {
    font-size: 14px;
  }
}
</style>
