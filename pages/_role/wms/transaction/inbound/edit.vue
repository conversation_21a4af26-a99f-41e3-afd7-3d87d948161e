<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 入库单
      el-breadcrumb-item {{ query.audit ? $t("common.base.audit") : query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="100px",
        size="small",
        @submit.native.prevent="() => {}"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(
            v-model="formData.name",
            :disabled="!query.edit || query.audit"
          )
        //- el-form-item(label="寄存时间")
        //-   el-date-picker(
        //-     v-model="formData.time",
        //-     type="daterange",
        //-     range-separator="至",
        //-     start-placeholder="开始日期",
        //-     end-placeholder="结束日期",
        //-     placeholder="选择日期时间"
        //-   )
        //- el-form-item(
        //-   label="有效期",
        //-   prop="name",
        //-   :rules="[$rule.required(null, '名称不能为空')]",
        //-   required
        //- )
        //-   el-input(v-model="formData.name", :disabled="!query.edit")
        //- el-form-item(
        //-   label="批次号",
        //-   prop="name",
        //-   :rules="[$rule.required(null, '名称不能为空')]",
        //-   required
        //- )
        //-   el-input(v-model="formData.name", :disabled="!query.edit")
        //- el-form-item(
        //-   label="寄存仓库",
        //-   prop="warehouseId",
        //-   :rules="[$rule.required(null, '寄存仓库不能为空')]",
        //-   required
        //- )
        //-   el-select(v-model="formData.warehouseId", @change="warehouseChange")
        //-     el-option(
        //-       v-for="option in oneLoadParam.warehouses",
        //-       :key="option.id",
        //-       :label="option.name",
        //-       :value="option.id"
        //-     )
        el-form-item(
          label="商品列表：",
          :rules="[$rule.required(null, '批次号不能为空')]",
          required
        )
          el-button.add-product-btn(
            v-if="query.edit && !query.audit",
            type="text",
            @click="handleOpenSelectProductClick"
          ) 选择入库商品
          el-table.table(
            :data="formData.products",
            border,
            ref="multipleTable",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(prop="name", label="名称(sku)")
              template(#default="scope")
                div {{ $fieldUtils.getFieldValue(scope.row, "pd.name", scope.row.productSpaceId) }} ({{ $fieldUtils.getFieldValue(scope.row, "pd.sku", scope.row.productSpaceId) }})
            el-table-column(
              label="批次号",
              prop="lotNum",
              :render-header="addRedStar",
              width="200"
            )
              template(#default="scope")
                el-form-item(
                  label-width="0",
                  :prop="`products.${scope.$index}.lotNum`",
                  :rules="[$rule.required(null, '批次号不能为空')]"
                )
                  el-input(
                    v-model="scope.row.lotNum",
                    :disabled="!query.edit || query.audit"
                  )
            el-table-column(
              label="数量",
              prop="num",
              :render-header="addRedStar",
              width="200"
            )
              template(#default="scope")
                el-form-item(
                  label-width="0",
                  :prop="`products.${scope.$index}.num`",
                  :rules="[$rule.required(null, '数量不能为空')]"
                )
                  el-input-number(
                    :step="1",
                    :min="1",
                    :max="10000",
                    v-model="scope.row.num",
                    :disabled="!query.edit || query.audit"
                  )
            el-table-column(prop="remark", label="备注")
              template(#default="scope")
                el-input(
                  v-model="scope.row.remark",
                  type="textarea",
                  :rows="2",
                  :disabled="!query.edit || query.audit"
                )
            el-table-column(
              label="操作",
              width="250",
              align="center",
              v-if="query.edit && !query.audit"
            )
              template(#default="scope")
                el-popconfirm(
                  title="确认要删除数据吗？",
                  @confirm="handleProductRemoveClick(scope.row, scope.$index)"
                )
                  el-button(
                    slot="reference",
                    type="text",
                    icon="el-icon-tickets"
                  ) 移除

        el-form-item(label="备注", prop="remark")
          el-input(
            v-model="formData.remark",
            :disabled="!query.edit || query.audit",
            type="textarea",
            :rows="2"
          )
        el-form-item(
          v-if="!query.edit && !query.audit",
          label="审核流程：",
          prop="audit"
        )
          ybiz-audittimeline(:value="formData.bpmWorkflowRecords")
        el-form-item(v-if="!query.audit")
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
        AuditBtn(v-if="query.audit", :id="query.id", @goBack="goBack")

      //- 商品编辑界面
      el-dialog(
        :visible="bizData.selectProduct.visible",
        title="选择商品",
        :before-close="() => (bizData.selectProduct.visible = false)",
        width="80%"
      )
        wms-transaction-comps-select-product-window(
          :hasIds="[]",
          @add="handleSelectProductAdd"
        )
        template(#footer)
          //- el-button(type="primary") 添加
          el-button(@click="() => (bizData.selectProduct.visible = false)") 取消
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import contentApi, { WmsTransactionProduct } from "~/api/wms/mg/transactionApi";

import inboundApi, {
  WmsTransactionInboundAddVo,
} from "~/api/wms/mg/transaction/inboundApi";
import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import WmsTransactionCompsSelectProductWindow from "../comps/select-product-window.vue";
import AuditBtn from "../comps/audit-btn.vue";
import arrayUtils from "~/utils/arrayUtils";
import YbizAudittimeline from "~/components/ybiz/audittimeline.vue";

type FormData = Omit<WmsTransactionInboundAddVo, "products"> & {
  products?: Product[];
};

type Product = WmsTransactionProduct & {
  pd?: WmsProductSpace;
};

const urlPre = "/wms/transaction";

@Component({
  name: "role-wms-transaction-inbound-edit",
  components: {
    WmsTransactionCompsSelectProductWindow,
    AuditBtn,
    YbizAudittimeline,
  },
})
export default class WmsTransactionInboundEdit extends mixins(BaseVue) {
  loading = true;
  selectProductVisible = false;
  query = {
    id: "",
    cpid: "",
    edit: true,
    audit: false,
  };
  formData: FormData = {
    name: "",
    products: [],
    remark: "",
    reasonType: "NORMAL",
  };
  bizData = {
    selectProduct: {
      visible: false,
    },
  };

  oneLoadParam = {
    inited: false,
    warehouses: <WmsWarehouse[]>[],
    // ...param
  };

  get hasProductSpaceIds() {
    return this.formData.products!.map((x) => x.productSpaceId!);
  }

  async created() {
    this.init();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    {
      let param: WmsMgWarehouseGetAllListQueryParam = {};
      param.state = ["OK"];
      let warehouses = await warehouseApi.getAllList(param);
      this.oneLoadParam.warehouses = warehouses;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.cpid = compUtils.getQueryValue(this, "cpid", "");
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.audit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "audit", "false")
    );
    if (this.query.audit) {
      this.query.edit = false;
    } else {
      this.query.edit = commonFunUtils.parseBoolean(
        compUtils.getQueryValue(this, "edit", "true")
      );
    }
    if (this.query.cpid || this.query.id) {
      let nfd: FormData | null = null;
      if (this.query.cpid) {
        let content = await contentApi.get(this.query.cpid);
        nfd = { ...content };
      } else {
        let content = await contentApi.get(this.query.id);
        nfd = { ...content };
      }
      nfd = nfd!;

      let psids = nfd.products!.map((x) => x.productSpaceId!);
      let pds = await productSpaceApi.getList({ id: psids });
      let id2pd = arrayUtils.array2map(pds, "id");
      for (let pd of nfd.products!) {
        let p = id2pd.get(pd.productSpaceId!);
        pd.pd = p;
      }
      this.formData = nfd;
    }
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: FormData = { ...data };
      // 新增
      let detail = await inboundApi.add(body);
      this.query.id = detail.id!;
      let msg: any = this.$t("common.success.save");
      this.$notify.success(msg);

      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async handleProductRemoveClick(pd: Product, idx: number) {
    this.formData.products!.splice(idx, 1);
  }

  async goBack() {
    // this.$router.push(`/${compUtils.getQueryValue(this, "role", "")}${urlPre}`);
    this.$router.back();
  }

  handleOpenSelectProductClick() {
    this.bizData.selectProduct.visible = true;
  }

  handleSelectProductAdd(pd: WmsProductSpace) {
    this.formData.products!.push({
      pd,
      productSpaceId: pd.id,
      lotNum: "",
      num: 1,
      remark: "",
    });
    this.bizData.selectProduct.visible = false;
  }
}
</script>

<style lang="scss" scoped>
.add-product-btn {
  font-size: 14px;
}
</style>
