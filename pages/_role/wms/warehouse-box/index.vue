<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.wms-warehouse-box") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small"
      )
        el-form-item(prop="containerIds", label="仓库位置")
          el-cascader(
            v-model="query.containerIds",
            :options="oneLoadParam.containerTree",
            :props="{ checkStrictly: false, label: 'name', value: 'id' }",
            clearable,
            style="min-width: 300px",
            @keyup.enter.native="handleSearchClick"
          )
        el-form-item(prop="keyword", label="关键词")
          el-input(
            v-model="query.keyword",
            placeholder="关键词",
            @keyup.enter.native="handleSearchClick"
          )
        el-form-item
          el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="pd.name", label="名称")
      el-table-column(prop="pd.sku", label="SKU")
      el-table-column(prop="pbox.lotNum", label="批次号")
      el-table-column(prop="numUsed", label="数量")
        template(#default="scope")
          p 可出库：{{ scope.row.numStorage - scope.row.numLock }}
          p 总数量：{{ scope.row.numStorage }}
          p 已锁定：{{ scope.row.numLock }}
      el-table-column(prop="containerId", label="存储位置")
        template(#default="scope")
          p {{ getContainerById(scope.row) }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-tickets",
            @click="handleModifyClick(scope.row, false)"
          ) {{ $t("common.base.detail") }}
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import warehouseApi, {
  WmsMgWarehouseGetAllListQueryParam,
  WmsWarehouse,
} from "~/api/wms/mg/warehouseApi";
import contentApi, {
  WmsWarehouseBox,
  WmsMgWarehouseBoxGetListCountQueryParam,
  WmsMgWarehouseBoxGetListQueryParam,
} from "@/api/wms/mg/warehouse/boxApi";
import containerApi, {
  WmsWarehouseContainer,
  WmsMgWarehouseContainerGetAllListQueryParam,
} from "@/api/wms/mg/warehouse/containerApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import productBoxApi, { WmsProductBox } from "~/api/wms/mg/product/boxApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import arrayUtils from "~/utils/arrayUtils";
import productHelper from "~/helper/wms/productHelper";

type TableContent = WmsWarehouseBox & {
  pbox?: WmsProductBox;
  pd?: WmsProductSpace;
};
type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];
  };
type GetListCountQueryParam = WmsMgWarehouseBoxGetListCountQueryParam;
type GetListQueryParam = WmsMgWarehouseBoxGetListQueryParam;
const urlPre = "/wms/warehouse-box";

@Component({
  name: "role-wms-warehouse-box",
})
export default class WmsWarehouseBoxIndex extends mixins(BaseVue) {
  loading = true;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
    containerIds: <string[]>[],
  };
  oneLoadParam = {
    inited: false,
    // ...param
    containerTree: <ContainerTree[]>[],
    id2container: <Map<string, ContainerTree> | null>null,
  };
  bizData = {
    contents: <Array<TableContent>>[],
    id2space: <Map<string, WmsProductSpace>>new Map(),
    id2pbox: <Map<string, WmsProductBox>>new Map(),
  };

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    let warehouseId = "";

    let wparam: WmsMgWarehouseGetAllListQueryParam = {};
    wparam.state = ["OK"];
    let warehouses = await warehouseApi.getAllList(wparam);

    let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
    cparam.state = ["OK"];
    cparam.warehouseId = warehouseId;
    let containers: ContainerTree[] = await containerApi.getAllList(cparam);
    containers.map((x) => {
      if (!x.idParent || x.idParent == "0") {
        x.idParent = x.warehouseId;
      }
    });
    warehouses.forEach((x) => {
      let wc: ContainerTree = {};
      wc.id = x.id;
      wc.idParent = "0";
      wc.name = x.name;
      wc.storage = false;
      wc.no = "";
      wc.state = x.state;
      wc.createTime = x.createTime;
      containers.push(wc);
    });

    this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");

    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    this.oneLoadParam.containerTree = containerTree;

    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    let containerId =
      this.query.containerIds.length > 0
        ? this.query.containerIds[this.query.containerIds.length - 1]
        : null;

    let param: GetListCountQueryParam = {};

    this.loading = true;
    try {
      this.query.total = await contentApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContent() {
    let containerId =
      this.query.containerIds.length > 0
        ? this.query.containerIds[this.query.containerIds.length - 1]
        : null;

    let param: GetListQueryParam = {};
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;
    
    param.page = param.page - 1;

    this.loading = true;
    try {
      type WmsWarehouseBoxExtend = WmsWarehouseBox & {
        productSpaceId?: string;
      };
      let contents: WmsWarehouseBoxExtend[] = await contentApi.getList(param);
      await productHelper.warehouseBoxPutMsg(contents, this.bizData.id2pbox);
      await productHelper.productBoxPutMsg(contents, this.bizData.id2space);
      this.bizData.contents = contents;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async putPdMsg(contents: TableContent[]) {
    {
      let id2pbox = this.bizData.id2pbox;
      let loadProducts: TableContent[] = [];
      for (let wbox of contents) {
        let pbox = id2pbox.get(wbox.productBoxId!);
        if (pbox) {
          wbox.pbox = pbox;
        } else {
          loadProducts.push(wbox);
        }
      }

      if (loadProducts.length > 0) {
        let pboxids = loadProducts.map((x) => x.productBoxId!);
        let pboxs = await productBoxApi.getList({ id: pboxids });
        pboxs.forEach((x) => id2pbox.set(x.id!, x));
        for (let wbox of loadProducts) {
          let pbox = id2pbox.get(wbox.productBoxId!);
          wbox.pbox = pbox;
        }
      }
    }
    {
      let id2space = this.bizData.id2space;
      let loadProducts: TableContent[] = [];
      for (let wbox of contents) {
        if (!wbox.pbox) {
          continue;
        }
        let pbox = wbox.pbox;
        let pd = id2space.get(pbox.productSpaceId!);
        if (pd) {
          wbox.pd = pd;
        } else {
          loadProducts.push(wbox);
        }
      }
      if (loadProducts.length > 0) {
        let pdids = loadProducts.map((x) => x.pbox!.productSpaceId!);
        let pds = await productSpaceApi.getList({ id: pdids });
        pds.forEach((x) => id2space.set(x.id!, x));
        for (let wbox of loadProducts) {
          let pd = id2space.get(wbox.pbox!.productSpaceId!);
          wbox.pd = pd;
        }
      }
    }
  }
  //补充商品所属货架信息
  getContainerById(wbox: TableContent) {
    if (!this.oneLoadParam.id2container) {
      return "";
    }
    let cs = arrayUtils.deepFindByMap(
      this.oneLoadParam.id2container,
      "idParent",
      wbox.containerId!
    );
    return cs
      .reverse()
      .map((x) => x.name)
      .join(" > ");
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit`
    );
  }

  async handleModifyClick(record: TableContent, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  
  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

