<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.wms-warehouse-box") }}
      el-breadcrumb-item {{ this.query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-descriptions(:column="1")
        el-descriptions-item(label="名称") {{formData.pd && formData.pd.name}}
        el-descriptions-item(label="SKU") {{formData.pd && formData.pd.sku}}
        el-descriptions-item(label="批次号") {{formData.pbox && formData.pbox.lotNum}}
        el-descriptions-item(label="总数量") {{formData.pbox && formData.pbox.numStorage}}
        el-descriptions-item(label="已锁定数量") {{formData.pbox && formData.pbox.numLock}}
        el-descriptions-item(label="可出库数量") {{formData.pbox && formData.pbox.numStorage - formData.pbox.numLock}}
        el-descriptions-item(label="仓库位置") {{ getContainerById(formData) }}

      el-button(@click="goBack()") {{ $t("common.base.cancel") }}
          
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import warehouseBoxApi,{
  WmsWarehouseBox,
  WmsMgWarehouseBoxGetListCountQueryParam,
  WmsMgWarehouseBoxGetListQueryParam
} from "@/api/wms/mg/warehouse/boxApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import productBoxApi, { WmsProductBox } from "~/api/wms/mg/product/boxApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import warehouseApi, { WmsMgWarehouseGetAllListQueryParam } from "~/api/wms/mg/warehouseApi";
import containerApi, { WmsMgWarehouseContainerGetAllListQueryParam, WmsWarehouseContainer } from "~/api/wms/mg/warehouse/containerApi";
import arrayUtils from "~/utils/arrayUtils";

type FormData = WmsWarehouseBox & {
  pbox?: WmsProductBox;
  pd?: WmsProductSpace;
};

type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];
};

const urlPre = "/wms/warehouse-box";

@Component({
  name: "role-wms-warehouse-box-edit",
})
export default class WmsWarehouseBoxEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    edit: true,
  };
  formData: FormData = {};

  oneLoadParam = {
    inited: false,
    // ...param
    containerTree: <ContainerTree[]>[],
    id2container: <Map<string, ContainerTree> | null>null,
  };

  async created() {
    this.init();
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }

    let warehouseId = "";

    let wparam: WmsMgWarehouseGetAllListQueryParam = {};
    wparam.state = ["OK"];
    let warehouses = await warehouseApi.getAllList(wparam);

    let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
    cparam.state = ["OK"];
    cparam.warehouseId = warehouseId;
    let containers: ContainerTree[] = await containerApi.getAllList(cparam);
    containers.map((x) => {
      if (!x.idParent || x.idParent == "0") {
        x.idParent = x.warehouseId;
      }
    });
    warehouses.forEach((x) => {
      let wc: ContainerTree = {};
      wc.id = x.id;
      wc.idParent = "0";
      wc.name = x.name;
      wc.storage = false;
      wc.no = "";
      wc.state = x.state;
      wc.createTime = x.createTime;
      containers.push(wc);
    });

    this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");

    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    this.oneLoadParam.containerTree = containerTree;

    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.edit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "edit", "true")
    );
    if (this.query.id) {
      let wbox = await warehouseBoxApi.getList({id:[this.query.id]});
      if(wbox.length){
        let pbox = await productBoxApi.get(wbox[0].productBoxId!);
        if(pbox){
          let pd = await productSpaceApi.get(pbox.productSpaceId!);
          this.formData = wbox[0];
          this.formData.pbox = pbox;
          this.formData.pd = pd;
        }
      }
    }
  }

  //补充商品所属货架信息
  getContainerById(wbox: FormData) {
    if (!this.oneLoadParam.id2container) {
      return "";
    }
    let cs = arrayUtils.deepFindByMap(
      this.oneLoadParam.id2container,
      "idParent",
      wbox.containerId!
    );
    return cs
      .reverse()
      .map((x) => x.name)
      .join(" > ");
  }

  async goBack() {
    this.$router.push(`/${compUtils.getQueryValue(this, "role", "")}${urlPre}`);
  }
}
</script>

<style lang="scss" scoped>
</style>
