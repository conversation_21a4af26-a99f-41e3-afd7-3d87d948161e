<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.wms-warehouse") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        //- el-form-item(prop="warehouseId", label="仓库")
        //-   el-select(v-model="query.warehouseId", @change="handleSearchClick")
        //-     el-option(
        //-       v-for="option in oneLoadParam.warehouses",
        //-       :key="option.id",
        //-       :label="option.name",
        //-       :value="option.id"
        //-     )
        //- el-form-item(prop="keyword", label="关键词")
        //-   el-input(
        //-     v-model="query.keyword",
        //-     placeholder="关键词",
        //-     @keyup.enter.native="handleSearchClick"
        //-   )
        //- el-form-item
        //-   el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="name", label="名称")
      el-table-column(prop="state", label="状态")
        template(#default="scope")
          //- el-tag(:type="!scope.row.ban ? 'success' : 'danger'") {{ !scope.row.ban ? "启用中" : "禁用" }}
          | {{ $dic.getFieldValue("wms.warehouse.state", scope.row.state, scope.row.state) }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-tickets",
            @click="handleModifyClick(scope.row, false)"
          ) {{ $t("common.base.detail") }}
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            @click="handleContainerClick(scope.row)"
          ) 货架信息
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import contentApi, {
  WmsMgWarehouseGetAllListQueryParam,
  WmsWarehouse,
} from "~/api/wms/mg/warehouseApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

type TableContent = WmsWarehouse;

const urlPre = "/wms/warehouse";

@Component({
  name: "role-wms-warehouse",
})
export default class WmsWarehouseIndex extends mixins(BaseVue) {
  loading = 0;
  query = {};
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    contents: <Array<TableContent>>[],
  };

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    await this.loadContent();
  }

  async loadContent() {
    let param: WmsMgWarehouseGetAllListQueryParam = {};
    param.state = ["OK"];
    this.loading++;
    try {
      this.bizData.contents = await contentApi.getAllList(param);
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit`
    );
  }

  async handleModifyClick(record: TableContent, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }

  async handleContainerClick(record: TableContent) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/wms/container?warehouse_id=${record.id}`
    );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

