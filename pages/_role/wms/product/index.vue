<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.wms-product") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small",
        @submit.native.prevent="()=>{}"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        //- el-form-item(prop="warehouseId", label="仓库")
        //-   el-select(v-model="query.warehouseId", @change="handleSearchClick")
        //-     el-option(
        //-       v-for="option in oneLoadParam.warehouses",
        //-       :key="option.id",
        //-       :label="option.name",
        //-       :value="option.id"
        //-     )
        el-form-item(prop="keyword", label="关键词")
          el-input(
            v-model="query.keyword",
            placeholder="关键词",
            @keyup.enter.native="handleSearchClick"
          )
        el-form-item
          el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="name", label="名称")
      el-table-column(prop="sku", label="SKU")
      el-table-column(prop="up", label="状态")
        template(#default="scope")
          el-tag(:type="!scope.row.ban ? 'success' : 'danger'") {{ !scope.row.ban ? "启用中" : "禁用" }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="250", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-tickets",
            @click="handleModifyClick(scope.row, false)"
          ) {{ $t("common.base.detail") }}
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-button.red(
            v-if="scope.row.archived",
            slot="reference",
            type="text",
            icon="el-icon-delete",
            @click="handleArchivedClick(scope.row, !scope.row.archived)"
          ) 取消归档
          el-popconfirm.mgl10(
            v-if="!scope.row.up && !scope.row.archived",
            title="确认要归档数据吗？",
            @confirm="handleArchivedClick(scope.row, !scope.row.archived)"
          )
            el-button.red(slot="reference", type="text",icon="el-icon-delete") 归档
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import contentApi, {
  WmsProductSpace,
  WmsMgProductSpaceGetListCountQueryParam,
  WmsMgProductSpaceGetListQueryParam,
  WmsProductBatchArchivedVo,
} from "~/api/wms/mg/product/spaceApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

type TableContent = WmsProductSpace;

type GetListCountQueryParam = WmsMgProductSpaceGetListCountQueryParam;

type GetListQueryParam = WmsMgProductSpaceGetListQueryParam;

const urlPre = "/wms/product";

@Component({
  name: "role-wms-product",
})
export default class WmsProductIndex extends mixins(BaseVue) {
  loading = true;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    contents: <Array<TableContent>>[],
  };

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.keyword = compUtils.getQueryValue(this, "keyword", "");
    this.query.page = +compUtils.getQueryValue(this, "page", "1");
    this.query.rows = +compUtils.getQueryValue(this, "rows", "20");
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    let param: GetListCountQueryParam = {};
    param.keyword = this.query.keyword;
    routerUtils.putQueryValue(this, param);

    this.loading = true;
    try {
      this.query.total = await contentApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContent() {
    let param: GetListQueryParam = {};
    param.keyword = this.query.keyword;
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, param);

    param.page = param.page - 1;

    this.loading = true;
    try {
      this.bizData.contents = await contentApi.getList(param);
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleAddClick() {
    this.$router.push(`/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit`);
  }

  async handleArchivedClick(record: TableContent, archived: boolean) {
    let param: WmsProductBatchArchivedVo = {};
    param.ids = [record.id!];
    param.archived = archived;
    await contentApi.batchArchived(param);
    this.loadContent();
  }

  async handleModifyClick(record: TableContent, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

