<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.wms-container") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) {{ $t("common.base.add") }}
        el-form-item(prop="warehouseId", label="仓库")
          el-select(v-model="query.warehouseId", @change="handleSearchClick")
            el-option(
              v-for="option in oneLoadParam.warehouses",
              :key="option.id",
              :label="option.name",
              :value="option.id"
            )
        el-form-item
          el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      default-expand-all,
      row-key="id",
      header-cell-class-name="table-header",
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }",
      v-loading="loading > 0"
    )
      //- el-table-column(type="selection", width="55", align="center")
      el-table-column(prop="name", label="名称", width="250")
      el-table-column(prop="no", label="货架编号")
      el-table-column(label="类型", width="100")
        template(#default="scope") {{ scope.row.storage ? "货架" : "区域" }}
      el-table-column(prop="warehouseName", label="所属仓库")
      el-table-column(prop="numMax", label="最大容量", width="100")
        template(#default="scope") {{ scope.row.storage ? scope.row.numMax : "-" }}
      el-table-column(prop="numMax", label="已使用容量", width="100")
        template(#default="scope") {{ scope.row.storage ? scope.row.numUsed : "-" }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")
      el-table-column(label="操作", width="300", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-edit",
            @click="handleAddChildrenClick(scope.row)",
            v-if="!scope.row.storage"
          ) 添加子货架
          el-button(
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-popconfirm(
            title="确认要删除数据吗？",
            @confirm="handleDeleteClick(scope.row)"
          )
            el-button.red(slot="reference", type="text", icon="el-icon-delete") {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import warehouseApi, {
  WmsMgWarehouseGetAllListQueryParam,
  WmsWarehouse,
} from "~/api/wms/mg/warehouseApi";
import contentApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
} from "~/api/wms/mg/warehouse/containerApi";
import arrayUtils from "~/utils/arrayUtils";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

type Content = WmsWarehouseContainer & {
  children?: Array<Content>;
};

const urlPre = "/wms/container";

@Component({
  name: "role-wms-container",
})
export default class WmsContainerIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    warehouseId: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
    warehouses: <WmsWarehouse[]>[],
  };
  bizData = {
    contents: <Content[]>[],
  };

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...

    {
      let param: WmsMgWarehouseGetAllListQueryParam = {};
      param.state = ["OK"];
      let warehouses = await warehouseApi.getAllList(param);
      this.oneLoadParam.warehouses = warehouses;
      if (!this.query.warehouseId) {
        if (warehouses.length > 0) {
          this.query.warehouseId = warehouses[0].id!;
        }
      }
    }
  }

  async initDetail() {
    this.query.warehouseId = compUtils.getQueryValue(this, "warehouse_id", "");
    if (!this.query.warehouseId && this.oneLoadParam.warehouses.length > 0) {
      this.query.warehouseId = this.oneLoadParam.warehouses[0].id!;
    }

    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    await this.loadContent();
  }

  async loadContent() {
    routerUtils.putQueryValue(this, { warehouse_id: this.query.warehouseId });
    let param: WmsMgWarehouseContainerGetAllListQueryParam = { ...this.query };
    this.loading++;
    try {
      let res = await contentApi.getAllList(param);
      let contents = arrayUtils.marginArray(
        res,
        "warehouseId",
        this.oneLoadParam.warehouses,
        "id",
        (p, b) => ({ ...p, warehouseName: b ? b.name : "" })
      );
      let root = arrayUtils.array2tree(contents, "id", "idParent", "children");
      this.bizData.contents = root;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleSearchClick() {
    await this.loadContent();
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}${urlPre}/edit?warehouse_id=${this.query.warehouseId}`
    );
  }

  async handleModifyClick(record: Content) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }`
    );
  }

  async handleAddChildrenClick(record: Content) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id_parent=${
        record.id
      }&warehouse_id=${record.warehouseId}`
    );
  }

  async handleDeleteClick(record: Content) {
    this.loading++;
    try {
      // await mallCategoryApi.delete(record.id!);
      this.$notify.success("删除数据成功过");
      await this.loadContent();
    } finally {
      this.loading--;
    }
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}
</style>
