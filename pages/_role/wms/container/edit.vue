<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.wms-container") }}
      el-breadcrumb-item {{ this.query.id ? $t("pages.cms.subject.edit.title_edit") : $t("pages.cms.subject.edit.title_add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="150px",
        size="small"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name")
        el-form-item(
          label="货架编号",
          prop="no",
          :rules="[$rule.required(null, '货架编号不能为空')]",
          required
        )
          el-input(v-model="formData.no")
        el-form-item(
          label="所属仓库",
          prop="warehouseId",
          :rules="[$rule.required(null, '所属仓库不能为空')]",
          required
        )
          el-select(v-model="formData.warehouseId", @change="warehouseChange")
            el-option(
              v-for="option in oneLoadParam.warehouses",
              :key="option.id",
              :label="option.name",
              :value="option.id"
            )
        el-form-item(
          label="所属上一级货架",
          prop="idParent",
          :rules="[$rule.required(null, '货架节点不能为空')]",
          required
        )
          el-cascader(
            v-model="formData.idParent",
            :options="containerTree",
            :props="{ checkStrictly: true, label: 'label', value: 'id' }",
            clearable
          )
        el-form-item(prop="storage", label="类型", required)
          //- el-switch(
          //-   v-model="formData.storage",
          //-   active-text="货架",
          //-   inactive-text="区域",
          //-   active-color="green",
          //-   inactive-color="gray"
          //- )
          el-radio-group(
            v-model="formData.storage",
            :disabled="query.id ? true : false"
          )
            el-radio(:label="false") 区域
            el-radio(:label="true") 货架
        el-form-item(
          v-if="formData.storage",
          label="保存条件",
          prop="environments"
        )
          el-select(v-model="formData.environments", multiple)
            el-option(
              v-for="option in $dic.getOptions('wms.warehouse.container.env')",
              :key="option.value",
              :label="option.label",
              :value="option.value"
            )
        el-form-item(
          label="最大容量",
          :rules="[$rule.required(null, '最大容量不能为空')]",
          required,
          v-if="formData.storage"
        )
          el-input-number(
            v-model="formData.numMax",
            :min="query.id ? formData.numUsed : 0"
          )
          el-tag.mgl10 已使用: {{ formData.numUsed }}
        el-form-item(label="备注")
          el-input(v-model="formData.remark", type="textarea", :rows="2")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";

import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import contentApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
  WmsWarehouseContainerAddVo,
} from "~/api/wms/mg/warehouse/containerApi";

import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import sortableUtils from "~/utils/sortableUtils";

type FormData = Omit<WmsWarehouseContainer, "idParent"> & {
  idParent: Array<string>;
  environments: string[];
};

type ContainerTreeNode = WmsWarehouseContainer & {
  label?: string;
  children?: ContainerTreeNode[];
};

@Component({
  name: "role-wms-container-edit",
})
export default class WmsContainerEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    idParent: "0",
    warehouseId: "",
  };
  formData: FormData = {
    no: "",
    name: "",
    warehouseId: "",
    remark: "",
    idParent: ["0"],
    environments: [],
    storage: false,
    numMax: 100,
    numUsed: 0,
  };
  defContainer = {
    id: "0",
    name: "（根货架）",
    label: "（根货架）",
  };
  containerTree: Array<ContainerTreeNode> = [{ ...this.defContainer }];
  oneLoadParam = {
    inited: false,
    // ...param
    warehouses: <WmsWarehouse[]>[],
    id2containers: <Map<string, ContainerTreeNode> | null>null,
    continers: <ContainerTreeNode[] | null>null,
  };

  async created() {
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.idParent = compUtils.getQueryValue(this, "id_parent", "0");
    this.query.warehouseId = compUtils.getQueryValue(this, "warehouse_id", "");
    this.init();
  }

  async mounted() {
    // this.initSortable();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }

    {
      let param: WmsMgWarehouseGetAllListQueryParam = {};
      param.state = ["OK"];
      let warehouses = await warehouseApi.getAllList(param);
      this.oneLoadParam.warehouses = warehouses;
      if (!this.query.warehouseId) {
        if (warehouses.length > 0) {
          this.query.warehouseId = warehouses[0].id!;
        }
      }
    }
    {
      let params: WmsMgWarehouseContainerGetAllListQueryParam = {
        state: ["OK"],
      };
      let containers: ContainerTreeNode[] = await contentApi.getAllList(params);
      containers.map((x) => (x.label = `${x.name}(${x.no})`));
      this.oneLoadParam.id2containers = arrayUtils.array2map(containers, "id");
      this.oneLoadParam.continers = containers;
    }

    this.oneLoadParam.inited = true;
  }

  async initDetail() {
    let form = this.formData;
    if (this.query.id) {
      let detail = await contentApi.get(this.query.id);
      form = { ...(<any>detail) };
      this.formData = form;
      await this.initParam();
      let ids = arrayUtils
        .deepFindByMap(
          this.oneLoadParam.id2containers!,
          "idParent",
          detail.idParent
        )
        .reverse()
        .map((x) => x.id!);
      form.idParent = ids.length <= 0 ? ["0"] : ids;

      form.environments = form.environment ? form.environment.split(",") : [];

      this.formData.warehouseId = form.warehouseId;
      await this.initParam();
    } else {
      form.environments = [];
      if (this.query.idParent) {
        form.idParent = arrayUtils
          .deepFindByMap(
            this.oneLoadParam.id2containers!,
            "idParent",
            this.query.idParent
          )
          .reverse()
          .map((x) => x.id!);
      } else {
        form.idParent = ["0"];
      }

      this.formData.warehouseId = this.query.warehouseId;
      await this.initParam();
    }

    this.formData = form;
  }

  async initParam() {
    let containers = this.oneLoadParam.continers!.filter(
      (x) => x.warehouseId == this.formData.warehouseId
    );
    containers = containers.filter((x) => !x.storage); //筛选出货架区域
    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    this.containerTree = [this.defContainer, ...containerTree];
  }

  async warehouseChange() {
    await this.initParam();
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: WmsWarehouseContainerAddVo = {
        ...(<Omit<FormData, "idParent">>data),
      };
      body.idParent = data.idParent[data.idParent.length - 1];
      body.environment = data.environments ? data.environments.join(",") : "";
      // body.propertyStructs = body.propertyStructs!.filter((x: any) => !x.def);
      if (this.query.id) {
        // 更新
        await contentApi.modify(this.query.id, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await contentApi.add(body);
        this.query.id = detail.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.back();
    // this.$router.push(
    //   `/${compUtils.getQueryValue(this, "role", "")}/wms/container`
    // );
  }
}
</script>

<style lang="scss" scoped>
.property-structs-table::v-deep table tbody .cell {
  min-height: 60px;
}
</style>
