<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.wms-inventory") }}
  .container
    //- 表头
    .handle-box
      el-form(
        ref="queryForm",
        :model="query",
        :inline="true",
        v-loading="!oneLoadParam.inited",
        size="small"
      )
        el-form-item(prop="add")
          el-button(
            type="primary",
            icon="el-icon-plus",
            @click="handleAddClick"
          ) 新增盘点
        //- el-form-item(prop="warehouseId", label="仓库")
        //-   el-select(v-model="query.warehouseId", @change="handleSearchClick")
        //-     el-option(
        //-       v-for="option in oneLoadParam.warehouses",
        //-       :key="option.id",
        //-       :label="option.name",
        //-       :value="option.id"
        //-     )
        //- el-form-item(prop="keyword", label="关键词")
        //-   el-input(
        //-     v-model="query.keyword",
        //-     placeholder="关键词",
        //-     @keyup.enter.native="handleSearchClick"
        //-   )
        //- el-form-item
        //-   el-button(type="primary", @click="handleSearchClick") 搜索
    //- 表单内容
    el-table.table(
      :data="bizData.contents",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="no", label="盘点编号")
      el-table-column(prop="name", label="盘点名称")
      el-table-column(prop="state", label="盘点状态", width="120")
        template(#default="scope")
          el-tag(
            :type="$dic.getFieldPropValue('wms.inventory.state', scope.row.state, 'tag-type', '')"
          ) {{ $dic.getFieldValue("wms.inventory.state", scope.row.state, "") }}
      el-table-column(prop="containerId", label="盘点范围", width="400")
        template(#default="scope")
          p(v-for="(cs, i) in getContainerById(scope.row)", :key="i") {{ cs }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="250", align="center")
        template(#default="scope")
          el-button(
            type="text",
            @click="handleOpenSelectProductClick(scope.row)",
            v-if="scope.row.state !== 'SUBMIT' && scope.row.state !== 'CANCEL'"
          ) 查看盘点商品
          el-button(type="text", @click="handleModifyClick(scope.row, false)") {{ $t("common.base.detail") }}
          //- el-button(
          //-   v-if="!scope.row.up && !scope.row.archived",
          //-   type="text",
          //-   @click="handleModifyClick(scope.row)"
          //- ) {{ $t("common.base.modify") }}
          el-button(
            v-if="scope.row.state == 'MANAGE_AWAIT'",
            type="text",
            @click="handleAuditClick(scope.row, 'first')"
          ) {{ $t("common.base.audit") }}
          el-popconfirm(
            title="确认开始盘点？",
            @confirm="handleStartClick(scope.row)",
            v-if="scope.row.state == 'BEGIN_AWAIT'"
          )
            el-button(slot="reference", type="text", style="margin: 0 10px") 开始盘点
          el-popconfirm(
            title="确认取消盘点？",
            @confirm="handleCancelClick(scope.row)",
            v-if="scope.row.state !== 'FINISH' && scope.row.state !== 'CANCEL'"
          )
            el-button(
              slot="reference",
              type="text",
              style="margin: 0 10px; color: #f56c6c"
            ) 取消盘点
          el-button(
            v-if="scope.row.state == 'INVENTORY_RUNING' || scope.row.state == 'REJECT'",
            type="text",
            @click="handleInputClick(scope.row)"
          ) 盘点数据录入
          el-button(
            v-if="scope.row.state == 'MANAGE_FINISH_AWAIT'",
            type="text",
            @click="handleAuditClick(scope.row, 'final')"
          ) 终审
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
  //- 商品编辑界面
  el-dialog(
    :visible="bizData.selectProduct.visible",
    title="盘点商品",
    :before-close="() => (bizData.selectProduct.visible = false)",
    width="80%"
  )
    wms-inventory-comps-select-product-window(
      :inventoryId="bizData.selectProduct.id",
      :edit="false"
    )
      template(#footer)
        //- el-button(type="primary") 添加
        el-button(@click="() => (bizData.selectProduct.visible = false)") 取消
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { WmsWarehouseBox } from "~/api/wms/mg/warehouse/boxApi";
import containerApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
} from "~/api/wms/mg/warehouse/containerApi";
import warehouseApi, {
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import contentApi, {
  WmsInventory,
  WmsMgInventoryGetListQueryParam,
} from "~/api/wms/mg/inventoryApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import WmsInventoryCompsSelectProductWindow from "./comps/select-productbox-window.vue";

type TableContent = WmsInventory;
type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];
  };
const urlPre = "/wms/inventory";

@Component({
  name: "role-wms-inventory",
  components: {
    WmsInventoryCompsSelectProductWindow,
  },
})
export default class WmsInventoryIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
    containerTree: <ContainerTree[]>[],
    id2container: <Map<string, ContainerTree> | null>null,
  };
  bizData = {
    contents: <Array<TableContent>>[],
    selectProduct: {
      visible: false,
      edit: true,
      id: "",
    },
  };

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    let warehouseId = "";

    let wparam: WmsMgWarehouseGetAllListQueryParam = {};
    wparam.state = ["OK"];
    let warehouses = await warehouseApi.getAllList(wparam);

    let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
    cparam.state = ["OK"];
    cparam.warehouseId = warehouseId;
    let containers: ContainerTree[] = await containerApi.getAllList(cparam);
    containers.map((x) => {
      if (!x.idParent || x.idParent == "0") {
        x.idParent = x.warehouseId;
      }
    });
    warehouses.forEach((x) => {
      let wc: ContainerTree = {};
      wc.id = x.id;
      wc.idParent = "0";
      wc.name = x.name;
      wc.storage = false;
      wc.no = "";
      wc.state = x.state;
      wc.createTime = x.createTime;
      containers.push(wc);
    });

    this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");

    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    this.oneLoadParam.containerTree = containerTree;
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    this.loading++;
    try {
      this.query.total = await contentApi.getListCount();
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async loadContent() {
    let param: WmsMgInventoryGetListQueryParam = {};
    param.page = this.query.page - 1 ?? 0;
    param.rows = this.query.rows ?? 20;
    this.loading++;
    try {
      this.bizData.contents = await contentApi.getList(param);
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit`
    );
  }
  //修改
  async handleModifyClick(record: TableContent, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }
  //数据录入
  async handleInputClick(record: TableContent, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }&edit=false&input=true`
    );
  }

  //审核
  async handleAuditClick(record: TableContent, type: string) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}${urlPre}/edit?id=${
        record.id
      }&audit=true&type=${type}`
    );
  }
  //开始盘点
  async handleStartClick(record: TableContent) {
    await contentApi.begin(record.id!);
    this.$notify.success(<any>{ message: "开始盘点成功！" });
    await this.loadContentCount();
  }
  //取消盘点
  async handleCancelClick(record: TableContent) {
    await contentApi.cancel(record.id!);
    this.$notify.success(<any>{ message: "取消盘点成功！" });
    await this.loadContentCount();
  }

  //查看盘点商品
  handleOpenSelectProductClick(record: TableContent) {
    this.bizData.selectProduct.id = record.id!;
    this.bizData.selectProduct.edit = false;
    this.bizData.selectProduct.visible = true;
  }

  //补充商品所属货架信息
  getContainerById(wbox: TableContent) {
    console.log("wbox", wbox);

    if (!this.oneLoadParam.id2container) {
      return "";
    }
    let warehouseContainerIds = wbox.warehouseContainerId?.split(",");
    let warehouseContainers: Array<string> = [];
    warehouseContainerIds?.forEach((x) => {
      let cs = arrayUtils.deepFindByMap(
        this.oneLoadParam.id2container!,
        "idParent",
        x!
      );
      warehouseContainers.push(
        cs
          .reverse()
          .map((x) => x.name)
          .join(" > ")
      );
    });

    return warehouseContainers;
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;

  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>
