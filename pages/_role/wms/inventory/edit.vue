<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.wms-inventory") }}
      el-breadcrumb-item {{ this.query.id ? $t("pages.cms.subject.edit.title_edit") : $t("pages.cms.subject.edit.title_add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="150px",
        size="small"
      )
        el-form-item(
          label="盘点名称",
          prop="name",
          :rules="[$rule.required(null, '盘点名称不能为空')]",
          required
        )
          el-input(
            v-model="formData.name",
            :disabled="!query.edit || query.audit"
          )
        el-form-item(label="盘点范围：", required)
          el-table.table(
            :data="formData.boxs",
            border,
            ref="multipleTable",
            header-cell-class-name="table-header",
            v-loading="loading"
          )
            el-table-column(prop="warehouseContainerId", label="盘点区域 / 货架")
              template(#default="scope")
                el-form-item(width="0", prop="warehouseBoxIds")
                  el-cascader(
                    v-model="scope.row.warehouseBoxIds",
                    :options="oneLoadParam.containerTree",
                    :props="{ checkStrictly: true, label: 'name', value: 'id' }",
                    clearable,
                    :disabled="!query.edit || query.audit",
                    style="width: 100%",
                    @change="() => { $rule.formCheck(this, 'form'); }"
                  )
                    template(slot-scope="{ node, data }")
                      span {{ data.name }}
                      span(v-if="data.productBoxId") ({{ data.numHas }})
            el-table-column(
              label="操作",
              width="200",
              align="center",
              v-if="query.edit && !query.audit"
            )
              template(#default="boxscope")
                el-popconfirm(
                  title="确认要删除数据吗？",
                  @confirm="handleBoxRemoveClick(boxscope.$index)"
                )
                  el-button(
                    slot="reference",
                    type="text",
                    icon="el-icon-tickets"
                  ) 移除
          el-button(
            v-if="query.edit && !query.audit",
            type="text",
            @click="handleAddBoxClick"
          ) 添加盘点区域
        el-form-item(label="备注")
          el-input(
            v-model="formData.remark",
            type="textarea",
            :rows="2",
            :disabled="!query.edit || query.audit"
          )
        template(v-if="query.input || formData.products.length")
          el-form-item(
            style="margin-top: 30px; height: 1px; background-color: #f1f1f1"
          )
          el-form-item(label="盘点数据录入", style="font-weight: bold")

          el-form-item(label="盘点结果：", required)
            el-radio-group(
              v-model="inventoryResultState",
              :disabled="!query.input"
            )
              el-radio(:label="true") 正常
              el-radio(:label="false") 异常
          el-form-item(
            label="盘点异常商品：",
            :rules="[$rule.required(null, '批次号不能为空')]",
            required,
            v-if="!inventoryResultState || formData.products.length"
          )
            el-button.add-product-btn(
              v-if="query.input",
              type="text",
              @click="handleOpenSelectProductClick"
            ) 选择盘点商品
            el-table.table(
              :data="formData.products",
              border,
              ref="multipleTable",
              header-cell-class-name="table-header",
              v-loading="loading"
            )
              el-table-column(
                prop="pd.name",
                label="名称(sku)",
                align="center",
                width="200"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`products.${scope.$index}.pd.name`"
                  ) {{ $fieldUtils.getFieldValue(scope.row, "pd.name", scope.row.productSpaceId) }} ({{ $fieldUtils.getFieldValue(scope.row, "pd.sku", scope.row.productSpaceId) }})
              el-table-column(
                label="批次号",
                prop="pbox.lotNum",
                width="200",
                align="center"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`products.${scope.$index}.pbox.lotNum`"
                  ) {{ scope.row.pbox.lotNum }}
              el-table-column(prop="warehouseBoxId", label="存储位置", width="300")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`products.${scope.$index}.warehouseBoxId`"
                  ) {{ getContainerById(scope.row) }}
              el-table-column(prop="state", label="盘点状态", width="100")
                template(#default="scope")
                  el-tag(
                    :type="$dic.getFieldPropValue('wms.inventory.product.state', scope.row.state, 'tag-type', '')"
                  ) {{ $dic.getFieldValue("wms.inventory.product.state", scope.row.state, "") }}
              el-table-column(
                label="库存数量",
                prop="warehouseNumStorage",
                width="100",
                align="center"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`products.${scope.$index}.warehouseNumStorage`"
                  ) {{ scope.row.warehouseNumStorage }}
              el-table-column(
                label="实际数量",
                prop="numReal",
                :render-header="addRedStar",
                width="200",
                align="center"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`products.${scope.$index}.numReal`",
                    :rules="[$rule.required(null, '数量不能为空')]"
                  )
                    el-input-number(
                      :step="1",
                      :min="1",
                      v-model="scope.row.numReal",
                      :disabled="!query.input"
                    )
              el-table-column(
                label="差异数量",
                prop="numDiffer",
                width="100",
                align="center"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`products.${scope.$index}.numDiffer`"
                  ) {{ scope.row.warehouseNumStorage ? scope.row.numReal - scope.row.warehouseNumStorage : 0 }}

              el-table-column(prop="remark", label="备注", align="center")
                template(#default="scope")
                  el-input(
                    v-model="scope.row.remark",
                    type="textarea",
                    :rows="2",
                    :disabled="!query.input"
                  )
              el-table-column(
                label="操作",
                width="100",
                align="center",
                v-if="query.input"
              )
                template(#default="scope")
                  el-popconfirm(
                    title="确认要删除数据吗？",
                    @confirm="handleProductRemoveClick(scope.row, scope.$index)"
                  )
                    el-button(
                      slot="reference",
                      type="text",
                      icon="el-icon-tickets"
                    ) 移除

        el-form-item(v-if="!query.audit")
          el-button(
            type="primary",
            v-if="query.edit || query.input",
            @click="submit()"
          ) {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
        AuditBtn(v-if="query.audit", :id="query.id", @goBack="goBack")
      //- 商品编辑界面
      el-dialog(
        :visible="bizData.selectProduct.visible",
        title="选择商品",
        :before-close="() => (bizData.selectProduct.visible = false)",
        width="80%"
      )
        wms-inventory-comps-select-productbox-window(
          :hasIds="hasProductSpaceIds",
          :inventoryId="query.id",
          :edit="true",
          :initState="[]",
          @add="handleSelectProductAdd"
        )
          template(#footer)
            //- el-button(type="primary") 添加
            el-button(@click="() => (bizData.selectProduct.visible = false)") 取消
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";

import warehouseApi, {
  WmsWarehouse,
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import contentApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
  WmsWarehouseContainerAddVo,
} from "~/api/wms/mg/warehouse/containerApi";
import inventoryProductApi, {
  WmsInventoryProduct,
  WmsInventoryProductUpdateProductVo,
  WmsInventoryProductUpdateVo,
  WmsMgInventoryProductGetListQueryParam,
} from "~/api/wms/mg/inventory/productApi";
import inventoryApi, { WmsInventoryAddVo } from "~/api/wms/mg/inventoryApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import sortableUtils from "~/utils/sortableUtils";
import commonFunUtils from "~/utils/commonFunUtils";
import WmsInventoryCompsSelectProductboxWindow from "./comps/select-productbox-window.vue";
import { WmsProductSpace } from "~/api/wms/mg/product/spaceApi";
import { WmsTransactionProduct } from "~/api/wms/mg/transactionApi";
import { WmsWarehouseBox } from "~/api/wms/mg/warehouse/boxApi";
import containerApi from "~/api/wms/mg/warehouse/containerApi";
import { WmsProductBox } from "~/api/wms/mg/product/boxApi";
import { WmsTransactionOutboundPutBoxBoxVo } from "~/api/wms/mg/transaction/outboundApi";
import AuditBtn from "./comps/audit-btn.vue";
import productHelper, { WmsBizTmpl } from "~/helper/wms/productHelper";

type FormData = WmsWarehouseContainer &
  WmsInventoryAddVo & {
    products: InventoryProduct[];
    boxs: BoxType[];
  };

type ContainerTreeNode = WmsWarehouseContainer & {
  children?: ContainerTreeNode[];
};

type BoxType = WmsTransactionOutboundPutBoxBoxVo & {
  warehouseBoxIds?: string[];
};

type InventoryProduct = WmsInventoryProduct &
  WmsBizTmpl &
  WmsInventoryProductUpdateProductVo;

// type InventoryProduct = WmsInventoryProduct & WmsInventoryProductUpdateProductVo & {
//   pd?: WmsProductSpace;
//   box?: WmsProductBox;
//   // boxs?: BoxType[];
//   containerTree?: ContainerTree[];
// };

type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];
  };
@Component({
  name: "role-wms-inventory-edit",
  components: {
    WmsInventoryCompsSelectProductboxWindow,
    AuditBtn,
  },
})
export default class WmsInventoryEdit extends mixins(BaseVue) {
  loading = true;
  selectProductVisible = false;
  inventoryResultState: boolean = true;
  query = {
    id: "",
    idParent: "0",
    warehouseId: "",
    edit: true,
    audit: false,
    input: false,
    type: "first",
  };
  formData: FormData = {
    name: "",
    remark: "",
    products: [],
    boxs: [],
  };
  defContainer = {
    id: "0",
    name: "（根货架）",
  };
  containerTree: Array<ContainerTreeNode> = [{ ...this.defContainer }];
  oneLoadParam = {
    inited: false,
    // ...param
    warehouses: <WmsWarehouse[]>[],
    containerTree: <ContainerTree[]>[],
    id2container: <Map<string, ContainerTree> | null>null,
  };
  bizData = {
    selectProduct: {
      visible: false,
    },
    id2space: <Map<string, WmsProductSpace>>new Map(),
    id2pbox: <Map<string, WmsProductBox>>new Map(),
    id2wbox: <Map<string, WmsWarehouseBox>>new Map(),
  };
  id2containers: Map<string, ContainerTreeNode> | null = null;

  async created() {
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.idParent = compUtils.getQueryValue(this, "id_parent", "0");
    this.query.warehouseId = compUtils.getQueryValue(this, "warehouse_id", "");
    this.query.type = compUtils.getQueryValue(this, "type", "first");
    this.query.edit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "edit", "true")
    );
    this.query.audit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "audit", "false")
    );
    this.query.input = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "input", "false")
    );
    console.log("query.id", this.query.id);

    await this.initOneLoad();
    await this.initParam();
    await this.initDetail();
    this.loading = false;
  }

  async mounted() {
    // this.initSortable();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }
  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    let warehouseId = "";

    let wparam: WmsMgWarehouseGetAllListQueryParam = {};
    wparam.state = ["OK"];
    let warehouses = await warehouseApi.getAllList(wparam);

    let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
    cparam.state = ["OK"];
    cparam.warehouseId = warehouseId;
    let containers: ContainerTree[] = await containerApi.getAllList(cparam);
    containers.map((x) => {
      if (!x.idParent || x.idParent == "0") {
        x.idParent = x.warehouseId;
      }
    });
    warehouses.forEach((x) => {
      let wc: ContainerTree = {};
      wc.id = x.id;
      wc.idParent = "0";
      wc.name = x.name;
      wc.storage = false;
      wc.no = "";
      wc.state = x.state;
      wc.createTime = x.createTime;
      containers.push(wc);
    });

    this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");

    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    this.oneLoadParam.containerTree = containerTree;

    this.oneLoadParam.inited = true;
    // ...
  }
  async initParam() {
    let params: WmsMgWarehouseContainerGetAllListQueryParam = {
      warehouseId: this.formData.warehouseId,
      // state:[]
    };
    let containers = await contentApi.getAllList(params);
    // containers = containers.filter((x) => !x.storage); //筛选出货架区域
    this.id2containers = arrayUtils.array2map(containers, "id");
    let root = arrayUtils.array2tree(containers, "id", "idParent", "children");
    if (this.query.id) {
      const clearId = (arr: Array<ContainerTreeNode>, id: string) => {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].id == id) {
            arr.splice(i, 1);
            i--;
            continue;
          }
          if (arr[i].children) {
            clearId(arr[i].children!, id);
          }
        }
      };
      clearId(root, this.query.id);
    }
    this.containerTree = [this.defContainer, ...root];
  }

  async initDetail() {
    let form = this.formData;
    if (this.query.id) {
      let detail = await inventoryApi.get(this.query.id);
      form = { ...(<any>detail) };

      let id2containerStorage = this.oneLoadParam.id2container!;
      let boxs: Array<object> = [];
      form.warehouseContainerIds?.forEach((x) => {
        let cs = arrayUtils.deepFindByMap(id2containerStorage, "idParent", x);
        let warehouseBoxIds = cs.reverse().map((x) => x.id!);
        boxs.push({ warehouseBoxIds });
      });

      form.boxs = boxs;

      let param: WmsMgInventoryProductGetListQueryParam = {};
      // param.keyword = this.query.keyword;
      // param.containerId = this.query.id;
      param.page = 0;
      param.rows = 200;
      param.inventory_id = this.query.id;
      param.state = ["SURPLUS", "SHORTAGE"]; //查询盘盈、盘亏状态商品
      let contents: InventoryProduct[] = await inventoryProductApi.getList(
        param
      );
      await productHelper.bizPutMsg(contents, this.bizData.id2wbox);
      await productHelper.warehouseBoxPutMsg(contents, this.bizData.id2pbox);
      await productHelper.productBoxPutMsg(contents, this.bizData.id2space);
      form.products = contents;

      this.inventoryResultState = form.products.length > 0 ? false : true;
    }

    this.formData = { ...this.formData, ...form };
  }

  async warehouseChange() {
    await this.initParam();
  }

  async handleAddBoxClick() {
    console.log("this.formData.boxs", this.formData.boxs);

    this.formData.boxs!.push({
      warehouseBoxIds: [],
      // num: 1,
      // remark: "",
    });
    this.$rule.formCheck(this, "form");
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }

      let containerIds: Array<string> = [];
      this.formData.boxs.forEach((x) => {
        containerIds.push(x.warehouseBoxIds![x.warehouseBoxIds!.length - 1]);
      });
      this.formData.warehouseContainerIds = containerIds;

      if (this.query.input) {
        // 更新商品数据
        let body: WmsInventoryProductUpdateVo = {};
        body.products = this.formData.products;
        await inventoryProductApi.putProductMsg(
          { inventory_id: this.query.id },
          body
        );
        this.$notify.success("盘点商品数据更新成功");
      } else {
        // 新增
        let body: WmsInventoryAddVo = this.formData;
        let detail = await inventoryApi.add(body);
        this.query.id = detail.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }

      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    // this.$router.push(
    //   `/${compUtils.getQueryValue(this, "role", "")}/wms/inventory`
    // );
    this.$router.back();
  }

  handleOpenSelectProductClick() {
    this.bizData.selectProduct.visible = true;
  }

  handleSelectProductAdd(pd: InventoryProduct) {
    console.log("add", pd);

    this.formData.products!.push({
      pd: pd.pd,
      pbox: pd.pbox,
      wbox: pd.wbox,
      id: pd.id,
      state: pd.state,
      warehouseNumStorage: pd.warehouseNumStorage,
      numReal: pd.numReal,
      remark: "",
    });
    this.bizData.selectProduct.visible = false;
  }

  async handleProductRemoveClick(pd: InventoryProduct, idx: number) {
    this.formData.products!.splice(idx, 1);
  }

  async handleBoxRemoveClick(idx: number) {
    this.formData.boxs!.splice(idx, 1);
  }

  getContainerById(pd: InventoryProduct) {
    if (!this.oneLoadParam.id2container) {
      return "";
    }

    let cs = arrayUtils.deepFindByMap(
      this.oneLoadParam.id2container,
      "idParent",
      pd.wbox!.containerId!
    );
    return cs
      .reverse()
      .map((x) => x.name)
      .join(" > ");
  }
  get hasProductSpaceIds() {
    return this.formData.products?.map((x) => x.id!);
  }
}
</script>

<style lang="scss" scoped>
.property-structs-table::v-deep table tbody .cell {
  min-height: 60px;
}
</style>
