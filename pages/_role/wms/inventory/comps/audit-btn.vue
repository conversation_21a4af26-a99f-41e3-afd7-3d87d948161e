<template lang="pug">
div(v-loading="loading")
  el-form-item(label="审核原因")
    el-input(v-model="remark", type="textarea", :rows="2")
  el-form-item
    el-button(type="primary", @click="handleAuditClick(true)") 审核通过
    el-button(type="danger", @click="handleAuditClick(false)") 审核驳回
    el-button(@click="goBack()") 取 消
</template>
  
<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import inventoryApi, { WmsInventoryAuditVo } from "~/api/wms/mg/inventoryApi";

import { BaseVue } from "~/model/vue";

@Component({
  name: "role-wms-transaction-inbound-edit",
  props: ["id"],
})
export default class WmsTransactionInboundEdit extends mixins(BaseVue) {
  loading = false;
  remark = "";

  get _id() {
    return this.$props.id ? this.$props.id : "";
  }

  async handleAuditClick(audit: boolean) {
    let param: WmsInventoryAuditVo = {};
    param.audit = audit;
    param.remark = this.remark;
    if (!param.audit && !param.remark) {
      this.$notify.error("驳回必须填写驳回理由。");
      return;
    }

    this.loading = true;
    try {
      await inventoryApi.audit(this._id, param);

      this.$msg.refreshBpmTodoCount();
      this.goBack();
    } finally {
      this.loading = false;
    }
  }

  goBack() {
    this.$emit("goBack");
  }
}
</script>
  
<style lang="scss" scoped>
.add-product-btn {
  font-size: 14px;
}
</style>
