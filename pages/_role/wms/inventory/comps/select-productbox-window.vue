<template lang="pug">
div
  //- 表头
  .handle-box
    el-form(
      ref="queryForm",
      :model="query",
      :inline="true",
      v-loading="!oneLoadParam.inited",
      size="small",
      @submit.native.prevent="() => handleSearchClick()"
    )
      el-form-item(prop="containerId", label="仓库位置")
        el-cascader(
          v-model="query.containerId",
          :options="oneLoadParam.containerTree",
          :props="{ checkStrictly: true, emitPath: false, label: 'name', value: 'id' }",
          clearable,
          style="min-width: 300px",
          @keyup.enter.native="handleSearchClick",
          @change="handleSearchClick"
        )
      el-form-item(prop="state", label="盘点状态")
        el-select(v-model="query.state", multiple, @change="handleSearchClick")
          el-option(
            v-for="item in $dic.getFields('wms.inventory.product.state')",
            :key="item.code",
            :label="item.label",
            :value="item.code"
          )
      el-form-item(prop="keyword", label="关键词")
        el-input(
          v-model="query.keyword",
          placeholder="关键词",
          @keyup.enter.native="handleSearchClick"
        )
      el-form-item
        el-button(type="primary", @click="handleSearchClick") 搜索
  //- 表单内容
  el-table.table(
    :data="bizData.contents",
    border,
    ref="multipleTable",
    header-cell-class-name="table-header",
    v-loading="loading"
  )
    el-table-column(prop="pd.name", label="名称", width="150")
    el-table-column(prop="pd.sku", label="SKU", width="100")
    el-table-column(prop="pbox.lotNum", label="批次号")
    el-table-column(prop="state", label="盘点状态", width="90")
      template(#default="scope")
        el-tag(
          :type="$dic.getFieldPropValue('wms.inventory.product.state', scope.row.state, 'tag-type', '')"
        ) {{ $dic.getFieldValue("wms.inventory.product.state", scope.row.state, "") }}
    el-table-column(prop="numReal", label="数量", width="120")
      template(#default="scope")
        span(v-if="scope.row.state == 'WAITE'") {{ scope.row.warehouseNumStorage }}
        span(v-else-if="scope.row.state != 'OK'") {{ scope.row.warehouseNumStorage }} -> {{ scope.row.numReal }}
        span(v-else) {{ scope.row.numReal }}
    //- el-table-column(prop="remark", label="备注")
    el-table-column(prop="warehouseBoxId", label="存储位置", width="400")
      template(#default="scope")
        p {{ getContainerById(scope.row) }}
    el-table-column(label="操作", width="250", align="center", v-if="_edit")
      template(#default="scope")
        el-button(
          v-if="_hasIds.indexOf(scope.row.id) < 0",
          type="text",
          icon="el-icon-tickets",
          @click="handleAddClick(scope.row)"
        ) 添加到列表
        div(v-else) 已添加
  .y-footer
    //- 分页信息
    .pagination
      el-pagination(
        background,
        :page-sizes="[5, 10, 20, 50, 100]",
        layout="total, sizes, prev, pager, next",
        :current-page="query.page",
        :page-size="query.rows",
        :total="query.total",
        @current-change="handlePageChange",
        @size-change="handleSizeChange"
      )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import contentApi, {
  WmsInventoryProduct,
  WmsMgInventoryProductGetListCountQueryParam,
  WmsMgInventoryProductGetListQueryParam,
} from "~/api/wms/mg/inventory/productApi";
import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import { BaseVue } from "~/model/vue";
import { WmsWarehouseBox } from "~/api/wms/mg/warehouse/boxApi";
import containerApi, {
  WmsMgWarehouseContainerGetAllListQueryParam,
  WmsWarehouseContainer,
} from "~/api/wms/mg/warehouse/containerApi";
import warehouseApi, {
  WmsMgWarehouseGetAllListQueryParam,
} from "~/api/wms/mg/warehouseApi";
import arrayUtils from "~/utils/arrayUtils";
import productHelper, { WmsBizTmpl } from "~/helper/wms/productHelper";
import { WmsProductBox } from "~/api/wms/mg/product/boxApi";
import compUtils from "~/utils/compUtils";

type TableContent = WmsInventoryProduct & WmsBizTmpl;

type GetListCountQueryParam = WmsMgInventoryProductGetListCountQueryParam;

type GetListQueryParam = WmsMgInventoryProductGetListQueryParam;
type ContainerTree = WmsWarehouseBox &
  WmsWarehouseContainer & {
    children?: ContainerTree[];
  };
const urlPre = "/wms/product";

@Component({
  name: "role-wms-transaction-comps-select-productbox-window",
  props: ["hasIds", "inventoryId", "edit", "initState"],
})
export default class WmsTransactionCompsSelectProductboxWindow extends mixins(
  BaseVue
) {
  loading = true;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
    containerId: "",
    state: ["SURPLUS", "SHORTAGE"],
  };
  oneLoadParam = {
    inited: false,
    // ...param
    containerTree: <ContainerTree[]>[],
    id2container: <Map<string, ContainerTree> | null>null,
  };
  bizData = {
    contents: <Array<TableContent>>[],
    id2space: <Map<string, WmsProductSpace>>new Map(),
    id2pbox: <Map<string, WmsProductBox>>new Map(),
    id2wbox: <Map<string, WmsWarehouseBox>>new Map(),
  };

  get _hasIds() {
    return this.$props.hasIds ? this.$props.hasIds : [];
  }

  get _edit() {
    return this.$props.edit ? this.$props.edit : false;
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    let warehouseId = "";

    let wparam: WmsMgWarehouseGetAllListQueryParam = {};
    wparam.state = ["OK"];
    let warehouses = await warehouseApi.getAllList(wparam);

    let cparam: WmsMgWarehouseContainerGetAllListQueryParam = {};
    cparam.state = ["OK"];
    cparam.warehouseId = warehouseId;
    let containers: ContainerTree[] = await containerApi.getAllList(cparam);
    containers.map((x) => {
      if (!x.idParent || x.idParent == "0") {
        x.idParent = x.warehouseId;
      }
    });
    warehouses.forEach((x) => {
      let wc: ContainerTree = {};
      wc.id = x.id;
      wc.idParent = "0";
      wc.name = x.name;
      wc.storage = false;
      wc.no = "";
      wc.state = x.state;
      wc.createTime = x.createTime;
      containers.push(wc);
    });

    this.oneLoadParam.id2container = arrayUtils.array2map(containers, "id");

    let containerTree = arrayUtils.array2tree(
      containers,
      "id",
      "idParent",
      "children"
    );
    const clearContainer = (cts: ContainerTree[]): ContainerTree[] => {
      let ncs: ContainerTree[] = [];
      for (let ct of cts) {
        let cns: ContainerTree[] = [];
        if (ct.children) {
          cns = clearContainer(ct.children);
        }
        if (ct.storage) {
          ct.children = undefined;
          ncs.push(ct);
        } else if (cns.length > 0) {
          ct.children = cns;
          ncs.push(ct);
        }
      }
      return ncs;
    };
    containerTree = clearContainer(containerTree);
    this.oneLoadParam.containerTree = containerTree;
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.state = this.$props.initState
      ? this.$props.initState
      : ["SURPLUS", "SHORTAGE"];

    await this.loadContentCount();
  }

  async mounted() {
    await this.init();
  }

  async loadContentCount() {
    let param: GetListCountQueryParam = {};
    param.inventory_id = this.$props.inventoryId;
    param.keyword = this.query.keyword;
    param.state = this.query.state;
    param.state = param.state || [];
    param.containerId = this.query.containerId;
    this.loading = true;
    try {
      this.query.total = await contentApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.contents = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContent() {
    let param: GetListQueryParam = {};
    param.keyword = this.query.keyword;
    param.containerId = this.query.containerId;
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;
    param.state = this.query.state;
    param.state = param.state || [];
    param.inventory_id = this.$props.inventoryId;
    param.page = param.page - 1;

    this.loading = true;
    try {
      let contents: TableContent[] = await contentApi.getList(param);
      await productHelper.bizPutMsg(contents, this.bizData.id2wbox);
      await productHelper.warehouseBoxPutMsg(contents, this.bizData.id2pbox);
      await productHelper.productBoxPutMsg(contents, this.bizData.id2space);

      this.bizData.contents = contents;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 0;
    await this.loadContentCount();
  }

  async handleAddClick(pd: TableContent) {
    this.$emit("add", pd);
  }

  getContainerById(pd: TableContent) {
    if (!this.oneLoadParam.id2container) {
      return "";
    }

    let cs = arrayUtils.deepFindByMap(
      this.oneLoadParam.id2container,
      "idParent",
      pd.wbox!.containerId!
    );
    return cs
      .reverse()
      .map((x) => x.name)
      .join(" > ");
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;

  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>
