<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.mall-brand") }}
  .container
    //- 表头
    .handle-box
      el-button(type="primary", icon="el-icon-plus", @click="handleAddClick") {{ $t("common.base.add") }}
      el-input.handle-input(
        v-model="query.keyword",
        placeholder="关键词",
        @keyup.enter.native="handleSearchClick"
      )
      el-button(
        type="primary",
        icon="el-icon-search",
        @click="handleSearchClick"
      ) 搜索
      //- a.downswagger(:href="swaggerUrl", target="_blank") 下载Swagger文档
    //- 表单内容
    el-table.table(
      :data="bizData.brands",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="logoImg", label="Logo", width="122")
        template(#default="scope")
          el-image(
            :src="getImageUrlOne(scope.row.logoImg)",
            :lazy="true",
            style="width: 100px; height: 100px"
          )
            .image-slot(slot="error")
              i.el-icon-picture-outline(v-if="scope.row.logoImg")
              span(v-else) 暂无图片
      el-table-column(prop="name", label="名称")
      el-table-column(prop="aliasName", label="别称")
      el-table-column(prop="up", label="状态")
        template(#default="scope")
          el-tag(:type="!scope.row.ban ? 'success' : 'danger'") {{ !scope.row.ban ? "启用中" : "禁用" }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-tickets",
            @click="handleModifyClick(scope.row, false)"
          ) {{ $t("common.base.detail") }}
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import mallBrandApi, {
  MallBrand,
  MallMgBrandGetListCountQueryParam,
  MallMgBrandGetListQueryParam,
} from "~/api/mall/mg/brandApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";
import urlUtils from "~/utils/urlUtils";

@Component({
  name: "role-mall-brand",
})
export default class MallBrandIndex extends mixins(BaseVue) {
  loading = true;
  query = {
    rows: 20,
    page: 1,
    total: 0,
    keyword: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    brands: <Array<MallBrand>>[],
  };

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.keyword = routerUtils.getQueryValue(this, "keyword", "");
    this.query.page = +routerUtils.getQueryValue(this, "page", "1");
    this.query.rows = +routerUtils.getQueryValue(this, "rows", "20");
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    let param: MallMgBrandGetListCountQueryParam = {};
    param.keyword = this.query.keyword;
    routerUtils.putQueryValue(this, param);

    this.loading = true;
    try {
      this.query.total = await mallBrandApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.brands = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async loadContent() {
    let param: MallMgBrandGetListQueryParam = {};
    param.keyword = this.query.keyword;
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, param);

    param.page = param.page - 1;

    this.loading = true;
    try {
      this.bizData.brands = await mallBrandApi.getList(param);
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/brand/edit`
    );
  }

  async handleModifyClick(record: MallBrand, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/brand/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}

::v-deep .image-slot {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: #96999f;
  background-color: #f5f7fa;

  span {
    font-size: 12px;
  }
}
</style>

