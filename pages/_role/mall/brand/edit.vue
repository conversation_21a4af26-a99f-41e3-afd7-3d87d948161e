<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.mall-brand") }}
      el-breadcrumb-item {{ this.query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="80px"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name", :disabled="!query.edit")
        el-form-item(label="Logo", prop="logoImg")
          y-upload(
            v-model="formData.logoImg",
            :limit="1",
            type="pic",
            :multiple="true",
            accept="image/*",
            :disabled="!query.edit"
          )
        el-form-item(label="品牌别名", prop="aliasName")
          y-tag(v-model="formData.aliasName", :disabled="!query.edit")
        el-form-item(prop="ban")
          el-switch(
            v-model="formData.ban",
            active-text="禁用",
            inactive-text="启用",
            active-color="red",
            inactive-color="green",
            :disabled="!query.edit"
          )
        el-form-item(label="备注")
          el-input(
            v-model="formData.remark",
            type="textarea",
            :rows="2",
            :disabled="!query.edit"
          )
        el-form-item
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import mallBrandApi, { MallBrandAddVo } from "~/api/mall/mg/brandApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

type FormData = MallBrandAddVo;

@Component({
  name: "role-mall-brand-edit",
})
export default class MallBrandEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    edit: true,
  };
  formData: FormData = {
    name: "",
    aliasName: "",
    remark: "",
    ban: false,
  };

  oneLoadParam = {
    inited: false,
    // ...param
  };

  async created() {
    this.init();
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    this.query.id = routerUtils.getQueryValue(this, "id", "");
    this.query.edit = commonFunUtils.parseBoolean(
      routerUtils.getQueryValue(this, "edit", "true")
    );
    if (this.query.id) {
      let brand = await mallBrandApi.get(this.query.id);
      this.formData = { ...brand };
    }
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: MallBrandAddVo = { ...data };
      if (this.query.id) {
        // 更新
        await mallBrandApi.modify(this.query.id, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await mallBrandApi.add(body);
        this.query.id = detail.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/brand`
    );
  }
}
</script>

<style lang="scss" scoped>
</style>
