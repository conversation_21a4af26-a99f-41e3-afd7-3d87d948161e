<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.mall-mallinfo") }}
  .container
    //- 表头
    .handle-box
      el-button(type="primary", icon="el-icon-plus", @click="handleAddClick") {{ $t("common.base.add") }}
    //- 表单内容
    el-table.table(
      :data="bizData.infos",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="id", label="Code")
      el-table-column(prop="name", label="名称")
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-tickets",
            @click="handleModifyClick(scope.row, false)"
          ) {{ $t("common.base.detail") }}
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import mallInfoApi, { MallInfo } from "~/api/mall/mg/mall/infoApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-mall-mallinfo",
})
export default class MallMallinfo extends mixins(BaseVue) {
  loading = true;
  query = {};
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    infos: <Array<MallInfo>>[],
  };

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    await this.loadContent();
  }

  async loadContent() {
    this.loading = true;
    try {
      this.bizData.infos = await mallInfoApi.getAllList();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/mallinfo/edit`
    );
  }

  async handleModifyClick(record: MallInfo, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/mallinfo/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

