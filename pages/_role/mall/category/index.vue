<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.mall-category") }}
  .container
    //- 表头
    .handle-box
      el-button.mr10(
        type="primary",
        icon="el-icon-plus",
        @click="handleAddClick"
      ) {{ $t("common.base.add") }}
    //- 表单内容
    el-table.table(
      :data="contents",
      border,
      ref="multipleTable",
      default-expand-all,
      row-key="id",
      header-cell-class-name="table-header",
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }",
      v-loading="loading"
    )
      //- el-table-column(type="selection", width="55", align="center")
      //- el-table-column(prop="id", label="Id", align="center")
      el-table-column(prop="name", label="名称")
      el-table-column(label="禁用")
        template(#default="scope")
          el-tag(:type="!scope.row.ban ? 'success' : 'danger'") {{ !scope.row.ban ? "启用" : "禁用" }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")
      el-table-column(label="操作", width="230", align="center")
        template(#default="scope")
          el-button(
            type="text",
            icon="el-icon-edit",
            @click="handleAddChildrenClick(scope.row)"
          ) 添加子分类
          el-button(
            type="text",
            icon="el-icon-edit",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-popconfirm.mgl10(
            title="确认要删除数据吗？",
            @confirm="handleDeleteClick(scope.row)"
          )
            el-button.red(slot="reference", type="text", icon="el-icon-delete") {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import mallCategoryApi, { MallCategoryListVo } from "@/api/mall/mg/categoryApi";
import arrayUtils from "~/utils/arrayUtils";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

type Content = MallCategoryListVo & {
  children?: Array<Content>;
};

@Component({
  name: "role-mall-category",
})
export default class MallCategoryIndex extends mixins(BaseVue) {
  loading: boolean = true;
  query = { keyword: "" };
  contents: Array<Content> = [];

  async activated() {
    await this.refreshTable();
  }

  async refreshTable() {
    this.loading = true;
    try {
      let contents = await mallCategoryApi.getAllList();
      let root = arrayUtils.array2tree(contents, "id", "idParent", "children");
      this.contents = root;
    } catch (error) {
      this.$notify.error({ title: "加载出错，请稍后重试", message: "" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/category/edit`
    );
  }

  async handleModifyClick(record: Content) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/category/edit?id=${
        record.id
      }`
    );
  }

  async handleAddChildrenClick(record: Content) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/mall/category/edit?id_parent=${record.id}`
    );
  }

  async handleDeleteClick(record: Content) {
    this.loading = true;
    try {
      await mallCategoryApi.delete(record.id!);
      this.$notify.success("删除数据成功过");
      await this.refreshTable();
    } finally {
      this.loading = true;
    }
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}
</style>

