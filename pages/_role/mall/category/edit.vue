<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.mall-category") }}
      el-breadcrumb-item {{ this.query.id ? $t("pages.power.role.edit.title_edit") : $t("pages.power.role.edit.title_add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="80px"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name")
        el-form-item(
          label="父节点",
          prop="idParent",
          :rules="[$rule.required(null, '节点不能为空')]",
          required
        )
          el-cascader(
            v-model="formData.idParent",
            :options="categoryTree",
            :props="{ checkStrictly: true, label: 'name', value: 'id' }",
            clearable
          )
        el-form-item(prop="ban")
          el-switch(
            v-model="formData.ban",
            active-text="禁用",
            inactive-text="启用",
            active-color="red",
            inactive-color="green"
          )
        el-form-item(label="字段数据")
          .property-structs-table(ref="propertyStructsDiv")
            el-table(
              :data="formData.propertyStructs",
              border,
              ref="multipleTable",
              header-cell-class-name="table-header"
            )
              el-table-column(width="35", label="排序")
                template(#default="scope")
                  .y-sorthandle
                    i.el-icon-rank
              el-table-column(
                label="code",
                prop="code",
                :render-header="addRedStar",
                width="150"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`propertyStructs.${scope.$index}.code`",
                    :rules="$rule.required(null, 'Code不能为空')",
                    required
                  )
                    el-input(
                      v-model="scope.row.code",
                      :disabled="scope.row.def"
                    )
              el-table-column(
                label="名称",
                prop="name",
                :render-header="addRedStar",
                width="200"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`propertyStructs.${scope.$index}.name`",
                    :rules="$rule.required(null, '名称不能为空')",
                    required
                  )
                    el-input(
                      v-model="scope.row.name",
                      :disabled="scope.row.def"
                    )
              el-table-column(label="分组", prop="groupName", width="200")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`propertyStructs.${scope.$index}.groupName`"
                  )
                    el-input(
                      v-model="scope.row.groupName",
                      :disabled="scope.row.def"
                    )
              el-table-column(
                label="类型",
                prop="type",
                :render-header="addRedStar",
                width="200"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`propertyStructs.${scope.$index}.type`",
                    :rules="$rule.required(null, '类型不能为空')",
                    required
                  )
                    el-select(
                      v-model="scope.row.type",
                      :disabled="scope.row.def"
                    )
                      el-option(
                        v-for="item in $dic.getOptions('mall.category.prop.type')",
                        :key="item.value",
                        :label="item.label",
                        :value="item.value"
                      )
              el-table-column(label="备注", prop="remark")
                template(#default="scope")
                  el-input(
                    v-model="scope.row.remark",
                    :disabled="scope.row.def",
                    type="textarea"
                  )
              el-table-column(label="操作", width="80")
                template(#default="scope")
                  template(v-if="!scope.row.def")
                    el-popconfirm(
                      title="确认要删除数据吗？",
                      @confirm="formData.propertyStructs.splice(scope.$index, 1)"
                    )
                      el-button.red(
                        slot="reference",
                        type="text",
                        icon="el-icon-delete"
                      ) {{ $t("common.base.delete") }}
            el-button.mgt10(@click="handleAddClick") 添加行
        el-form-item(label="备注")
          el-input(v-model="formData.remark", type="textarea", :rows="2")
        el-form-item
          el-button(type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import mallCategoryApi, {
  MallCategoryAddVo,
  MallCategoryListVo,
  MallProductCategoryPropertyStructBo,
} from "~/api/mall/mg/categoryApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";
import sortableUtils from "~/utils/sortableUtils";

type FormDataPropertyStruct = MallProductCategoryPropertyStructBo & {
  def: boolean | undefined;
};

type FormData = Omit<MallCategoryAddVo, "idParent" | "propertyStructs"> & {
  idParent: Array<string>;
  propertyStructs: Array<FormDataPropertyStruct>;
};

type CategoryTreeNode = MallCategoryListVo & {
  children?: CategoryTreeNode[];
};

@Component({
  name: "role-mall-category-edit",
})
export default class MallCategoryEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    idParent: "0",
  };
  formData: FormData = {
    name: "",
    remark: "",
    ban: false,
    //   // 名称, 必须字段, 最小长度：1, 最大长度：64
    // code?: string;
    // // 名称, 必须字段, 最小长度：1, 最大长度：64
    // name?: string;
    // // 数据类型, 必须字段, 最小长度：1, 最大长度：32
    // type?: string;
    // // 分组名称, 最小长度：0, 最大长度：256
    // groupName?: string;
    // // 备注信息, 最小长度：0, 最大长度：256
    // remark?: string;
    // 父一级标示符
    // def?: boolean;
    propertyStructs: [],
    idParent: ["0"],
  };
  defCategory = {
    id: "0",
    name: "（根节点）",
  };
  categoryTree: Array<CategoryTreeNode> = [{ ...this.defCategory }];

  async created() {
    this.query.id = routerUtils.getQueryValue(this, "id", "");
    this.query.idParent = routerUtils.getQueryValue(this, "id_parent", "0");

    await this.initParam();
    await this.initDetail();
    this.loading = false;
  }

  async mounted() {
    this.initSortable();
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async initParam() {
    let categories = await mallCategoryApi.getAllList();
    let root = arrayUtils.array2tree(categories, "id", "idParent", "children");
    if (this.query.id) {
      const clearId = (arr: Array<CategoryTreeNode>, id: string) => {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].id == id) {
            arr.splice(i, 1);
            i--;
            continue;
          }
          if (arr[i].children) {
            clearId(arr[i].children!, id);
          }
        }
      };
      clearId(root, this.query.id);
    }
    this.categoryTree = [this.defCategory, ...root];
  }

  async initDetail() {
    let form = this.formData;
    if (this.query.id) {
      let detail = await mallCategoryApi.get(this.query.id);
      form = { ...(<any>detail) };
      form.idParent = [detail.idParent!];
    } else {
      form.idParent = [this.query.idParent];
    }
    this.formData = form;
  }

  async initSortable() {
    let el: any = this.$refs.propertyStructsDiv;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "formData.propertyStructs", {
      handle: ".y-sorthandle",
    });
  }

  async handleAddClick() {
    this.formData.propertyStructs?.push({
      code: "",
      type: "STRING",
      name: "",
      groupName: "",
      remark: "",
      def: false,
    });
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: MallCategoryAddVo = { ...(<Omit<FormData, "idParent">>data) };
      body.idParent = data.idParent[data.idParent.length - 1];
      body.propertyStructs = body.propertyStructs!.filter((x: any) => !x.def);
      if (this.query.id) {
        // 更新
        await mallCategoryApi.modify(this.query.id, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await mallCategoryApi.add(body);
        this.query.id = detail.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/category`
    );
  }

  @Watch("formData.idParent", { deep: true })
  async detailIdParentWatch(nv: Array<string>, ov: Array<string>) {
    if (nv[nv.length - 1] === ov[ov.length - 1]) {
      return;
    }

    let detail = this.formData;

    let catJson = detail.idParent ? JSON.stringify(detail.idParent) : null;

    let idParent =
      detail.idParent && detail.idParent.length > 0
        ? detail.idParent[detail.idParent.length - 1]
        : "0";

    // 调整默认显示分类信息
    let ps = this.formData.propertyStructs!;
    ps = ps.filter((x) => !x.def);

    let formIdParent = null;
    if (idParent != "0") {
      let cat = await mallCategoryApi.getDetail(idParent);
      let parentCats = arrayUtils.tree2array(cat, "parentCategory").reverse();

      // 调整级联选择框内容
      let cIds = parentCats.map((x) => x.id!);
      formIdParent = cIds;

      // 调整默认显示属性
      let defProp: Array<FormDataPropertyStruct> = [];
      parentCats.forEach((c) =>
        c.propertyStructs?.forEach(
          (prop: MallProductCategoryPropertyStructBo) => {
            let dp: FormDataPropertyStruct = { ...prop, def: true };
            defProp.push(dp);
          }
        )
      );
      ps = [...defProp, ...ps];
    } else {
      formIdParent = ["0"];
    }
    if (formIdParent) {
      let catNJson = formIdParent ? JSON.stringify(formIdParent) : null;
      if (catNJson !== catJson) {
        this.formData.idParent = formIdParent;
      }
    }

    this.formData.propertyStructs = ps;
  }
}
</script>

<style lang="scss" scoped>
.property-structs-table::v-deep table tbody .cell {
  min-height: 60px;
}
</style>
