<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.mall-freight") }}
  .container
    //- 表头
    .handle-box
      el-button(type="primary", icon="el-icon-plus", @click="handleAddClick") {{ $t("common.base.add") }}
      el-select(v-model="query.mallId", @change="handleMallIdChange")
        el-option(
          v-for="item in oneLoadParam.mallInfos",
          :key="item.id",
          :label="item.name",
          :value="item.id"
        )

      //- el-input.handle-input(
      //-   v-model="query.keyword",
      //-   placeholder="关键词",
      //-   @keyup.enter.native="handleSearchClick"
      //- )
      //- el-button(
      //-   type="primary",
      //-   icon="el-icon-search",
      //-   @click="handleSearchClick"
      //- ) 搜索
      //- a.downswagger(:href="swaggerUrl", target="_blank") 下载Swagger文档
    //- 表单内容
    el-table.table(
      :data="bizData.freights",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading > 0"
    )
      el-table-column(prop="name", label="名称")
      el-table-column(prop="def", label="默认")
        template(#default="scope")
          el-tag(v-if="scope.row.def", type="success") 默认
          el-button(
            v-if="!scope.row.def",
            type="text",
            @click="hanldeFreightDefClick(scope.row)"
          ) 设置为默认
      el-table-column(prop="content.type", label="类型")
        template(#default="scope")
          | {{ $dic.getFieldValue("mall.freight.content.type", scope.row.content.type, scope.row.content.type) }}
      el-table-column(prop="content.detail", label="信息")
        template(#default="scope")
          div(v-if="'FREE' == scope.row.content.type") 免运费
          div(v-if="'FIXED' == scope.row.content.type") 固定费率：{{ scope.row.content.price }}
          div(v-if="'RANGE' == scope.row.content.type")
            p(v-for="range in scope.row.content.ranges") {{ range.weight }}kg {{ getShowVolume(range) }}：￥{{ range.price }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(type="text", @click="handleModifyClick(scope.row, false)") {{ $t("common.base.detail") }}
          el-button(type="text", @click="handleModifyClick(scope.row)") {{ $t("common.base.modify") }}
          el-button(type="text", @click="handleAddCopyClick(scope.row)") 复制
          el-popconfirm(
            title="确认要删除数据吗？",
            @confirm="handleDeleteClick(scope.row)"
          )
            el-button.mgl10(slot="reference", type="text") {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import mallFreightApi, {
  MallFreightBo,
  MallFreightContentBo$MallFreightContentRangBo,
  MallMgFreightGetListQueryParam,
} from "~/api/mall/mg/freightApi";
import mallInfoApi, { MallInfo } from "~/api/mall/mg/mall/infoApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";

@Component({
  name: "role-mall-freight",
})
export default class MallFreightIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    mallId: "",
  };
  oneLoadParam = {
    inited: false,
    // ...param
    mallInfos: <MallInfo[]>[],
  };
  bizData = {
    freights: <Array<MallFreightBo>>[],
  };

  getShowVolume(range: MallFreightContentBo$MallFreightContentRangBo) {
    let volume = range.volume;
    if (!volume) {
      return "";
    }
    if (volume > 1000 * 1000) {
      volume = volume / 1000 / 1000;
      return `${volume}m^3`;
    }
    if (volume > 1000) {
      volume = volume / 1000;
      return `${volume}dm^3`;
    }
    return `${volume}cm^3`;
  }

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
    this.query.mallId = compUtils.getQueryValue(this, "mall_id", "");

    let mallInfos = await mallInfoApi.getAllList();
    this.oneLoadParam.mallInfos = mallInfos;
    if (mallInfos.length > 0 && !this.query.mallId) {
      this.query.mallId = mallInfos[0].id!;
    }
  }

  async initDetail() {
    await this.loadContentCount();
  }

  async activated() {
    await this.init();
  }

  async loadContentCount() {
    await this.loadContent();
  }

  async loadContent() {
    if (!this.query.mallId) {
      this.$notify.error("商城信息未找到。");
      return;
    }

    let param: MallMgFreightGetListQueryParam = {};
    param.mallId = this.query.mallId;

    routerUtils.putQueryValue(this, param);

    this.loading++;
    try {
      this.bizData.freights = await mallFreightApi.getList(param);
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async hanldeFreightDefClick(freight: MallFreightBo) {
    this.loading++;
    try {
      await mallFreightApi.putDefault(freight.id!);
      await this.loadContent();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleDeleteClick(freight: MallFreightBo) {
    this.loading++;
    try {
      await mallFreightApi.delete(freight.id!);
      await this.loadContent();
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading--;
    }
  }

  async handleMallIdChange() {
    this.loadContent();
  }

  async handleAddCopyClick(record: MallFreightBo) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/freight/edit?cpid=${
        record.id
      }`
    );
  }

  async handleAddClick() {
    if (!this.query.mallId) {
      this.$notify.error("商城为空时不能创建");
      return;
    }
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/mall/freight/edit?mall_id=${this.query.mallId}`
    );
  }

  async handleModifyClick(record: MallFreightBo, edit: boolean = true) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/freight/edit?id=${
        record.id
      }${edit ? "" : "&edit=false"}`
    );
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-input {
  width: 300px;
  display: inline-block;
}
</style>

