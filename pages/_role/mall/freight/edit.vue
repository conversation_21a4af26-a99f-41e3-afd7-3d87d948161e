<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.mall-freight") }}
      el-breadcrumb-item {{ this.query.id ? $t("common.base.edit") : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="80px"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name", :disabled="!query.edit")

        el-form-item(
          prop="mallId",
          label="商城信息",
          :rules="[$rule.required(null, '商城信息不能为空')]",
          required
        )
          el-select(
            v-model="formData.mallId",
            :disabled="!!query.id || !query.edit",
            style="width: 100%"
          )
            el-option(
              v-for="item in oneLoadParam.mallInfos",
              :key="item.id",
              :label="item.name",
              :value="item.id"
            )
        el-form-item(prop="def", label="默认")
          el-switch(
            v-model="formData.def",
            active-color="green",
            inactive-color="red",
            :disabled="!query.edit"
          )
        el-form-item(
          label="类型",
          prop="content.type",
          :rules="[$rule.required(null, '类型不能为空')]",
          required
        )
          el-select(
            v-model="formData.content.type",
            :disabled="!query.edit",
            style="width: 100%"
          )
            el-option(
              v-for="item in $dic.getOptions('mall.freight.content.type')",
              :key="item.value",
              :label="item.label",
              :value="item.value"
            )
        el-form-item(
          v-if="'FIXED' == formData.content.type",
          prop="content.price",
          label="费用",
          required
        )
          el-input-number(
            controls-position="right",
            size="mini",
            v-model="formData.content.price",
            :precision="2",
            :step="1",
            :min="0",
            :max="1000000000",
            :disabled="!query.edit"
          )
        el-form-item(
          v-show="'RANGE' == formData.content.type",
          prop="content.ranges"
        )
          div(ref="rangesDiv")
            el-table(:data="formData.content.ranges", ref="rangesTable")
              el-table-column(width="35", label="排序")
                template(#default="scope")
                  .y-sorthandle
                    i.el-icon-rank
              el-table-column(prop="price", label="费用")
                template(#default="scope")
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    v-model="scope.row.price",
                    :precision="2",
                    :step="1",
                    :min="0",
                    :max="1000000000",
                    :disabled="!query.edit"
                  )
              el-table-column(prop="weight", label="最低重量（kg）")
                template(#default="scope")
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    v-model="scope.row.weight",
                    :precision="5",
                    :step="1",
                    :min="0",
                    :max="1000000000",
                    :disabled="!query.edit"
                  )
              el-table-column(prop="volume", label="最低体积（cm^3）")
                template(#default="scope")
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    v-model="scope.row.volume",
                    :precision="5",
                    :step="1000",
                    :min="0",
                    :max="1000000000",
                    :disabled="!query.edit",
                    style="width: 200px"
                  )
              el-table-column(prop="operator", label="操作")
                template(#default="scope")
                  el-button(
                    v-if="query.edit",
                    type="text",
                    @click="handleContentRangeDeleteClick(scope.$index)"
                  ) 删除
            el-button.mgt10(
              v-if="query.edit",
              @click="handleContentRangeAddClick"
            ) 添加
        el-form-item(label="备注")
          el-input(
            v-model="formData.remark",
            type="textarea",
            :rows="2",
            :disabled="!query.edit"
          )
        el-form-item
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Vue, Watch } from "nuxt-property-decorator";
import mallFreightApi, {
  MallFreightAddVo,
  MallFreightContentBo,
} from "~/api/mall/mg/freightApi";
import mallInfoApi, { MallInfo } from "~/api/mall/mg/mall/infoApi";
import { BaseVue } from "~/model/vue";
import commonFunUtils from "~/utils/commonFunUtils";
import compUtils from "~/utils/compUtils";
import sortableUtils from "~/utils/sortableUtils";

type FormData = MallFreightAddVo;

@Component({
  name: "role-mall-freight-edit",
})
export default class MallFreightEdit extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    cpid: "",
    edit: true,
  };
  formData: FormData = {
    name: "",
    mallId: "",
    content: <MallFreightContentBo>{
      type: "FREE",
      price: 0,
      ranges: [],
    },
    def: true,
    remark: "",
  };

  oneLoadParam = {
    inited: false,
    // ...param
    mallInfos: <MallInfo[]>[],
  };

  async created() {
    this.init();
  }

  async mounted() {
    this.initSortable();
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...

    this.formData.mallId = compUtils.getQueryValue(this, "mall_id", "");

    let mallInfos = await mallInfoApi.getAllList();
    this.oneLoadParam.mallInfos = mallInfos;
    if (!this.formData.mallId && mallInfos.length > 0) {
      this.formData.mallId = mallInfos[0].id!;
    }
  }

  async initDetail() {
    this.query.id = compUtils.getQueryValue(this, "id", "");
    this.query.cpid = compUtils.getQueryValue(this, "cpid", "");
    this.query.edit = commonFunUtils.parseBoolean(
      compUtils.getQueryValue(this, "edit", "true")
    );
    if (this.query.id) {
      let freight = await mallFreightApi.get(this.query.id);
      this.formData = { ...freight };
    } else if (this.query.cpid) {
      let freight = await mallFreightApi.get(this.query.cpid);
      this.formData = { ...freight };
    }
  }

  async initSortable() {
    let el: any = this.$refs.rangesDiv;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "formData.content.ranges", {
      handle: ".y-sorthandle",
    });
  }

  async handleContentRangeDeleteClick(idx: number) {
    this.formData.content!.ranges!.splice(idx, 1);
  }

  async handleContentRangeAddClick() {
    this.formData.content!.ranges!.push({
      price: 0,
      weight: 0,
      volume: 0,
    });
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        return;
      }
      let data = this.formData;
      let body: MallFreightAddVo = { ...data };
      if (this.query.id) {
        // 更新
        await mallFreightApi.modify(this.query.id, body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await mallFreightApi.add(body);
        this.query.id = detail.id!;
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg: any = this.$t("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg: any = this.$t("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/freight`
    );
  }
}
</script>

<style lang="scss" scoped>
</style>
