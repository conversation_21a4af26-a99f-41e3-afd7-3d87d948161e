<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades 订单列表
  .container
    //- 表头
    .handle-box
      el-input.handle-input.mgr10(
        v-model="query.keyword",
        placeholder="关键词",
        @keyup.enter.native="handleSearchClick"
      )
      el-button(
        type="primary",
        icon="el-icon-search",
        @click="handleSearchClick"
      ) 搜索

    //- 表单内容
    .orders-table(v-loading="loading > 0")
      .orders-item(v-for="orders in bizData.orderses")
        .orders-nochildren(
          v-if="!orders.childrenOrders || orders.childrenOrders.length <= 0"
        )
          .orders-title
            .orders-label 订单号：{{ orders.id }}
            .orders-label 下单时间：{{ $moment(orders.submitTime).format("YYYY-MM-DD HH:mm:ss") }}
            .orders-label 供应商：{{ orders.storeName }}
            .orders-label 价格：￥{{ orders.priceOrders }}
            .orders-label 订单状态：
              el-tag(
                :type="orders.state != 'CANCEL' ? (orders.state == 'FINISH' ? 'success' : '') : 'danger'"
              ) {{ $dic.getFieldValue("mall.orders.state", orders.state, orders.state) }}
            .orders-operate.orders-label(v-if="orders.state == 'PAY'")
              a(
                href="javascript:void(0)",
                @click="handleOrdersDeliveryClick(orders)"
              ) 发货
            .orders-operate.orders-label(v-if="orders.state == 'NEW'")
              a(
                href="javascript:void(0)",
                @click="handleOrdersCancelClick(orders)"
              ) 取消
          .orders-product(v-for="pd in orders.products")
            .pd-pic
              el-image(
                :src="getImageUrlOne(pd.productPic)",
                :preview-src-list="getImageUrls(pd.productPic)",
                :lazy="true",
                style="width: 100px; height: 100px"
              )
            .pd-name {{ pd.productName }}
            .pd-num x{{ pd.num }}
        .orders-split(
          v-if="orders.childrenOrders && orders.childrenOrders.length > 0"
        )
          .orders-title
            .orders-label 订单号：{{ orders.id }}
            .orders-label 下单时间：{{ $moment(orders.submitTime).format("YYYY-MM-DD HH:mm:ss") }}
          .orders-childrenOrders(v-for="porders in orders.childrenOrders")
            .orders-title
              .orders-label 订单号：{{ porders.id }}
              .orders-label 下单时间：{{ $moment(porders.submitTime).format("YYYY-MM-DD HH:mm:ss") }}
              .orders-label 供应商：{{ orders.storeName }}
              .orders-label 价格：￥{{ porders.priceOrders }}
              .orders-label 订单状态：
                el-tag(
                  :type="porders.state != 'CANCEL' ? (porders.state == 'FINISH' ? 'success' : '') : 'danger'"
                ) {{ $dic.getFieldValue("mall.orders.state", porders.state, porders.state) }}
              .orders-operate.orders-label(v-if="porders.state == 'PAY'")
                a(
                  href="javascript:void(0)",
                  @click="handleOrdersDeliveryClick(porders)"
                ) 发货
              .orders-operate.orders-label(v-if="orders.state == 'NEW'")
                a(
                  href="javascript:void(0)",
                  @click="handleOrdersCancelClick(orders)"
                ) 取消
            .orders-product(v-for="pd in porders.products")
              .pd-pic
                el-image(
                  :src="getImageUrlOne(pd.productPic)",
                  :preview-src-list="getImageUrls(pd.productPic)",
                  :lazy="true",
                  style="width: 100px; height: 100px"
                )
              .pd-name {{ pd.productName }}
              .pd-num x{{ pd.num }}
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import urlUtils from "~/utils/urlUtils";

import mgOrdersApi, {
  MallMgOrdersGetListCountQueryParam,
  MallMgOrdersGetListQueryParam,
  OrdersDeliveryVo,
  OrdersListVo,
} from "~/api/mall/mg/ordersApi";

import shopOrdersApi, { OrdersCancelVo } from "~/api/mall/shopper/ordersApi";

@Component({
  name: "role-mall-orders",
})
export default class PageMallOrdersIndex extends mixins(BaseVue) {
  loading = 0;
  query = {
    state: "",
    keyword: "",
    total: 0,
    page: 1,
    rows: 10,
  };
  formData = {};
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    orderses: <OrdersListVo[]>[],
  };

  async mounted() {
    await this.init();
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }
  async loadContentCount() {
    let param: MallMgOrdersGetListCountQueryParam = { ...this.query };

    this.loading++;
    try {
      let result = await mgOrdersApi.getListCount(param);
      this.query.total = result;
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.bizData.orderses = [];
      }
    } finally {
      this.loading--;
    }
  }
  async loadContent() {
    let param: MallMgOrdersGetListQueryParam = { ...this.query };

    this.loading++;
    try {
      param.page = param.page! - 1;
      let result = await mgOrdersApi.getList(param);
      this.bizData.orderses = result;
    } finally {
      this.loading--;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleOrdersDeliveryClick(orders: OrdersListVo) {
    let body: OrdersDeliveryVo = {};
    body.logistics = "顺丰";
    body.no = "1234134";
    await mgOrdersApi.delivery(orders.id!, body);
    await this.loadContent();
  }

  async handleOrdersCancelClick(orders: OrdersListVo) {
    let body: OrdersCancelVo = {};
    body.remark = "取消";
    await shopOrdersApi.cancel(orders.id!, body);
    await this.loadContent();
  }

  async handleDetailClick(record: OrdersListVo) {
    // if (!record.spaceIds) {
    //   return;
    // }
    // let spaceIds = record.spaceIds?.split(",");
    // this.$router.push(`/demo/mallweb/detail?id=${spaceIds[0]}`);
  }

  async toPage(url: string) {
    this.$router.push(url);
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.orders-table {
  .orders-split {
    .orders-childrenOrders {
      border-top: solid 1px #ccc;
    }
  }

  .orders-item {
    background-color: #eee;
    margin-bottom: 10px;
    width: 100%;
    border: solid 1px #aaa;

    .orders-title {
      padding: 0px 6px;
      display: flex;
      font-size: 16px;
      height: 40px;

      .orders-label {
        margin-right: 20px;
        height: 36px;
        line-height: 36px;
      }
    }

    .orders-product {
      background-color: #fff;
      padding: 6px;
      border-top: solid 1px #ccc;

      display: flex;

      .pd-pic {
        width: 14%;
      }

      .pd-name {
        width: 50%;
        font-size: 14px;
        font-weight: 800;
        line-height: 100px;
        height: 100px;
        color: #555;
      }
      .pd-num {
        width: 50px;
        font-size: 14px;
        font-weight: 800;
        line-height: 100px;
        height: 100px;
        color: #aaa;
      }
    }
  }
}
</style>

