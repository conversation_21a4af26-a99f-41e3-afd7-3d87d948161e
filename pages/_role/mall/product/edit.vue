<template lang="pug">
.page-mall-product-edit
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar {{ $t("menu.mall-product") }}
      el-breadcrumb-item {{ query.id ? (query.edit ? $t("common.base.edit") : $t("common.base.detail")) : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="80px",
        @submit.native.prevent="() => {}"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name", :disabled="!query.edit")
        el-form-item(
          label="分类",
          prop="categoryId",
          :rules="[$rule.required(null, '分类不能为空')]",
          required
        )
          el-cascader(
            v-model="formData.categoryId",
            :options="oneLoadParam.categoryTree",
            :props="{ checkStrictly: false, label: 'name', value: 'id' }",
            clearable,
            :disabled="!!query.id || !query.edit",
            style="width: 100%"
          )
        el-form-item(
          label="品牌",
          prop="brandId",
          :rules="[$rule.required(null, '品牌不能为空')]",
          required
        )
          el-select(
            v-model="formData.brandId",
            filterable,
            remote,
            reserve-keyword,
            :remote-method="handleBrandAutocompleteSearch",
            placeholder="请输入关键词",
            :loading="bizData.brandsLoading",
            :disabled="!!query.id || !query.edit",
            style="width: 100%"
          )
            el-option(
              v-for="item in bizData.brands",
              :key="item.id",
              :label="item.name",
              :value="item.id"
            )
        el-form-item(
          label="类型",
          prop="type",
          :rules="[$rule.required(null, '类型不能为空')]",
          required
        )
          el-select(
            v-model="formData.type",
            :disabled="!!query.id || !query.edit",
            style="width: 100%"
          )
            el-option(
              v-for="option in $dic.getOptions('mall.product.type')",
              :key="option.value",
              :label="option.label",
              :value="option.value"
            )
        el-form-item(label="SPU", prop="spu")
          el-input(v-model="formData.spu", :disabled="!query.edit")
        el-form-item(label="标签", prop="tag")
          y-tag(v-model="formData.tag", :disabled="!query.edit")
        el-form-item(
          label="图片",
          prop="pic",
          :rules="[$rule.required(null, '图片不能为空')]",
          required
        )
          y-upload(
            v-model="formData.pic",
            :limit="9",
            type="pic",
            :multiple="true",
            accept="image/*",
            :disabled="!query.edit"
          )
        el-form-item(label="视频", prop="pic")
          y-upload(
            v-model="formData.video",
            :limit="1",
            type="pic",
            accept="video/*",
            :disabled="!query.edit"
          )
        el-form-item(
          label="移动版详情",
          prop="contentMobile",
          :rules="[$rule.required(null, '详情不能为空')]",
          required
        )
          ym-richtext(
            v-model="formData.contentMobile",
            :disabled="!query.edit"
          )
        el-form-item(
          label="PC版详情",
          prop="contentPc",
          :rules="[$rule.required(null, '详情不能为空')]",
          required
        )
          ym-richtext(v-model="formData.contentPc", :disabled="!query.edit")

        el-form-item(label="通用规格")
          .property-table(ref="propertyDiv")
            el-table(
              :data="formData.properties",
              border,
              ref="propertyTable",
              header-cell-class-name="table-header"
            )
              el-table-column(v-if="query.edit", width="35", label="排序")
                template(#default="scope")
                  .y-sorthandle
                    i.el-icon-rank
              el-table-column(
                label="属性名称",
                prop="name",
                :render-header="addRedStar",
                width="200"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`properties.${scope.$index}.name`",
                    required,
                    :rules="[$rule.required(null, '属性名称不能为空')]"
                  )
                    el-input(
                      v-model="scope.row.name",
                      :disabled="!!scope.row.code || !query.edit"
                    )
              el-table-column(label="属性分组", prop="groupName", width="200")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`properties.${scope.$index}.groupName`"
                  )
                    el-input(
                      v-model="scope.row.groupName",
                      :disabled="!!scope.row.code || !query.edit"
                    )
              el-table-column(label="单位属性", prop="space", width="80px")
                template(#default="scope")
                  el-switch(
                    v-model="scope.row.space",
                    active-color="#13ce66",
                    inactive-color="#ff4949",
                    @change="handlePropSpaceChange(scope.row)",
                    :disabled="!query.edit"
                  )
              el-table-column(
                label="值",
                prop="value",
                :render-header="addRedStar"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`properties.${scope.$index}`",
                    :rules="[checkPropertyValueEmpty(null, '属性值不能为空')]"
                  )
                    el-autocomplete(
                      v-if="!scope.row.space",
                      v-model="scope.row.value",
                      :disabled="!!scope.row.def || !query.edit",
                      :fetch-suggestions="(k, cb) => handlePropAutocompleteSearch(k, cb, scope.row)",
                      placeholder="请输入内容",
                      :trigger-on-focus="false",
                      style="width: 100%"
                    )
                    template(v-if="scope.row.space")
                      .value-tag
                        y-tag(
                          :disabled="!query.edit",
                          v-model="scope.row.values",
                          type="array",
                          :autocompleteCb="(k, cb) => handlePropAutocompleteSearch(k, cb, scope.row)",
                          @input="handlePropTagChange()"
                        )
              el-table-column(label="备注", prop="remark")
                template(#default="scope")
                  el-input(
                    v-if="scope.row.code",
                    v-model="scope.row.remark",
                    :disabled="!!scope.row.code"
                  )
              el-table-column(v-if="query.edit", label="操作", width="80")
                template(#default="scope")
                  template(v-if="!scope.row.code")
                    el-popconfirm(
                      title="确认要删除数据吗？",
                      @confirm="handlePropDeleteClick(scope.$index)"
                    )
                      el-button.red(
                        slot="reference",
                        type="text",
                        icon="el-icon-delete"
                      ) {{ $t("common.base.delete") }}
            el-button.mgt10(v-if="query.edit", @click="handlePropAddClick") 添加行

        el-form-item(label="销售单位")
          .space-batch-put(v-if="query.edit")
            .label 批量操作：
            .value
              .put-win
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要重新生成名称吗？",
                    @confirm="handleSpaceBathPutNameClick()"
                  )
                    el-button(slot="reference", size="mini") 自动名称生成
              .put-win
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要使用第一张图替换所有图片吗？",
                    @confirm="handleSpaceBathPutPicClick()"
                  )
                    el-button(slot="reference", size="mini") 使用第一张图设置所有图片
              .put-win
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要清空SKU吗？",
                    @confirm="handleSpaceBathPutSkuClick()"
                  )
                    el-button(slot="reference", size="mini") 清空SKU
              .put-win
                span.space-bath-label 单价：
                span.space-bath-value
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    style="width: 100px",
                    v-model="bizData.spaceBathPutForm.unitPrice"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部单价设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick('unitPrice')"
                  )
                    el-button(slot="reference", size="mini") 设置
              .put-win
                span.space-bath-label 展示单价：
                span.space-bath-value
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    style="width: 100px",
                    v-model="bizData.spaceBathPutForm.unitPriceShow"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部展示单价设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick('unitPriceShow')"
                  )
                    el-button(slot="reference", size="mini") 设置
              .put-win
                span.space-bath-label 库存：
                span.space-bath-value
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    style="width: 100px",
                    v-model="bizData.spaceBathPutForm.quantity"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部库存设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick('quantity')"
                  )
                    el-button(slot="reference", size="mini") 设置
              .put-win
                span.space-bath-label 重量：
                span.space-bath-value
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    style="width: 100px",
                    :precision="4",
                    :step="1",
                    :min="0",
                    :max="10000",
                    v-model="bizData.spaceBathPutForm.baseWeight"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部重量设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick('baseWeight')"
                  )
                    el-button(slot="reference", size="mini") 设置
              .put-win
                span.space-bath-label 体积：
                span.space-bath-value
                  el-input(
                    style="width: 100px",
                    size="mini",
                    v-model="bizData.spaceBathPutForm.baseVolume"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部体积设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick('baseVolume')"
                  )
                    el-button(slot="reference", size="mini") 设置
          .space-table(ref="spacesDiv")
            el-table(
              :data="formData.spaces",
              border,
              ref="spacesTable",
              :span-method="objectSpanMethod",
              header-cell-class-name="table-header"
            )
              //- el-table-column(width="35", label="排序")
              //-   template(#default="scope")
              //-     .y-sorthandle
              //-       i.el-icon-rank
              //- 属性信息
              el-table-column(
                v-for="(struct, idx) in formData.properties",
                :key="idx",
                v-if="struct.space",
                :label="struct.name",
                :prop="`prop.${struct.idxCode}`",
                width="150"
              )
                template(#default="scope")
                  el-form-item(label-width="0")
                    el-input(
                      v-model="scope.row[`prop.${struct.idxCode}`]",
                      :disabled="true"
                    )
              el-table-column(
                label="名称",
                prop="name",
                :render-header="addRedStar",
                width="200"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.name`",
                    :rules="$rule.required(null, '名称不能为空')"
                  )
                    el-input(v-model="scope.row.name", :disabled="!query.edit")
              el-table-column(
                label="图片",
                prop="pic",
                :render-header="addRedStar",
                width="160"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.pic`",
                    :rules="$rule.required(null, '图片不能为空')"
                  )
                    y-upload(
                      v-model="scope.row.pic",
                      :limit="1",
                      type="pic",
                      :multiple="true",
                      accept="image/*",
                      :disabled="!query.edit"
                    )
              el-table-column(label="UPC", prop="upc", width="160")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.upc`"
                  )
                    el-input(v-model="scope.row.upc", :disabled="!query.edit")
              el-table-column(label="SKU", prop="sku", width="160")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.sku`",
                    :rules="[skuCheck(scope.$index)]"
                  )
                    el-input(v-model="scope.row.sku", :disabled="!query.edit")
              el-table-column(
                label="可售",
                prop="selling",
                :render-header="addRedStar",
                width="80px"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.selling`",
                    :rules="$rule.required(null, '销售状态不能为空')"
                  )
                  el-switch(
                    v-model="scope.row.selling",
                    active-color="#13ce66",
                    inactive-color="#ff4949",
                    :disabled="!query.edit"
                  )
              el-table-column(
                label="单价",
                prop="unitPrice",
                :render-header="addRedStar",
                width="190"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.selling`",
                    :rules="$rule.required(null, '单价不能为空')"
                  )
                    el-input-number(
                      controls-position="right",
                      size="mini",
                      v-model="scope.row.unitPrice",
                      :precision="2",
                      :step="1",
                      :min="0",
                      :max="1000000000",
                      :disabled="!query.edit"
                    )
              el-table-column(label="显示价格", prop="unitPriceShow", width="190")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.unitPriceShow`"
                  )
                    el-input-number(
                      controls-position="right",
                      size="mini",
                      v-model="scope.row.unitPriceShow",
                      :precision="2",
                      :step="1",
                      :min="0",
                      :max="1000000000",
                      :disabled="!query.edit"
                    )
              el-table-column(
                label="库存数量",
                prop="quantity",
                :render-header="addRedStar",
                width="190"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.quantity`",
                    :rules="$rule.required(null, '库存不能为空')"
                  )
                    el-input-number(
                      controls-position="right",
                      size="mini",
                      v-model="scope.row.quantity",
                      :precision="0",
                      :step="1",
                      :min="0",
                      :max="1000000",
                      :disabled="!query.edit"
                    )
              el-table-column(
                label="重量（kg）",
                prop="baseWeight",
                :render-header="addRedStar",
                width="190"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.baseWeight`",
                    :rules="$rule.required(null, '重量不能为空')"
                  )
                    el-input-number(
                      controls-position="right",
                      size="mini",
                      v-model="scope.row.baseWeight",
                      :precision="4",
                      :step="1",
                      :min="0",
                      :max="10000",
                      :disabled="!query.edit"
                    )
              el-table-column(
                label="体积，单位：cm（10*30*100）",
                prop="baseVolume",
                :render-header="addRedStar",
                width="200"
              )
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.baseVolume`",
                    :rules="[checkSpaceBaseVolume()]"
                  )
                    el-input(
                      v-model="scope.row.baseVolume",
                      :disabled="!query.edit"
                    )
              el-table-column(label="备注", prop="remark", width="190")
                template(#default="scope")
                  el-form-item(
                    label-width="0",
                    :prop="`spaces.${scope.$index}.remark`"
                  )
                    el-input(
                      v-model="scope.row.remark",
                      ,
                      type="textarea",
                      :rows="2",
                      :disabled="!query.edit"
                    )
        el-form-item(label="备注")
          el-input(
            v-model="formData.remark",
            type="textarea",
            :rows="2",
            :disabled="!query.edit"
          )
        el-form-item
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import mallCategoryApi, {
  MallCategoryListVo,
  MallProductCategoryBo,
  MallProductCategoryPropertyStructBo,
} from "~/api/mall/mg/categoryApi";
import mallBrandApi, { MallBrand } from "~/api/mall/mg/brandApi";
import mallProductApi, {
  MallProductAddVo,
  MallProductModifySpaceVo,
  MallProductPropertyBo,
} from "~/api/mall/mg/productApi";
import auxyAutocompleteApi, {
  BasicAuxyAutocompleteAutoGetAutocompletesQueryParam,
} from "~/api/basic/auxy/autocomplete/autoApi";
import YmRichtext from "~/components/ym/richtext.vue";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import routerUtils from "~/utils/routerUtils";
import sortableUtils from "~/utils/sortableUtils";
import objUtils from "~/utils/objUtils";
import commonFunUtils from "~/utils/commonFunUtils";
import brandApi from "~/api/mall/mg/brandApi";

type CategoryTree = MallCategoryListVo & {
  children?: Array<CategoryTree>;
};

type FormDataProperty = MallProductPropertyBo &
  MallProductCategoryPropertyStructBo & {
    idxCode: string;
    space: boolean;
    value: string;
    values: Array<string>;
  };

type FormDataSpacePropType = {
  [key in `prop.${string}`]: string;
};

type FormDataSpace = Omit<MallProductModifySpaceVo, "properties"> &
  FormDataSpacePropType;

type FormData = Omit<
  MallProductAddVo,
  "properties" | "spaces" | "categoryId"
> & {
  categoryId: Array<string>;
  properties: Array<FormDataProperty>;
  spaces: Array<FormDataSpace>;
};

type SpaceBathPubForm = {
  unitPrice: number;
  unitPriceShow: number;
  quantity: number;
  baseWeight: number;
  baseVolume: string;
};

@Component({
  name: "role-mall-product-edit",
  components: {
    YmRichtext,
  },
})
export default class MallProductEdit extends mixins(BaseVue) {
  loading = true;
  query = { id: "", cpid: "", edit: true };
  formData: FormData = {
    // 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
    type: "BASE",
    // 名称, 必须字段, 最小长度：0, 最大长度：64
    name: "",
    // SPU信息, 最小长度：0, 最大长度：32
    spu: "",
    tag: "",
    // 品牌名称, 最小长度：0, 最大长度：64
    brandId: "",
    // 图片信息, 必须字段, 最小长度：0, 最大长度：1024
    pic: "",
    // 移动版详情, 最小长度：0, 最大长度：65535
    contentMobile: "",
    // PC版详情, 必须字段, 最小长度：1, 最大长度：65535
    contentPc: "",
    // 视频信息, 最小长度：1, 最大长度：1024
    video: "",
    // 商品类型, 最小长度：0, 最大长度：32
    categoryId: [],
    // 所有的分类属性数据
    properties: <Array<FormDataProperty>>[],

    // 不同规格数据, 必须字段
    // // 名称, 最小长度：0, 最大长度：64
    // name?: string;
    // // 默认商品图片, 最小长度：0, 最大长度：256
    // pic?: string;
    // // 单价, 必须字段
    // unitPrice?: number;
    // // 显示价格
    // unitPriceShow?: number;
    // // 商品upc条码, 最小长度：0, 最大长度：32
    // upc?: string;
    // // 商品规格属性
    // properties?: Array<MallProductPropertyBo>;
    spaces: <Array<FormDataSpace>>[],
  };

  oneLoadParam = {
    inited: false,
    categoryTree: <Array<CategoryTree>>[],
  };

  bizData = {
    spaceBathPutForm: <SpaceBathPubForm>{
      unitPrice: 0,
      unitPriceShow: 0,
      quantity: 0,
      baseWeight: 0,
      baseVolume: "",
    },
    brands: <MallBrand[]>[],
    brandsLoading: false,
  };

  private catId2detail: Map<string, Array<MallProductCategoryBo>> = new Map();

  private idCount = 1;

  async created() {
    this.query.id = routerUtils.getQueryValue(this, "id", "");
    this.query.cpid = routerUtils.getQueryValue(this, "cpid", "");
    this.query.edit = commonFunUtils.parseBoolean(
      routerUtils.getQueryValue(this, "edit", "true")
    );
    await this.init();
  }

  async mounted() {
    this.initSortable();
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  checkSpaceBaseVolume() {
    return {
      trigger: ["blur", "change"],
      validator: (rule: any, value: string, cb: any) => {
        if (!value) {
          cb(new Error("该字段不能为空"));
          return;
        }
        let svs = value.split("*");
        if (svs.length != 3) {
          cb(new Error("字段格式异常，参考格式（15*8*5），单位：cm"));
          return;
        }
        for (let v of svs) {
          if (!v) {
            cb(new Error("字段格式异常，参考格式（15*8*5），单位：cm"));
            return;
          }
          let rgx = /^-?\d+(\.\d+)?$/;
          if (!rgx.test(v)) {
            cb(new Error("字段格式异常，参考格式（15*8*5），单位：cm"));
            return;
          }
        }
        cb();
      },
    };
  }

  checkPropertyValueEmpty(prp: any, msg: string = "") {
    msg = msg ? msg : "该字段不能为空";
    return {
      trigger: ["blur", "change"],
      validator: (rule: any, value: FormDataProperty, cb: any) => {
        if (value.space) {
          if (value.values && value.values.length > 0) {
            cb();
          } else {
            cb(new Error(msg));
          }
        } else {
          if (value.value) {
            cb();
          } else {
            cb(new Error(msg));
          }
        }
      },
    };
  }

  skuCheck(idx: number) {
    return {
      trigger: ["blur", "change"],
      validator: (rule: any, value: string, cb: any) => {
        if (!value) {
          cb();
          return;
        }
        let spaces = this.formData.spaces;
        for (let sidx in spaces) {
          if (+sidx == idx) {
            continue;
          }
          let space = spaces[sidx];
          if (!space.sku) {
            continue;
          }
          if (space.sku == value) {
            cb(new Error("SKU重复"));
          }
        }
        cb();
      },
    };
  }

  private getIdxCode() {
    this.idCount++;
    return "CODE_" + this.idCount;
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async getCategoriesById(id: string) {
    let cats = this.catId2detail.get(id);
    if (cats) {
      return cats;
    }
    let cat = await mallCategoryApi.getDetail(id);
    cats = arrayUtils.tree2array(cat, "parentCategory").reverse();
    this.catId2detail.set(id, cats);
    return cats;
  }

  async initSortable() {
    let el: any = this.$refs.propertyDiv;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "formData.properties", {
      handle: ".y-sorthandle",
    });
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;

    let categories = await mallCategoryApi.getAllList();
    let root = arrayUtils.array2tree(categories, "id", "idParent", "children");
    this.oneLoadParam.categoryTree = root;

    await this.handleBrandAutocompleteSearch("");
  }

  /**
   * 初始化详情，实现数据回现
   */
  async initDetail() {
    if (this.query.id || this.query.cpid) {
      let id = this.query.id ? this.query.id : this.query.cpid;
      let detail = await mallProductApi.get(id);
      let parentCats = await this.getCategoriesById(detail.categoryId!);
      // 调整级联选择框内容
      let cIds = parentCats.map((x) => x.id!);

      // 品牌数据
      {
        if (this.bizData.brands.map((x) => x.id).indexOf(detail.brandId) < 0) {
          let brand = await brandApi.get(detail.brandId!);
          if (brand) {
            this.bizData.brands = [brand, ...this.bizData.brands];
          }
        }
      }

      // 属性梳理
      let properties: Array<FormDataProperty> = [];
      let propStructCode2struct: Map<
        string,
        MallProductCategoryPropertyStructBo
      > = new Map();
      parentCats.forEach((c) =>
        c.propertyStructs?.forEach((propStruct) => {
          propStructCode2struct.set(propStruct.code!, propStruct);
        })
      );

      // 挨个处理属性
      let propName2prop: Map<string, FormDataProperty> = new Map();
      let propCode2prop: Map<string, FormDataProperty> = new Map();
      for (let prop of detail.properties!) {
        let nprop = null;
        if (prop.code) {
          let struct = propStructCode2struct.get(prop.code);
          if (struct) {
            // 当前属性存在
            nprop = {
              ...prop,
              ...struct,
              values: [],
              space: false,
              idxCode: this.getIdxCode(),
            };
          } else {
            // 当前属性不存在
            nprop = {
              ...prop,
              code: "",
              values: [],
              space: false,
              idxCode: this.getIdxCode(),
            };
          }
        } else {
          // 自定义属性
          nprop = {
            ...prop,
            values: [],
            space: false,
            idxCode: this.getIdxCode(),
          };
        }
        if (nprop.code) {
          propCode2prop.set(nprop.code, <any>nprop);
        }
        propName2prop.set((nprop.groupName ?? "") + nprop.name, <any>nprop);
        properties.push(<any>nprop!);
      }

      // space处理
      let spaces: Array<FormDataSpace> = [];
      for (let ospace of detail.spaces!) {
        // 复制售卖单位
        let space: FormDataSpace = { ...ospace };

        // 处理属性信息
        for (let prop of ospace.properties!) {
          let nprop = null;
          if (prop.code) {
            // 以前是一个标准属性
            nprop = propCode2prop.get(prop.code);
            if (nprop) {
              // 已创建
              if (nprop.values.indexOf(prop.value!) < 0) {
                nprop.values.push(prop.value!);
              }
            } else {
              // 未创建
              let struct = propStructCode2struct.get(prop.code);
              if (struct) {
                // 新分类也是标准属性
                // 当前属性存在
                nprop = {
                  ...prop,
                  ...struct,
                  value: "",
                  values: [prop.value],
                  space: true,
                  idxCode: this.getIdxCode(),
                };
              } else {
                nprop = {
                  ...prop,
                  code: "",
                  value: "",
                  values: [prop.value],
                  space: true,
                  idxCode: this.getIdxCode(),
                };
              }
              if (nprop.code) {
                propCode2prop.set(nprop.code, <any>nprop);
              }
              propName2prop.set(
                (nprop.groupName ?? "") + nprop.name,
                <any>nprop
              );
              properties.push(<any>nprop!);
            }
          } else {
            nprop = propName2prop.get((prop.groupName ?? "") + prop.name);
            if (nprop) {
              // 已构建
              if (nprop.values.indexOf(prop.value!) < 0) {
                nprop.values.push(prop.value!);
              }
            } else {
              // 未构建
              nprop = {
                ...prop,
                value: "",
                values: [prop.value],
                space: true,
                idxCode: this.getIdxCode(),
              };
              propName2prop.set(
                (nprop.groupName ?? "") + nprop.name,
                <any>nprop
              );
              properties.push(<any>nprop!);
            }
          }
          // 追加space属性
          (<any>space)[`prop.${nprop!.idxCode}`] = prop.value;
        }

        spaces.push(space);
      }
      // 处理新增属性
      for (let struct of propStructCode2struct.values()) {
        if (propCode2prop.has(struct.code!)) {
          continue;
        }
        let nprop = {
          ...struct,
          value: "",
          values: [],
          space: false,
          idxCode: this.getIdxCode(),
        };
        properties.push(<any>nprop!);
      }

      let formData: FormData = {
        ...detail,
        categoryId: cIds,
        spaces: spaces,
        properties: properties,
      };

      this.formData = formData;
    }
  }

  async handleSpaceBathPutNameClick() {
    let value = this.formData.name;
    for (const space of this.formData.spaces) {
      let name = value;
      for (let prop of this.formData.properties) {
        let pval = (<any>space)[`prop.${prop.idxCode}`];
        if (pval) {
          name = name + " " + pval;
        }
      }
      space.name = name;
    }
  }

  async handleSpaceBathPutPicClick() {
    let pic = this.formData.pic;
    if (!pic) {
      return;
    }
    pic = pic.split(",")[0];
    for (const space of this.formData.spaces) {
      space.pic = pic;
    }
  }

  async handleSpaceBathPutSkuClick() {
    for (const space of this.formData.spaces) {
      space.sku = "";
    }
  }

  async handleSpaceBathPutClick(
    type: keyof Extract<SpaceBathPubForm, FormDataSpace>
  ) {
    let value = this.bizData.spaceBathPutForm![type];
    for (const space of this.formData.spaces) {
      (<any>space)[type] = value;
    }
  }

  async handleBrandAutocompleteSearch(keyword: string) {
    this.bizData.brandsLoading = true;
    try {
      this.bizData.brands = await mallBrandApi.getList({
        keyword,
        ban: false,
        rows: 10,
      });
    } finally {
      this.bizData.brandsLoading = false;
    }
  }

  async handlePropAddClick() {
    let nprop = <FormDataProperty>{};
    nprop.code = "";
    nprop.name = "";
    nprop.groupName = "";
    nprop.value = "";
    nprop.values = [];
    nprop.space = false;
    nprop.idxCode = this.getIdxCode();
    this.formData.properties?.push(nprop);

    await this.refreshUpdateSpaces();
  }

  async handlePropDeleteClick(idx: number) {
    this.formData.properties.splice(idx, 1);
    await this.refreshUpdateSpaces();
  }

  async handlePropSpaceChange(record: FormDataProperty) {
    if (record.space) {
      if (record.value) {
        record.values = [record.value];
      } else {
        record.values = [];
      }
      record.value = "";
    } else {
      if (record.values && record.values.length > 0) {
        record.value = record.values[0];
      } else {
        record.value = "";
      }
      record.values = [];
    }
    await this.refreshUpdateSpaces();
  }

  async handlePropTagChange() {
    await this.refreshUpdateSpaces();
  }

  async handlePropAutocompleteSearch(
    key: string,
    cb: (result: Array<any>) => void,
    record: FormDataProperty
  ) {
    if (!key || !record.categoryId || !record.code) {
      cb([]);
      return;
    }
    let query: BasicAuxyAutocompleteAutoGetAutocompletesQueryParam = {};
    query.keyword = key;
    query.bs_code = "MALL_CATEGORY_PROP";
    query.code = record.categoryId;
    query.attr = record.code;
    let results = await auxyAutocompleteApi.getAutocompletes(query);
    // 调用 callback 返回建议列表的数据
    cb(results);
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        this.$notify.error("存在部分字段未按照规定填写");
        return;
      }
      let data = this.formData;
      let body = objUtils.deepClone(data);
      // 整合数据
      const clearProp = (prop: any) => {
        delete prop.values;
        delete prop.space;
        delete prop.categoryId;
        delete prop.idxCode;
        for (let key in prop) {
          if (key.startsWith("prop.")) {
            delete prop[key];
          }
        }
      };
      (<any>body).categoryId = body.categoryId[body.categoryId.length - 1];
      let properties = body.properties;
      let productPropties = properties.filter((x) => !x.space);
      productPropties.forEach((x: any) => clearProp(x));
      body.properties = productPropties;

      let spacePropties = properties.filter((x) => x.space);
      (<any>body).spaces = body.spaces.map((x: any) => {
        let space: MallProductModifySpaceVo = { ...x };
        let props: Array<MallProductPropertyBo> = [];
        for (let sp of spacePropties) {
          let prop: MallProductPropertyBo = { ...sp };
          prop.value = x[`prop.${sp.idxCode}`];
          clearProp(prop);
          props.push(prop);
        }
        space.properties = props;
        return space;
      });
      if (this.query.id) {
        // 更新
        await mallProductApi.modify(this.query.id, <any>body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await mallProductApi.add(<any>body);
        this.query.id = detail.id!;
        for (let i = 0; i < data.spaces.length; i++) {
          this.formData.spaces[i].id = data.spaces[i].id;
        }
        routerUtils.putQueryValue(this, { id: this.query.id });
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack(true);
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg = this.$tc("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg = this.$tc("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack(success = false) {
    // this.$router.push(`/mall/product${success ? "?state=NEW" : ""}`);
    this.$router.back();
  }

  /**
   * 同属性值的单元格合并
   */
  objectSpanMethod(record: {
    row: FormDataSpace;
    column: any;
    rowIndex: number;
    columnIndex: number;
  }) {
    if (!record.column.property) {
      return;
    }
    let prop: `prop.${string}` = record.column.property;
    if (!prop.startsWith("prop.")) {
      return;
    }
    let nValue = (record.row as any)[prop];
    if (!nValue) {
      return;
    }
    let spaces = this.formData.spaces;
    if (!spaces) {
      return;
    }
    if (record.rowIndex > 0) {
      let nv: any = spaces[record.rowIndex - 1];
      if (nv && nv[prop] == nValue) {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
    let idx = 0;
    while (spaces.length > record.rowIndex + idx + 1) {
      let nv: any = spaces[record.rowIndex + idx + 1];
      if (!nv) {
        break;
      }
      if (nv[prop] != nValue) {
        break;
      }
      idx++;
    }
    if (idx <= 0) {
      return;
    }
    return {
      rowspan: idx + 1,
      colspan: 1,
    };
  }

  /**
   * 刷新space中的数据，进行重新匹配以及重分配
   */
  async refreshUpdateSpaces() {
    let ps: Array<FormDataProperty> = this.formData.properties!;
    let spacesProperties = ps.filter((x) => x.space); // space属性

    let newSpacePropertyTypes: Array<FormDataSpacePropType> = [];
    const putSpaces = (spaceProp: FormDataSpacePropType, idx: number) => {
      if (spacesProperties.length <= idx) {
        // 深度到达
        newSpacePropertyTypes.push(spaceProp);
        return;
      }
      let prop = spacesProperties[idx];
      if (!prop.values || prop.values.length <= 0) {
        putSpaces(spaceProp, idx + 1);
        return;
      }
      for (let value of prop.values) {
        let nsp: FormDataSpacePropType = { ...spaceProp };
        (<any>nsp)[`prop.${prop.idxCode!}`] = value;
        putSpaces(nsp, idx + 1);
      }
    };
    putSpaces({}, 0);

    let oldSpaces: Array<FormDataSpace> = this.formData.spaces;
    let spaces: Array<FormDataSpace> = [];
    const getPropCount = (obj: any) => {
      if (!obj) {
        return 0;
      }
      let count = 0;
      for (let k in obj) {
        if (k.startsWith("prop.")) {
          count++;
        }
      }
      return count;
    };
    // 循环处理
    let ids = new Set();
    const findSpaceByNewProp = (
      spacePropType: any
    ): { used: FormDataSpace | null; noused: FormDataSpace | null } => {
      let used = null;
      for (let oldSpace of oldSpaces) {
        let diffCount = 0;
        for (let key in spacePropType) {
          if (!key.startsWith("prop.")) {
            continue;
          }
          if ((<any>oldSpace)[key] != (<any>spacePropType)[key]) {
            diffCount++;
            if (used) {
              break;
            }
          }
        }
        if (diffCount <= 0) {
          if (!oldSpace.id || !ids.has(oldSpace.id)) {
            return { noused: oldSpace, used: null };
          } else {
            // 一样的优先，直接覆盖
            used = oldSpace;
          }
        } else if (diffCount == 1) {
          // 不一样的需要判断有没有，有就跳过
          if (!used) {
            used = oldSpace;
          }
        }
      }
      return { used, noused: null };
    };
    const findSpaceByOldProp = (
      spacePropType: any
    ): { used: FormDataSpace | null; noused: FormDataSpace | null } => {
      let used = null;
      for (let oldSpace of oldSpaces) {
        let ok = true;
        for (let key in oldSpace) {
          if (!key.startsWith("prop.")) {
            continue;
          }
          if ((<any>oldSpace)[key] != (<any>spacePropType)[key]) {
            ok = false;
            break;
          }
        }
        if (ok) {
          if (!oldSpace.id || !ids.has(oldSpace.id)) {
            return { noused: oldSpace, used: null };
          } else {
            used = oldSpace;
          }
        }
      }
      return { used, noused: null };
    };
    const newSpace = (spacePropType: FormDataSpacePropType): FormDataSpace => {
      let space: FormDataSpace = {
        ...spacePropType,
        selling: true,
        name: "",
        unitPrice: 0,
        unitPriceShow: 0,
        upc: "",
        sku: "",
        pic: "",
        quantity: 0,
        baseVolume: "",
        baseWeight: 1,
      };
      return space;
    };
    const copySpace = (
      oldSpace: FormDataSpace,
      spacePropType: FormDataSpacePropType
    ): FormDataSpace => {
      let space = { ...oldSpace };
      for (let key in space) {
        if (key.startsWith("prop.")) {
          delete (<any>space)[key];
        }
      }
      space = { ...space, ...spacePropType };
      return space;
    };
    for (let spacePropType of newSpacePropertyTypes) {
      if (oldSpaces.length <= 0) {
        let space = newSpace(spacePropType);
        spaces.push(space!);
      } else {
        let oldPropCount = getPropCount(oldSpaces[0]);
        let newPropCount = getPropCount(spacePropType);
        if (oldPropCount >= newPropCount) {
          // 旧的比新的多，说明减少了字段
          // 旧属性和新属性一样，说明修改了数据
          let result = findSpaceByNewProp(spacePropType);
          if (result.noused) {
            let space = copySpace(result.noused, spacePropType);
            spaces.push(space);
            if (space.id) {
              ids.add(space.id);
            }
          } else if (result.used) {
            let space = copySpace(result.used, spacePropType);
            delete space.id;
            spaces.push(space);
          } else {
            let space = newSpace(spacePropType);
            spaces.push(space!);
          }
        } else {
          // 新的比旧的多，说明增加了字段
          let result = findSpaceByOldProp(spacePropType);
          if (result.noused) {
            let space = copySpace(result.noused, spacePropType);
            spaces.push(space);
            if (space.id) {
              ids.add(space.id);
            }
          } else if (result.used) {
            let space = copySpace(result.used, spacePropType);
            delete space.id;
            spaces.push(space);
          } else {
            let space = newSpace(spacePropType);
            spaces.push(space!);
          }
        }
      }
    }

    this.formData.spaces = spaces;
  }

  /**
   * 刷新分类信息以及对应的属性信息
   */
  async refreshCategoryAndProperties() {
    let detail = this.formData;

    // 调整默认显示分类信息
    let ps: Array<FormDataProperty> = this.formData.properties!;
    let customProperties = ps.filter((x) => !x.code); // 自定义属性

    if (detail.categoryId && detail.categoryId.length > 0) {
      let categoryId = detail.categoryId[detail.categoryId.length - 1];
      let parentCats = await this.getCategoriesById(categoryId);

      // 调整级联选择框内容
      let cIds = parentCats.map((x) => x.id!);
      this.formData.categoryId = cIds;

      // 调整默认显示属性列表
      let propCode2prop: Map<string, FormDataProperty> = new Map();
      ps.forEach((x) => x.code && propCode2prop.set(x.code!, x));

      let defProp: Array<FormDataProperty> = [];
      parentCats.forEach((c) =>
        c.propertyStructs?.forEach((propStruct) => {
          let nprop: FormDataProperty | undefined = propCode2prop.get(
            propStruct.code!
          );
          if (!nprop) {
            nprop = {
              ...propStruct,
              value: "",
              values: [],
              space: false,
              idxCode: this.getIdxCode(),
            };
          }
          defProp.push(nprop!);
        })
      );
      ps = [...defProp, ...customProperties];
    } else {
      ps = [...customProperties];
    }

    this.formData.properties = ps;

    await this.refreshUpdateSpaces();
  }

  @Watch("formData.categoryId", { deep: true })
  async formDataCategoryIdWatch(nv: Array<string>, ov: Array<string>) {
    if (nv[nv.length - 1] === ov[ov.length - 1]) {
      return;
    }
    this.refreshCategoryAndProperties();
  }
}
</script>

<style lang="scss" scoped>
.page-mall-product-edit {
  // .value-tag {
  //   .el-tag {
  //     margin-right: 10px;
  //   }
  //   .button-new-tag {
  //     margin-right: 10px;
  //   }
  //   .input-new-tag {
  //     margin-right: 10px;
  //     width: 120px;
  //   }
  // }
}
.space-batch-put {
  border: solid 1px #ccc;
  display: flex;

  .label {
    height: 100%;
    width: 100px;
  }

  .value {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .space-bath-label {
    margin-left: 12px;
  }
  .space-bath-submit {
    margin-left: 4px;
  }
}

.space-table::v-deep table tbody .cell {
  min-height: 70px;
}

.property-table::v-deep table tbody .cell {
  min-height: 60px;
}
</style>
