<template lang="pug">
.page-mall-product-index
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades {{ $t("menu.mall-product") }}
  .container
    //- 表头
    .handle-box
      el-button(type="primary", icon="el-icon-plus", @click="handleAddClick") {{ $t("common.base.add") }}
      el-cascader(
        v-model="query.categoryId",
        :options="oneLoadParam.categoryTree",
        :props="{ checkStrictly: true, label: 'name', value: 'id' }",
        clearable,
        placeholder="分类过滤",
        @change="handleSearchClick"
      )
      el-select(v-model="query.state", @change="handleSearchClick")
        el-option(key="DEF", label="默认", value="DEF")
        el-option(
          v-for="option in $dic.getOptions('mall.product.state')",
          :key="option.value",
          :label="option.label",
          :value="option.value"
        )
      el-select(
        v-model="query.brandId",
        filterable,
        remote,
        clearable,
        reserve-keyword,
        :remote-method="handleBrandAutocompleteSearch",
        placeholder="请输入品牌关键词",
        :loading="bizData.brandsLoading",
        @change="handleSearchClick"
      )
        el-option(
          v-for="item in bizData.brands",
          :key="item.id",
          :label="item.name",
          :value="item.id"
        )
      el-input.handle-input(
        v-model="query.keyword",
        placeholder="关键词",
        @keyup.enter.native="handleSearchClick"
      )
      el-button(
        type="primary",
        icon="el-icon-search",
        @click="handleSearchClick"
      ) 搜索
      //- a.downswagger(:href="swaggerUrl", target="_blank") 下载Swagger文档
    //- 表单内容
    el-table.table(
      :data="products",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading",
      @expand-change="handleProductsExpandChange"
    )
      //- SPU详细信息
      el-table-column(type="expand", label="规格")
        template(#default="scope")
          el-form.demo-table-expand(label-position="left", label-width="80px")
            el-form-item(label="商品名称: ")
              span {{ scope.row.name }}
            el-form-item(label="商品分类: ")
              span {{ getCategoryShowNameById(scope.row.categoryId) }}
            el-form-item(label="SPU: ")
              span {{ scope.row.spu }}
            el-form-item(label="商品类型: ")
              span {{ $dic.getFieldValue("mall.product.type", scope.row.type, scope.row.type) }}

            //- SKU 列表
            el-form-item(label="商品规格: ")
              .space-batch-put 批量操作
                span.space-bath-label 允许预定：
                span.space-bath-value
                  el-switch(
                    v-model="bizData.batchStock.formData.allowNegative",
                    active-color="#13ce66",
                    inactive-color="#ff4949"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部预定开关设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick(scope.row, 'allowNegative', bizData.batchStock.formData.allowNegative)"
                  )
                    el-button(slot="reference", size="mini") 设置
                span.space-bath-label 预定数量：
                span.space-bath-value
                  el-input-number(
                    controls-position="right",
                    size="mini",
                    style="width: 100px",
                    v-model="bizData.batchStock.formData.incoming"
                  )
                span.space-bath-submit
                  el-popconfirm(
                    title="该操作将覆盖当前值，确认要将全部预定数量设置为当前数据吗？",
                    @confirm="handleSpaceBathPutClick(scope.row, 'incoming', bizData.batchStock.formData.incoming)"
                  )
                    el-button(slot="reference", size="mini") 设置
                span.space-bath-submit
                  el-button(
                    slot="reference",
                    size="mini",
                    @click="handleSpaceBathPutQuantiryClick(scope.row)"
                  ) 批量调整库存

              //- sku列表字段
              el-table(:data="scope.row.spaces")
                el-table-column(label="ID", prop="id")
                el-table-column(label="名称", prop="name")
                el-table-column(label="SKU", prop="sku")
                el-table-column(label="单价", prop="unitPrice")
                el-table-column(label="展示单价", prop="unitPriceShow")
                el-table-column(label="可销售")
                  template(#default="scope")
                    el-tag(:type="scope.row.selling ? 'success' : 'danger'") {{ scope.row.selling ? "可销售" : "不销售" }}
                el-table-column(label="当前库存数", prop="stock.quantity")
                  template(#default="scope")
                    //- span {{ scope.row.stock ? (scope.row.stock.quantity >= 0 ? scope.row.stock.quantity : 0 - scope.row.stock.quantity + " (预售)") : "加载中" }}
                    //- span {{ scope.row.stock ? scope.row.stock.quantity : "加载中" }}
                    span {{ scope.row.stock ? (scope.row.stock.quantity >= 0 ? scope.row.stock.quantity : `${scope.row.stock.quantity} (预售量，还可预售：${scope.row.stock.incoming + scope.row.stock.quantity})`) : "加载中" }}
                el-table-column(label="允许预定", prop="stock.allowNegative")
                  template(#default="scope")
                    el-switch(
                      v-if="scope.row.stock",
                      v-model="scope.row.stock.allowNegative",
                      @change="handleStockAllowNegativeChange(scope.row.stock)"
                    )
                    div(v-else) 加载中
                el-table-column(
                  label="可预订库存数",
                  prop="stock.incoming",
                  width="130"
                )
                  template(#default="scope")
                    template(v-if="scope.row.stock")
                      template(v-if="scope.row.stock.changeIncoming")
                        el-input-number(
                          size="mini",
                          controls-position="right",
                          v-model="scope.row.stock.changeIncomingValue",
                          style="width: 100px"
                        )
                        el-button.mgl10(
                          type="text",
                          @click="handleStockIncomingSubmitClick(scope.row)"
                        ) 提交
                        el-button.mgl10(
                          type="text",
                          @click="handleStockIncomingCancelClick(scope.row)"
                        ) 取消
                      template(v-else)
                        span {{ scope.row.stock.incoming }}
                        el-button.mgl10(
                          type="text",
                          @click="handleStockIncomingClick(scope.row)"
                        ) 修改
                    template(v-else)
                      span 加载中
                el-table-column(label="操作", prop="operate")
                  template(#default="sscope")
                    el-button(
                      v-if="sscope.row.stock",
                      type="text",
                      @click="handleStockClick(scope.row, sscope.row)"
                    ) 调整库存

      //- SPU列表字段
      el-table-column(prop="pic", label="图片")
        template(#default="scope")
          el-image(
            :src="getImageUrlOne(scope.row.pic)",
            :preview-src-list="getImageUrls(scope.row.pic)",
            :lazy="true",
            style="width: 100px; height: 100px"
          )
      el-table-column(prop="name", label="名称")
      el-table-column(prop="categoryId", label="分类")
        template(#default="scope") {{ getCategoryShowNameById(scope.row.categoryId) }}
      el-table-column(prop="brandName", label="品牌")
      el-table-column(prop="spu", label="SPU")
      el-table-column(prop="state", label="状态")
        template(#default="scope")
          el-tag(
            :type="$dic.getFieldPropValue('mall.product.state', scope.row.state, 'tag-type', scope.row.state)"
          ) {{ $dic.getFieldValue("mall.product.state", scope.row.state, scope.row.state) }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div
            p {{ $moment(scope.row.createTime).format("YYYY-MM-DD") }}
            p {{ $moment(scope.row.createTime).format("HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(type="text", @click="handleDetailClick(scope.row)") {{ $t("common.base.detail") }}
          el-button(
            v-if="!scope.row.archived",
            type="text",
            @click="handleUpClick(scope.row, !scope.row.up)"
          ) {{ !scope.row.up ? "上架" : "下架" }}
          el-button(type="text", @click="handleCopyClick(scope.row)") 复制
          el-button(
            v-if="!scope.row.up && !scope.row.archived",
            type="text",
            @click="handleModifyClick(scope.row)"
          ) {{ $t("common.base.modify") }}
          el-button.red(
            v-if="scope.row.archived",
            slot="reference",
            type="text",
            @click="handleArchivedClick(scope.row, !scope.row.archived)"
          ) 取消归档
          el-popconfirm.mgl10(
            v-if="!scope.row.up && !scope.row.archived",
            title="确认要归档数据吗？",
            @confirm="handleArchivedClick(scope.row, !scope.row.archived)"
          )
            el-button.red(slot="reference", type="text") 归档

    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )

  //- 库存调整
  el-dialog(
    title="库存调整",
    :visible.sync="bizData.stock.dialogVisible",
    width="50%",
    :before-close="() => (bizData.stock.dialogVisible = false)"
  )
    el-form(
      ref="stockForm",
      :model="bizData.stock.formData",
      label-width="80px",
      v-loading="bizData.stock.formLoading"
    )
      el-form-item(
        label="操作类型",
        prop="type",
        :rules="[$rule.required(null, '操作类型不能为空')]",
        required
      )
        el-radio-group(v-model="bizData.stock.formData.type")
          el-radio-button(label="ADD") 增减
          el-radio-button(label="SET") 设置
      el-form-item(
        label="数量",
        prop="num",
        :rules="[stockFormRuleNum()]",
        required
      )
        el-input-number(
          controls-position="right",
          v-model="bizData.stock.formData.num"
        )
        span.mgr10 当前库存：{{ bizData.stock.validStock.quantity }}
      el-form-item(label="备注", prop="remark")
        el-input(
          v-model="bizData.stock.formData.remark",
          type="textarea",
          :rows="2"
        )

      //- span.dialog-footer(slot="footer")
      el-form-item
        el-button(type="primary", @click="handleStockSubmitClick") 确 定
        el-button(@click="bizData.stock.dialogVisible = false") 取 消
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import mallCategoryApi, { MallCategoryListVo } from "~/api/mall/mg/categoryApi";
import mallProductApi, {
  MallMgProductGetListCountQueryParam,
  MallMgProductGetListQueryParam,
  MallProductListSpaceVo,
  MallProductListVo,
} from "~/api/mall/mg/productApi";
import mallStockApi, {
  MallProductStock,
  StockDeductParamVo,
  StockModifyAttrParamBo,
} from "~/api/mall/mg/product/stockApi";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import routerUtils from "~/utils/routerUtils";
import urlUtils from "~/utils/urlUtils";
import mallBrandApi, { MallBrand } from "~/api/mall/mg/brandApi";
import stringUtils from "~/utils/stringUtils";
import brandApi from "~/api/mall/mg/brandApi";
import { BadRequestException } from "~/utils/request";
import compUtils from "~/utils/compUtils";

type CategoryTreeNode = MallCategoryListVo & {
  children?: Array<CategoryTreeNode>;
};

type TableMallProductListSpaceStockVo = MallProductStock & {
  changeIncomingValue?: number;
  changeIncoming?: boolean;
};

type TableMallProductListSpaceVo = MallProductListSpaceVo & {
  stock?: TableMallProductListSpaceStockVo | null;
};

type TableProductListVo = Omit<MallProductListVo, "spaces"> & {
  brandName?: string;
  /**
   * 不同规格数据
   */
  spaces?: Array<TableMallProductListSpaceVo>;
};

type CallbackType<R> = () => R;

@Component({
  name: "role-mall-product",
})
export default class MallProductIndex extends mixins(BaseVue) {
  loading: boolean = true;
  query = {
    page: 1,
    rows: 20,
    total: 0,
    keyword: "",
    brandId: "",
    categoryId: <Array<string>>[],
    state: "UP", // DEF, UP, DOWN, ARCHIVED
  };
  oneLoadParam = {
    inited: false,
    categoryTree: <Array<CategoryTreeNode>>[],
    catId2cat: <Map<string, MallCategoryListVo> | null>null,
  };
  bizData = {
    brands: <MallBrand[]>[],
    brandsLoading: false,
    brandId2brand: new Map<string, MallBrand | null>(),

    batchStock: {
      formData: {
        allowNegative: false,
        incoming: 0,
      },
    },
    stock: {
      dialogVisible: false,
      submitCb: <CallbackType<Promise<void>> | null>null,
      validStock: {
        quantity: 0,
      },

      row: <TableProductListVo | null>null,
      spaceRow: <TableMallProductListSpaceVo | null>null,

      formLoading: false,

      formData: {
        type: "",
        num: 0,
        remark: "",
      },
    },
  };
  products: Array<TableProductListVo> = [];

  stockFormRuleNum = (msg: string = "") => {
    return {
      trigger: ["blur", "change"],
      validator: (rule: any, value: any, cb: any) => {
        if (value == undefined || value == null) {
          cb(new Error("不能为空"));
          return;
        }
        let vn = +value;
        if ("ADD" == this.bizData.stock.formData.type) {
          if (vn == 0) {
            cb(new Error("增减库存时，数量不能为0"));
            return;
          } else {
            if (vn > 0) {
              cb();
              return;
            }
            let stock = this.bizData.stock.validStock!;
            if (!stock) {
              cb();
              return;
            }
            if (vn < 0 - stock.quantity!) {
              cb(
                new Error(
                  `增减库存时，数量不能小于（0-当前库存数的），当前最小：${
                    0 - stock.quantity! < 0 ? 0 - stock.quantity! : 1
                  }`
                )
              );
              return;
            }
          }
        }
        cb();
      },
    };
  };

  created() {
    this.query.page = +routerUtils.getQueryValue(this, "page", "1");
    this.query.rows = +routerUtils.getQueryValue(this, "rows", "20");
  }

  async activated() {
    await this.init();
  }

  async init() {
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;

    let categories = await mallCategoryApi.getAllList();
    let catId2cat = new Map();
    categories.forEach((x) => catId2cat.set(x.id, x));
    this.oneLoadParam.catId2cat = catId2cat;

    let root: Array<CategoryTreeNode> = arrayUtils.array2tree(
      categories,
      "id",
      "idParent",
      "children"
    );
    this.oneLoadParam.categoryTree = [...root];
  }

  async initDetail() {
    this.query.keyword = routerUtils.getQueryValue(this, "keyword", "");
    this.query.state = routerUtils.getQueryValue(this, "state", "DEF");
    let categoryId = routerUtils.getQueryValue(this, "category_id", "");
    if (categoryId) {
      let parentCats = this.getCategoriesById(categoryId);
      // 调整级联选择框内容
      let cIds = parentCats.map((x) => x.id!);
      this.query.categoryId = cIds;
    }
    this.query.brandId = routerUtils.getQueryValue(this, "brand_id", "");
    if (this.query.brandId) {
      try {
        let brand = await mallBrandApi.get(this.query.brandId);
        this.bizData.brands.push(brand);
      } catch (error) {
        this.query.brandId = "";
      }
    }
    this.query.page = +routerUtils.getQueryValue(this, "page", "1");
    this.query.rows = +routerUtils.getQueryValue(this, "rows", "20");
    await this.loadContentCount();
  }

  getCategoriesById(id: string | undefined) {
    let cats = [];
    while (id) {
      let cat = this.oneLoadParam.catId2cat!.get(id);
      if (cat) {
        cats.push(cat);
        id = cat.idParent;
      } else {
        id = undefined;
      }
    }
    return cats.reverse();
  }

  getCategoryShowNameById(id: string | null | undefined) {
    let name = "";
    while (id) {
      let cat = this.oneLoadParam.catId2cat!.get(id);
      if (cat) {
        if (name) {
          name = cat.name + " > " + name;
        } else {
          name = cat.name!;
        }
        id = cat.idParent;
      } else {
        id = null;
      }
    }
    return name;
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  getCategoryId(categoryIds: Array<string>) {
    return categoryIds && categoryIds.length > 0
      ? categoryIds[categoryIds.length - 1]
      : "";
  }

  async loadContentCount() {
    let param: MallMgProductGetListCountQueryParam = {};
    param.keyword = this.query.keyword;
    param.categoryId = this.getCategoryId(this.query.categoryId);
    routerUtils.putQueryValue(this, stringUtils.objectKeyToSnake(param)!);

    param.state =
      "DEF" == this.query.state ? ["NEW", "UP"] : [this.query.state];

    this.loading = true;
    try {
      this.query.total = await mallProductApi.getListCount(param);
      if (this.query.total > 0) {
        await this.loadContent();
      } else {
        this.products = [];
      }
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  async getBrandsByIds(ids: string[]): Promise<MallBrand[]> {
    let brandId2brand = this.bizData.brandId2brand;
    let qids = ids.filter((id) => !brandId2brand.has(id));
    if (qids.length > 0) {
      let brands = await brandApi.getList({ id: ids });
      brands.forEach((b) => brandId2brand.set(b.id!, b));
    }
    let rbs: MallBrand[] = [];
    for (const id of ids) {
      let b = brandId2brand.get(id);
      if (b) {
        rbs.push(b);
      } else {
        brandId2brand.set(id, null);
      }
    }
    return rbs;
  }

  async loadContent() {
    let param: MallMgProductGetListQueryParam = {};
    param.keyword = this.query.keyword;
    param.brandId = this.query.brandId;
    param.categoryId = this.getCategoryId(this.query.categoryId);
    param.page = this.query.page ?? 0;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, stringUtils.objectKeyToSnake(param)!);
    param.state =
      "DEF" == this.query.state ? ["NEW", "UP"] : [this.query.state];

    param.page = param.page - 1;

    this.loading = true;
    try {
      let products: TableProductListVo[] = await mallProductApi.getList(param);
      if (products && products.length > 0) {
        let bids = products.map((x) => x.brandId!);
        let brands = await this.getBrandsByIds(bids);
        products = arrayUtils.marginArray(
          products,
          "brandId",
          brands,
          "id",
          (p, b) => ({ ...p, brandName: b ? b.name : "" })
        );
      }
      this.products = products;
    } catch (error) {
      this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
      console.log(error);
    } finally {
      this.loading = false;
    }
  }

  // 批量设置库存参
  async handleSpaceBathPutClick(
    product: TableProductListVo,
    key: keyof StockModifyAttrParamBo,
    value: any
  ) {
    let stocks = product.spaces!.map((x) => x.stock!);
    let params: StockModifyAttrParamBo[] = [];
    for (const stock of stocks) {
      let param: StockModifyAttrParamBo = {};
      param.allowNegative = stock.allowNegative;
      param.depotId = stock.depotId;
      param.incoming = stock.incoming;
      param.spaceId = stock.spaceId;
      param[key] = value;
      params.push(param);
    }
    try {
      await mallStockApi.batchModify(params);
      for (const space of product.spaces!) {
        space.stock![key] = value;
      }
    } catch (error) {
      throw error;
    }
  }

  // 开始批量库存设置
  async handleSpaceBathPutQuantiryClick(row: TableProductListVo) {
    await this.refreshProductStock(row);

    this.bizData.stock.submitCb = this.batchStockSubmit;
    this.bizData.stock.row = row;
    this.bizData.stock.spaceRow = null;
    let minQuantity = Math.min(...row.spaces!.map((x) => x.stock!.quantity!));
    this.bizData.stock.validStock.quantity = minQuantity;

    this.bizData.stock.formData = {
      type: "ADD",
      num: 0,
      remark: "",
    };
    this.bizData.stock.dialogVisible = true;
  }

  // 库存数调整
  async batchStockSubmit() {
    if (!this.bizData.stock.row) {
      return;
    }
    if (!(await this.$rule.formCheck(this, "stockForm"))) {
      return;
    }

    this.bizData.stock.formLoading = true;
    let product = this.bizData.stock.row;

    let params: StockDeductParamVo[] = [];
    for (const space of product.spaces!) {
      let param: StockDeductParamVo = {
        ...space.stock,
        ...this.bizData.stock.formData,
      };
      param.allowNegative = false;
      params.push(param);
    }
    try {
      await mallStockApi.batchDeduct(params);

      this.bizData.stock.dialogVisible = false;
      this.bizData.stock.row = null;
      this.bizData.stock.spaceRow = null;

      // 刷新库存数据
      await this.refreshProductStock(product);

      this.$notify.success("调整成功");
    } catch (error) {
      throw error;
    } finally {
      this.bizData.stock.formLoading = false;
    }
  }

  // 开始预定库存数
  async handleStockIncomingClick(space: TableMallProductListSpaceVo) {
    this.$set(space.stock!, "changeIncomingValue", 0 + space.stock!.incoming!);
    this.$set(space.stock!, "changeIncoming", true);
    // stock.changeIncoming = true;
  }

  // 取消设置
  async handleStockIncomingCancelClick(space: TableMallProductListSpaceVo) {
    this.$set(space.stock!, "changeIncoming", false);
  }

  // 提交设置预定库存
  async handleStockIncomingSubmitClick(space: TableMallProductListSpaceVo) {
    let stock = space.stock!;
    let param: StockModifyAttrParamBo = {};
    param.allowNegative = stock.allowNegative;
    param.depotId = stock.depotId;
    param.incoming = stock.changeIncomingValue;
    param.spaceId = stock.spaceId;
    try {
      await mallStockApi.batchModify([param]);
      this.$set(space.stock!, "incoming", stock.changeIncomingValue);
      this.$set(space.stock!, "changeIncoming", false);
    } catch (error) {
      throw error;
    }
  }

  // 设置开关预定库存
  async handleStockAllowNegativeChange(
    stock: TableMallProductListSpaceStockVo
  ) {
    let param: StockModifyAttrParamBo = {};
    param.allowNegative = stock.allowNegative;
    param.depotId = stock.depotId;
    param.incoming = stock.incoming;
    param.spaceId = stock.spaceId;
    try {
      await mallStockApi.batchModify([param]);
    } catch (error) {
      stock.allowNegative = !stock.allowNegative;
      throw error;
    }
  }

  // 开始进行预定库存
  async handleStockClick(
    row: TableProductListVo,
    spaceRow: TableMallProductListSpaceVo
  ) {
    await this.refreshProductStock(row);

    this.bizData.stock.submitCb = this.oneStockSubmit;
    this.bizData.stock.row = row;
    this.bizData.stock.spaceRow = spaceRow;
    this.bizData.stock.validStock.quantity = spaceRow.stock?.quantity!;

    this.bizData.stock.formData = {
      type: "ADD",
      num: 0,
      remark: "",
    };
    this.bizData.stock.dialogVisible = true;
  }

  async handleStockSubmitClick() {
    if (this.bizData.stock.submitCb) {
      await this.bizData.stock.submitCb();
    }
  }

  // 库存数调整
  async oneStockSubmit() {
    if (!this.bizData.stock.row || !this.bizData.stock.spaceRow) {
      return;
    }
    if (!(await this.$rule.formCheck(this, "stockForm"))) {
      return;
    }

    this.bizData.stock.formLoading = true;
    let spaceRow: TableMallProductListSpaceVo = this.bizData.stock.spaceRow;
    try {
      let param: StockDeductParamVo = {
        ...spaceRow.stock,
        ...this.bizData.stock.formData,
      };
      param.allowNegative = false;
      await mallStockApi.batchDeduct([param]);

      // let row = this.bizData.stock.row!;
      // this.$set(row, "spaces", [...row.spaces!]);

      this.bizData.stock.dialogVisible = false;
      let row = this.bizData.stock.row;
      this.bizData.stock.row = null;
      this.bizData.stock.spaceRow = null;

      // 刷新库存数据
      await this.refreshProductStock(row);

      this.$notify.success("调整成功");
    } catch (error) {
      if (error instanceof BadRequestException) {
        if ("OUT_OF_STOCK" == error.code) {
          // 刷新库存数据
          let stocks = await mallStockApi.getList({
            space_id: [spaceRow.stock!.spaceId!],
            depot_id: [spaceRow.stock!.depotId!],
          });
          if (stocks.length <= 0) {
            throw error;
          }
          this.bizData.stock.spaceRow!.stock = stocks[0];
          this.bizData.stock.validStock.quantity = stocks[0].quantity!;
          await this.$rule.formCheck(this, "stockForm");
          return;
        }
      }
      throw error;
    } finally {
      this.bizData.stock.formLoading = false;
    }
  }

  // 刷新当前库存数
  async refreshProductStock(row: TableProductListVo) {
    if (!row.spaces || !row.spaces[0]) {
      return;
    }
    let sids: string[] = row.spaces!.map((x) => x.id!);
    let stocks = await mallStockApi.getList({
      space_id: sids,
      depot_id: ["ALL"],
      rows: 2000,
    });
    let id2stock = new Map();
    stocks.forEach((x) => id2stock.set(x.spaceId, x));
    for (let i = 0; i < row.spaces!.length; i++) {
      let space = row.spaces![i];
      space.stock = id2stock.get(space.id);
      row.spaces[i] = { ...space };
    }
    this.$set(row, "spaces", [...row.spaces]);
  }

  // 展开列表详情时触发
  async handleProductsExpandChange(row: TableProductListVo) {
    if (!row.spaces || !row.spaces[0] || row.spaces[0].stock) {
      return;
    }
    await this.refreshProductStock(row);
  }

  async handleBrandAutocompleteSearch(keyword: string) {
    this.bizData.brandsLoading = true;
    try {
      this.bizData.brands = await mallBrandApi.getList({
        keyword,
        rows: 10,
      });
    } finally {
      this.bizData.brandsLoading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleUpClick(record: MallProductListVo, up: boolean = true) {
    this.loading = true;
    try {
      await mallProductApi.batchUp({
        mallIds: ["malldef"],
        operate: up ? "UP" : "DOWN",
        ids: [record.id!],
      });
      await this.loadContentCount();
    } finally {
      this.loading = false;
    }
  }

  async handleAddClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/product/edit`
    );
  }
  async handleDetailClick(record: MallProductListVo) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/product/edit?id=${
        record.id
      }&edit=false`
    );
  }

  async handleCopyClick(record: MallProductListVo) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/product/edit?cpid=${
        record.id
      }`
    );
  }

  async handleModifyClick(record: MallProductListVo) {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/mall/product/edit?id=${
        record.id
      }`
    );
  }

  async handleArchivedClick(
    record: MallProductListVo,
    archived: boolean = true
  ) {
    this.loading = true;
    try {
      await mallProductApi.batchArchived({
        ids: [record.id!],
        archived,
      });
      await this.loadContentCount();
    } finally {
      this.loading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
  & > * {
    margin-right: 10px;
  }
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}

.space-batch-put {
  border: solid 1px #ccc;
  .space-bath-label {
    margin-left: 12px;
  }
  .space-bath-submit {
    margin-left: 4px;
  }
}
</style>

