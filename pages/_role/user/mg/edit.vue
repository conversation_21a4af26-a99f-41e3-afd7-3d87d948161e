<template lang="pug">
.page-mall-product-edit
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        a(@click="goBack")
          i.el-icon-lx-calendar 用户列表
      el-breadcrumb-item {{ query.id ? (query.edit ? $t("common.base.edit") : $t("common.base.detail")) : $t("common.base.add") }}
  .container
    .y-page-form.form-box
      el-form(
        ref="form",
        :model="formData",
        v-loading="loading",
        label-width="80px"
      )
        el-form-item(
          label="名称",
          prop="name",
          :rules="[$rule.required(null, '名称不能为空')]",
          required
        )
          el-input(v-model="formData.name", :disabled="!query.edit")
        el-form-item(
          label="分类",
          prop="categoryId",
          :rules="[$rule.required(null, '分类不能为空')]",
          required
        )
          el-cascader(
            v-model="formData.categoryId",
            :options="categoryTree",
            :props="{ checkStrictly: false, label: 'name', value: 'id' }",
            clearable,
            :disabled="!!query.id || !query.edit",
            style="width: 100%"
          )
        el-form-item(
          label="品牌",
          prop="brandId",
          :rules="[$rule.required(null, '品牌不能为空')]",
          required
        )
          el-input(
            v-model="formData.brandName",
            :disabled="!!query.id || !query.edit"
          )
        el-form-item(
          label="类型",
          prop="type",
          :rules="[$rule.required(null, '类型不能为空')]",
          required
        )
          el-select(
            v-model="formData.type",
            :disabled="!!query.id || !query.edit"
          )
            el-option(
              v-for="option in $dic.getOptions('mall.product.type')",
              :key="option.value",
              :label="option.label",
              :value="option.value"
            )
        el-form-item(label="SPU", prop="spu")
          el-input(v-model="formData.spu", :disabled="!query.edit")
        el-form-item(label="备注")
          el-input(
            v-model="formData.remark",
            type="textarea",
            :rows="2",
            :disabled="!query.edit"
          )
        el-form-item
          el-button(v-if="query.edit", type="primary", @click="submit()") {{ $t("common.base.submit") }}
          el-button(@click="goBack()") {{ $t("common.base.cancel") }}
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import mallCategoryApi, {
  MallCategoryListVo,
  MallProductCategoryBo,
  MallProductCategoryPropertyStructBo,
} from "~/api/mall/mg/categoryApi";
import mallProductApi, {
  MallProductAddVo,
  MallProductModifySpaceVo,
  MallProductPropertyBo,
} from "~/api/mall/mg/productApi";
import auxyAutocompleteApi, {
  BasicAuxyAutocompleteAutoGetAutocompletesQueryParam,
} from "~/api/basic/auxy/autocomplete/autoApi";
import YmRichtext from "~/components/ym/richtext.vue";
import { BaseVue } from "~/model/vue";
import arrayUtils from "~/utils/arrayUtils";
import routerUtils from "~/utils/routerUtils";
import sortableUtils from "~/utils/sortableUtils";
import objUtils from "~/utils/objUtils";
import commonFunUtils from "~/utils/commonFunUtils";

type CategoryTree = MallCategoryListVo & {
  children?: Array<CategoryTree>;
};

type FormDataProperty = MallProductPropertyBo &
  MallProductCategoryPropertyStructBo & {
    idxCode: string;
    space: boolean;
    value: string;
    values: Array<string>;
    addValue: boolean;
  };

type FormDataSpacePropType = {
  [key in `prop.${string}`]: string;
};

type FormDataSpace = Omit<MallProductModifySpaceVo, "properties"> &
  FormDataSpacePropType;

type FormData = Omit<
  MallProductAddVo,
  "properties" | "spaces" | "categoryId"
> & {
  categoryId: Array<string>;
  properties: Array<FormDataProperty>;
  spaces: Array<FormDataSpace>;
};

@Component({
  name: "role-user-mg-edit",
  components: {
    YmRichtext,
  },
})
export default class UserMgEdit extends mixins(BaseVue) {
  loading = true;
  query = { id: "", edit: true };
  formData: FormData = {
    // 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
    type: "BASE",
    // 名称, 必须字段, 最小长度：0, 最大长度：64
    name: "",
    // SPU信息, 最小长度：0, 最大长度：32
    spu: "",
    // 品牌名称, 最小长度：0, 最大长度：64
    brandId: "",
    // 图片信息, 必须字段, 最小长度：0, 最大长度：1024
    pic: "",
    // 移动版详情, 最小长度：0, 最大长度：65535
    contentMobile: "",
    // PC版详情, 必须字段, 最小长度：1, 最大长度：65535
    contentPc: "",
    // 视频信息, 最小长度：1, 最大长度：1024
    video: "",
    // 商品类型, 最小长度：0, 最大长度：32
    categoryId: [],
    // 所有的分类属性数据
    properties: <Array<FormDataProperty>>[],

    // 不同规格数据, 必须字段
    // // 名称, 最小长度：0, 最大长度：64
    // name?: string;
    // // 默认商品图片, 最小长度：0, 最大长度：256
    // pic?: string;
    // // 单价, 必须字段
    // unitPrice?: number;
    // // 显示价格
    // unitPriceShow?: number;
    // // 商品upc条码, 最小长度：0, 最大长度：32
    // upc?: string;
    // // 商品规格属性
    // properties?: Array<MallProductPropertyBo>;
    spaces: <Array<FormDataSpace>>[],
  };
  categoryTree: Array<CategoryTree> = [];

  private catId2detail: Map<string, Array<MallProductCategoryBo>> = new Map();

  private idCount = 1;

  async created() {
    this.query.id = routerUtils.getQueryValue(this, "id", "");
    this.query.edit = commonFunUtils.parseBoolean(
      routerUtils.getQueryValue(this, "edit", "true")
    );
    await this.initParam();
    await this.initDetail();
    this.loading = false;
  }

  async mounted() {
    this.initSortable();
  }

  checkPropertyValueEmpty(prp: any, msg: string = "") {
    msg = msg ? msg : "该字段不能为空";
    return {
      trigger: ["blur", "change"],
      validator: (rule: any, value: FormDataProperty, cb: any) => {
        if (value.space) {
          if (value.values && value.values.length > 0) {
            cb();
          } else {
            cb(new Error(msg));
          }
        } else {
          if (value.value) {
            cb();
          } else {
            cb(new Error(msg));
          }
        }
      },
    };
  }

  skuCheck(idx: number) {
    return {
      trigger: ["blur", "change"],
      validator: (rule: any, value: string, cb: any) => {
        if (!value) {
          cb();
          return;
        }
        let spaces = this.formData.spaces;
        for (let sidx in spaces) {
          if (+sidx == idx) {
            continue;
          }
          let space = spaces[sidx];
          if (!space.sku) {
            continue;
          }
          if (space.sku == value) {
            cb(new Error("SKU重复"));
          }
        }
        cb();
      },
    };
  }

  private getIdxCode() {
    this.idCount++;
    return "CODE_" + this.idCount;
  }

  addRedStar(h: any, record: { column: any }) {
    return [
      h("span", { style: "color: red" }, "*"),
      h("span", " " + record.column.label),
    ];
  }

  async getCategoriesById(id: string) {
    let cats = this.catId2detail.get(id);
    if (cats) {
      return cats;
    }
    let cat = await mallCategoryApi.getDetail(id);
    cats = arrayUtils.tree2array(cat, "parentCategory").reverse();
    this.catId2detail.set(id, cats);
    return cats;
  }

  async initSortable() {
    let el: any = this.$refs.propertyDiv;
    let tbody = el.querySelector(".el-table__body-wrapper tbody");
    sortableUtils.sortable(tbody, this, "formData.properties", {
      handle: ".y-sorthandle",
    });
  }

  async initParam() {
    let categories = await mallCategoryApi.getAllList();
    let root = arrayUtils.array2tree(categories, "id", "idParent", "children");
    this.categoryTree = root;
  }

  /**
   * 初始化详情，实现数据回现
   */
  async initDetail() {
    if (this.query.id) {
      let detail = await mallProductApi.get(this.query.id);
      let parentCats = await this.getCategoriesById(detail.categoryId!);
      // 调整级联选择框内容
      let cIds = parentCats.map((x) => x.id!);

      // 属性梳理
      let properties: Array<FormDataProperty> = [];
      let propStructCode2struct: Map<
        string,
        MallProductCategoryPropertyStructBo
      > = new Map();
      parentCats.forEach((c) =>
        c.propertyStructs?.forEach((propStruct) => {
          propStructCode2struct.set(propStruct.code!, propStruct);
        })
      );

      // 挨个处理属性
      let propName2prop: Map<string, FormDataProperty> = new Map();
      let propCode2prop: Map<string, FormDataProperty> = new Map();
      for (let prop of detail.properties!) {
        let nprop = null;
        if (prop.code) {
          let struct = propStructCode2struct.get(prop.code);
          if (struct) {
            // 当前属性存在
            nprop = {
              ...prop,
              ...struct,
              values: [],
              space: false,
              addValue: false,
              idxCode: this.getIdxCode(),
            };
          } else {
            // 当前属性不存在
            nprop = {
              ...prop,
              code: "",
              values: [],
              space: false,
              addValue: false,
              idxCode: this.getIdxCode(),
            };
          }
        } else {
          // 自定义属性
          nprop = {
            ...prop,
            values: [],
            space: false,
            addValue: false,
            idxCode: this.getIdxCode(),
          };
        }
        if (nprop.code) {
          propCode2prop.set(nprop.code, <any>nprop);
        }
        propName2prop.set((nprop.groupName ?? "") + nprop.name, <any>nprop);
        properties.push(<any>nprop!);
      }

      // space处理
      let spaces: Array<FormDataSpace> = [];
      for (let ospace of detail.spaces!) {
        // 复制售卖单位
        let space: FormDataSpace = { ...ospace };

        // 处理属性信息
        for (let prop of ospace.properties!) {
          let nprop = null;
          if (prop.code) {
            // 以前是一个标准属性
            nprop = propCode2prop.get(prop.code);
            if (nprop) {
              // 已创建
              if (nprop.values.indexOf(prop.value!) < 0) {
                nprop.values.push(prop.value!);
              }
            } else {
              // 未创建
              let struct = propStructCode2struct.get(prop.code);
              if (struct) {
                // 新分类也是标准属性
                // 当前属性存在
                nprop = {
                  ...prop,
                  ...struct,
                  value: "",
                  values: [prop.value],
                  space: true,
                  addValue: false,
                  idxCode: this.getIdxCode(),
                };
              } else {
                nprop = {
                  ...prop,
                  code: "",
                  value: "",
                  values: [prop.value],
                  space: true,
                  addValue: false,
                  idxCode: this.getIdxCode(),
                };
              }
              if (nprop.code) {
                propCode2prop.set(nprop.code, <any>nprop);
              }
              propName2prop.set(
                (nprop.groupName ?? "") + nprop.name,
                <any>nprop
              );
              properties.push(<any>nprop!);
            }
          } else {
            nprop = propName2prop.get((prop.groupName ?? "") + prop.name);
            if (nprop) {
              // 已构建
              if (nprop.values.indexOf(prop.value!) < 0) {
                nprop.values.push(prop.value!);
              }
            } else {
              // 未构建
              nprop = {
                ...prop,
                value: "",
                values: [prop.value],
                space: true,
                addValue: false,
                idxCode: this.getIdxCode(),
              };
              propName2prop.set(
                (nprop.groupName ?? "") + nprop.name,
                <any>nprop
              );
              properties.push(<any>nprop!);
            }
          }
          // 追加space属性
          (<any>space)[`prop.${nprop!.idxCode}`] = prop.value;
        }

        spaces.push(space);
      }
      // 处理新增属性
      for (let struct of propStructCode2struct.values()) {
        if (propCode2prop.has(struct.code!)) {
          continue;
        }
        let nprop = {
          ...struct,
          value: "",
          values: [],
          space: false,
          addValue: false,
          idxCode: this.getIdxCode(),
        };
        properties.push(<any>nprop!);
      }

      let formData: FormData = {
        ...detail,
        categoryId: cIds,
        spaces: spaces,
        properties: properties,
      };

      this.formData = formData;
    }
  }

  async handlePropAddClick() {
    let nprop = <FormDataProperty>{};
    nprop.code = "";
    nprop.name = "";
    nprop.groupName = "";
    nprop.value = "";
    nprop.values = [];
    nprop.space = false;
    nprop.addValue = true;
    nprop.idxCode = this.getIdxCode();
    this.formData.properties?.push(nprop);

    await this.refreshUpdateSpaces();
  }

  async handlePropDeleteClick(idx: number) {
    this.formData.properties.splice(idx, 1);
    await this.refreshUpdateSpaces();
  }

  async handlePropSpaceChange(record: FormDataProperty) {
    if (record.space) {
      if (record.value) {
        record.values = [record.value];
        record.addValue = false;
      } else {
        record.values = [];
        record.addValue = true;
      }
      record.value = "";
    } else {
      if (record.values && record.values.length > 0) {
        record.value = record.values[0];
      } else {
        record.value = "";
      }
      record.addValue = false;
      record.values = [];
    }
    await this.refreshUpdateSpaces();
  }

  async handlPropTagAddClick(struct: FormDataProperty) {
    let that = this;
    struct.addValue = true;
    struct.value = "";
    this.$nextTick(() => {
      let inputRef: any = that.$refs[`prop_${struct.name}_input`];
      if (inputRef) {
        inputRef.focus();
      }
    });
  }

  async handlPropTagValueSubmitClick(struct: FormDataProperty) {
    setTimeout(async () => {
      if (!struct.addValue || !struct.value) {
        return;
      }
      struct.values.push(struct.value);
      struct.addValue = false;
      await this.refreshUpdateSpaces();
    }, 100);
  }

  async handlPropTagCloseClick(struct: FormDataProperty, tag: string) {
    let idx = struct.values.indexOf(tag);
    if (idx >= 0) {
      struct.values.splice(idx, 1);
      await this.refreshUpdateSpaces();
    }
  }

  async handlPropTagValueCancelClick(struct: FormDataProperty) {
    struct.addValue = false;
  }

  async handlePropAutocompleteSearch(
    key: string,
    cb: (result: Array<any>) => void,
    record: FormDataProperty
  ) {
    if (!key || !record.categoryId || !record.code) {
      cb([]);
      return;
    }
    let query: BasicAuxyAutocompleteAutoGetAutocompletesQueryParam = {};
    query.keyword = key;
    query.bs_code = "MALL_CATEGORY_PROP";
    query.code = record.categoryId;
    query.attr = record.code;
    let results = await auxyAutocompleteApi.getAutocompletes(query);
    // 调用 callback 返回建议列表的数据
    cb(results);
  }

  async submit() {
    this.loading = true;
    try {
      if (!(await this.$rule.formCheck(this, "form"))) {
        this.$notify.error("存在部分字段未按照规定填写");
        return;
      }
      let data = this.formData;
      let body = objUtils.deepClone(data);
      // 整合数据
      const clearProp = (prop: any) => {
        delete prop.values;
        delete prop.space;
        delete prop.categoryId;
        delete prop.addValue;
        delete prop.idxCode;
        for (let key in prop) {
          if (key.startsWith("prop.")) {
            delete prop[key];
          }
        }
      };
      (<any>body).categoryId = body.categoryId[body.categoryId.length - 1];
      let properties = body.properties;
      let productPropties = properties.filter((x) => !x.space);
      productPropties.forEach((x: any) => clearProp(x));
      body.properties = productPropties;

      let spacePropties = properties.filter((x) => x.space);
      (<any>body).spaces = body.spaces.map((x: any) => {
        let space: MallProductModifySpaceVo = { ...x };
        let props: Array<MallProductPropertyBo> = [];
        for (let sp of spacePropties) {
          let prop: MallProductPropertyBo = { ...sp };
          prop.value = x[`prop.${sp.idxCode}`];
          clearProp(prop);
          props.push(prop);
        }
        space.properties = props;
        return space;
      });
      if (this.query.id) {
        // 更新
        await mallProductApi.modify(this.query.id, <any>body);
        let msg: any = this.$t("common.success.modify");
        this.$notify.success(msg);
      } else {
        // 新增
        let detail = await mallProductApi.add(<any>body);
        this.query.id = detail.id!;
        for (let i = 0; i < data.spaces.length; i++) {
          this.formData.spaces[i].id = data.spaces[i].id;
        }
        routerUtils.putQueryValue(this, { id: this.query.id });
        let msg: any = this.$t("common.success.save");
        this.$notify.success(msg);
      }
      this.goBack();
    } catch (e) {
      if (this.query.id) {
        // 更新
        let msg = this.$tc("common.error.modify");
        this.$notify.error(msg);
      } else {
        // 新增
        let msg = this.$tc("common.error.save");
        this.$notify.error(msg);
      }
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async goBack() {
    this.$router.push(`/user/mg`);
  }

  /**
   * 刷新space中的数据，进行重新匹配以及重分配
   */
  async refreshUpdateSpaces() {
    let ps: Array<FormDataProperty> = this.formData.properties!;
    let spacesProperties = ps.filter((x) => x.space); // space属性

    let newSpacePropertyTypes: Array<FormDataSpacePropType> = [];
    const putSpaces = (spaceProp: FormDataSpacePropType, idx: number) => {
      if (spacesProperties.length <= idx) {
        // 深度到达
        newSpacePropertyTypes.push(spaceProp);
        return;
      }
      let prop = spacesProperties[idx];
      if (!prop.values || prop.values.length <= 0) {
        putSpaces(spaceProp, idx + 1);
        return;
      }
      for (let value of prop.values) {
        let nsp: FormDataSpacePropType = { ...spaceProp };
        (<any>nsp)[`prop.${prop.idxCode!}`] = value;
        putSpaces(nsp, idx + 1);
      }
    };
    putSpaces({}, 0);

    let oldSpaces: Array<FormDataSpace> = this.formData.spaces;
    let spaces: Array<FormDataSpace> = [];
    const getPropCount = (obj: any) => {
      if (!obj) {
        return 0;
      }
      let count = 0;
      for (let k in obj) {
        if (k.startsWith("prop.")) {
          count++;
        }
      }
      return count;
    };
    // 循环处理
    let ids = new Set();
    const findSpaceByNewProp = (
      spacePropType: any
    ): { used: FormDataSpace | null; noused: FormDataSpace | null } => {
      let used = null;
      for (let oldSpace of oldSpaces) {
        let diffCount = 0;
        for (let key in spacePropType) {
          if (!key.startsWith("prop.")) {
            continue;
          }
          if ((<any>oldSpace)[key] != (<any>spacePropType)[key]) {
            diffCount++;
            if (used) {
              break;
            }
          }
        }
        if (diffCount <= 0) {
          if (!oldSpace.id || !ids.has(oldSpace.id)) {
            return { noused: oldSpace, used: null };
          } else {
            // 一样的优先，直接覆盖
            used = oldSpace;
          }
        } else if (diffCount == 1) {
          // 不一样的需要判断有没有，有就跳过
          if (!used) {
            used = oldSpace;
          }
        }
      }
      return { used, noused: null };
    };
    const findSpaceByOldProp = (
      spacePropType: any
    ): { used: FormDataSpace | null; noused: FormDataSpace | null } => {
      let used = null;
      for (let oldSpace of oldSpaces) {
        let ok = true;
        for (let key in oldSpace) {
          if (!key.startsWith("prop.")) {
            continue;
          }
          if ((<any>oldSpace)[key] != (<any>spacePropType)[key]) {
            ok = false;
            break;
          }
        }
        if (ok) {
          if (!oldSpace.id || !ids.has(oldSpace.id)) {
            return { noused: oldSpace, used: null };
          } else {
            used = oldSpace;
          }
        }
      }
      return { used, noused: null };
    };
    const newSpace = (spacePropType: FormDataSpacePropType): FormDataSpace => {
      let space: FormDataSpace = {
        ...spacePropType,
        selling: true,
        name: "",
        unitPrice: 0,
        unitPriceShow: 0,
        upc: "",
        sku: "",
        pic: "",
        quantity: 0,
      };
      return space;
    };
    const copySpace = (
      oldSpace: FormDataSpace,
      spacePropType: FormDataSpacePropType
    ): FormDataSpace => {
      let space = { ...oldSpace };
      for (let key in space) {
        if (key.startsWith("prop.")) {
          delete (<any>space)[key];
        }
      }
      space = { ...space, ...spacePropType };
      return space;
    };
    for (let spacePropType of newSpacePropertyTypes) {
      if (oldSpaces.length <= 0) {
        let space = newSpace(spacePropType);
        spaces.push(space!);
      } else {
        let oldPropCount = getPropCount(oldSpaces[0]);
        let newPropCount = getPropCount(spacePropType);
        if (oldPropCount >= newPropCount) {
          // 旧的比新的多，说明减少了字段
          // 旧属性和新属性一样，说明修改了数据
          let result = findSpaceByNewProp(spacePropType);
          if (result.noused) {
            let space = copySpace(result.noused, spacePropType);
            spaces.push(space);
            if (space.id) {
              ids.add(space.id);
            }
          } else if (result.used) {
            let space = copySpace(result.used, spacePropType);
            delete space.id;
            spaces.push(space);
          } else {
            let space = newSpace(spacePropType);
            spaces.push(space!);
          }
        } else {
          // 新的比旧的多，说明增加了字段
          let result = findSpaceByOldProp(spacePropType);
          if (result.noused) {
            let space = copySpace(result.noused, spacePropType);
            spaces.push(space);
            if (space.id) {
              ids.add(space.id);
            }
          } else if (result.used) {
            let space = copySpace(result.used, spacePropType);
            delete space.id;
            spaces.push(space);
          } else {
            let space = newSpace(spacePropType);
            spaces.push(space!);
          }
        }
      }
    }

    this.formData.spaces = spaces;
  }

  /**
   * 刷新分类信息以及对应的属性信息
   */
  async refreshCategoryAndProperties() {
    let detail = this.formData;

    // 调整默认显示分类信息
    let ps: Array<FormDataProperty> = this.formData.properties!;
    let customProperties = ps.filter((x) => !x.code); // 自定义属性

    if (detail.categoryId && detail.categoryId.length > 0) {
      let categoryId = detail.categoryId[detail.categoryId.length - 1];
      let parentCats = await this.getCategoriesById(categoryId);

      // 调整级联选择框内容
      let cIds = parentCats.map((x) => x.id!);
      this.formData.categoryId = cIds;

      // 调整默认显示属性列表
      let propCode2prop: Map<string, FormDataProperty> = new Map();
      ps.forEach((x) => x.code && propCode2prop.set(x.code!, x));

      let defProp: Array<FormDataProperty> = [];
      parentCats.forEach((c) =>
        c.propertyStructs?.forEach((propStruct) => {
          let nprop: FormDataProperty | undefined = propCode2prop.get(
            propStruct.code!
          );
          if (!nprop) {
            nprop = {
              ...propStruct,
              value: "",
              values: [],
              space: false,
              addValue: false,
              idxCode: this.getIdxCode(),
            };
          }
          defProp.push(nprop!);
        })
      );
      ps = [...defProp, ...customProperties];
    } else {
      ps = [...customProperties];
    }

    this.formData.properties = ps;

    await this.refreshUpdateSpaces();
  }

  @Watch("formData.categoryId", { deep: true })
  async formDataCategoryIdWatch(nv: Array<string>, ov: Array<string>) {
    if (nv[nv.length - 1] === ov[ov.length - 1]) {
      return;
    }
    this.refreshCategoryAndProperties();
  }
}
</script>

<style lang="scss" scoped>
.page-mall-product-edit {
  .value-tag {
    .el-tag {
      margin-right: 10px;
    }
    .button-new-tag {
      margin-right: 10px;
    }
    .input-new-tag {
      margin-right: 10px;
      width: 120px;
    }
  }
}
.space-table::v-deep table tbody .cell {
  min-height: 60px;
}

.property-table::v-deep table tbody .cell {
  min-height: 60px;
}
</style>
