<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades 用户列表
  .container
    //- 表头
    .handle-box
      //- el-button.mr10(
      //-   type="primary",
      //-   icon="el-icon-plus",
      //-   @click="handleAddClick"
      //- ) {{ $t("common.base.add") }}
      el-input.handle-input.mr10(
        v-model="query.keyword",
        placeholder="关键词",
        @keyup.enter.native="handleSearchClick"
      )
      el-button.mr10(
        type="primary",
        icon="el-icon-search",
        @click="handleSearchClick"
      ) 搜索
    //- 表单内容
    el-table.table(
      :data="users",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="username", label="用户名")
      el-table-column(prop="name", label="名称")
      el-table-column(prop="ban", label="状态")
        template(#default="scope")
          el-tag(:type="!scope.row.ban ? 'success' : 'danger'") {{ !scope.row.ban ? "正常" : "禁用" }}
      el-table-column(prop="createTime", type="date", label="创建时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="loginLastTime", type="date", label="最后登录时间")
        template(#default="scope")
          div {{  scope.row.loginLastTime ? $moment(scope.row.loginLastTime).format("YYYY-MM-DD HH:mm:ss") : ''  }}
      el-table-column(prop="loginLastIp", label="最后登录IP")
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(type="text", @click="handleChangePasswordClick(scope.row)") 重置密码
          el-button(type="text", @click="handleRuleClick(scope.row)") 权限
          //- el-button(type="text", @click="handleModifyClick(scope.row)") {{ $t("common.base.detail") }}
          //- el-button(type="text", @click="handleModifyClick(scope.row)") {{ $t("common.base.modify") }}
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )

  el-dialog(
    title="修改密码",
    :visible.sync="bizData.resetPasswordData.open",
    :before-close="() => { bizData.resetPasswordData.open = false; }"
  )
    template
      el-form(:model="bizData.resetPasswordData", ref="resetPasswordForm")
        el-form-item(label="新密码", prop="password")
          el-input(
            type="password",
            autocomplete="off",
            v-model="bizData.resetPasswordData.password",
            placeholder="为空时表示密码设置为空，此时将不可用账户密码登录"
          )
        el-form-item(
          label="确认密码",
          prop="password2",
          :rules="[$rule.valid(ruleResetPasswordPassword2)]"
        )
          el-input(
            type="password",
            autocomplete="off",
            v-model="bizData.resetPasswordData.password2",
            @keyup.enter.native="handleChangePasswordSubmit"
          )
        el-form-item
          el-button(
            type="primary",
            @click="handleChangePasswordSubmit",
            :loading="bizData.resetPasswordData.loading > 0"
          ) 提交
          el-button(
            @click="() => { bizData.resetPasswordData.open = false; }",
            :loading="bizData.resetPasswordData.loading > 0"
          ) 取消
</template>

<script lang="ts">
import { Component, mixins, Vue } from "nuxt-property-decorator";
import authUserInfoApi, {
  AuthUserInfoGetListCountQueryParam,
  AuthUserInfoGetListQueryParam,
  UserInfoChangePasswordIdParamVo,
  UserListVo,
} from "~/api/auth/user/infoApi";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";
import routerUtils from "~/utils/routerUtils";
import urlUtils from "~/utils/urlUtils";

@Component({
  name: "role-user-mg",
})
export default class UserMgIndex extends mixins(BaseVue) {
  loading: boolean = true;
  query = {
    page: 1,
    rows: 20,
    total: 0,
    keyword: "",
  };
  users: Array<UserListVo> = [];
  bizData = {
    resetPasswordData: {
      open: false,
      record: <UserListVo | null>null,
      password: "",
      password2: "",
      loading: 0,
    },
  };

  created() {
    this.initQuery();
  }

  async mounted() {
    await this.init();
  }

  async ruleResetPasswordPassword2(rule: any, value: any) {
    if (
      this.bizData.resetPasswordData.password !=
      this.bizData.resetPasswordData.password2
    ) {
      throw new Error("确认密码与密码不一致");
    }
  }

  initQuery() {
    this.query.keyword = routerUtils.getQueryValue(this, "keyword", "");
    this.query.page = +routerUtils.getQueryValue(this, "page", "1");
    this.query.rows = +routerUtils.getQueryValue(this, "rows", "20");
  }

  async init() {
    await this.loadContentCount();
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  getCategoryId(categoryIds: Array<string>) {
    return categoryIds && categoryIds.length > 0
      ? categoryIds[categoryIds.length - 1]
      : "";
  }

  async loadContentCount() {
    let param: AuthUserInfoGetListCountQueryParam = {};
    param.keyword = this.query.keyword;
    routerUtils.putQueryValue(this, param);

    this.loading = true;
    try {
      this.query.total = await authUserInfoApi.getListCount(param);
      this.query.page = 1;
      if (this.query.total > 0) {
        await this.loadContents();
      } else {
        this.users = [];
      }
    } catch (error) {
      console.log(error);
      throw error;
      // this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
    } finally {
      this.loading = false;
    }
  }

  async loadContents() {
    let param: AuthUserInfoGetListQueryParam = {};
    param.keyword = this.query.keyword;
    param.page = this.query.page ?? 1;
    param.rows = this.query.rows ?? 20;

    routerUtils.putQueryValue(this, param);

    param.page = param.page - 1;

    this.loading = true;
    try {
      this.users = await authUserInfoApi.getList(param);
    } catch (error) {
      console.log(error);
      throw error;
      // this.$notify.error(<any>{ message: "加载出错，请稍后重试" });
    } finally {
      this.loading = false;
    }
  }

  async initParam() {}

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContents();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContents();
  }

  async handleSearchClick() {
    await this.loadContentCount();
  }

  async handleRuleClick(record: UserListVo) {
    this.$router.push(
      `/${compUtils.getQueryValue(
        this,
        "role",
        ""
      )}/power/rule/USER/MALL_CATEGORY?filter_type=ROLE&owner_id=${record.id}`
    );
  }

  // async handleAddClick() {
  //   this.$router.push(
  //     `/${compUtils.getQueryValue(this, "role", "")}/user/mg/edit`
  //   );
  // }

  // async handleModifyClick(record: UserListVo) {
  //   this.$router.push(
  //     `/${compUtils.getQueryValue(this, "role", "")}/user/mg/edit?id=${
  //       record.id
  //     }`
  //   );
  // }

  handleChangePasswordClick(record: UserListVo) {
    this.bizData.resetPasswordData.record = record;
    this.bizData.resetPasswordData.open = true;
  }

  async handleChangePasswordSubmit() {
    if (!(await this.$rule.formCheck(this, "resetPasswordForm"))) {
      return;
    }

    try {
      this.bizData.resetPasswordData.loading++;

      let body: UserInfoChangePasswordIdParamVo = {};
      body.userId = this.bizData.resetPasswordData.record?.id;
      body.password = this.bizData.resetPasswordData.password;
      await authUserInfoApi.changePasswordById(body);
      this.bizData.resetPasswordData.open = false;

      this.$message.success("修改密码成功");
    } finally {
      this.bizData.resetPasswordData.loading--;
    }
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}
.mr10 {
  margin-right: 10px;
}
</style>

