<template>
  <div class="page-container">
    <!-- 搜索 -->
    <div class="search-box" ref="searchBox">
      <div class="input-dropdown-wrapper">
        <el-input placeholder="搜索" v-model="filterParams.keyword" @keyup.enter.native="search()"
          @focus="visible = true">
          <template #prefix>
            <img src="/img/home/<USER>" class="search-icon" alt="">
          </template>
          <template #suffix>
            <div class="search-type-btn-wrap">
              <span :class="searchType == 'case' ? 'search-type-btn search-type-active' : 'search-type-btn'"
                @click="searchTypeChange('case')">案例</span>
              <span :class="searchType == 'file' ? 'search-type-btn search-type-active' : 'search-type-btn'"
                @click="searchTypeChange('file')">素材</span>
            </div>
          </template>
        </el-input>
        <!-- 自定义下拉框区域 -->
        <transition name="fade-slide">
          <div v-if="visible" class="custom-dropdown">
            <div class="dropdown-item">
              <div class="dropdown-title">热门搜索</div>
              <div>
                <span class="dropdown-tag" v-for="(item, index) in tagList1" :key="index">
                  {{ item }}
                </span>
              </div>
            </div>
            <div class="dropdown-item">
              <div class="dropdown-title">热门案例</div>
              <div>
                <span class="dropdown-tag" v-for="(item, index) in tagList2" :key="index">
                  {{ item }}
                </span>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </div>
    <!-- 筛选 -->
    <div class="filter-box">
      <div class="filter-box-item">
        <span :class="filterType == 'all' ? 'filter-label filter-label-active' : 'filter-label'"
          @click="filterTypeChange('all')">综合</span>
        <span :class="filterType == 'latest' ? 'filter-label filter-label-active' : 'filter-label'"
          @click="filterTypeChange('latest')">最新</span>
        <span :class="filterType == 'hot' ? 'filter-label filter-label-active' : 'filter-label'"
          @click="filterTypeChange('hot')">热门</span>
      </div>
      <div class="filter-box-item">
        <img src="/img/home/<USER>" alt="">
        <span @click="filterDialogVisible = true">筛选</span>
      </div>
    </div>
    <!-- 卡片列表 -->
    <div class="article-box">
      <y-card v-for="(item, index) in testCards" :key="index" :info="item" />
    </div>
    <div class="load-more">
      <span>加载更多</span>
    </div>
    <!-- 筛选弹窗 -->
    <!-- <el-dialog :visible.sync="filterDialogVisible" width="760px" :before-close="handleClose" :show-close="false"
      custom-class="filter-dialog">
      <div slot="title" class="filter-dialog-header">
        <p>筛选</p>
        <img src="/img/home/<USER>" alt="" @click="filterDialogVisible = false">
      </div>
      <div class="filter-dialog-aside">
        <span :class="filterTagTypeIndex == index ? 'filter-tag-type filter-tag-type-active' : 'filter-tag-type'"
          v-for="(item, index) in categories" :key="index" @click="filterTagTypeChange(index)">{{ item.label }}</span>
      </div>
      <div class="filter-dialog-article">
        <div class="filter-tag-item" v-for="(item, index) in options" :key="index" :label="item">
          <div class="common-checkbox active">
            <span class="iconfont icon-gougou"></span>
          </div>
          <y-checkbox v-model="item.checked"></y-checkbox>
          <div class="filter-tag-info">
            <span>{{ item.label }}</span>
            <span>111</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <y-button type="secondary" @click="filterDialogVisible = false">清除全部</y-button>
        <y-button type="inverse" @click="filterDialogVisible = false">显示结果</y-button>
      </span>
    </el-dialog> -->
     <y-tag-library-dialog
        :visible.sync="filterDialogVisible"
        :value="checkedValue"
        dialog-title="筛选"
        :options="options"
        :categories="categories"
        @confirm="handleClose"
        @close="handleClose"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role",
  layout: "good",
})
export default class HomeIndex extends Vue {
  filterParams = {
    keyword: "",
    tag: []
  }
  searchType = "case";
  filterType = "all";
  filterTagTypeIndex = 0;
  visible = false;
  filterDialogVisible = false;
  checkedValue = [];
  tagList1 = ['营销规划', '主视觉设计', '联名推荐', '包豪斯风格', '产品设计', '3D设计元素', '单色系', '视觉风格', '多巴胺风格', '行业', '品牌', '分类', '营销规划', '主视觉设计', '联名推荐', '包豪斯风格', '产品设计', '3D设计元素'];
  tagList2 = ['2025星巴克樱花季设计', '麦当劳儿童节营销规划'];
  categories = [
    { value: "1", label: "流行热点" },
    { value: "2", label: "视觉风格" },
    { value: "3", label: "行业" },
  ];
  options = [
    { value: "1-1", label: "二次元周边", category: "1", checked: false },
    { value: "1-2", label: "明星周边", category: "1", checked: false },
    { value: "1-3", label: "环保周边", category: "1", checked: false },
    { value: "1-4", label: "宠物周边", category: "1", checked: false },
    { value: "2-1", label: "Y2K", category: "2", checked: false },
    { value: "2-2", label: "赛博朋克", category: "2", checked: false },
    { value: "2-3", label: "静奢风", category: "2", checked: false },
    { value: "2-4", label: "极繁主义", category: "2", checked: false },
    { value: "2-5", label: "多巴胺", category: "2", checked: false },
    { value: "3-1", label: "金融", category: "3", checked: false },
    { value: "3-2", label: "医学", category: "3", checked: false },
    { value: "3-3", label: "传媒", category: "3", checked: false },
    { value: "3-4", label: "人工智能", category: "3", checked: false },
  ]
  testCards = [
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/8cb81bc5b3a014add7a056ef7e7543c9.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/c226c4a608dd9b01109ae5168f3e11ce.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/051fa4cbdf4229cbf1e714537ea8f197.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/ba09789f1a943c97a3f17fd6eb854f38.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/69ba0ea7f3f8b908534546953a6fc352.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/dad21fa040f3a69837a8ab482931db16.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/54e0937e53d918ed85c29c5c9490c00d.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/4f8188eace96b08d2c455d7a24648459.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/5700b6b0c3d7a3a17a75063c3562c293.pn' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/86a840063d04e5b3989e5a988b06ae5e.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/8cb81bc5b3a014add7a056ef7e7543c9.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/c226c4a608dd9b01109ae5168f3e11ce.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/051fa4cbdf4229cbf1e714537ea8f197.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/ba09789f1a943c97a3f17fd6eb854f38.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/69ba0ea7f3f8b908534546953a6fc352.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/dad21fa040f3a69837a8ab482931db16.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/54e0937e53d918ed85c29c5c9490c00d.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/4f8188eace96b08d2c455d7a24648459.png' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/5700b6b0c3d7a3a17a75063c3562c293.pn' },
    { name: '标题', img: 'http://mg.goodgifts.yheart.cn/api/sys/fs/86a840063d04e5b3989e5a988b06ae5e.png' },
  ]
  search() {

  }
  mounted() {
    document.addEventListener('click', this.onDocumentClick);
  }
  beforeDestroy() {
    document.removeEventListener('click', this.onDocumentClick);
  }
  onDocumentClick(e: MouseEvent) {
    const target = e.target as HTMLElement;
    const dropdownEl = this.$refs.searchBox as HTMLElement;
    const isInside = dropdownEl.contains(target);
    const isTag = target.className === 'dropdown-tag';

    if (!isInside || isTag) {
      this.visible = false;
    }
  }
  goDetail(item: any) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push({
      path: `/${role}/case/detail`,
      query: { id: item.id },
    });
  }
  searchTypeChange(val: string) {
    this.searchType = val;
  }

  filterTypeChange(val: string) {
    this.filterType = val;
  }

  filterTagTypeChange(val: number) {
    this.filterTagTypeIndex = val;
  }

  handleClose() {
    this.filterDialogVisible = false;
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 48px;
}

.search-box {
  width: 1000px;
  position: relative;
}

.search-icon {
  width: 18px;
  height: 18px;
  padding-bottom: 2px;
}

.search-type-btn-wrap {
  height: 100%;
  display: flex;
  align-items: center;
}

.search-type-btn {
  // border: 1px solid red;
  padding: 8px 12px;
  border-radius: 28px;
  background-blend-mode: overlay;
  font-family: 'PingFangSC';
  font-size: 14px;
  color: rgba(235, 235, 245, 0.7);
  margin: 0 8px;
  cursor: pointer;
}

.search-type-active {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
  color: #fff;
}

/** 搜索框样式篡改 */
::v-deep {
  .el-input__prefix {
    display: flex;
    align-items: center;
    padding-left: 10px;
  }

  .el-input__inner {
    background-color: transparent;
    padding-left: 40px;
    height: 50px;
    border: 0;

    &::placeholder {
      font-family: 'PingFangSC';
      font-size: 14px;
      color: rgba(235, 235, 245, 0.7);
    }
  }

}

.input-dropdown-wrapper {
  width: 100%;
  position: absolute;
  z-index: 100;
  top: 24px;
  border-radius: 28px;
  border: solid 1px rgba(255, 255, 255, 0.15);
  background-color: rgba(23, 22, 24);
}

/**搜索下拉框过渡 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease-in;
}

.fade-slide-enter {
  opacity: 0;
}

.fade-slide-leave-to {
  opacity: 0;
}

/* 下拉区域样式 */
.custom-dropdown {
  width: 100%;
  font-family: 'PingFangSC';
  font-size: 14px;
  color: #fff;
  box-sizing: border-box;
  border-radius: 4px;
  margin-top: 2px;
  padding: 20px 40px;
}

.dropdown-item {
  margin-bottom: 16px;
}

.dropdown-title {
  font-family: 'PingFangSC';
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 16px;
}

.dropdown-tag {
  display: inline-block;
  font-family: 'PingFangSC';
  font-size: 14px;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 8px;
  border: solid 1px rgba(255, 255, 255, 0.15);
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.filter-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-family: 'PingFangSC';
  color: rgba(235, 235, 245, 0.7);
  margin-top: 100px;

  // border: 1px solid red;
  img {
    width: 18px;
    height: 18px;
    margin-right: 12px;
  }

  span {
    cursor: pointer;
  }
}

.filter-box-item {
  padding: 12px 24px;
  display: flex;
  align-items: center;
}

.filter-label {
  display: inline-block;
  margin: 0 12px;
  padding: 4px 0;
  border-bottom: 2px solid transparent;
}

.filter-label-active {
  color: #fff;
  border-bottom: 2px solid #fff;
}

/** 对话框样式 */
::v-deep {
  .filter-dialog {
    background-color: rgb(23, 22, 24);
    border-radius: 28px;
    font-family: 'PingFangSC';
  }

  .el-dialog__header {
    padding-bottom: 0;
    padding-top: 30px;
  }

  .el-dialog__body {
    display: flex;
    height: 510px;
  }
}

.filter-dialog-header {
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'PingFangSC';
  font-size: 18px;
  font-weight: 500;
  // border: 1px solid red;

  img {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-dialog-aside {
  width: 180px;
  flex-shrink: 0;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.filter-tag-type {
  width: fit-content;
  font-family: 'PingFangSC';
  font-size: 14px;
  display: block;
  padding: 16px 24px;
  border-radius: 16px;
  color: rgba(235, 235, 245, 0.7);
  cursor: pointer;
}

.filter-tag-type-active {
  color: #fff;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
}

.filter-dialog-article {
  flex-grow: 1;
  padding: 0 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.filter-tag-item {
  height: 21px;
  display: flex;
  align-items: center;
  padding: 12px 0;
  // border: 1px solid red;
}

.filter-tag-info {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'PingFangSC';
  font-size: 14px;
  color: #fff;
  padding-left: 8px;

  span {
    display: block;
  }
}

.common-checkbox {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
  border: solid 1px rgba(255, 255, 255, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-sizing: border-box;

  span {
    display: none;
  }

  &.active {
    background-color: rgba(255, 255, 255);
    color: rgba(23, 22, 24);

    span {
      font-size: 8px;
      display: inline-block;
      color: rgba(23, 22, 24);
    }
  }
}

.article-box {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 48px 24px;
  margin: 24px 0px;
  // border: 1px solid red;
}

.load-more {
  font-family: 'PingFangSC';
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  padding: 12px 16px;
  border-radius: 28px;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.05));
  border: solid 1px rgba(255, 255, 255, 0.15);
  margin: 24px 0;
  cursor: pointer;
}
</style>
