<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card shadow="hover" class="mgb20" style="height:300px;">
          <div class="user-info">
            <img src="~/static/img/logo.png" class="user-avator" alt />
            <div class="user-info-cont">
              <div class="user-info-name">{{ userInfo.id }}</div>
              <div>{{ userInfo.roleName }}</div>
            </div>
          </div>
          <div class="user-info-list">
            系统当前时间：
            <span>{{ dateFormate(serverTime) }}</span>
          </div>
          <div class="user-info-list" v-if="userInfo.loginThisTime">
            本次登录时间：
            <span>{{ dateFormate(userInfo.loginThisTime) }}</span>
          </div>
          <div class="user-info-list" v-if="userInfo.loginThisAddr">
            本次登录地点：
            <span>{{ userInfo.loginThisAddr }}</span>
          </div>
          <div class="user-info-list" v-if="userInfo.loginThisIp">
            本次登录　IP：
            <span>{{ userInfo.loginThisIp }}</span>
          </div>
          <div style="width: 100%;border-top: solid 1px #ccc;margin: 2px 0;"></div>
          <div class="user-info-list" v-if="userInfo.loginLastTime">
            上次登录时间：
            <span>{{ dateFormate(userInfo.loginLastTime) }}</span>
          </div>
          <div class="user-info-list" v-if="userInfo.loginLastAddr">
            上次登录地点：
            <span>{{ userInfo.loginLastAddr }}</span>
          </div>
          <div class="user-info-list" v-if="userInfo.loginLastIp">
            上次登录　IP：
            <span>{{ userInfo.loginLastIp }}</span>
          </div>
        </el-card>
        <el-card shadow="hover" style="height:252px;">
          <template #header>
            <div class="clearfix">
              <span>语言详情</span>
            </div>
          </template>
          Vue
          <el-progress :percentage="71.3" color="#42b983"></el-progress>JavaScript
          <el-progress :percentage="24.1" color="#f1e05a"></el-progress>CSS
          <el-progress :percentage="13.7"></el-progress>HTML
          <el-progress :percentage="5.9" color="#f56c6c"></el-progress>
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-row :gutter="20" class="mgb20">
          <el-col :span="8">
            <el-card shadow="hover" :body-style="{ padding: '0px' }">
              <div class="grid-content grid-con-1">
                <i class="el-icon-user-solid grid-con-icon"></i>
                <div class="grid-cont-right">
                  <div class="grid-num">1234</div>
                  <div>用户访问量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" :body-style="{ padding: '0px' }">
              <div class="grid-content grid-con-2">
                <i class="el-icon-message-solid grid-con-icon"></i>
                <div class="grid-cont-right">
                  <div class="grid-num cursor-pointer" @click="handleInboxMsgClick">{{ msgCount }}
                  </div>
                  <div>系统消息</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" :body-style="{ padding: '0px' }">
              <div class="grid-content grid-con-3">
                <i class="el-icon-s-goods grid-con-icon"></i>
                <div class="grid-cont-right">
                  <div class="grid-num">5000</div>
                  <div>数量</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card shadow="hover" style="height:460px;">
          <template #header>
            <div class="clearfix">
              <span>待办事项</span>
              <el-button style="float: right; padding: 3px 0" type="text">添加</el-button>
            </div>
          </template>

          <div style="overflow-y: auto;height: 380px;">
            <el-table :show-header="false" :data="todoList" style="width:100%;">
              <el-table-column width="40">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.status"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column>
                <template #default="scope">
                  <div class="todo-item" :class="{'todo-item-del': scope.row.status}">
                    {{ scope.row.title }}</div>
                </template>
              </el-table-column>
              <el-table-column width="60">
                <template>
                  <i class="el-icon-edit"></i>
                  <i class="el-icon-delete"></i>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover">
          <schart ref="bar"
                  class="schart"
                  canvasId="bar"
                  :options="options"></schart>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <schart ref="line"
                  class="schart"
                  canvasId="line"
                  :options="options2"></schart>
        </el-card>
      </el-col>
    </el-row> -->
  </div>
</template>

<script lang="ts">
import moment from "moment";
import { Component, Vue, Watch } from "vue-property-decorator";
import { UserByMeInfoVo } from "~/api/auth/biz/userApi";
import compUtils from "~/utils/compUtils";
// import Schart from "vue-schart";
import timeUtils from "~/utils/timeUtils";

type UserInfo = UserByMeInfoVo & {
  roleName?: string;
};

@Component({
  name: "role",
  // components: { Schart },
})
export default class HomeIndex extends Vue {
  todoList = [
    {
      title: "今天要修复100个bug",
      status: false,
    },
    {
      title: "今天要修复100个bug",
      status: false,
    },
    {
      title: "今天要写100行代码加几个bug吧",
      status: false,
    },
    {
      title: "今天要修复100个bug",
      status: false,
    },
    {
      title: "今天要修复100个bug",
      status: true,
    },
    {
      title: "今天要写100行代码加几个bug吧",
      status: true,
    },
    {
      title: "今天要修复100个bug",
      status: true,
    },
    {
      title: "今天要写100行代码加几个bug吧",
      status: true,
    },
    {
      title: "今天要修复100个bug",
      status: true,
    },
    {
      title: "今天要写100行代码加几个bug吧",
      status: true,
    },
    {
      title: "今天要修复100个bug",
      status: true,
    },
    {
      title: "今天要写100行代码加几个bug吧",
      status: true,
    },
  ];
  data = [
    {
      name: "2018/09/04",
      value: 1083,
    },
    {
      name: "2018/09/05",
      value: 941,
    },
    {
      name: "2018/09/06",
      value: 1139,
    },
    {
      name: "2018/09/07",
      value: 816,
    },
    {
      name: "2018/09/08",
      value: 327,
    },
    {
      name: "2018/09/09",
      value: 228,
    },
    {
      name: "2018/09/10",
      value: 1065,
    },
  ];
  options = {
    type: "bar",
    title: {
      text: "最近一周各品类销售图",
    },
    xRorate: 25,
    labels: ["周一", "周二", "周三", "周四", "周五"],
    datasets: [
      {
        label: "家电",
        data: [234, 278, 270, 190, 230],
      },
      {
        label: "百货",
        data: [164, 178, 190, 135, 160],
      },
      {
        label: "食品",
        data: [144, 198, 150, 235, 120],
      },
    ],
  };
  options2 = {
    type: "line",
    title: {
      text: "最近几个月各品类销售趋势图",
    },
    labels: ["6月", "7月", "8月", "9月", "10月"],
    datasets: [
      {
        label: "家电",
        data: [234, 278, 270, 190, 230],
      },
      {
        label: "百货",
        data: [164, 178, 150, 135, 160],
      },
      {
        label: "食品",
        data: [74, 118, 200, 235, 90],
      },
    ],
  };

  userInfo: UserInfo = {};
  serverTime: Date = new Date();
  serverTimer: NodeJS.Timeout | null = null;

  get lastLoginMsg() {
    let addr = this.userInfo.loginLastAddr;
    let ip = this.userInfo.loginLastIp;
    let msg = "";
    if (addr) {
      let ads = addr.split("|");
      msg = msg + ads.filter((x) => x && "0" != x).join(",");
    }
    if (ip) {
      msg = msg + `(${ip})`;
    }
    return msg;
  }

  get msgCount() {
    return this.$store.state.msg && this.$store.state.msg.unreadCount >= 0
      ? this.$store.state.msg.unreadCount
      : "加载中";
  }

  async mounted() {
    let that = this;
    this.serverTimer = setInterval(() => {
      that.serverTime = timeUtils.serverTime;
    }, 1000);

    await this.loadUserInfo();
  }

  destroy() {
    if (this.serverTimer != null) {
      clearInterval(this.serverTimer);
      this.serverTimer = null;
    }
  }

  async loadUserInfo() {
    this.userInfo = await this.$store.dispatch("auth/getUserInfo");
    if (this.userInfo && this.userInfo.roleCode) {
      this.userInfo.roleName =
        (<string>this.userInfo.roleCode).indexOf("ROLE_ADMIN") >= 0
          ? "超级管理员"
          : "普通用户";
    }
  }

  async handleInboxMsgClick() {
    this.$router.push(
      `/${compUtils.getQueryValue(this, "role", "")}/base/msg-inbox`
    );
  }

  dateFormate(time: any) {
    if (!time) {
      return "";
    }
    return moment(time).format("yyyy-MM-DD HH:mm:ss");
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.grid-content {
  display: flex;
  align-items: center;
  height: 100px;
}

.grid-cont-right {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #999;
}

.grid-num {
  font-size: 30px;
  font-weight: bold;
}

.cursor-pointer {
  cursor: pointer;
}

.grid-con-icon {
  font-size: 50px;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
  color: #fff;
}

.grid-con-1 .grid-con-icon {
  background: rgb(45, 140, 240);
}

.grid-con-1 .grid-num {
  color: rgb(45, 140, 240);
}

.grid-con-2 .grid-con-icon {
  background: rgb(100, 213, 114);
}

.grid-con-2 .grid-num {
  color: rgb(45, 140, 240);
}

.grid-con-3 .grid-con-icon {
  background: rgb(242, 94, 67);
}

.grid-con-3 .grid-num {
  color: rgb(242, 94, 67);
}

.user-info {
  display: flex;
  align-items: center;
  /* padding-bottom: 10px; */
  border-bottom: 2px solid #ccc;
  margin-bottom: 12px;
}

.user-avator {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.user-info-cont {
  padding-left: 50px;
  flex: 1;
  font-size: 14px;
  color: #999;
}

.user-info-cont div:first-child {
  font-size: 30px;
  color: #222;
}

.user-info-list {
  font-size: 14px;
  color: #999;
  line-height: 22px;
}

.user-info-list span {
  margin-left: 70px;
}

.mgb20 {
  margin-bottom: 20px;
}

.todo-item {
  font-size: 14px;
}

.todo-item-del {
  text-decoration: line-through;
  color: #999;
}

.schart {
  width: 100%;
  height: 300px;
}
</style>
