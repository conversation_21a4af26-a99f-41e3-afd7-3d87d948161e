<template lang="pug">
div
  y-upload(v-model="file", type="pic", limit="10", :multiple="true")
  y-upload(
    v-model="file",
    type="pic",
    limit="10",
    :multiple="true",
    :disabled="true"
  )
  y-upload(v-model="file", limit="10", :multiple="true")
  y-upload(v-model="file", limit="10", :multiple="true", :disabled="true")
  el-input(v-model="file")
  y-select(
    v-model="selectValue",
    filterable,
    remote,
    :remote-method-page="remoteMethod"
  )
    el-option(v-for="item in fs", :key="item", :value="item", :label="item")
    el-option(key="2", value="2", label="12")
  div =>{{ selectValue }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { RemoteMethodParam } from "~/components/y/select.vue";

import { BaseVue } from "~/model/vue";

@Component({
  name: "demo-index02",
})
export default class DemoIndex02 extends mixins(BaseVue) {
  file =
    "e3bf8a98272f7418ffd2e040ab58db1d.jpeg,38e9b8074782022e1950f0c99d066c37.png,adcf12e710a149ec5e8989c420d14522.jpeg," +
    "9b2a18613d657469acf4e7f75aa55a48.jpg,90ca41da1c7cff097a4cfbd88ccfc867.jpg,596e8fecab9cb594587e6bcdcfd9b36b.png," +
    "0e2252d4ab8047c8648698a033b9979a.jpeg,298a85966880e2fc3a5b2d18a1c018ee.jpg";
  fs = this.file.split(",");
  selectValue = "";

  remoteMethod(data: RemoteMethodParam) {
    console.log("---->", data);
    if (data.page > 1) {
      data.end();
    }
  }
}
</script>

<style lang="sass" scoped>
</style>
