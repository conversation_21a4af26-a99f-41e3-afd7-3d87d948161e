<template lang="pug">
.page-demo-index05
  el-button(@click="handleSm2Click") Sm2
  el-button(@click="handleSm2Click2") Sm22
  el-button(@click="handleSm3Click") Sm3
  el-button(@click="handleSm4Click") Sm4
  el-button(@click="handleSm4Click2") Sm42
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import sm from "sm-crypto";
import cryptoUtils from "~/utils/cryptoUtils";
import stringUtils from "~/utils/stringUtils";

@Component({
  name: "demo-index06",
  layout: "empty",
})
export default class DemoIndex06 extends mixins(BaseVue) {
  pubKeyBase64 =
    "BODPabpo8tm0fPgOgaPK0mJEWxppcWv2zEnAx8hsL4c5wiQwKExd7F93FEeKTeHNuIDWKK88/a+DxV3N8PTeZ8c=";
  priKeyBase64 = "AMUVrGKaZgWEy3KndhVtExjHvspk98PcgYWFe3zeU2z1";
  etxt = "你好，世界ts！";
  sm4KeyBase64 = "4Y4d3GozGBzmpqYUBYS5Jg==";
  sm4IvBase64 = "gnmy1/7+spqRjsLcR3tavQ==";

  handleSm2Click() {
    let ckey = "";
    // {
    //   const kp = sm.sm2.generateKeyPairHex();
    //   this.priKeyBase64 = stringUtils.hexToBase64(kp.privateKey);
    //   this.pubKeyBase64 = stringUtils.hexToBase64(kp.publicKey);
    //   console.log("pubKeyBase64:", this.pubKeyBase64);
    //   console.log("priKeyBase64:", this.priKeyBase64);
    // }
    {
      const pubKey = stringUtils.base64ToHex(this.pubKeyBase64);
      const cipherHex = sm.sm2.doEncrypt(this.etxt, pubKey, 1);
      console.log("加密结果（Hex）:", "04" + cipherHex);

      // 转成 Base64，便于传输
      const cipherBase64 = stringUtils.hexToBase64("04" + cipherHex);
      console.log("加密结果（Base64）:", cipherBase64);

      ckey = cipherBase64;
    }
    {
      const priKey = stringUtils.base64ToHex(this.priKeyBase64);
      const hexTxt = stringUtils.base64ToHex(ckey);
      const msg = sm.sm2.doDecrypt(hexTxt, priKey, 1);
      console.log("签名结果（Base64）:", msg);
    }
  }
  handleSm2Click2() {
    // const kp = sm.sm2.generateKeyPairHex();
    const pubKey = stringUtils.base64ToHex(this.pubKeyBase64);
    const priKey = stringUtils.base64ToHex(this.priKeyBase64);
    const userId = "1234567812345678";
    {
      const key = cryptoUtils.generateKey("SM2_256");
      console.log("公钥:", key.publicKeyBase64);
      console.log("私钥:", key.keyBase64);
      const s = cryptoUtils.sign("SM2_256", this.etxt, userId, key.keyBase64!);
      console.log("签名结果（Base64）:", s);
      const ok = cryptoUtils.verify(
        "SM2_256",
        this.etxt,
        s,
        userId,
        key.publicKeyBase64!
      );
      console.log("验签结果:", ok);
    }
    {
      const key = cryptoUtils.generateKey("SM2_256");
      console.log("公钥:", key.publicKeyBase64);
      console.log("私钥:", key.keyBase64);
      const s = cryptoUtils.encrypt("SM2_256", this.etxt, key.publicKeyBase64!);
      console.log("密文结果（Base64）:", s);
      const c = cryptoUtils.decrypt("SM2_256", s, key.keyBase64!);
      console.log("明文结果:", c);
    }
  }

  handleSm3Click() {
    console.log(sm);
    const cipherBase64 = sm.sm3(this.etxt);
    console.log("加密结果（Base64）:", cipherBase64);
  }

  handleSm4Click() {
    // const gkey = this.generateSm4Key();
    // const giv = this.generateSm4Key();
    // this.sm4KeyBase64 = stringUtils.hexToBase64(gkey);
    // this.sm4IvBase64 = stringUtils.hexToBase64(giv);
    console.log("加密key结果（Base64）:", this.sm4KeyBase64);
    console.log("加密iv结果（Base64）:", this.sm4IvBase64);

    const key = stringUtils.base64ToHex(this.sm4KeyBase64);
    const iv = stringUtils.base64ToHex(this.sm4IvBase64);

    let cipherHex = sm.sm4.encrypt(this.etxt, key, {
      padding: "pkcs#7",
      mode: "cbc",
      iv,
    });
    // 转成 Base64，便于传输
    let cipherBase64 = stringUtils.hexToBase64(cipherHex);
    console.log("加密结果（Base64）:", cipherBase64);

    cipherBase64 = "dT6jzUahw6LPfs/rjSbj5g==";
    cipherHex = stringUtils.base64ToHex(cipherBase64);
    const plainHex = sm.sm4.decrypt(cipherHex, key, {
      padding: "pkcs#7",
      mode: "cbc",
      iv,
    });
    console.log("解密结果:", plainHex);
  }
  handleSm4Click2() {
    console.log("sm4");
    const key = cryptoUtils.generateKey("SM4_128");
    console.log("密钥:", key);
    const c = cryptoUtils.encrypt("SM4_128", this.etxt, key.keyBase64!);
    console.log("密文:", c);
    const plain = cryptoUtils.decrypt("SM4_128", c, key.keyBase64!);
    console.log("明文:", plain);
  }

  generateSm4Key(): string {
    const chars = "0123456789abcdef";
    let key = "";
    for (let i = 0; i < 32; i++) {
      key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return key;
  }
}
</script>
