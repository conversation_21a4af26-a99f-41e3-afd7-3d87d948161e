<template lang="pug">
.page-demo-index03
  nuxt-link(to="/def/demo/index05") 渲染
  ym-imageshowedit(v-model="contentJson", :imgBaseUrl="imgBaseUrl")
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import YmImageshowedit from "~/components/ym/imageshowdisedit.vue";

@Component({
  name: "demo-index03",
  components: {
    YmImageshowedit,
  },
})
export default class DemoIndex03 extends mixins(BaseVue) {
  imgBaseUrl = "https://mg.cloud.yheart.cn/api/sys/fs/";
  contentJson =
    '{"bgImgUrl":"38e9b8074782022e1950f0c99d066c37.png","points":[{"code":"VALUE_ACTIVITY_POINT_1","top":0.02236,"left":0.00727,"width":0.97818,"height":0.95208},{"code":"VALUE_ACTIVITY_POINT_2","top":0.05112,"left":0.30909,"width":0.22727,"height":0.77636},{"code":"VALUE_ACTIVITY_POINT_3","top":0.00958,"left":0.47636,"width":0.37273,"height":0.68051}]}';
}
</script>
<style lang="scss">
.layout-content:has(.page-demo-index03) {
  padding: 0;
}
</style>
<style lang="scss" scoped>
.page-demo-index03 {
  height: 100%;
  width: 100%;
}
</style>

