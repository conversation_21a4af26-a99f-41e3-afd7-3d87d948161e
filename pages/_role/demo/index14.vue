<template>
  <div>
    <div class="cards-wrap">
      <div v-for="(card, index) in cards" :key="card.id" class="card" @click="pickOption" :class="{
        first: index === 0,
        second: index === 1,
        third: index === 2,
        'last-card': index > 2,
        swipe: index === 0 && swiping,
      }">
        <h5 v-if="index === 0">{{ card.title }}</h5>
      </div>
    </div>
  </div>
</template>

<script>
let idCounter = 5;

export default {
  name: "SwipeCards",
  data() {
    return {
      cards: [
        {
          id: 1,
          title: "卡片1",
        },
        {
          id: 2,
          title: "卡片2",
        },
        {
          id: 3,
          title: "卡片3",
        },
        {
          id: 4,
          title: "卡片4",
        },
        {
          id:5,
          title: "卡片5",
        },
      ],
      swiping: false,
    };
  },
  computed: {
    visibleCards() {
      // 返回前3张卡片用于显示
      return this.cards.slice(0, 3);
    },
  },
  methods: {
    pickOption() {
      this.swiping = true;

      // 等动画完成后移除顶部卡片并添加一个新卡片到队列末尾
      setTimeout(() => {
        const swiped = this.cards.shift();

        // // 模拟生成新的卡片
        // const newCard = {
        //   id: idCounter++,
        //   title: `卡片${idCounter}`,
        // };

        // this.cards.push(newCard);
        this.swiping = false;
      }, 300);
    },
  },
};
</script>

<style scoped>
body {
  margin: 0;
  background: salmon;
}
h1 {
  color: white;
  margin-top: 40px;
  text-align: center;
}
.cards-wrap {
  margin-top: 10px;
  width: 380px;
  height: 400px;
  margin: 200px auto;
  perspective: 100px;
  perspective-origin: 50% 90%;
  position: relative;
}
.card {
  width: 320px;
  height: 240px;
  padding: 24px 30px;
  background: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  transform-origin: 50% 50%;
  transition: all 0.8s;
  transform: translate3d(0, 0, 0px);
}
.card h5{
  text-align: center;
  line-height: 240px;
}
.card.first {
  transform: translate3d(0, 0, 0px);
  z-index: 3;
}
.card.second {
  transform: translate3d(0, 0, -10px);
  z-index: 2;
}
.card.third {
  transform: translate3d(0, 0, -20px);
  z-index: 1;
}
.card.last-card {
  transform: translate3d(0, 0, -20px);
  z-index: 0;
}
.card:not(.first) {
  opacity: 0.95;
}
.card.swipe {
  transform: rotate(30deg) translate3d(120%, -50%, 0px) !important;
  opacity: 0;
  visibility: hidden;
}
.card h5 {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 20px;
  margin-top: 0;
}
.option {
  width: 100%;
  padding: 18px 10px 18px 84px;
  text-align: left;
  border: none;
  outline: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 400;
  background: #f8f8f8;
  background-size: 24px;
  margin-bottom: 20px;
}
.option:hover {
  background: #faeeee;
}
</style>
