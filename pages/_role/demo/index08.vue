<template>
  <div class="scene" @mousemove="handleMouseMove" @mouseleave="resetRotation">
    <div class="card-stack" :style="{ transform: stackTransform }">
      <div class="card-stack">
        <div v-for="(card, index) in cards" :key="card.id" class="card" :style="getCardStyle(index)"
          @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = null">
          <div class="card-content">
            <h2>{{ card.title }}</h2>
            <img :src="card.image" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      cards: [
        { id: 1, title: "Vehicle", image: "https://source.unsplash.com/400x200/?car" },
        { id: 2, title: "Fashion", image: "https://source.unsplash.com/400x200/?fashion" },
        { id: 3, title: "Conceptual Design", image: "https://source.unsplash.com/400x200/?concept" },
        { id: 4, title: "IP", image: "https://source.unsplash.com/400x200/?animation" },
        { id: 5, title: "Poster", image: "https://source.unsplash.com/400x200/?poster" },
        { id: 6, title: "Storyboard", image: "https://source.unsplash.com/400x200/?storyboard" },
        { id: 7, title: "Product", image: "https://source.unsplash.com/400x200/?product" },
        { id: 8, title: "Branding", image: "https://source.unsplash.com/400x200/?brand" },
      ],
      hoverIndex: null,
      rotateX: 0,
      rotateY: 0,
    };
  },
  computed: {
    stackTransform() {
      return `rotateX(${this.rotateX}deg) rotateY(${this.rotateY}deg)`;
    },
  },
  methods: {
    getCardStyle(index) {
      const x = -index * 90; // 每张卡片向左偏移
      const y = -index * 40; // 每张卡片向上偏移
      const z = -index * 100; // 远近
      // const scale = 1 - index * 0.01;//近大远小
      const scale = 1;
      const baseRotate = 30; // 固定基础倾斜角
      // const baseRotate = 0; // 固定基础倾斜角
      const zIndex = 100 - index;
      const hoverTransform = this.hoverIndex === index ? " translateZ(80px) scale(1.05)" : "";

      return {
        transform: `translateX(${x}px) translateY(${y}px) translateZ(${z}px) scale(${scale}) rotateY(${baseRotate}deg)${hoverTransform}`,
        zIndex: this.hoverIndex === index ? 999 : zIndex,
      };
    },
    handleMouseMove(e) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      const rotateY = ((x - centerX) / centerX) * 10;
      const rotateX = -((y - centerY) / centerY) * 10;

      this.rotateX = rotateX;
      this.rotateY = rotateY;
    },
    resetRotation() {
      this.rotateX = 0;
      this.rotateY = 0;
    },
  },
};
</script>

<style scoped>
.scene {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f6f6f6;
  perspective: 1600px;
  overflow: hidden;
}

.card-stack {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
  position: relative;
  width: 1000px;
  height: 300px;
}

.card {
  position: absolute;
  top: 0;
  right: 0;
  width: 400px;
  height: 220px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, z-index 0.2s;
  cursor: pointer;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card h2 {
  margin: 12px 16px;
  font-size: 20px;
  color: #333;
}

.card img {
  flex-grow: 1;
  width: 100%;
  object-fit: cover;
}
</style>
