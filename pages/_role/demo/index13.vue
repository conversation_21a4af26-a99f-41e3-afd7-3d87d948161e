<template>
  <div class="fan-layout">
    <div
      v-for="(angle, i) in angles"
      :key="i"
      class="card"
      :style="getCardStyle(angle, i)"
      @click="togglePopup(i)"
    >
      {{ i + 1 }}
    </div>
  </div>
</template>

<script>
export default {
  name: "FanCardPopout",
  data() {
    return {
      angles: [-30, -15, 0, 15, 30],
      popupIndex: null,
    };
  },
  methods: {
    togglePopup(i) {
      this.popupIndex = this.popupIndex === i ? null : i;
    },
    getCardStyle(angle, i) {
      const isPopped = this.popupIndex === i;
      return {
        transform: `
          rotate(${angle}deg)
          translateY(-100px)
          ${isPopped ? "translateY(-40px) translateZ(120px)" : ""}
        `,
        background: `hsl(${i * 60}, 80%, 60%)`,
        zIndex: isPopped ? 10 : i,
      };
    },
  },
};
</script>

<style scoped>
.fan-layout {
  position: relative;
  width: 600px;
  height: 300px;
  margin:200px auto;
  transform-style: preserve-3d;
  perspective: 1000px;
  perspective-origin: center bottom;
}
.card {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 150px;
  height: 200px;
  margin-left: -75px;
  transform-origin: bottom center;
  border-radius: 12px;
  color: white;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  transition: transform 0.4s ease, z-index 0.3s;
  cursor: pointer;
}
</style>
