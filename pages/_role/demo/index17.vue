<template>
  <div>
    <div class="img-container">
      <el-image class="img" v-for="(img, idx) in images" :key="idx" :src="img" fit="contain"></el-image>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      hover: false,
      images: [
        require('@/static/img/login-bg.jpg'),
        require('@/static/img/login-bg.jpg'),
        require('@/static/img/img.jpg'),
        require('@/static/img/img.jpg'),
      ],
    };
  },
  methods: {
    
  },
};
</script>

<style lang="scss" scoped>
.img-container {
  position: relative;
  width: 300px;
  height: 300px;
  overflow: hidden;
  border: 1px solid #ccc;

  .img {
    // object-fit: cover;
    // user-select: none;
    // pointer-events: none;
    align-items: center;
    border-color: transparent;
    border-style: solid;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    overflow: hidden;
    transform-origin: top left;
    transition: all .3s ease-in-out;

    &:nth-child(1) {
      border-width: 0;
      height: 100%;
      position: absolute;
      width: 100%;
    }

    &:nth-child(2) {
      border-width: 0 0 1px 1px;
      height: 50%;
      transform: translate(200%) scale(2);
      width: 50%;
    }

    &:nth-child(3) {
      border-width: 1px 1px 0 0;
      height: 50%;
      transform: translate(0, 100%);
      width: 50%;
    }

    &:nth-child(4) {
      border-width: 1px 0 0 1px;
      height: 50%;
      transform: translate(100%, 0);
      width: 50%;
    }
  }
}

.img-container:hover {
  .img:nth-child(1) {
    border-width: 0 1px 1px 0;
    height: 100%;
    position: absolute;
    width: 100%;
    transform: scale(0.5);
  }

  .img:nth-child(2) {
    border-width: 0 0 1px 1px;
    height: 50%;
    transform: translate(100%) scale(1);
    width: 50%;
  }

  .img:nth-child(3) {
    border-width: 1px 1px 0 0;
    height: 50%;
    transform: translate(0, 0);
    width: 50%;
  }

  .img:nth-child(4) {
    border-width: 1px 0 0 1px;
    height: 50%;
    transform: translate(100%, -100%);
    width: 50%;
  }
}
</style>
