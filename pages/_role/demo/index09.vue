<template>
  <div class="scene" @mousemove="handleMouseMove" @mouseleave="resetRotation">
    <div class="card-stack" :style="{ transform: stackTransform }">
      <div v-for="(card, index) in cards"
           :key="card.id"
           class="card"
           :style="getCardStyle(index)"
           @mouseenter="hoverIndex = index"
           @mouseleave="!activeCardId && (hoverIndex = null)"
           @click="toggleActiveCard(card.id)">
        <div class="card-content">
          <h2>{{ card.title }}</h2>
          <img :src="card.image" alt="" />
          <p v-if="activeCardId === card.id" class="card-description">
            {{ card.description || '暂无描述' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      cards: [
        { id: 1, title: "Vehicle", image: "https://source.unsplash.com/400x200/?car", description: "Vehicle design project." },
        { id: 2, title: "Fashion", image: "https://source.unsplash.com/400x200/?fashion", description: "Fashion concept collection." },
        { id: 3, title: "Conceptual Design", image: "https://source.unsplash.com/400x200/?concept", description: "Abstract ideas and visuals." },
        { id: 4, title: "IP", image: "https://source.unsplash.com/400x200/?animation", description: "Intellectual property visuals." },
        { id: 5, title: "Poster", image: "https://source.unsplash.com/400x200/?poster", description: "Promotional poster artworks." },
        { id: 6, title: "Storyboard", image: "https://source.unsplash.com/400x200/?storyboard", description: "Storyboard for film/commercial." },
        { id: 7, title: "Product", image: "https://source.unsplash.com/400x200/?product", description: "Product design shots." },
        { id: 8, title: "Branding", image: "https://source.unsplash.com/400x200/?brand", description: "Brand identity showcase." },
      ],
      hoverIndex: null,
      rotateX: 0,
      rotateY: 0,
      activeCardId: null,
    };
  },
  computed: {
    stackTransform() {
      return `rotateX(${this.rotateX}deg) rotateY(${this.rotateY}deg)`;
    },
  },
  methods: {
    getCardStyle(index) {
      const card = this.cards[index];
      const isActive = this.activeCardId === card.id;
      const x = -index * 90;
      const y = -index * 40;
      const z = -index * 100;
      const baseRotate = 30;
      const scale = 1;

      if (isActive) {
        return {
          transform: `translateZ(300px) scale(1.3) rotateY(0deg)`,
          zIndex: 1000,
        };
      }

      const hoverTransform = this.hoverIndex === index ? " translateZ(80px) scale(1.05)" : "";

      return {
        transform: `translateX(${x}px) translateY(${y}px) translateZ(${z}px) scale(${scale}) rotateY(${baseRotate}deg)${hoverTransform}`,
        zIndex: this.hoverIndex === index ? 999 : 100 - index,
      };
    },
    handleMouseMove(e) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      // this.rotateY = ((x - centerX) / centerX) * 10;
      // this.rotateX = -((y - centerY) / centerY) * 10;
    },
    resetRotation() {
      this.rotateX = 0;
      this.rotateY = 0;
    },
    toggleActiveCard(id) {
      this.activeCardId = this.activeCardId === id ? null : id;
    },
  },
};
</script>

<style scoped>
.scene {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f6f6f6;
  perspective: 1600px;
  overflow: hidden;
}

.card-stack {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
  position: relative;
  width: 1000px;
  height: 300px;
}

.card {
  position: absolute;
  top: 0;
  right: 0;
  width: 400px;
  height: 220px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.4s ease, z-index 0.3s;
  cursor: pointer;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card h2 {
  margin: 12px 16px 8px;
  font-size: 20px;
  color: #333;
}

.card img {
  flex-grow: 1;
  width: 100%;
  object-fit: cover;
}

.card-description {
  padding: 10px 16px;
  font-size: 14px;
  color: #555;
  background: #fafafa;
}
</style>
