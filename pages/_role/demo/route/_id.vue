<template lang="pug">
.index
  .win
    el-button(@click="handleQueryClick") 测试跳转
    el-button(@click="handlePageClick") 测试page

  .show
    div 测试页面 {{ dialogFormVisible ? "显示" : "隐藏" }}

  .dig
    el-dialog(
      :visible.sync="dialogFormVisible",
      :before-close="() => { dialogFormVisible = false; }"
    )
      div 弹出框内容
</template>

<script lang="ts">
import { Component } from "nuxt-property-decorator";
import Vue from "vue";

@Component({
  name: "demo-route",
  layout: "empty",
})
export default class PageDemoRoute extends Vue {
  dialogFormVisible = false;
  id = "";
  queryId = "";

  // beforeRouteUpdate(to: any, from: any, next: any) {
  //   // 不重建组件，而是手动处理新数据
  //   console.log("[beforeRouteUpdate] 路由参数变化:", to.params.id);

  //   // 示例：可以在这里调用 API 更新数据
  //   this.id = to.params.id;
  //   this.queryId = to.query.id;

  //   // 不触发组件重建
  //   next(false);
  // }

  async handleQueryClick() {
    this.$router.replace("/def/demo/route/1?id=2");
    this.dialogFormVisible = true;
  }

  async handlePageClick() {
    this.$router.replace("/def/demo/route/2?id=2");
    this.dialogFormVisible = true;
  }
}
</script>

<style scoped></style>


