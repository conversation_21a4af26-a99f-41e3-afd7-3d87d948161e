<template>
  <div class="container">
    <h2>多行流式图片布局</h2>
    <div class="flow-container">
      <div class="flow-item" v-for="(img, index) in imgList" :key="index">
        <img :src="img.src" :alt="img.title" />
        <p class="title">{{ img.title }}</p>
        <p class="info">{{ img.info }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MultiRowImageFlow",
  data() {
    return {
      imgList: [],
      imgArr: [
        require('@/static/img/waterfullImg/1.jpg'),
        require('@/static/img/waterfullImg/6.jpg'),
        require('@/static/img/waterfullImg/7.jpg'),
        require('@/static/img/waterfullImg/10.jpg'),
        require('@/static/img/waterfullImg/12.jpg'),
        require('@/static/img/waterfullImg/13.jpg'),
        require('@/static/img/waterfullImg/14.jpg'),
        require('@/static/img/waterfullImg/15.jpg'),
        require('@/static/img/waterfullImg/16.jpg'),
        require('@/static/img/waterfullImg/19.jpg'),
        require('@/static/img/waterfullImg/2.png'),
        require('@/static/img/waterfullImg/4.png'),
        require('@/static/img/waterfullImg/5.png'),
        require('@/static/img/waterfullImg/8.png'),
        require('@/static/img/waterfullImg/9.png'),
        require('@/static/img/waterfullImg/11.png'),
        require('@/static/img/waterfullImg/17.png'),
        require('@/static/img/waterfullImg/18.png'),
      ]
    };
  },
  mounted() {
    this.imgList = this.imgArr.map(src => ({
      src,
      title: '标题文字',
      info: '图片说明信息'
    }));
  }
};
</script>
<style scoped>
.container {
  padding: 20px;
}

.flow-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-start;
}

.flow-item {
  display: flex;
  flex-direction: column;
  max-width: 300px;
  flex-shrink: 0;
}

.flow-item img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 14px;
  color: #444;
  margin-top: 6px;
}

.info {
  font-size: 12px;
  color: #888;
}
</style>

