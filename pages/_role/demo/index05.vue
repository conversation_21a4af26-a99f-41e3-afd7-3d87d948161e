<template lang="pug">
.page-demo-index05
  nuxt-link(to="/def/demo/index03") 编辑
  el-input(v-model="contentJson", type="textarea", :row="6")
  .show-win
    ym-imageshowdisplay(
      v-model="contentJson",
      :imgBaseUrl="imgBaseUrl",
      @click="handleItemClick"
    )
</template>

<script lang="ts">
import { Component, mixins, Watch } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import YmImageshowdisplay from "~/components/ym/imageshowdisplay.vue";
import { PointContent } from "~/components/ym/imageshowdisedit.vue";

@Component({
  name: "demo-index03",
  layout: "empty",
  components: {
    YmImageshowdisplay,
  },
})
export default class DemoIndex03 extends mixins(BaseVue) {
  imgBaseUrl = "https://mg.cloud.yheart.cn/api/sys/fs/";
  contentJson =
    '{"bgImgUrl":"38e9b8074782022e1950f0c99d066c37.png","points":[{"code":"VALUE_ACTIVITY_POINT_1","top":0.02236,"left":0.00727,"width":0.97818,"height":0.95208},{"code":"VALUE_ACTIVITY_POINT_2","top":0.05112,"left":0.30909,"width":0.22727,"height":0.77636},{"code":"VALUE_ACTIVITY_POINT_3","top":0.00958,"left":0.47636,"width":0.37273,"height":0.68051}]}';

  handleItemClick(item: PointContent) {
    this.$notify.info(`点击到了：${item.code}模块`);
  }
}
</script>
<style lang="scss">
.layout-content:has(.page-demo-index03) {
  padding: 0;
}
</style>
<style lang="scss" scoped>
.page-demo-index05 {
  width: 100%;

  .show-win {
    width: 50%;
  }
}
</style>

