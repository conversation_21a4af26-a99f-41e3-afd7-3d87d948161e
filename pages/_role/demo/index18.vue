<template>
  <div class="watermark">
    <input type="file" @change="handleFileChange" accept="image/*" />
    <div v-if="resultImage" class="preview">
      <img :src="resultImage" alt="Watermarked Image" />
      <a :href="resultImage" download="watermarked.png">下载带水印图片</a>
    </div>
    <canvas ref="canvas" style="display: none;"></canvas>
  </div>
</template>

<script>
export default {
  data() {
    return {
      resultImage: null,
    };
  },
  methods: {
    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.onload = () => this.drawMultiWatermark(img);
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    drawMultiWatermark(img) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");

      canvas.width = img.width;
      canvas.height = img.height;

      // 绘制原图
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);

      // 创建一个临时 canvas 用于绘制水印图层
      const tmpCanvas = document.createElement('canvas');
      const tmpCtx = tmpCanvas.getContext('2d');
      tmpCanvas.width = canvas.width;
      tmpCanvas.height = canvas.height;

      // 水印样式
      const text = '© YourSite.com';
      tmpCtx.font = "20px sans-serif";
      tmpCtx.fillStyle = "rgba(255, 255, 255, 0.2)";
      tmpCtx.textAlign = "center";
      tmpCtx.textBaseline = "middle";

      // 旋转角度（单位：弧度）
      const angle = -30 * Math.PI / 180;
      tmpCtx.translate(tmpCanvas.width / 2, tmpCanvas.height / 2);
      tmpCtx.rotate(angle);
      tmpCtx.translate(-tmpCanvas.width / 2, -tmpCanvas.height / 2);

      // 多次绘制水印文字
      const gapX = 200;
      const gapY = 150;

      for (let x = -canvas.width; x < canvas.width * 2; x += gapX) {
        for (let y = -canvas.height; y < canvas.height * 2; y += gapY) {
          tmpCtx.fillText(text, x, y);
        }
      }

      // 叠加水印图层
      ctx.drawImage(tmpCanvas, 0, 0);

      // 导出图片
      this.resultImage = canvas.toDataURL("image/png");
    }
  }
};
</script>

<style scoped>
.watermark {
  padding: 20px;
}
.preview img {
  max-width: 100%;
  margin-top: 20px;
}
</style>
