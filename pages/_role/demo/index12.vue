<template>
  <div class="carousel-container">
    <div
      class="carousel"
      :class="{ paused: isPaused }"
      @mouseenter="isPaused = true"
      @mouseleave="isPaused = false"
    >
      <div
        class="carousel-card"
        v-for="n in cardCount"
        :key="n"
        :style="getCardStyle(n)"
      >
        {{ n }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Carousel3D",
  data() {
    return {
      cardCount: 6,
      isPaused: false,
    };
  },
  methods: {
    getCardStyle(n) {
      const angle = (360 / this.cardCount) * (n - 1);
      return {
        transform: `rotateY(${angle}deg) translateZ(400px)`,
        background: `hsl(${angle}, 70%, 60%)`,
      };
    },
  },
};
</script>

<style scoped>
.carousel-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  perspective: 1200px;
  overflow: hidden;
}
.carousel {
  position: relative;
  width: 300px;
  height: 200px;
  transform-style: preserve-3d;
  animation: rotate 15s linear infinite;
}
.carousel.paused {
  animation-play-state: paused;
}
.carousel-card {
  position: absolute;
  width: 280px;
  height: 180px;
  border-radius: 20px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  color: #fff;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  line-height: 180px;
}
@keyframes rotate {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}
</style>
