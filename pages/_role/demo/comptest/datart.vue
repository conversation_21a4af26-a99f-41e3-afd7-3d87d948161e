<template lang="pug">
div
  el-input(v-model="shareId", placeholder="请输入分享码")
  el-input(v-model="shareKey", placeholder="请输入分享码")
  y-datartshare(:id="shareId", :code="shareCode")
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import datarShareApi from "~/api/basic/datart/share/codeApi";
import routerUtils from "~/utils/routerUtils";

@Component({
  name: "demo-comptest-datart",
  components: {},
})
export default class PageDemoComptestDatart extends mixins(BaseVue) {
  shareId = "";
  shareKey = "";
  shareCode = "";

  created() {
    this.shareId = routerUtils.getParamValue(
      this,
      "id",
      "36dd1e2bc3534a5d97fb1a71c4721e0c"
    );
    this.shareKey = routerUtils.getParamValue(
      this,
      "key",
      "c7fa40405373a849becfe8a32304da73b23ae46bd9a12bd0e3669d34864c76d0"
    );
  }

  async mounted() {
    let r = await datarShareApi.buildCode({
      id: this.shareId,
      key: this.shareKey,
    });
    this.shareCode = r.code!;
  }
}
</script>

<style lang="sass" scoped>
</style>
