<template>
  <div class="card">
    <div class="cover">
      <img src="@/static/img/img.jpg" alt="watch cover" />
    </div>
    <div class="details">
      <div>
        <img src="@/static/img/img.jpg" alt="watch" />
        <h3>苹果电话手表</h3>
        <h2>¥ 1999</h2>
        <a href="https://www.webqdkf.com/" target="_blank">添加购物车</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProductCard",
};
</script>

<style scoped>
* {
  box-sizing: border-box;
  font-family: 'Poppins', sans-serif;
}
.card {
  position: relative;
  margin: 200px auto;
  width: 300px;
  height: 400px;
  background: #fff;
  transform-style: preserve-3d;
  transform: perspective(2000px);
  box-shadow: inset 300px 0 50px rgba(0, 0, 0, 0.15),
    0 20px 20px rgba(0, 0, 0, 0.15);
  transition: 1s;
}
.card:hover {
  transform: perspective(2000px) translateX(50%);
  box-shadow: inset 20px 0 50px rgba(0, 0, 0, 0.15),
    0 10px 100px rgba(0, 0, 0, 0.15);
}
.card .cover {
  position: relative;
  width: 100%;
  height: 100%;
  transform-origin: left;
  z-index: 2;
  transition: 1s ease-out;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-style: preserve-3d;
  overflow: hidden;
}
.card .cover img {
  max-width: 100%;
  z-index: 1;
}
.card:hover .cover {
  transform: rotateY(-180deg);
}
.card .cover::before {
  content: "";
  position: absolute;
  width: 10px;
  background: #fff;
  height: 150%;
  transform: rotate(36.5deg);
  box-shadow: 0 0 0 20px #47bfce;
  transition: 0.5s;
  transition-delay: 1s;
}
.card:hover .cover::before {
  width: 0px;
  box-shadow: 0 0 0 250px #47bfce;
  transform: rotate(143.5deg);
}
.card .details {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  overflow: hidden;
}
.card .details h3 {
  font-weight: 500;
  margin: 5px 0;
}
.card .details h2 {
  font-size: 1.5em;
  color: #e82a5b;
  font-weight: 600;
}
.card .details a {
  display: inline-block;
  padding: 8px 20px;
  margin-top: 5px;
  background: #47bfce;
  color: #fff;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 25px;
  text-decoration: none;
}
</style>
