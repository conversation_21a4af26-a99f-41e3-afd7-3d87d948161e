<template>
  <div class="upload-container">
    <h2>拖拽或点击上传文件</h2>

    <div
      class="drop-zone"
      :class="{ dragover: isDragging }"
      @dragover.prevent="onDragOver"
      @dragleave="onDragLeave"
      @drop.prevent="onDrop"
      @click="triggerFileInput"
    >
       {{ isDragging ? '松开文件以上传' : '拖拽文件到此，或点击选择' }}
      <input
        type="file"
        ref="fileInput"
        style="display: none"
        multiple
        @change="onFileChange"
      />
    </div>

    <ul v-if="files.length">
      <li v-for="(file, index) in files" :key="index">
        {{ file.name }} ({{ (file.size / 1024).toFixed(1) }} KB)
      </li>
    </ul>
    <!-- <el-image src=""></el-image> -->
  </div>
</template>

<script>
import fileuploadUtils from "~/utils/fileuploadUtils";
export default {
  name: 'DragUpload',
  data() {
    return {
      isDragging: false,
      files: []
    };
  },
  methods: {
    //元素或选择在有效的放置目标上方移动时连续地触发
    onDragOver() {
      this.isDragging = true;
    },
    //拖动的元素离开一个有效的放置目标时触发
    onDragLeave() {
      this.isDragging = false;
    },
    //当被拖动的元素或选中的文本/链接在有效的放置目标上释放时触发
    onDrop(e) {
      this.isDragging = false;
      const droppedFiles = Array.from(e.dataTransfer.files);
      this.handleFiles(droppedFiles);
    },
    onFileChange(e) {
      const selectedFiles = Array.from(e.target.files);
      this.handleFiles(selectedFiles);

      // 重置 input，否则再次选相同文件不会触发 change
      this.$refs.fileInput.value = '';
    },
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    handleFiles(fileList) {
      this.files = fileList;
      this.uploadFiles();
    },
    uploadFiles() {
      if (!this.files.length) return;
      console.log("========上传>>>", this.files);
      
      // const formData = new FormData();
      // this.files.forEach(file => {
      //   formData.append('files[]', file);
      // });

      // // 模拟上传
      // console.log('自动上传文件：', this.files);
      // alert(`上传了 ${this.files.length} 个文件`);
      for (let f of this.files) {
        fileuploadUtils
          .upload({
            file: f,
          })
          .then((data) => {
            // 移除上传标识
            console.log("上传成功", data);
          })
      }
      // this.files = [];
      // 实际上传可使用 axios:
      // axios.post('/api/upload', formData)
    }
  }
};
</script>

<style scoped>
.upload-container {
  font-family: Arial, sans-serif;
  max-width: 600px;
  margin: 40px auto;
  padding: 20px;
}

.drop-zone {
  border: 2px dashed #ccc;
  padding: 60px;
  text-align: center;
  color: #999;
  transition: 0.3s;
  cursor: pointer;
  user-select: none;
}

.drop-zone.dragover {
  border-color: #333;
  background-color: #f4faff;
  color: #333;
}

ul {
  margin-top: 20px;
  padding-left: 0;
}

li {
  list-style: none;
  margin-bottom: 5px;
}
</style>
