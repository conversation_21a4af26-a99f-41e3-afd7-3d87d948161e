<template>
    <div>
        <el-row>
            推流地址：
            <el-input v-model="pushurl"
                      style="width: 400px" />
            <el-button @click="camera">推流</el-button>
            <el-button @click="cameraAuditOn">开音</el-button>
            <el-button @click="cameraAuditOff">关音</el-button>
            <el-button @click="cameraVideoOff">关像</el-button>
            <el-button @click="cameraVideoOn">开像</el-button>
            <video ref="cameravideo"
                   style="width: 500px; height: 400px;"></video>
        </el-row>

        <el-row>
            播放地址：
            <el-input v-model="playerurl"
                      style="width: 400px" />
            <el-button @click="player">拉流</el-button>
            <el-button @click="playerAuditOn">开音</el-button>
            <el-button @click="playerAuditOff">关音</el-button>
            <el-button @click="playerVideoOff">关像</el-button>
            <el-button @click="playerVideoOn">开像</el-button>
            <span>{{ playernetwork }}</span>
            <video ref="playervideo"
                   style="width: 500px; height: 400px;"
                   v-show="playershow"></video>
        </el-row>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, VuexModule } from "nuxt-property-decorator";
import {
    AliRTS,
    IConfig,
    IStreamConfig,
    LocalStream,
    RemoteStream,
    RtsClient
} from "aliyun-rts-sdk";

@Component({
    name: "live",
    components: {}
})
export default class Live extends Vue {
    pushurl: string =
        "artc://push.shicanxinxi.com/test/test?auth_key=1638255681-0-0-5cd150c59996a106b36442a9c884eeab";
    cameraRts: RtsClient | null = null;
    cameraStream: LocalStream | null = null;

    playerurl: string =
        "artc://live.shicanxinxi.com/test/test?auth_key=1638255681-0-0-ba8fe759c77f5d687f69b31401ab1072";
    playerRts: RtsClient | null = null;
    playerStream: RemoteStream | null = null;
    /**
     * 网络情况
     * 表示音频/视频元素的当前网络状态：
        0 = NETWORK_EMPTY - 音频/视频尚未初始化
        1 = NETWORK_IDLE - 音频/视频是活动的且已选取资源，但并未使用网络
        2 = NETWORK_LOADING - 浏览器正在下载数据
        3 = NETWORK_NO_SOURCE - 未找到音频/视频来源
     */
    playernetwork = 0;
    playershow = true;

    created() {}

    async mounted() {
        console.log("mounted");
        this.init();
    }

    init() {
        let fun = () => {
            this.playernetwork = this.$refs.playervideo
                ? (<any>this.$refs.playervideo).networkState
                : 0;
            setTimeout(fun, 500);
        };
        fun();
    }

    async camera() {
        if (this.cameraRts != null) {
            await this.cameraRts.unpublish();
            this.cameraRts = null;
        }
        try {
            this.cameraRts = AliRTS.createClient({});
            let res = await this.cameraRts!.isSupport({
                isReceiveVideo: true
            });

            let list = await AliRTS.getCameraList();
            console.log(list);

            let config: IStreamConfig = {
                video: true,
                audio: true,
                screen: false
            };
            this.cameraStream = await AliRTS.createStream(config);
            // 可用
            console.log("支持webrtc============");

            await this.cameraRts!.publish(this.pushurl, this.cameraStream);

            // mediaElement是媒体标签audio或video
            this.cameraStream.play(<HTMLMediaElement>this.$refs.cameravideo);
        } catch (err) {
            // 不可用
            console.log(`not support errorCode: ${err.errorCode}`);
            console.log(`not support message: ${err.message}`);
        }
    }

    async cameraAuditOn() {
        this.cameraStream?.enableAudio();
    }
    async cameraAuditOff() {
        this.cameraStream?.disableAudio();
    }
    async cameraVideoOn() {
        this.cameraStream?.enableVideo();
    }
    async cameraVideoOff() {
        this.cameraStream?.disableVideo();
    }

    async player() {
        if (this.playerRts != null) {
            await this.playerRts.unpublish();
            this.playerRts = null;
        }
        try {
            let config: IConfig = {};
            this.playerRts = AliRTS.createClient(config);
            let res = await this.playerRts.isSupport({ isReceiveVideo: true });
            // 可用
            console.log("支持webrtc============");

            this.playerStream = await this.playerRts.subscribe(this.playerurl);
            // mediaElement是媒体标签audio或video
            this.playerStream.play(<HTMLMediaElement>this.$refs.playervideo);
        } catch (err) {
            // 不可用
            console.log(`not support errorCode: ${err.errorCode}`);
            console.log(`not support message: ${err.message}`);
        }
    }
    async playerAuditOff() {
        (<HTMLMediaElement>this.$refs.playervideo).muted = true;
    }
    async playerAuditOn() {
        (<HTMLMediaElement>this.$refs.playervideo).muted = false;
    }
    async playerVideoOn() {
        this.playershow = true;
    }
    async playerVideoOff() {
        this.playershow = false;
    }
}
</script>

<style lang="sass" scoped>
</style>
