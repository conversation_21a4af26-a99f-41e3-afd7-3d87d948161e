<template lang="pug">
div
  h1 Demo
  div
    el-button(type="primary", @click="toPage('/demo/index03')") 前往模版编辑页
    el-button(type="primary", @click="toPage('/demo/mallweb')") 前往商城
    el-button(
      v-if="$auth.v_hasPower('ROLE_ADMIN')",
      type="primary",
      @click="toPage('/sysbase/log')"
    ) 前往Log
  .clsssaaa
    //- y-fun-login(sysAppId="20K7WWTC959BTB99")
    //-     template(slot-scope="{ imgUrl }")
    //-         img(:src="imgUrl")
    //- .ss(v-power="powerList", v-show="true", :class="powerList[0]")
    //-   p haha
    //- template(v-power="powerList") aa
    el-input(v-model="powerList[0]")
    //- el-row(v-power="powerList") ss
  div
    el-button(type="primary", @click="toPage('/demo/index21')") 卡片效果
    el-button(type="primary", @click="toPage('/demo/index20')") 标签效果
    el-button(type="primary", @click="toPage('/demo/index19')") 拖拽上传
    el-button(type="primary", @click="toPage('/demo/index18')") 图片水印
    el-button(type="primary", @click="toPage('/demo/index17')") 卡片封面效果
    el-button(type="primary", @click="toPage('/demo/index16')") 贺卡效果
    el-button(type="primary", @click="toPage('/demo/index15')") 卡片立方体
    el-button(type="primary", @click="toPage('/demo/index14')") 卡片堆叠切换
    el-button(type="primary", @click="toPage('/demo/index13')") 卡片扇形布局
    el-button(type="primary", @click="toPage('/demo/index12')") 卡片旋转布局
    el-button(type="primary", @click="toPage('/demo/index08')") preserve-3d卡片01
    el-button(type="primary", @click="toPage('/demo/index09')") preserve-3d卡片02
    el-button(type="primary", @click="toPage('/demo/index10')") 卡片布局切换
    el-button(type="primary", @click="toPage('/demo/index07')") 瀑布流
    div(v-if="$auth.v_hasPower(powerList)") 权限控制显示
    div
      ul
        li(v-for="op in $dic.getOptions('auth.user.type')") {{ op.value }}
    div 页面角色权限{{ $auth.v_getPageRole() }}
    div
      el-button(@click="() => handleSwitchPageRole('ROLE_ADMIN')") 切换角色到ROLE_ADMIN
      el-button(@click="() => handleSwitchPageRole('ROLE_MANAGE')") 切换角色到ROLE_MANAGE
  div
    //- 表单内容
    el-table.table(:data="tables")
      //- el-table-column(type="selection", width="55", align="center")
      el-table-column(prop="code", label="Code", align="center")
      el-table-column(prop="name", label="名称")
        //- v-table-column="{ type: 'date', format: 'yyyy-MM-DD HH:mm:ss' }",
      el-table-column(prop="createTime", label="时间")
      el-table-column(prop="remark", label="备注")
  div
    el-form(ref="form", label-width="80px")
      el-form-item(label="上传图片")
        //- y-upload(v-model="file", limit="3")
        y-upload(v-model="file", type="pic", limit="3", :multiple="true")
      el-form-item(label="上传图片2")
        y-upload(
          v-model="file",
          :multiple="true",
          limit="3",
          :show-qrcode="true"
        )
      el-form-item(label="水印图片")
        y-upload(
          v-model="file",
          :multiple="true",
          :watermark="true",
          :show-qrcode="true"
        )
      el-form-item(label="上传图片3")
        y-upload(
          v-model="file",
          :multiple="true",
          type="pic",
          :show-qrcode="true"
        )
      el-form-item(label="上传图片4")
        y-upload(v-model="file", type="pic", :disabled="true")
      el-form-item(label="上传图片5")
        y-upload(v-model="file", :disabled="true")
      el-form-item(label="上传")
        y-upload(v-model="file", :multiple="true")
      el-form-item(label="图片显示")
        el-image(v-for="item in fileUrls", :key="item", :src="item")
      el-form-item(label="富文本2")
        el-input(v-model="file")
      el-form-item(label="富文本")
        ym-richtext(v-model="richtext")
      el-form-item(label="富文本1")
        el-input(v-model="richtext")
      el-form-item(label="富文本2")
        el-input(v-model="richtext")
    el-button(@click="outClick") Out
  el-input(v-model="richtext")
  div(v-html="richtext")
  el-dialog(:visible.sync="dialogFormVisible")
    ym-richtext(v-model="richtext")
  el-button(@click="() => { dialogFormVisible = true; }") openDialog
  #myChart(:style="{ width: '300px', height: '300px' }")
  | sss
  ym-echart(
    subtext="测试信息",
    v-model="chartdata",
    x-filed="time",
    y-field="data1",
    type="bar",
    title="测试图表标题",
    :style="{ height: '300px' }"
  )
  el-button(@click="refreshEchart") 刷新Echar
  el-button(@click="refreshPage") 刷新Page
  div
    div 时间 {{ testTime }} {{ $moment(testTime).format("YYYY-MM-DD HH:mm:ss") }}
  div(style="height: 150px; width: 150px")
    div 授权桥测试二维码
    y-wxmpscanbridge(
      :type="bondType",
      :sys-app-id="sysAppId",
      @success="wxMpScanSuccess",
      @scan="wxMpScanScan"
    )
  div
    div Excel导出测试
      el-button(@click="excelExport") 导出Excel
    div Excel导入测试
      ym-excelimport(
        title="导入用户数据",
        :columns="importCloumns",
        @import="handleImportExcel"
      )
      //- ym-excelimport(
      //-   title="导入用户数据",
      //-   :columns="importCloumns",
      //-   :befordealcb="excelImportBeforDeal",
      //-   @import="handleImportExcel"
      //- )
  //- div
  //-     button(@click="regionRequst") 获取region
  div
    div 多文件上传
    input(
      type="file",
      multiple="true",
      ref="mfileUpload",
      @change="handleMfileupadChange"
    )
  div
    div 文件上传
    el-form(ref="form", label-width="80px")
      el-form-item(label="上传")
        input(type="file", ref="fileUpdate")
        //- y-upload(v-model="fileForm.file", type="pic", limit="3")
      el-form-item(label="地理位置")
        el-input(v-model="fileForm.areaId")
      el-button(@click="handleFileUpdate()") 提交
  div
    div 路由测试
    div
      el-button(@click="routeClick({ type: 'a' })") 点击type=a
      el-button(@click="routeClick({ type: 'b' })") 点击type=b
  div
    div 剪贴板
      el-button(@click="copyText()") 点击复制
      input(ref="copyInput")
</template>

<script lang="ts">
import {
  Component,
  mixins,
  Prop,
  Vue,
  VuexModule,
} from "nuxt-property-decorator";
import moment from "moment";
import { config } from "~/config/global.config";

// 引入基本模板
let echarts = require("echarts/lib/echarts");
// // 引入柱状图组件
// require('echarts/lib/chart/bar')
// // 引入提示框和title组件
// require('echarts/lib/component/tooltip')
// require('echarts/lib/component/title')
import { GridComponent } from "echarts/components";
echarts.use([GridComponent]); // 引入提示框，标题，直角坐标系组件，组件后缀都为 Component
import { LineChart } from "echarts/charts";
echarts.use([LineChart]);
// import XLSX from "xlsx";
import excelUtils, {
  ExcelAnalysisSheetMsg,
  ExcelExportByListOneSheetOption,
  ExcelExportByRequestOneSheetOption,
} from "@/utils/excelUtils";
import { ImportEvent } from "~/components/ym/excelimport.vue";
import YmRichtext from "~/components/ym/richtext.vue";
import YmEchart from "~/components/ym/echart.vue";
import YmExcelimport from "~/components/ym/excelimport.vue";
import { request } from "@/utils/request";
import urlUtils from "~/utils/urlUtils";
import { BaseVue } from "~/model/vue";
import compUtils from "~/utils/compUtils";

@Component({
  name: "demo",
  components: {
    YmRichtext,
    YmEchart,
    YmExcelimport,
  },
})
export default class Demo extends mixins(BaseVue) {
  file = "";
  richtext = "<h1>Hello</h1>";
  dialogFormVisible = false;
  testTime = new Date().getTime();

  chartdata: Array<any> = [];

  accessToken = "";

  tables = [
    {
      code: "code01",
      name: "名称01",
      createTime: new Date().getTime(),
      remark: "",
    },
    {
      code: "code02",
      name: "名称02",
      createTime: new Date(),
      remark: "",
    },
  ];

  bondType = "WX_MP_BOND2";

  powerList = ["ROLE_ADMIN"];

  excelImportBeforDeal(obj: ExcelAnalysisSheetMsg) {
    console.log("excelimport ==>>", obj);
    let data = obj.data;
    let rs: string[][] = [];
    for (let i = 0; i < data.length; i++) {
      if (i > 9) {
        break;
      }
      rs.push(data[i]);
    }
    return rs;
  }

  get fileUrls() {
    return this.file
      ? urlUtils.file2array(this.file, { nwt: true, token: this.accessToken })
      : [];
  }

  get sysAppId() {
    return config.sysInfo.wxMpSysAppId;
  }

  yfield1 = [
    {
      type: "pie",
      name: "time",
      field: "data1",
    },
  ];
  yfield = [
    {
      type: "line",
      name: "数据01",
      field: "data1",
    },
    {
      type: "line",
      name: "数据02",
      field: "data2",
    },
  ];

  async toPage(url: string) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${url}`);
  }

  async handleSwitchPageRole(pageRole: string) {
    await this.$store.dispatch("auth/putPageRole", pageRole);
  }

  async handleMfileupadChange() {
    let files = this.$refs.mfileUpload;
    console.log(files);
    // debugger;
    // console.log(files);
  }

  async copy2text(text: string) {
    let ok = false;
    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
      try {
        await navigator.clipboard.writeText(text);
        ok = true;
      } catch (err) {
        console.error("Failed to copy: ", err);
      }
    }
    if (!ok) {
      if (document.execCommand) {
        let copyInput = <any>this.$refs.copyInput;
        copyInput.value = text;
        copyInput.select();
        if (document.execCommand("copy")) {
          document.execCommand("copy");
          console.log("复制成功");
        }
      }
    }
    return ok;
  }

  async copyText() {
    await this.copy2text("测试复制");
    window.alert("复制OK");
  }

  routeClick(param: any) {
    this.$router.replace(urlUtils.putParam(this.$route.fullPath, param));
  }

  outClick() {
    console.log(this.file);
    console.log(this.richtext);
  }

  created() {
    console.log("created");
    document.addEventListener('contextmenu', function (event) {
      event.preventDefault(); // 阻止右键菜单的默认行为
      // 在此处编写右键点击事件的处理逻辑
      console.log('鼠标右键点击了！');
    });
    // this.$auth.hasPower("ROLE_ADMIN");
  }
  async mounted() {
    console.log("mounted");
    // this.bondType = "WX_MP_BOND";
    this.refreshEchart();
    this.drawLine();
    this.accessToken = await this.$store.dispatch("auth/getToken");
  }

  refreshPage() {
    this.$store.commit("tags/reloadPage");
  }

  refreshEchart() {
    let data = [];
    let h = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    for (let i = 0; i < h.length; i++) {
      data.push({
        time: h[i],
        data1: Math.random() * 300,
        data2: Math.random() * 300,
      });
    }
    this.chartdata = data;
  }

  drawLine() {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(document.getElementById("myChart"));
    // 绘制图表
    myChart.setOption({
      xAxis: {
        type: "category",
        data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: "line",
        },
      ],
    });
  }

  async wxMpScanScan() {
    console.log("---------------scan");
  }
  async wxMpScanSuccess() {
    console.log("====================success");
  }

  async excelExport() {
    console.log("导出Excel");
    // https://www.jianshu.com/p/31534691ed53
    // https://github.com/SheetJS/sheetjs#supported-output-formats

    let list = [];
    for (let i = 0; i < 1000; i++) {
      list.push({
        name: `张三${i}`,
        score: i,
        no: `2020${i}`,
        ok: i % 2 == 0,
      });
    }

    let op = <ExcelExportByListOneSheetOption>{};
    op.data = list;
    op.filename = "测试.xlsx";
    op.columns = [
      { code: "name", name: "名称" },
      { code: "no", name: "学号" },
      { code: "score", name: "成绩" },
      {
        code: "ok",
        type: "boolean",
        name: "过关",
        truename: "是",
        falsename: "否",
      },
    ];
    excelUtils.exportData(op);
  }

  importCloumns: any = [
    { code: "name", name: "半年销量", require: true },
    { code: "code", name: "商家数量", require: false },
    { code: "content", name: "content", require: false },
  ];
  handleImportExcel(e: ImportEvent) {
    // debugger;
    console.log("===import>>", e.data);
    e.close();
  }
  ss() {
    let op = <ExcelExportByRequestOneSheetOption>{};
    op.filename = "测试.xlsx";
    // 字段信息
    op.columns = [
      { code: "name", name: "名称" },
      { code: "no", name: "学号" },
      { code: "score", name: "成绩" },
      {
        code: "ok",
        type: "boolean",
        name: "过关",
        truename: "是",
        falsename: "否",
      },
    ];
    // 进度显示
    op.processCallback = (ctx) => {
      console.log(ctx.count, "/", ctx.all);
    };
    op.requestCount = async () => {
      return 1000;
    };
    op.requestData = async (rows, page) => {
      let list = [];
      for (let i = 0; i < 1000; i++) {
        list.push({
          name: `张三${i}`,
          score: i,
          no: `2020${i}`,
          ok: i % 2 == 0,
        });
      }
      return list;
    };
  }

  fileForm = {
    file: null,
    areaId: "1234",
  };
  async handleFileUpdate() {
    console.log("ww");
    let files = (<any>this.$refs.fileUpdate).files;
    let file = files[0];
    this.fileForm.file = file;
    let data = this.fileForm;

    let result = await request.post(
      `${config.baseUrl.apiUrl}/mg/open/map/import/sync`,
      data,
      { file: true }
    );
    console.log(data);
  }
}
</script>

<style lang="sass" scoped>
</style>
