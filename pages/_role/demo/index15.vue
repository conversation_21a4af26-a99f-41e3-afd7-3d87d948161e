<template>
  <div class="cube-container">
    <div class="cube">
      <div class="face front">Front</div>
      <div class="face back">Back</div>
      <div class="face right">Right</div>
      <div class="face left">Left</div>
      <div class="face top">Top</div>
      <div class="face bottom">Bottom</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CardCube"
};
</script>

<style scoped>
.cube-container {
  width: 200px;
  height: 200px;
  perspective: 1000px;
  margin: 200px auto;
}
.cube {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  animation: spin 10s infinite linear;
}
.face {
  position: absolute;
  width: 200px;
  height: 200px;
  color: white;
  font-size: 24px;
  line-height: 200px;
  text-align: center;
  background: #e74c3c;
  border: 2px solid #fff;
}
.front  { transform: translateZ(100px); }
.back   { transform: rotateY(180deg) translateZ(100px); }
.right  { transform: rotateY(90deg) translateZ(100px); }
.left   { transform: rotateY(-90deg) translateZ(100px); }
.top    { transform: rotateX(90deg) translateZ(100px); }
.bottom { transform: rotateX(-90deg) translateZ(100px); }

@keyframes spin {
  from { transform: rotateX(0deg) rotateY(0deg); }
  to { transform: rotateX(360deg) rotateY(360deg); }
}
</style>
