<template>
  <div class="card-container">
    <div
      v-for="(group, groupIndex) in cardGroups"
      :key="groupIndex"
      class="card-group"
      @mouseenter="hoverIndex = groupIndex"
      @mouseleave="hoverIndex = null"
    >
      <div
        v-for="(card, i) in group"
        :key="i"
        class="card"
        :style="getCardStyle(group.length, i, groupIndex)"
      >
        卡片 {{ i + 1 }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      hoverIndex: null,
      cardGroups: [
        [1],
        [1, 2],
        [1, 2, 3],
        [1, 2, 3, 4],
        [1, 2, 3, 4, 5],
      ],
      baseRotate: 5,
      expandOffset: 18,
    };
  },
  methods: {
    getCardStyle(groupLength, cardIndex, groupIndex) {
      const isHovered = this.hoverIndex === groupIndex;
      const isTop = cardIndex === groupLength - 1;
      let rotate = 0;
      let translateY = 0;
      let translateX = 0;

      if (!isTop) {
        const direction = (groupLength - cardIndex - 1) % 2 === 0 ? -1 : 1;
        rotate = direction * this.baseRotate;
        if (isHovered) {
          rotate *= 4;
          translateX = direction * this.expandOffset;
          translateY = -6;
        }
      } else if (isHovered) {
        translateY = -10; // 顶部卡片向上跳
      }
      return {
        transform: `rotate(${rotate}deg) translate(${translateX}px, ${translateY}px)`,
        zIndex: cardIndex,
        top: `${cardIndex * 5}px`,
        left: `${cardIndex * 5}px`,
      };
    },
  },
};
</script>

<style scoped>
.card-container {
  display: flex;
  justify-content: space-around;
  padding: 40px;
}
.card-group {
  position: relative;
  width: 120px;
  height: 160px;
}
.card {
  position: absolute;
  width: 100px;
  height: 140px;
  background: #ffffff;
  border: 1px solid #ccc;
  border-radius: 8px;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  user-select: none;
}
</style>
