<template lang="pug">
div
  h1 Demo
  div {{ $route.path }}
  div {{ demoval }}
  div {{ val01.txt }}
  div 页面角色：{{ $auth.v_getPageRole() }}
  div
    el-button(@click="putUrl({ a: '2-' + demoval })") 设置Url
</template>

<script lang="ts">
import {
  Component,
  mixins,
  Prop,
  Vue,
  VuexModule,
} from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import routerUtils from "~/utils/routerUtils";
import compUtils from "~/utils/compUtils";

@Component({
  name: "role-demo-demo",
  components: {},
})
export default class DemoDemoIndex extends mixins(BaseVue) {
  demoval = "";
  val01 = {
    txt: "",
  };

  activated() {
    this.demoval = compUtils.getQueryValue(this, "demo", "def");
  }

  putUrl(query: any) {
    routerUtils.putQueryValue(this, query);
    this.val01 = {
      txt: "信息" + this.demoval,
    };
  }
}
</script>

<style lang="sass" scoped>
</style>
