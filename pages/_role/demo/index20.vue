<template>
  <div class="tag-wrapper">
    <div class="tag-circle" :style="circleStyle">
      <div
        v-for="(tag, i) in tags"
        :key="i"
        class="tag"
        :style="getTagStyle(i, tags.length)"
        :data-color="tag.color"
      >
        <span :style="{ color: tag.color }">{{ tag.text }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "RotatingTags",
  data() {
    const tagTexts = [
      "korean afro-funk", "hyper-acid house", "techno big band", "bedroom pop ska",
      "sitar delta blues", "accordion jungle", "house symphonic metal", "gospelwave",
      "russian grunge", "lo-fi ambient dub", "j-pop chillisynth", "ambient trance",
      "disco classical", "drum and bass swing", "french glitch hop", "celtic cloud rap",
      "salsa polka", "havana dream pop", "bossa delta", "afropop glitch", "jungle ambient",
      "tech house soul", "grime disco dub", "french soul hop", "funkwave", "surf cumbia",
      "korean afro-funk", "hyper-acid house", "techno big band", "bedroom pop ska",
      "sitar delta blues", "accordion jungle", "house symphonic metal", "gospelwave",
      "russian grunge", "lo-fi ambient dub", "j-pop chillisynth", "ambient trance",
      "disco classical", "drum and bass swing", "french glitch hop", "celtic cloud rap",
      "salsa polka", "havana dream pop", "bossa delta", "afropop glitch", "jungle ambient",
      "tech house soul", "grime disco dub", "french soul hop", "funkwave", "surf cumbia",
      "korean afro-funk", "hyper-acid house", "techno big band", "bedroom pop ska",
      "sitar delta blues", "accordion jungle", "house symphonic metal", "gospelwave",
      "russian grunge", "lo-fi ambient dub", "j-pop chillisynth", "ambient trance",
      "disco classical", "drum and bass swing", "french glitch hop", "celtic cloud rap",
      "salsa polka", "havana dream pop", "bossa delta", "afropop glitch", "jungle ambient",
      "tech house soul", "grime disco dub", "french soul hop", "funkwave", "surf cumbia"
    ];
    return {
      radius: 500,
      tags: tagTexts.map(text => ({
        text,
        color: this.randomColor()
      }))
    };
  },
  computed: {
    circleStyle() {
      return {
        width: this.radius * 2 + "px",
        height: this.radius * 2 + "px"
      };
    }
  },
  methods: {
    getTagStyle(index, total) {
      const angle = (360 / total) * index;
      return {
        transform: `rotate(${angle}deg) translate(${this.radius}px) rotate(-${angle}deg)`,
        '--tag-color': this.tags[index].color
      };
    },
    randomColor() {
      const colors = ['#ff8a00', '#ff2d95', '#00c3ff', '#eaff00', '#f0f', '#0ff', '#8bc34a'];
      return colors[Math.floor(Math.random() * colors.length)];
    }
  }
};
</script>

<style scoped>
.tag-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: #111;
  overflow: hidden;
}

.tag-circle {
  position: relative;
  animation: rotateCircle 40s linear infinite;
}

.tag {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: 0 0;
  white-space: nowrap;
  font-size: 14px;
  font-family: sans-serif;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.tag:hover {
  background-color: var(--tag-color);
}
.tag:hover span {
  color: #fff !important;
}

@keyframes rotateCircle {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
