<template lang="pug">
.demo-index-pdf
  y-upload(v-model="file", @change="handleUploadChange")
  .pdf-content
    ym-pdfimage(:url="url", :scale="1.5", :showText="false")
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";

import YmPdfimage from "~/components/ym/pdfimage.vue";
import urlUtils from "~/utils/urlUtils";

@Component({
  name: "demo-index-pdf",
  components: {
    YmPdfimage,
  },
})
export default class DemoIndexPdf extends mixins(BaseVue) {
  file = "012bf6b9086895226469e562f09786f8";
  url: string | null = "";

  async mounted() {
    this.handleUploadChange(this.file);
  }

  handleUploadChange(file: string) {
    if (!file) {
      this.url = "";
      return;
    }
    this.url = urlUtils.file2url(file);
    // console.log("====");
  }
}
</script>
<style lang="scss" scoped>
.demo-index-pdf {
  .pdf-content {
    width: 500px;
  }
}
</style>
