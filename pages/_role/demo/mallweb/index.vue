<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades 商品列表
  .container
    //- 表头
    .handle-box
      //- el-button.mr10(
      //-   type="primary",
      //-   icon="el-icon-plus",
      //-   @click="handleAddClick"
      //- ) {{ $t("common.base.add") }}
      //- el-cascader.mr10(
      //-   v-model="query.categoryId",
      //-   :options="oneLoadParam.categoryTree",
      //-   :props="{ checkStrictly: true, label: 'name', value: 'id' }",
      //-   clearable,
      //-   placeholder="分类过滤",
      //-   @change="handleSearchClick"
      //- )
      //- el-select.mr10(v-model="query.state", @change="handleSearchClick")
      //-   el-option(key="ALL", label="全部", value="ALL")
      //-   el-option(
      //-     v-for="option in $dic.getOptions('mall.product.state')",
      //-     :key="option.value",
      //-     :label="option.label",
      //-     :value="option.value"
      //-   )
      //- el-select.mr10(
      //-   v-model="query.brandId",
      //-   filterable,
      //-   remote,
      //-   clearable,
      //-   reserve-keyword,
      //-   :remote-method="handleBrandAutocompleteSearch",
      //-   placeholder="请输入品牌关键词",
      //-   :loading="bizData.brandsLoading",
      //-   @change="handleSearchClick"
      //- )
      //-   el-option(
      //-     v-for="item in bizData.brands",
      //-     :key="item.id",
      //-     :label="item.name",
      //-     :value="item.id"
      //-   )
      el-button(@click="toPage('/demo/mallweb/cart')") 去购物车
      el-button(@click="toPage('/demo/mallweb/orders')") 去订单
      el-input.handle-input(
        v-model="query.keyword",
        placeholder="关键词",
        @keyup.enter.native="handleSearchClick"
      )
      el-button(
        type="primary",
        icon="el-icon-search",
        @click="handleSearchClick"
      ) 搜索
      //- a.downswagger(:href="swaggerUrl", target="_blank") 下载Swagger文档
    //- 表单内容
    el-table.table(
      :data="bizData.goods",
      border,
      ref="multipleTable",
      header-cell-class-name="table-header",
      v-loading="loading"
    )
      el-table-column(prop="pic", label="图片")
        template(#default="scope")
          el-image(
            :src="getImageUrlOne(scope.row.pic)",
            :preview-src-list="getImageUrls(scope.row.pic)",
            :lazy="true",
            style="width: 100px; height: 100px"
          )
      el-table-column(prop="name", label="名称")
      el-table-column(prop="categoryId", label="分类")
        //- template(#default="scope") {{ getCategoryShowNameById(scope.row.categoryId) }}
        template(#default="scope") {{ scope.row.categoryId }}
      el-table-column(prop="brandName", label="品牌")
      el-table-column(prop="unitPrice", label="价格")
        template(#default="scope")
          p 下单价：{{ scope.row.unitPriceOrdersMin }}
          p 售卖价：{{ scope.row.unitPriceMin }}
          p 展示价：{{ scope.row.unitPriceShowMin }}
      el-table-column(prop="spu", label="SPU")
      el-table-column(prop="createTime", type="date", label="上架时间")
        template(#default="scope")
          div {{ $moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
      el-table-column(prop="remark", label="备注")

      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-button(type="text", @click="handleDetailClick(scope.row)") {{ $t("common.base.detail") }}
    .y-footer
      //- 分页信息
      .pagination
        el-pagination(
          background,
          :page-sizes="[5, 10, 20, 50, 100]",
          layout="total, sizes, prev, pager, next",
          :current-page="query.page",
          :page-size="query.rows",
          :total="query.total",
          @current-change="handlePageChange",
          @size-change="handleSizeChange"
        )
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import urlUtils from "~/utils/urlUtils";

import shopperGoodsPriceApi, {
  GoodsPriceInfo,
  GoodsPriceParamVo,
} from "~/api/mall/shopper/goods/price/batch/queryApi";
import shopperGoodsSearchApi, {
  GoodsSearchParamVo,
  MallGoodsSearchListVo,
} from "~/api/mall/shopper/goods/searchApi";
import compUtils from "~/utils/compUtils";

@Component({
  name: "demo-mall-product",
})
export default class PageDemoMallWebIndex extends mixins(BaseVue) {
  loading = true;
  query = {
    keyword: "",
    total: 0,
    page: 1,
    rows: 20,
  };
  formData = {};
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    goods: <MallGoodsSearchListVo[]>[],
  };

  async mounted() {
    await this.init();
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  async init() {
    this.loading = true;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContentCount();
  }
  async loadContentCount() {
    await this.loadContent();
  }
  async loadContent() {
    let param: GoodsSearchParamVo = { ...this.query };

    this.loading = true;
    try {
      param.page = param.page! - 1;
      let result = await shopperGoodsSearchApi.search(param);
      this.query.total = result.total!;
      this.bizData.goods = result.goods!;
    } finally {
      this.loading = false;
    }
    await this.refreshPrice();
  }

  async refreshPrice() {
    this.loading = true;
    try {
      let param: GoodsPriceParamVo = {};
      if (this.bizData.goods.length <= 0) {
        return;
      }
      param.spaceIds = this.bizData.goods.map((x) => x.spaceIds!.split(",")[0]);
      let prices = await shopperGoodsPriceApi.getDetailBySpaceId(param);
      let id2price = new Map<string, GoodsPriceInfo>();
      prices.map((x) => id2price.set(x.spaceId!, x));

      for (const good of this.bizData.goods) {
        let spaceId = good.spaceIds!.split(",")[0];
        let price = id2price.get(spaceId);
        if (!price) {
          good.unitPriceMin = undefined;
          good.unitPriceShowMin = undefined;
          good.unitPriceOrdersMin = undefined;
          continue;
        }
        good.unitPriceMin = price.unitPrice;
        good.unitPriceShowMin = price.unitPriceShow;
        good.unitPriceOrdersMin = price.unitPriceOrders;
      }
    } finally {
      this.loading = false;
    }
  }

  async handlePageChange(page: number) {
    this.query.page = page;
    this.loadContent();
  }
  async handleSizeChange(size: number) {
    let start = (this.query.page - 1) * this.query.rows;
    let page = Math.floor(start / size) + 1;
    this.query.rows = size;
    this.query.page = page;
    await this.loadContent();
  }

  async handleSearchClick() {
    this.query.page = 1;
    await this.loadContentCount();
  }

  async handleDetailClick(record: MallGoodsSearchListVo) {
    if (!record.spaceIds) {
      return;
    }
    let spaceIds = record.spaceIds?.split(",");
    this.toPage(`/demo/mallweb/detail?id=${spaceIds[0]}`);
  }

  async toPage(url: string) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${url}`);
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;

  & > * {
    margin-right: 10px;
  }
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}
.mr10 {
  margin-right: 10px;
}
</style>

