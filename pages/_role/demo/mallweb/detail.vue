<template lang="pug">
.page-demo-mallweb-detail
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades 商品详情
  .container(v-loading="loading")
    //- 表头
    .handle-box
      h2.mgt10 基础信息：
      el-row
        el-image(
          :src="getImageUrlOne(bizData.goods.pic)",
          :preview-src-list="getImageUrls(bizData.goods.pic)",
          :lazy="true",
          style="width: 100px; height: 100px"
        )
      el-row {{ bizData.goods.spaces ? bizData.goods.spaces[0].name : "" }}
      el-row
        template(v-if="bizData.price")
          span.mgr10 下单价：{{ bizData.price.unitPriceOrders }}
          span.mgr10 售卖价：{{ bizData.price.unitPrice }}
          span.mgr10 展示价：{{ bizData.price.unitPriceShow }}
        template(v-else)
          span 该商品已下架
      el-row
        | 数量：
        el-input-number.mgl10(
          v-model="query.num",
          :min="1",
          :max="100",
          :precision="0",
          controls-position="right",
          @change="handleNumChange"
        )
        template(v-if="bizData.stock")
          el-tag.mgl10(
            :disable-transitions="false",
            :type="'OK' == bizData.stock.stockType || 'INCOMING' == bizData.stock.stockType ? 'success' : 'danger'"
          ) {{ "OK" == bizData.stock.stockType ? "有货" : "INCOMING" == bizData.stock.stockType ? "可预订" : "库存不足" }}
          span.mgl10.stock-num(
            v-if="('OK' == bizData.stock.stockType || 'INCOMING' == bizData.stock.stockType) && bizData.stock.quantity != null"
          ) 剩余{{ "INCOMING" == bizData.stock.stockType ? "可预订" : "" }}库存：{{ bizData.stock.quantity }}

      el-row.mgt10
        el-button(@click="handleAddCartClick") 加入购物车
        el-button(@click="toPage('/demo/mallweb/cart')") 去购物车
        el-button(@click="() => goBack()") 返回

      h2.mgt10 相似商品：
      div
        el-row(
          v-for="(sl, idx) in oneLoadParam.similars",
          :key="idx",
          style="border: solid 1px #ccc"
        )
          | {{ sl.name }}：
          el-radio-group(
            v-model="formData.similarSelected[idx]",
            @input="handleSimilerInput(idx)"
          )
            template(v-for="(value, vidx) in sl.values")
              el-tooltip.item(
                v-if="handleSimilerRedioDisabled(idx, vidx)",
                effect="dark",
                content="无此商品",
                placement="right"
              )
                el-radio-button(
                  :key="vidx",
                  :label="JSON.stringify(value.spaceIds)",
                  :disabled="true"
                ) {{ value.value }}
              el-radio-button(
                v-else,
                :key="vidx",
                :label="JSON.stringify(value.spaceIds)"
              ) {{ value.value }} {{ getSimilerRedioPriceDiffShow(idx, vidx) ? `(${getSimilerRedioPriceDiffShow(idx, vidx)})` : "" }}

      h2.mgt10 详情：
      div(v-html="bizData.goods.contentPc", style="border: solid 1px #ccc")
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import urlUtils from "~/utils/urlUtils";

import shopperGoodsSpaceApi, {
  MallGoodsDetailVo,
  MallGoodsSimilarValueVo,
  MallGoodsSimilarVo,
} from "~/api/mall/shopper/goods/spaceApi";
import shopperStockApi, {
  GoodsStockInfo,
  GoodsStockParamVo,
} from "~/api/mall/shopper/goods/stock/batch/queryApi";

import cartApi, { MallCartAddVo } from "~/api/mall/shopper/cartApi";
import routerUtils from "~/utils/routerUtils";
import { DelayBuffer } from "~/utils/toolsUtils";

import shopperGoodsPriceApi, {
  GoodsPriceInfo,
  GoodsPriceParamVo,
} from "~/api/mall/shopper/goods/price/batch/queryApi";
import compUtils from "~/utils/compUtils";

type SimilarMallGoodsSimilarValueVo = MallGoodsSimilarValueVo & {
  price?: GoodsPriceInfo;
};

type SimilarMallGoodsSimilarVo = Omit<MallGoodsSimilarVo, "values"> & {
  values?: Array<SimilarMallGoodsSimilarValueVo>;
};

@Component({
  name: "demo-mall-detail",
})
export default class PageDemoMallWebDetail extends mixins(BaseVue) {
  loading = true;
  query = {
    id: "",
    num: 1,
  };
  formData = {
    similarSelected: <string[]>[],
  };
  oneLoadParam = {
    inited: false,
    // ...param
    similars: <Array<SimilarMallGoodsSimilarVo>>[],
    similarsId2price: <Map<string, GoodsPriceInfo> | null>null,
  };
  bizData = {
    goods: <MallGoodsDetailVo>{ name: "", pic: "" },
    stock: <GoodsStockInfo | null>null,
    price: <GoodsPriceInfo | null>null,
  };

  async mounted() {
    await this.init();
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  async init() {
    this.loading = true;
    try {
      await this.initDetailBefor();
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading = false;
    }
  }

  async initDetailBefor() {
    if (!this.query.id) {
      this.query.id = routerUtils.getQueryValue(this, "id", "");
    }
    this.query.num = +routerUtils.getQueryValue(this, "num", "1");
    if (!this.query.id) {
      this.$alert("参数filterType为空，请联系管理员");
      this.$router.back();
      return;
    }
    let id = this.query.id!;
    this.bizData.goods = await shopperGoodsSpaceApi.getDetailBySpaceId(id);

    await Promise.all([this.refreshStock(), this.refreshPrice()]);
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...

    await this.refreshSimilar();
  }

  async initDetail() {}

  async refreshSimilar() {
    let id = this.query.id!;
    let similars = await shopperGoodsSpaceApi.getSimilarBySpaceId(id);
    // 排序等预处理

    // 整合参数
    let similarSelected = <string[]>[];
    for (let similar of similars) {
      let values = similar.values?.filter((x) => x.spaceIds!.indexOf(id) >= 0);
      if (values && values.length > 0) {
        similarSelected.push(JSON.stringify(values[0].spaceIds!));
      } else {
        similarSelected.push("");
      }
    }

    if (similars.length > 0) {
      let spids = new Set<string>();
      similars?.map((x) =>
        x.values?.map((p) => p.spaceIds?.map((id) => id && spids.add(id)))
      );

      let param: GoodsPriceParamVo = {};
      param.spaceIds = [...spids];
      let prices = await shopperGoodsPriceApi.getDetailBySpaceId(param);
      let spid2price = new Map();
      prices.forEach((price) => spid2price.set(price.spaceId, price));
      let similarsId2price = new Map();
      if (prices.length > 0) {
        prices.map((x) => similarsId2price.set(x.spaceId, x));
      }
      this.oneLoadParam.similarsId2price = similarsId2price;
    } else {
      this.oneLoadParam.similarsId2price = new Map();
    }
    this.oneLoadParam.similars = similars;
    this.formData.similarSelected = similarSelected;
  }

  async refreshPrice() {
    this.loading = true;
    try {
      let param: GoodsPriceParamVo = {};
      param.spaceIds = [this.bizData.goods.spaces![0].productSpaceId!];
      let prices = await shopperGoodsPriceApi.getDetailBySpaceId(param);
      if (prices.length <= 0) {
        return;
      }
      this.bizData.price = prices[0];
    } finally {
      this.loading = false;
    }
  }

  async refreshStock() {
    let num = this.query.num;
    let spaceId = this.query.id;
    let param: GoodsStockParamVo = {};
    param.spaceNumInfos = [{ spaceId, num }];
    routerUtils.putQueryValue(this, { num: num });
    let results: GoodsStockInfo[] = await shopperStockApi.getDetailBySpaceId(
      param
    );
    if (results.length <= 0) {
      throw Error("出现异常，请联系管理员");
    }
    this.bizData.stock = results[0];
  }

  numberChange = new DelayBuffer<string>({
    cb: () => this.refreshStock(),
    delay: true,
    timeout: 500,
  });

  async handleAddCartClick() {
    let param: MallCartAddVo = {};
    param.productSpaceId = this.query.id;
    param.num = this.query.num;
    let params = [param];
    await cartApi.batchPut(params);
    this.$notify.success("添加成功");
  }

  async handleNumChange() {
    this.bizData.stock = null;
    this.numberChange.put("");
  }

  handleSimilerRedioDisabled(idx: number, vidx: number): boolean {
    let arr = this.getSimilerNowId(idx, vidx);
    if (!arr) {
      return false;
    }
    return arr.length <= 0;
  }

  getSimilerRedioPriceDiffShow(idx: number, vidx: number): string {
    let arr = this.getSimilerNowId(idx, vidx);
    if (!arr) {
      return "";
    }
    if (arr.length <= 0) {
      return "";
    }
    let id = arr[0];
    let price = this.oneLoadParam.similarsId2price?.get(id) ?? null;
    if (!price) {
      return "";
    }
    let diffPrice =
      price.unitPriceOrders! - this.bizData.price!.unitPriceOrders!;
    if (diffPrice == 0) {
      return "";
    } else {
      return "" + diffPrice;
    }
  }

  getSimilerNowId(idx: number, vidx: number): string[] | null {
    let similarSelecteds = this.formData.similarSelected;
    // 除当前属性外的其它选项的交集
    let arr: string[] | null = null;
    for (let i in similarSelecteds) {
      if (idx == +i) {
        continue;
      }
      arr = this.arrMargin(arr, similarSelecteds[i]);
    }
    let values = this.oneLoadParam.similars[idx]?.values ?? null;
    if (!values) {
      return null;
    }
    let spaceIdsJson = JSON.stringify(values[vidx].spaceIds);
    return this.arrMargin(arr, spaceIdsJson);
  }

  arrMargin(arr: string[] | null, val: string | null): string[] | null {
    if (!val) {
      return arr;
    }
    let nvs: string[] = JSON.parse(val);
    if (arr == null) {
      arr = nvs;
    } else {
      arr = arr.filter((value) => nvs.includes(value));
    }
    return arr;
  }

  async handleSimilerInput(idx: number) {
    let similarSelecteds = this.formData.similarSelected;
    // 除当前属性外的其它选项的交集
    let arr: string[] | null = null;

    for (let i in similarSelecteds) {
      if (idx == +i) {
        continue;
      }
      arr = this.arrMargin(arr, similarSelecteds[i]);
    }
    // 单独对当前选项进行求交集
    arr = this.arrMargin(arr, similarSelecteds[idx]);
    if (!arr) {
      return;
    }
    if (arr!.length <= 0) {
      for (let i in similarSelecteds) {
        if (idx == +i) {
          continue;
        }
        similarSelecteds[i] = "";
      }
    } else if (arr?.length == 1) {
      let id = arr[0];
      routerUtils.putQueryValue(this, { id: id });
      this.query.id = id;
      this.init();
    }
  }

  async toPage(url: string) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${url}`);
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.page-demo-mallweb-detail {
  .stock-num {
    color: #f00;
  }
}
.handle-box {
  margin-bottom: 20px;
}

.handle-select {
  width: 120px;
}

.handle-input {
  width: 300px;
  display: inline-block;
}
.mr10 {
  margin-right: 10px;
}
</style>

