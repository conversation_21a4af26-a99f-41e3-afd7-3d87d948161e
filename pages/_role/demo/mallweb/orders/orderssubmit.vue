<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades 下单页面
  .container
    //- 表头
    .handle-box
    el-form.orders-form(
      ref="ordersForm",
      :model="formData",
      label-width="0",
      v-loading="loading > 0"
    )
      el-form-item(
        prop="addressId",
        :rules="[$rule.required(null, '收货人不能为空')]",
        required
      )
        //- 表单内容
        h2.mgb10 收货地址：
        el-table.table.address-table(
          :data="bizData.addresses",
          highlight-current-row,
          @current-change="handleAddressTableCurrentChange",
          ref="addressTable"
        )
          el-table-column(
            prop="name",
            label="名称",
            width="150",
            class-name="call-pointer"
          )
          el-table-column(
            prop="phone",
            label="手机号",
            width="150",
            class-name="call-pointer"
          )
          el-table-column(
            prop="email",
            label="Email",
            width="150",
            class-name="call-pointer"
          )
          el-table-column(
            prop="regionCode",
            label="地址",
            class-name="call-pointer"
          )
            template(#default="scope")
              div {{ getRegionByCode(scope.row.regionCode).map((x) => x.name).join(",") }},{{ scope.row.address }}
          el-table-column(prop="def", label="默认地址", width="90")
            template(#default="scope")
              div
                el-tag(
                  v-if="scope.row.def",
                  :disable-transitions="false",
                  type="success"
                ) 默认
                el-button(
                  v-if="!scope.row.def",
                  type="text",
                  @click="handleAddressPutDef(scope.row, scope.$index)"
                ) 设为默认
          el-table-column(prop="operate", label="操作", width="150")
            template(#default="scope")
              el-button.mgr10(
                type="text",
                icon="el-icon-edit",
                @click="handleAddressModifyClick(scope.row, scope.$index)"
              ) {{ $t("common.base.modify") }}
              el-popconfirm.mgr10(
                title="确认要删除数据吗？",
                @confirm="handleAddressDeleteClick(scope.row, scope.$index)"
              )
                el-button.mgr10(
                  slot="reference",
                  type="text",
                  icon="el-icon-delete"
                ) {{ $t("common.base.delete") }}
        el-button(@click="handleAddressAddClick") 添加

      h2 商品信息：
      el-table.table(
        :data="bizData.carts",
        border,
        selse,
        ref="cartsTable",
        header-cell-class-name="table-header",
        v-loading="loading > 0"
      )
        el-table-column(prop="pic", label="图片")
          template(#default="scope")
            el-image(
              :src="getImageUrlOne(scope.row.productPic)",
              :preview-src-list="getImageUrls(scope.row.productPic)",
              :lazy="true",
              style="width: 100px; height: 100px"
            )
        el-table-column(prop="productName", label="名称")
        el-table-column(prop="num", label="数量")
          template(#default="scope")
            div
              span {{ scope.row.num }}
              span(v-if="scope.row.product && scope.row.stock")
                el-tag.mgl10(
                  :disable-transitions="false",
                  :type="'OK' == scope.row.stock.stockType || (formData.allowNegative && 'INCOMING' == scope.row.stock.stockType) ? 'success' : 'danger'"
                ) {{ "OK" == scope.row.stock.stockType ? "有货" : formData.allowNegative && "INCOMING" == scope.row.stock.stockType ? "可预订" : "库存不足" }}
        el-table-column(prop="unitPrice", label="价格")
          template(#default="scope")
            template(v-if="scope.row.price")
              p 下单价：{{ scope.row.price.unitPriceOrders }}
              p 售卖价：{{ scope.row.price.unitPrice }}
              p 展示价：{{ scope.row.price.unitPriceShow }}
            template(v-else)
              span 数据未找到
        el-table-column(prop="product", label="状态")
          template(#default="scope")
            el-tag(:type="!!scope.row.product ? 'success' : 'danger'") {{ !!scope.row.product ? "销售中" : "下架" }}

      el-form-item(prop="allowNegative")
        el-checkbox(v-model="formData.allowNegative") 允许预定

      h2.mgt10 备注：
      el-form-item(prop="remark")
        el-input(v-model="formData.remark", type="textarea", :rows="2")

      el-form-item
        h3.pay-price-win 商品总额：
          span ￥{{ getShowPrice(formData.priceProduct) }}
        h3.pay-price-win 运费：
          span ￥{{ getShowPrice(formData.priceFreight) }}
        h3.pay-price-win 支付金额：
          span.num ￥{{ getShowPrice(formData.priceOrders) }}

      el-form-item.mgt10
        el-button.mgr10(type="primary", @click="handleSubmitOrdersClick") 下单
        el-button.mgr10(@click="() => goBack()") 返回

  //- 收货地址管理
  el-dialog(
    title="收货地址",
    :visible.sync="bizData.mallAddreeForm.dialogVisible",
    width="50%",
    :before-close="() => (bizData.mallAddreeForm.dialogVisible = false)"
  )
    el-form(
      ref="addressForm",
      :model="bizData.mallAddreeForm.formData",
      label-width="90px",
      v-loading="bizData.mallAddreeForm.formLoading > 0"
    )
      el-form-item(
        label="收货人",
        prop="name",
        :rules="[$rule.required(null, '收货人不能为空')]",
        required
      )
        el-input(v-model="bizData.mallAddreeForm.formData.name")
      el-form-item(
        label="手机号",
        prop="phone",
        :rules="[$rule.required(null, '手机号不能为空'), $rule.phone(null, '手机号格式不正确')]",
        required
      )
        el-input(v-model="bizData.mallAddreeForm.formData.phone")
      el-form-item(
        label="邮箱",
        prop="email",
        :rules="[$rule.email(null, '邮件格式不正确')]"
      )
        el-input(v-model="bizData.mallAddreeForm.formData.email")
      el-form-item(label="默认地址", prop="def")
        el-switch(v-model="bizData.mallAddreeForm.formData.def")
      el-form-item(
        label="地址",
        prop="regionCode",
        :rules="[$rule.required(null, '地址不能为空')]",
        required
      )
        el-cascader(
          v-model="bizData.mallAddreeForm.formData.regionCode",
          :options="oneLoadParam.regionTree",
          :props="{ checkStrictly: false, label: 'name', value: 'code' }",
          clearable,
          style="width: 100%"
        )
      el-form-item(
        label="详细地址",
        prop="address",
        :rules="[$rule.required(null, '详细地址不能为空')]",
        required
      )
        el-input(v-model="bizData.mallAddreeForm.formData.address")
      el-form-item(label="备注", prop="remark")
        el-input(
          v-model="bizData.mallAddreeForm.formData.remark",
          type="textarea",
          :rows="2"
        )

      //- span.dialog-footer(slot="footer")
      el-form-item
        el-button(type="primary", @click="handleMallAddressSubmitClick") 确 定
        el-button(@click="bizData.mallAddreeForm.dialogVisible = false") 取 消
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import urlUtils from "~/utils/urlUtils";
import arrayUtils from "~/utils/arrayUtils";

import shopperCartApi, {
  MallCart,
  MallShopperCartGetListQueryParam,
} from "~/api/mall/shopper/cartApi";
import shopperGoodsSpaceApi, {
  MallGoodsListVo,
} from "~/api/mall/shopper/goods/spaceApi";

import shopperGoodsPriceApi, {
  GoodsPriceInfo,
  GoodsPriceParamVo,
} from "~/api/mall/shopper/goods/price/batch/queryApi";
import stockApi, {
  GoodsStockInfo,
  GoodsStockParamVo,
} from "~/api/mall/shopper/goods/stock/batch/queryApi";
import shopperAddressApi, {
  MallAddress,
  MallAddressAddVo,
} from "~/api/mall/shopper/addressApi";
import regionApi, { MapRegion } from "~/api/basic/map/regionApi";
import ordersApi, {
  OrdersSubmitParamVo,
  OrdersSubmitProductParamVo,
} from "~/api/mall/shopper/ordersApi";

import freightApi, {
  MallFreightBo,
  MallShopperFreightGetListQueryParam,
} from "~/api/mall/shopper/freightApi";

import { Decimal } from "decimal.js/decimal";
import cartApi from "~/api/mall/shopper/cartApi";
import compUtils from "~/utils/compUtils";

type TableCart = MallCart & {
  product?: MallGoodsListVo;
  price?: GoodsPriceInfo;
  stock?: GoodsStockInfo;
};

type TableMallAddress = MallAddress & {};

type MapRegionTree = MapRegion & {
  children?: MapRegionTree[];
};

type AddressFormData = Omit<MallAddressAddVo, "regionCode"> & {
  regionCode: string[];
};

@Component({
  name: "demo-mall-orderssubmit",
})
export default class PageDemoMallWebOrderssubmit extends mixins(BaseVue) {
  loading = 0;
  query = {};
  formData = {
    allowNegative: true,
    addressId: "",
    remark: "",
    priceProduct: 0,
    priceOrders: 0,
    priceFreight: 0,
  };
  oneLoadParam = {
    inited: false,
    // ...param
    regionTree: <MapRegionTree[]>[],
    code2region: <Map<string, MapRegion> | null>null,
  };
  bizData = {
    carts: <TableCart[]>[],
    addresses: <TableMallAddress[]>[],
    mallAddreeForm: {
      dialogVisible: false,
      formLoading: 0,
      id: "",
      tableIdx: 0,
      formData: <AddressFormData>{
        name: "",
        phone: "",
        email: "",
        def: true,
        regionCode: [],
        address: "",
      },
    },
    store2freight: <Map<string, MallFreightBo>>new Map(),
  };

  async mounted() {
    await this.init();
  }

  getShowPrice(price: number) {
    return new Decimal(price).toFixed(2);
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  getRegionByCode(code: string): MapRegion[] {
    let code2region = this.oneLoadParam.code2region!;
    let regions = [];
    while (code) {
      let region = code2region.get(code);
      if (!region) {
        break;
      }
      regions.push(region);
      code = region.codeParent!;
    }
    return regions.reverse();
  }

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...

    await this.loadRegion();
  }

  async initDetail() {
    await Promise.all([this.loadAddress(), this.loadProducts()]);
  }

  async loadRegion() {
    this.loading++;
    try {
      let regions = await regionApi.getPubAllList();
      let code2region = new Map<string, MapRegion>();
      regions.map((r) => code2region.set(r.code!, r));
      this.oneLoadParam.code2region = code2region;
      let regionTree = arrayUtils.array2tree(
        regions,
        "code",
        "codeParent",
        "children"
      );
      this.oneLoadParam.regionTree = regionTree;
    } finally {
      this.loading--;
    }
  }

  async loadAddress() {
    this.loading++;
    try {
      let addresses: TableMallAddress[] = await shopperAddressApi.getList();
      this.bizData.addresses = addresses;

      for (let addr of addresses) {
        if (addr.def) {
          (<any>this.$refs.addressTable).setCurrentRow(addr);
        }
      }
    } finally {
      this.loading--;
    }
  }

  async loadProducts() {
    let param: MallShopperCartGetListQueryParam = { selected: true };
    this.loading++;
    try {
      let carts: TableCart[] = await shopperCartApi.getList(param);
      this.bizData.carts = carts;

      await this.refreshProduct();
    } finally {
      this.loading--;
    }
  }

  async refreshProduct() {
    let carts: TableCart[] = this.bizData.carts;
    let sIds = carts.map((x) => x.productSpaceId!);
    if (sIds.length > 0) {
      // 查询商品信息
      let products = await shopperGoodsSpaceApi.getListBySpaceIds(sIds);
      let sid2pd = new Map<string, MallGoodsListVo>();
      products.map((x) => sid2pd.set(x.spaces![0]!.productSpaceId!, x));
      let allOk = true;
      for (const cart of carts) {
        let pd = sid2pd.get(cart.productSpaceId!);
        if (!pd) {
          allOk = false;
          continue;
        }
        let space = pd.spaces![0]!;
        cart.productPic = space.pic + "," + pd.pic;
        cart.productName = space.name;
        this.$set(cart, "product", pd);
      }

      if (allOk) {
        await Promise.all([
          this.refreshPrice(),
          this.refreshStock(),
          this.refreshFeight(),
        ]);
      } else {
        await Promise.all([this.refreshPrice(), this.refreshStock()]);
      }
    }
  }

  async refreshFeight() {
    let carts: TableCart[] = this.bizData.carts;

    let param: MallShopperFreightGetListQueryParam = {};
    param.storeId = [...new Set(carts.map((x) => x.product!.storeId!))];
    let freights = await freightApi.getList(param);
    freights.forEach((x) => this.bizData.store2freight.set(x.storeId!, x));

    await this.computePriceOrders();
  }

  async refreshPrice() {
    let carts: TableCart[] = this.bizData.carts;
    this.loading++;
    try {
      let param: GoodsPriceParamVo = {};
      if (carts.length <= 0) {
        return;
      }
      param.spaceIds = carts.map((x) => x.productSpaceId!);
      let prices = await shopperGoodsPriceApi.getDetailBySpaceId(param);
      let id2price = new Map<string, GoodsPriceInfo>();
      prices.map((x) => id2price.set(x.spaceId!, x));

      for (const cart of carts) {
        let spaceId = cart.productSpaceId!;
        let price = id2price.get(spaceId);
        if (!price) {
          continue;
        }
        this.$set(cart, "price", price);
      }
      await this.computePriceOrders();
    } finally {
      this.loading--;
    }
  }

  async refreshStock() {
    let carts: TableCart[] = this.bizData.carts;
    if (carts.length <= 0) {
      return;
    }
    this.loading++;
    try {
      let param: GoodsStockParamVo = {};
      param.spaceNumInfos = carts.map((x) => ({
        spaceId: x.productSpaceId!,
        num: x.num,
      }));
      let stocks = await stockApi.getDetailBySpaceId(param);
      let id2stock = new Map<string, GoodsStockInfo>();
      stocks.map((x) => id2stock.set(x.spaceId!, x));

      for (const cart of carts) {
        let spaceId = cart.productSpaceId!;
        let stock = id2stock.get(spaceId);
        if (!stock) {
          continue;
        }
        this.$set(cart, "stock", stock);
      }
    } finally {
      this.loading--;
    }
  }

  async computePriceOrders() {
    let price = new Decimal("0");
    // 商品价格计算
    {
      for (let cart of this.bizData.carts) {
        if (!cart.price) {
          return;
        }
        if (!cart.product) {
          return;
        }
        let pdPrice = new Decimal(cart.price.unitPriceOrders!).mul(
          new Decimal(cart.num!)
        );
        price = price.add(pdPrice);
      }
    }
    this.formData.priceProduct = price.toNumber();

    // 运费计算
    {
      let storeId2carts = new Map<string, TableCart[]>();
      for (let cart of this.bizData.carts) {
        let pds = storeId2carts.get(cart.product!.storeId!);
        if (!pds) {
          pds = [];
          storeId2carts.set(cart.product!.storeId!, pds);
        }
        pds.push(cart);
      }

      let priceFreight = new Decimal(0);
      let store2freight = this.bizData.store2freight;
      for (let storeId of [...storeId2carts.keys()]) {
        let freight = store2freight.get(storeId);
        if (freight == null) {
          return;
        }
        if ("FREE" == freight.content!.type) {
          continue;
        } else if ("FIXED" == freight.content!.type) {
          priceFreight = priceFreight.add(new Decimal(freight.content!.price!));
        } else if ("RANGE" == freight.content!.type) {
          let pds = storeId2carts.get(storeId);
          let sumWeight = 0;
          let sumVolume = 0;
          for (let pd of pds!) {
            sumWeight =
              sumWeight + pd.product!.spaces![0]!.baseWeight! * pd.num!;
            let volume = pd.product!.spaces![0]!.baseVolume!;
            let vs = volume.split("*");
            let unitVolumn = +vs[0] * +vs[1] * +vs[2];
            sumVolume = sumVolume + unitVolumn * pd.num!;
          }
          let ranges = freight.content!.ranges!;
          let freightPrice = 0;
          for (let range of ranges) {
            if (range.volume! < sumVolume || range.weight! < sumWeight) {
              freightPrice = range.price!;
            }
          }
          priceFreight = priceFreight.add(new Decimal(freightPrice));
        } else {
          return;
        }
      }
      price = price.add(priceFreight);
      this.formData.priceFreight = priceFreight.toNumber();
    }
    this.formData.priceOrders = price.toNumber();
  }

  // 地址信息管理
  async handleAddressModifyClick(address: TableMallAddress, idx: number) {
    this.bizData.mallAddreeForm.formLoading++;
    try {
      let regionCode = this.getRegionByCode(address.regionCode!).map(
        (x) => x.code!
      );
      this.bizData.mallAddreeForm.formData = { ...address, regionCode };
      this.bizData.mallAddreeForm.id = address.id!;
      this.bizData.mallAddreeForm.tableIdx = idx;
      this.bizData.mallAddreeForm.dialogVisible = true;
    } finally {
      this.bizData.mallAddreeForm.formLoading--;
    }
  }

  async handleAddressPutDef(address: TableMallAddress, idx: number) {
    try {
      await shopperAddressApi.putDefault(address.id!);
      this.bizData.addresses.forEach((x) => (x.def = false));
      address.def = true;
    } finally {
      this.bizData.mallAddreeForm.formLoading--;
    }
  }

  async handleAddressDeleteClick(address: TableMallAddress, idx: number) {
    this.bizData.mallAddreeForm.formLoading++;
    try {
      await shopperAddressApi.delete(address.id!);
      if (this.formData.addressId == address.id) {
        address.id = "";
      }
      this.bizData.addresses.splice(idx, 1);
    } finally {
      this.bizData.mallAddreeForm.formLoading--;
    }
  }

  async handleAddressAddClick() {
    this.bizData.mallAddreeForm.formLoading++;
    try {
      this.bizData.mallAddreeForm.formData = {
        name: "",
        phone: "",
        email: "",
        def: true,
        regionCode: [],
        address: "",
      };
      this.bizData.mallAddreeForm.id = "";
      this.bizData.mallAddreeForm.dialogVisible = true;
    } finally {
      this.bizData.mallAddreeForm.formLoading--;
    }
  }
  async handleAddressTableCurrentChange(address: MallAddress) {
    this.formData.addressId = address?.id ?? "";
  }

  async handleMallAddressSubmitClick() {
    if (!(await this.$rule.formCheck(this, "addressForm"))) {
      return;
    }
    let addressForm = this.bizData.mallAddreeForm;
    let regionCode =
      addressForm.formData.regionCode[
        addressForm.formData.regionCode.length - 1
      ];
    let param: MallAddressAddVo = { ...addressForm.formData, regionCode };
    this.bizData.mallAddreeForm.formLoading++;
    try {
      let id = addressForm.id;
      if (id) {
        let address = await shopperAddressApi.modify(id, param);
        if (address.def) {
          this.bizData.addresses.forEach((x) => (x.def = false));
        }
        this.$set(this.bizData.addresses, addressForm.tableIdx, address);
      } else {
        let address = await shopperAddressApi.add(param);
        if (address.def) {
          this.bizData.addresses.forEach((x) => (x.def = false));
        }
        this.bizData.addresses.push(address);
      }
      this.bizData.mallAddreeForm.dialogVisible = false;
    } finally {
      this.bizData.mallAddreeForm.formLoading--;
    }
  }

  // 订单管理
  async handleSubmitOrdersClick() {
    if (!(await this.$rule.formCheck(this, "ordersForm"))) {
      return;
    }

    let body: OrdersSubmitParamVo = { ...this.formData };
    // 商品
    let products: OrdersSubmitProductParamVo[] = [];
    let carts = this.bizData.carts;
    if (carts && carts.length <= 0) {
      this.$notify.error("未找到商品信息");
      return;
    }
    for (let cart of carts) {
      if (!cart.stock || !cart.price) {
        this.$notify.error("商品信息加载异常");
        return;
      }
      let ok = !(
        "OK" == cart.stock.stockType ||
        (this.formData.allowNegative && "INCOMING" == cart.stock.stockType)
      );
      if (ok) {
        this.$notify.error("商品库存不足");
        return;
      }
      if (!this.bizData.store2freight.get(cart.product!.storeId!)) {
        this.$notify.error("部分商品运费模版异常，请联系商家。");
        return;
      }
      let pd: OrdersSubmitProductParamVo = {};
      pd.productSpaceId = cart.productSpaceId!;
      pd.num = cart.num!;
      products.push(pd);
    }
    body.products = products;

    //  地理位置
    const getAddress = () => {
      let addressId = this.formData.addressId;
      let addresses = this.bizData.addresses.filter((x) => x.id == addressId);
      if (addresses.length != 1) {
        throw Error("下单出错");
      }
      return addresses[0];
    };

    let address = getAddress();
    body.consignee = { ...address };

    this.loading++;
    try {
      let result = await ordersApi.submit(body);

      cartApi.batchDelete(carts.map((x) => x.productSpaceId!));

      let role = compUtils.getQueryValue(this, "role", "");
      if (!(result && result.orderses && result.orderses!.length > 0)) {
        this.$notify.success("下单成功");
        this.$router.replace(`/${role}/demo/mallweb/orders`);
        return;
      }
      let orders = result.orderses[0];
      let payOrders = await ordersApi.payRequest(orders.id!);
      console.log(payOrders.payOrdersId);
      this.$router.replace({
        path: `/${role}/common/pay`,
        query: {
          id: payOrders.payOrdersId,
          account_id: "1003",
          return_url: "/demo/mallweb/orders",
        },
      });
    } finally {
      this.loading--;
    }
  }

  async toPage(url: string) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${url}`);
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}

.address-table::v-deep tbody {
  .call-pointer {
    cursor: pointer;
  }
  tr.current-row > td {
    background-color: #409eff;
    color: #ffffff;

    .el-button--text {
      color: #fff;
    }
  }
}

.orders-form::v-deep .el-form-item.is-error .address-table {
  border: solid 1px #f56c6c;
}

.pay-price-win {
  .num {
    color: #f56c6c;
    font-size: 30px;
  }
}
</style>

