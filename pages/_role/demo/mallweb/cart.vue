<template lang="pug">
div
  .crumbs
    el-breadcrumb(separator="/")
      el-breadcrumb-item
        i.el-icon-lx-cascades 购物车
  .container
    //- 表头
    .handle-box
      el-button.mgr10(
        type="primary",
        icon="el-icon-plus",
        @click="handleOrdersClick"
      ) 去下单
      el-button.mgr10(@click="toPage('/demo/mallweb/orders')") 去订单
      el-button.mgr10(@click="() => goBack()") 返回
    //- 表单内容
    el-table.table.cart-table(
      :data="bizData.carts",
      border,
      selse,
      ref="cartsTable",
      header-cell-class-name="table-header",
      v-loading="loading > 0"
    )
      el-table-column(prop="selected", width="40")
        template(#default="scope")
          el-checkbox(
            v-model="scope.row.selected",
            @change="handleCartModifyChange(scope.row)"
          ) 
      el-table-column(prop="pic", label="图片")
        template(#default="scope")
          el-image(
            :src="getImageUrlOne(scope.row.productPic)",
            :preview-src-list="getImageUrls(scope.row.productPic)",
            :lazy="true",
            style="width: 100px; height: 100px"
          )
      el-table-column(prop="productName", label="名称")
      el-table-column(prop="num", label="数量")
        template(#default="scope")
          .cart-table-num
            el-input-number.number(
              size="mini",
              :min="1",
              :max="100",
              :precision="0",
              v-model="scope.row.num",
              @change="handleCartModifyChange(scope.row)"
            )
            .stock-msg(v-if="scope.row.product && scope.row.stock")
              el-tag.mgr10(
                :disable-transitions="false",
                :type="'OK' == scope.row.stock.stockType || 'INCOMING' == scope.row.stock.stockType ? 'success' : 'danger'"
              ) {{ "OK" == scope.row.stock.stockType ? "有货" : "INCOMING" == scope.row.stock.stockType ? "可预订" : "库存不足" }}
              span.mgr10.stock-num(
                v-if="('OK' == scope.row.stock.stockType || 'INCOMING' == scope.row.stock.stockType) && scope.row.stock.quantity != null"
              ) 剩余{{ "INCOMING" == scope.row.stock.stockType ? "可预订" : "" }}库存：{{ scope.row.stock.quantity }}
      el-table-column(prop="unitPrice", label="价格")
        template(#default="scope")
          template(v-if="scope.row.price")
            p 下单价：{{ scope.row.price.unitPriceOrders }}
            p 售卖价：{{ scope.row.price.unitPrice }}
            p 展示价：{{ scope.row.price.unitPriceShow }}
          template(v-else)
            span 数据未找到
      el-table-column(prop="product", label="状态")
        template(#default="scope")
          el-tag(:type="!!scope.row.product ? 'success' : 'danger'") {{ !!scope.row.product ? "销售中" : "下架" }}
      el-table-column(label="操作", width="200", align="center")
        template(#default="scope")
          el-popconfirm.mgl10(
            :title="$t('common.base.q_delete')",
            @confirm="handleDetailClick(scope.row, scope.$index)"
          )
            el-button.red(slot="reference", type="text") {{ $t("common.base.delete") }}
</template>

<script lang="ts">
import { Component, mixins } from "nuxt-property-decorator";
import { BaseVue } from "~/model/vue";
import urlUtils from "~/utils/urlUtils";

import shopperCartApi, {
  MallCart,
  MallCartAddVo,
  MallShopperCartGetListQueryParam,
} from "~/api/mall/shopper/cartApi";
import shopperGoodsSpaceApi, {
  MallGoodsListVo,
} from "~/api/mall/shopper/goods/spaceApi";

import shopperGoodsPriceApi, {
  GoodsPriceInfo,
  GoodsPriceParamVo,
} from "~/api/mall/shopper/goods/price/batch/queryApi";
import stockApi, {
  GoodsStockInfo,
  GoodsStockParamSpaceNumVo,
  GoodsStockParamVo,
} from "~/api/mall/shopper/goods/stock/batch/queryApi";
import { DelayOperate } from "~/utils/toolsUtils";
import compUtils from "~/utils/compUtils";

type TableCart = MallCart & {
  product?: MallGoodsListVo;
  price?: GoodsPriceInfo;
  stock?: GoodsStockInfo;
};

@Component({
  name: "demo-mall-cart",
})
export default class PageDemoMallWebCart extends mixins(BaseVue) {
  loading = 0;
  query = {};
  formData = {};
  oneLoadParam = {
    inited: false,
    // ...param
  };
  bizData = {
    delayOperateChange: new DelayOperate({ timeout: 350 }),
    carts: <TableCart[]>[],
  };

  async mounted() {
    await this.init();
  }

  getImageUrlOne(id: string) {
    let urls = this.getImageUrls(id);
    if (urls.length > 0) {
      return urls[0];
    } else {
      return "";
    }
  }
  getImageUrls(id: string) {
    return urlUtils.file2array(id);
  }

  async init() {
    this.loading++;
    try {
      await this.initOneLoad();
      await this.initDetail();
    } finally {
      this.loading--;
    }
  }

  async initOneLoad() {
    if (this.oneLoadParam.inited) {
      return;
    }
    this.oneLoadParam.inited = true;
    // ...
  }

  async initDetail() {
    await this.loadContent();
  }

  async loadContent() {
    let param: MallShopperCartGetListQueryParam = {};
    this.loading++;
    try {
      let carts: TableCart[] = await shopperCartApi.getList(param);
      this.bizData.carts = carts;

      this.refreshProduct(carts);
      this.refreshPrice(carts);
      this.refreshStock(carts);
    } finally {
      this.loading--;
    }
  }

  async refreshProduct(carts: TableCart[]) {
    let sIds = carts.map((x) => x.productSpaceId!);
    if (sIds.length > 0) {
      // 查询商品信息
      let products = await shopperGoodsSpaceApi.getListBySpaceIds(sIds);
      let sid2pd = new Map<string, MallGoodsListVo>();
      products.map((x) => sid2pd.set(x.spaces![0]!.productSpaceId!, x));
      for (const cart of carts) {
        let pd = sid2pd.get(cart.productSpaceId!);
        if (!pd) {
          continue;
        }
        let space = pd.spaces![0]!;
        cart.productPic = space.pic + "," + pd.pic;
        cart.productName = space.name;
        this.$set(cart, "product", pd);
      }
    }
  }

  async refreshPrice(carts: TableCart[]) {
    this.loading++;
    try {
      let param: GoodsPriceParamVo = {};
      if (carts.length <= 0) {
        return;
      }
      param.spaceIds = carts.map((x) => x.productSpaceId!);
      let prices = await shopperGoodsPriceApi.getDetailBySpaceId(param);
      let id2price = new Map<string, GoodsPriceInfo>();
      prices.map((x) => id2price.set(x.spaceId!, x));

      for (const cart of carts) {
        let spaceId = cart.productSpaceId!;
        let price = id2price.get(spaceId);
        if (!price) {
          continue;
        }
        this.$set(cart, "price", price);
      }
    } finally {
      this.loading--;
    }
  }

  async refreshStock(carts: TableCart[]) {
    if (carts.length <= 0) {
      return;
    }
    this.loading++;
    try {
      let param: GoodsStockParamVo = {};
      param.spaceNumInfos = carts.map((x) => ({
        spaceId: x.productSpaceId!,
        num: x.num,
      }));
      let stocks = await stockApi.getDetailBySpaceId(param);
      let id2stock = new Map<string, GoodsStockInfo>();
      stocks.map((x) => id2stock.set(x.spaceId!, x));

      for (const cart of carts) {
        let spaceId = cart.productSpaceId!;
        let stock = id2stock.get(spaceId);
        if (!stock) {
          continue;
        }
        this.$set(cart, "stock", stock);
      }
    } finally {
      this.loading--;
    }
  }

  async handleCartModifyChange(cart: TableCart) {
    cart.stock = undefined;
    if (!(await this.bizData.delayOperateChange.delay())) {
      return;
    }
    try {
      let putCart = async () => {
        let modifyVo: MallCartAddVo = {
          productSpaceId: cart.productSpaceId,
          num: cart.num,
          selected: cart.selected,
        };
        await shopperCartApi.batchPut([modifyVo]);
      };
      let queryStock = async (): Promise<GoodsStockInfo | null> => {
        let spaceNumInfo: GoodsStockParamSpaceNumVo = {};
        spaceNumInfo.spaceId = cart.productSpaceId;
        spaceNumInfo.num = cart.num;
        let stocks = await stockApi.getDetailBySpaceId({
          spaceNumInfos: [spaceNumInfo],
        });
        if (stocks && stocks.length > 0) {
          return stocks[0];
        } else {
          return null;
        }
      };
      let ps = await Promise.all([putCart(), queryStock()]);
      let stock = ps[1];
      this.$set(cart, "stock", stock);
    } catch (error) {
      throw error;
    }
  }

  async toPage(url: string) {
    let role = compUtils.getQueryValue(this, "role", "");
    this.$router.push(`/${role}${url}`);
  }

  async handleOrdersClick() {
    this.toPage(`/demo/mallweb/orders/orderssubmit?type=cart`);
  }

  async handleDetailClick(record: TableCart, idx: number) {
    let sids: string[] = [record.productSpaceId!];
    await shopperCartApi.batchDelete(sids);
    this.$notify.success("已从购物车移除");
    this.bizData.carts.splice(idx, 1);
  }

  async goBack() {
    this.$router.back();
  }
}
</script>
<style lang="scss" scoped>
.handle-box {
  margin-bottom: 20px;
}
.cart-table {
  .cart-table-num {
    height: 60px;
    // display: flex;
  }
}
</style>

