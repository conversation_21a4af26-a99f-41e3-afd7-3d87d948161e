{
    "editor.quickSuggestions": {
        //开启自动显示建议
        "other": true,
        "comments": true,
        "strings": true
    },
    // vscode默认启用了根据文件类型自动设置tabsize的选项
    "editor.detectIndentation": false,
    // #每次保存的时候自动格式化
    "editor.formatOnSave": true,
    // #每次保存的时候将代码按eslint格式进行修复
    "eslint.autoFixOnSave": true,
    // 添加 vue 支持
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        {
            "language": "html",
            "autoFix": true
        },
        {
            "language": "vue",
            "autoFix": true
        }
    ],
    //  #让prettier使用eslint的代码格式进行校验
    "prettier.eslintIntegration": true,
    //  #去掉代码结尾的分号
    "prettier.semi": false,
    //  #使用带引号替代双引号
    "prettier.singleQuote": false,
    //  #让函数(名)和后面的括号之间加个空格
    "javascript.format.insertSpaceBeforeFunctionParenthesis": true,
    // #这个按用户自身习惯选择
    "vetur.format.defaultFormatter.html": "js-beautify-html",
    // #让vue中的js按编辑器自带的ts格式进行格式化
    "vetur.format.defaultFormatter.js": "vscode-typescript",
    // 上面3个配一起 函数名后面才会加空格
    "vetur.format.defaultFormatterOptions": {
        // "wrap_attributes": "force-aligned",
        "js-beautify-html": {
            // - force: 对除第一个属性外的其他每个属性进行换行。
            // - force-aligned: 对除第一个属性外的其他每个属性进行换行，并保持对齐。
            // - force-expand-multiline: 对每个属性进行换行。
            // - aligned-multiple: 当超出折行长度时，将属性进行垂直对齐。
            "wrap_attributes": "aligned-multiple",
            // #vue组件中html代码格式化样式,可选
            "wrap_line_length": 100,
            "end_with_newline": false,
            "semi": true,
            "singleQuote": false
        },
        "prettier": {
            //设置分号
            "semi": true,
            //双引号变成单引号
            "singleQuote": false
            //禁止随时添加逗号,这个很重要。找了好久
            //   "trailingComma": "none"
        }
    },
    // 格式化stylus, 需安装Manta's Stylus Supremacy插件
    "stylusSupremacy.insertColons": false, // 是否插入冒号
    "stylusSupremacy.insertSemicolons": false, // 是否插入分号
    "stylusSupremacy.insertBraces": false, // 是否插入大括号
    "stylusSupremacy.insertNewLineAroundImports": false, // import之后是否换行
    "stylusSupremacy.insertNewLineAroundBlocks": false,
    "[javascript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features"
    },
    "window.zoomLevel": 0, // 两个选择器中是否换行
    /** Easy Sass 插件 **/
    "easysass.formats": [
        {
            "format": "expanded", // 没有缩进的、扩展的css代码
            "extension": ".css"
        }
    ],
    "easysass.targetDir": "./css/",
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },
    "vetur.format.options.tabSize": 2,
    "pugFormatter.tabSize": 2,
    "editor.tabSize": 2,
    "typescript.tsdk": "node_modules/typescript/lib",
    "notebook.output.textLineLimit": 100,
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 100,
    "i18n-ally.localesPaths": ["locales", "static/tinymce/langs"]
}
