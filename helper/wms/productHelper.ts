import productSpaceApi, {
  WmsProductSpace,
} from "@/api/wms/mg/product/spaceApi";
import productBox<PERSON>pi, { WmsProductBox } from "@/api/wms/mg/product/boxApi";

import warehouseBoxApi, { WmsWarehouseBox } from "@/api/wms/mg/warehouse/boxApi";

export type WmsProductBoxTmpl = {
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  pd?: WmsProductSpace;
}

export type WmsWarehouseBoxTmpl = WmsProductBoxTmpl & {
  productBoxId?: string;
  pbox?: WmsProductBox;
}

export type WmsBizTmpl = WmsWarehouseBoxTmpl & {
  warehouseBoxId?: string;
  wbox?: WmsWarehouseBox;
}

async function bizPutMsg(contents: WmsBizTmpl[], id2wboxCache: Map<string, WmsWarehouseBox> = new Map()) {
  {
    let id2wbox = id2wboxCache ? id2wboxCache : new Map();
    let loadProducts: WmsBizTmpl[] = [];
    for (let biz of contents) {
      let wbox = id2wbox.get(biz.warehouseBoxId!);
      if (wbox) {
        biz.wbox = wbox;
        biz.productBoxId = wbox.productBoxId!;
      } else {
        loadProducts.push(biz);
      }
    }

    if (loadProducts.length > 0) {
      let wboxids = loadProducts.map((x) => x.warehouseBoxId!);
      let wboxs = await warehouseBoxApi.getList({ id: wboxids });
      wboxs.forEach((x) => id2wbox.set(x.id!, x));
      for (let biz of loadProducts) {
        let wbox = id2wbox.get(biz.warehouseBoxId!);
        biz.wbox = wbox;
        if (wbox) {
          biz.productBoxId = wbox.productBoxId!;
        }
      }
    }
  }
  // await warehouseBoxPutMsg(contents, id2pdCache);
}

async function warehouseBoxPutMsg(contents: WmsWarehouseBoxTmpl[], id2pboxCache: Map<string, WmsProductBox> = new Map()) {
  {
    let id2pbox = id2pboxCache ? id2pboxCache : new Map();
    let loadProducts: WmsWarehouseBoxTmpl[] = [];
    for (let wbox of contents) {
      let pbox = id2pbox.get(wbox.productBoxId!);
      if (pbox) {
        wbox.pbox = pbox;
        wbox.productSpaceId = pbox.productSpaceId!;
      } else {
        loadProducts.push(wbox);
      }
    }

    if (loadProducts.length > 0) {
      let pboxids = loadProducts.map((x) => x.productBoxId!);
      let pboxs = await productBoxApi.getList({ id: pboxids });
      pboxs.forEach((x) => id2pbox.set(x.id!, x));
      for (let wbox of loadProducts) {
        let pbox = id2pbox.get(wbox.productBoxId!);
        wbox.pbox = pbox;
        if (pbox) {
          wbox.productSpaceId = pbox.productSpaceId!;
        }
      }
    }
  }
  // await productBoxPutMsg(contents, id2pdCache);
}

async function productBoxPutMsg(contents: WmsProductBoxTmpl[], cache: Map<string, WmsProductSpace> = new Map()) {
  let id2space = cache ? cache : new Map();
  let loadProducts: WmsProductBoxTmpl[] = [];
  for (let pbox of contents) {
    let pd = id2space.get(pbox.productSpaceId!);
    if (pd) {
      pbox.pd = pd;
    } else {
      loadProducts.push(pbox);
    }
  }
  if (loadProducts.length > 0) {
    let pdids = loadProducts.map((x) => x.productSpaceId!);
    let pds = await productSpaceApi.getList({ id: pdids });
    pds.forEach((x) => id2space.set(x.id!, x));
    for (let pbox of loadProducts) {
      let pd = id2space.get(pbox.productSpaceId!);
      pbox.pd = pd;
    }
  }
}


export default {
  bizPutMsg,
  productBoxPutMsg,
  warehouseBoxPutMsg,
}

