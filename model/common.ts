
export interface TokenMessageVo {
  id?: string
  token?: string;
  expries?: number;
  rtoken?: string;
  rexpries?: number;
}

export interface WsMsgSendBodyBo {
  bc: string;
  fc: string;
  ec: string;
  d: string;
}

// [User]用户信息表路径请求参数
export interface User {

  // 主键, 必须字段, 最小长度：1, 最大长度：32
  id?: string;
  // 名称, 最小长度：0, 最大长度：32
  name?: string;
  // 用户名, 最小长度：0, 最大长度：32
  username?: string;
  // 账户禁用
  ban?: boolean;
  // 账户生效时间
  startTime?: number;
  // 账户禁用时间
  endTime?: number;
  // 创建时间
  createTime?: number;
  // 上次登录地址
  loginLastAddr?: string;
  // 上次登录IP
  loginLastIp?: string;
  // 上次登录时间
  loginLastTime?: number;
  // 这次登录地址
  loginThisAddr?: string;
  // 这次登录IP
  loginThisIp?: string;
  // 这次登录时间
  loginThisTime?: number;
  /**
 * 是否是代理用户
 */
  authProxyUser?: boolean;
  /**
   * authProxyUserInfo
   */
  authProxyUserInfo?: User;
}

export type MenuItem = {
  /**
   * 图表信息
   */
  icon?: string;
  /**
   * 名称，编号信息，需对应国际化
   */
  name?: string;
  /**
   * 跳转的Url地址
   */
  url?: string;
  /**
   * URL的表达式（正则表达式），用于匹配当前菜单
   */
  urlExp?: string[];
  /**
   * 是否对当前启用缓存，默认：true
   */
  keepalive?: boolean;
  show?: boolean;
  /**
   * 需要当前用户具有此角色权限才显示
   */
  role?: string[];
  /**
   * 需要当前页面角色是当角色才是现实
   */
  pageRole?: string[];
  /**
   * 点击跳转到当前页面
   */
  toPageRole?: string;
  /**
   * 子菜单
   */
  subs?: MenuItem[];

  /**
   * 菜单编号,内部变量
   */
  _mid?: string;
};

export type TagNode = {
  path?: string,
  name?: string,
  routeName?: string,
  fullPath?: string,
  keepalive?: boolean,
}


