import { Vue } from "nuxt-property-decorator";
import moment from "moment";
import { Store } from 'vuex/types/index';
import dicUtils from "@/utils/dicUtils";
import { User } from '@/model/common';
import authUtils from "~/utils/authUtils";
import ruleUtils from "~/utils/ruleUtils";
import fieldUtils from "~/utils/fieldUtils";
import sysconfigUtils from "~/utils/sysconfigUtils";


/**
 * 消息处理
 */
export class VueMsg {
  content: { $store: Store<any> };
  constructor(content: { $store: Store<any> }) {
    this.content = content;
  }
  async refreshMsgCount() {
    return this.content.$store.dispatch("msg/refreshUnreadCount");
  }
  async refreshBpmTodoCount() {
    return this.content.$store.dispatch("msg/refreshBpmTodoCount");
  }
}

/**
 * 认证服务
 */
export class VueAuth {
  public static WATCH_PAGE_ROLE = '$store.state.auth.pageRole'

  content: { $store: Store<any> };
  constructor(content: { $store: Store<any> }) {
    this.content = content;
  }
  async getUserInfo(): Promise<null | User> {
    return <any>await this.content.$store.dispatch("auth/getUserInfo");
  }
  async getUserId(): Promise<null | string> {
    let userInfo = await this.getUserInfo();
    if (!userInfo) {
      return null;
    }
    return userInfo.id!;
  }
  async hasPower(power: string | Array<string>): Promise<boolean> {
    return <any>await this.content.$store.dispatch("auth/hasPower", power);
  }
  async getPageRole(): Promise<string | null> {
    return <any>await this.content.$store.dispatch("auth/getPageRole");
  }
  v_getPageRole(): string | null {
    this.content.$store.dispatch("auth/getPageRole");
    return this.content.$store.state.auth.pageRole;
  }
  v_hasPower(power: string | Array<string>): boolean {
    this.content.$store.dispatch('auth/getPowerList');
    let proobj = this.content.$store.state.auth.power;
    return authUtils.hasPower(proobj, power);
  }
  async getToken(): Promise<string | null> {
    return <any>await this.content.$store.dispatch('auth/getToken');
  }
}

/**
 * WebSocket管理
 */
export class VueWs {
  public static WATCH_WS_MSG = '$store.state.ws.msg'
  public static WATCH_WS_CONNECTED = '$store.state.ws.connectedCount'
  public static WATCH_WS_DISCONNECTED = '$store.state.ws.disconnectedCount'
  content: { $store: Store<any> };
  constructor(content: { $store: Store<any> }) {
    this.content = content;
  }
  async send(body: { type: string, data: string }): Promise<any> {
    return <any>await this.content.$store.dispatch('ws/send', body);
  }
  push(body: { type: string, data: string }) {
    this.content.$store.dispatch('ws/push', body);
  }
}

export class BaseVue extends Vue {
  public $moment = moment;
  public $dic: typeof dicUtils = <any>null;
  public $sysconfig: typeof sysconfigUtils = <any>null;
  public $msg: VueMsg = <any>null;
  public $auth: VueAuth = <any>null;
  public $rule: typeof ruleUtils = <any>null;
  public $ws: VueWs = <any>null;
  public $fieldUtils: typeof fieldUtils = <any>null;

  created(): any | Promise<any> { }
  mounted(): any | Promise<any> { }
  activated(): any | Promise<any> { }
  deactivated(): any | Promise<any> { }
  beforeDestroy(): any | Promise<any> { }
  destroyed(): any | Promise<any> { }

  // loading = 0;
  // query = {
  //   rows: 20,
  //   page: 1,
  //   total: 0,
  //   id: '',
  // }
  // formData = {
  //   name: ''
  // }
  // oneLoadParam = {
  //   inited: false,
  //   initCount: 0,
  //   // ...param
  // };
  // bizData = {
  //   data: []
  // }
  // 
  // async init() {
  //   this.loading++;
  //   if (this.oneLoadParam.initCount > 0) {
  //     return;
  //   }
  //   this.oneLoadParam.initCount++;
  //   try {
  //     await this.initDetailBefor();
  //     await this.initOneLoad();
  //     await this.initDetail();
  //   } finally {
  //     this.loading--;
  //     this.oneLoadParam.initCount--;
  //   }
  // }
  // 
  // async initDetailBefor() {
  //   this.query.keyword = routerUtils.getQueryValue(this, "keyword", "");
  //   this.query.page = +routerUtils.getQueryValue(this, "page", "1");
  //   this.query.rows = +routerUtils.getQueryValue(this, "rows", "20");
  // }
  // 
  // async initOneLoad() {
  //   if (this.oneLoadParam.inited) {
  //     return;
  //   }
  //   this.oneLoadParam.inited = true;
  //   // ...
  // }
  // 
  // async initDetail() {
  // await this.loadContentCount();
  // }
  // async loadContentCount() { }
  // async loadContent() { }
  // async submit() { }
  // async goBack() {}
  // async toPage(url: string) {
  //   let role = compUtils.getQueryValue(this, "role", "");
  //   this.$router.push(`/${role}${url}`);
  // }
}

