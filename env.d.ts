declare module 'sortablejs';

declare module 'sm-crypto' {
  // === SM2 签名参数 ===
  export interface Sm2SignatureOptions {
    /**
     * 椭圆曲线加速用的预计算点池（可忽略）
     */
    pointPool?: any[];
    /**
     * 是否使用 DER 编码（默认 true）
     */
    der?: boolean;
    /**
     * 是否使用 SM3 哈希（默认 true）
     */
    hash?: boolean;
    /**
     * 可选公钥（签名时加快验证用）
     */
    publicKey?: string;
    /**
     * 用户 ID，用于 Z 值计算，默认 '1234567812345678'
     */
    userId?: string;
  }

  // === SM2 验签参数 ===
  export interface Sm2VerifyOptions {
    /**
     * 是否为 DER 编码格式（默认 true）
     */
    der?: boolean;
    /**
     * 是否使用 SM3 哈希（默认 true）
     */
    hash?: boolean;
    /**
     * 用户 ID，必须与签名方一致（默认 '1234567812345678'）
     */
    userId?: string;
  }

  // === SM2 解密附加选项 ===
  export interface Sm2DecryptOptions {
    /**
     * 解密输出格式（默认 'string'）
     * - 'string': 返回明文字符串
     * - 'array': 返回字节数组
     */
    output?: 'string' | 'array';
  }

  // === SM2 接口定义 ===
  export interface Sm2 {
    /**
     * 生成 SM2 公私钥对（Hex 格式）
     * @param compress 是否压缩公钥（默认 false）
     */
    generateKeyPairHex(compress?: boolean): { publicKey: string; privateKey: string };

    /**
     * 从私钥导出对应的公钥（Hex）
     */
    getPublicKeyFromPrivateKey(privateKey: string): string;

    /**
     * 使用 SM2 公钥加密消息（返回 C1C3C2 格式 Hex）
     * @param cipherMode 默认为 1（C1C3C2），推荐保持默认
     */
    doEncrypt(
      msg: string,
      publicKey: string,
      cipherMode?: 0 | 1
    ): string;

    /**
     * 使用 SM2 私钥解密密文
     */
    doDecrypt(
      cipherHex: string,
      privateKey: string,
      cipherMode?: 0 | 1,
      options?: Sm2DecryptOptions
    ): string;

    /**
     * 使用私钥对消息进行签名（返回 Hex 字符串，可选 DER 编码）
     */
    doSignature(
      msg: string,
      privateKey: string,
      options?: Sm2SignatureOptions
    ): string;

    /**
     * 使用公钥验证签名结果
     */
    doVerifySignature(
      msg: string,
      signature: string,
      publicKey: string,
      options?: Sm2VerifyOptions
    ): boolean;

    /**
     * 压缩长公钥（65字节）为压缩格式（33字节）
     */
    compressPublicKeyHex(publicKey: string): string;

    /**
     * 比较两个公钥是否相等（支持压缩格式）
     */
    comparePublicKeyHex(publicKey1: string, publicKey2: string): boolean;

    /**
     * 校验公钥是否有效（格式与曲线点是否合理）
     */
    verifyPublicKey(publicKey: string): boolean;

    /**
     * 获取内部椭圆曲线点（调试/实验用）
     */
    getPoint(): any;
  }

  // === SM3 哈希函数（无状态）===
  export type Sm3 = (msg: string) => string;


  export interface Sm4Options {
    /**
     * 加密模式：ECB 或 CBC。ECB 是块加密的默认模式，CBC 需要 iv 参数。
     */
    mode?: 'ecb' | 'cbc';

    /**
     * 填充方式，常见有 PKCS7 和 ZeroPadding。默认使用 pkcs#7 填充（传 pkcs#5 也会走 pkcs#7 填充）。
     */
    padding?: 'pkcs#7' | 'ZeroPadding';

    /**
     * 初始化向量，仅 CBC 模式下有效，长度应为16字节（128位）
     */
    iv?: string;

    /**
     * 其他自定义参数（保留扩展用）
     */
    [key: string]: any;
  }

  // === SM4 对称加解密函数 ===
  export interface Sm4 {
    /**
     * 使用 SM4 加密（ECB 模式）
     */
    encrypt(msg: string, key: string, options?: Sm4Options): string;

    /**
     * 使用 SM4 解密（ECB 模式）
     */
    decrypt(cipher: string, key: string, options?: Sm4Options): string;
  }

  /**
   * sm-crypto 主入口对象
   */
  const smCrypto: {
    sm2: Sm2;
    sm3: Sm3;
    sm4: Sm4;
  };

  export default smCrypto;
}

