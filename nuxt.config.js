// require('dotenv').config({ path: '.env' })


export default {
  // Disable server-side rendering (https://go.nuxtjs.dev/ssr-mode)
  ssr: false,

  // Global page headers (https://go.nuxtjs.dev/config-head)
  head: {
    title: '管理系统',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: '' }
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/img/logo.svg' },
      // { rel: "stylesheet", href: "//at.alicdn.com/t/font_2566575_30qtap7789d.css" }
    ],
    script: [
      // { src: 'https://fastly.jsdelivr.net/npm/echarts-graph-modularity@2.0.0/dist/echarts-graph-modularity.min.js' },
      //     { src: 'https://gosspublic.alicdn.com/aliyun-oss-sdk-6.16.0.min.js' },
      // 移除了pagespy工具
      // { src: 'https://pagespy.yhert.com/page-spy/index.min.js', crossorigin: 'anonymous', async: 'true' },
      // { src: `${process.env.BASE_PATH ? process.env.BASE_PATH : ''}/js/pdf/pdf.js`, type: 'module', crossorigin: 'anonymous' }
    ],
  },
  loading: '~/components/layout/loading.vue',

  // Global CSS (https://go.nuxtjs.dev/config-css)
  css: [
    '~/static/css/main.scss',
  ],

  // Plugins to run before rendering page (https://go.nuxtjs.dev/config-plugins)
  plugins: [
    { src: '~/plugins/flexible.js', ssr: false },
    '~/plugins/vue-error-handle',
    '~/plugins/vue-base',
    '~/plugins/element-ui',
    '~/plugins/axios-accessor',
    '~/plugins/sysbase-init',
    '~/plugins/i18n',
    '~/plugins/ws',
    '~/plugins/auth',
    { src: '~/plugins/svg-icon.ts', ssr: false },
  ],

  // Auto import components (https://go.nuxtjs.dev/config-components)
  // components: true,
  components: [
    { path: '~/components/layout', prefix: 'layout', isAsync: false },
    { path: '~/components/y', prefix: 'y', isAsync: false },
    // { path: '~/components/ym', prefix: 'ym', isAsync: true },
  ],

  // Modules for dev and build (recommended) (https://go.nuxtjs.dev/config-modules)
  buildModules: [
    // https://go.nuxtjs.dev/typescript
    '@nuxt/typescript-build',
    // '@nuxtjs/tailwindcss',
  ],

  // Modules (https://go.nuxtjs.dev/config-modules)
  modules: [
    '@nuxtjs/axios',
    // '@nuxtjs/proxy',
    '@nuxtjs/dotenv',
    // 'nuxt-i18n'
  ],
  env: {
    NODE_ENV: process.env.NODE_ENV
  },
  router: {
    middleware: ['authenticated', 'tags'],
    base: process.env.BASE_PATH ? process.env.BASE_PATH : '',
  },
  axios: {
    proxy: true,
    credentials: false,
  },
  proxy: {
    '/api': {
      target: 'http://mg.goodgifts.yheart.cn/api',
      // target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '',
        changeOrigin: true
      }
    },
    '/report': {
      target: 'http://mg.goodgifts.yheart.cn/report',
      // target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: {
        '^/report': '',
        changeOrigin: true
      }
    },
  },

  // Build Configuration (https://go.nuxtjs.dev/config-build)
  build: {
    sourcemap: false,
    terserOptions: {    //JavaScript 压缩工具
      compress: {
        drop_console: true, // 删除 console.log 语句
        drop_debugger: true, // 删除 debugger 语句
        dead_code: true, // 删除不可达代码
        conditionals: true, // 优化条件表达式
        evaluate: true, // 尽可能地评估常量表达式
        sequences: true, // 将连续的语句序列转换为逗号表达式
        booleans: true, // 优化布尔值表达式
        loops: true, // 优化循环
        unused: true, // 删除未使用的变量和函数
        if_return: true, // 优化 if 语句和 return 语句的组合
        join_vars: true, // 合并连续的 var 声明
        collapse_vars: true, // 折叠变量
      },
      mangle: {
        keep_fnames: false, // 不保留函数名
        toplevel: true, // 混淆顶层作用域的变量和函数名
      },
      output: {
        comments: false, // 删除注释
        beautify: false, // 不生成美化的代码
      },
    },
    extractCSS: true,   // 将css抽取到单独的文件
    // splitChunks: false,  //将代码划分成多个文件
    analyze: false,
    postcss: [
      require('postcss-px2rem')({
        remUnit: 136  // 之所以写192是因为设了pc最大宽度1920px
      })
    ],
  },
  server: {
    port: 3000,
    host: '0.0.0.0'
  }
}

