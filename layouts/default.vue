<template lang="pug">
div
  layout-pageupdatecheck
  layout-header
  layout-sidebar
  .content-box(:class="{ 'content-collapse': collapse }")
    .layout-body
      layout-tags
      .layout-content
        //- transition(name="el-zoom-in-left")
        transition(name="el-fade-in")
          Nuxt(
            v-if="showPage",
            #default="{ component }",
            keep-alive,
            :keep-alive-props="{ max: 20, include: keepAliveIncloude, key: keepAliveKey }",
            ref="content"
          )
            //- transition(name="move", mode="out-in")
            component(:is="component")
</template>

<script lang="ts">
import { Component, Vue, Watch } from "nuxt-property-decorator";
import { TagNode } from "~/model/common";

@Component({
  name: "layoutsDefault",
})
export default class LayoutsDefault extends Vue {
  showPage = true;

  get collapse() {
    return this.$store.state.tags.collapse;
  }

  get tagsList() {
    return this.$store.state.tags.tagsList;
  }

  get keepAliveIncloude() {
    let tags: TagNode[] = this.$store.state.tags.tagsList;
    let keys = tags.filter((x) => x.keepalive).map((x) => x.routeName);
    return keys;
  }

  get keepAliveKey() {
    return this.$route.name;
  }

  @Watch("$store.state.tags.reloadCount")
  reloadEven() {
    // 移除组件
    this.showPage = false;
    // 在组件移除后，重新渲染组件
    // this.$nextTick可实现在DOM 状态更新后，执行传入的方法。
    this.$nextTick(() => {
      this.showPage = true;
    });
  }
}
</script>

<style lang="scss" scoped>
</style>
