<template lang="pug">
.layout-container
  layout-pageupdatecheck
  layout-good-header
  .layout-content-new
      transition(name="el-fade-in")
        Nuxt(
          v-if="showPage",
          #default="{ component }",
          keep-alive,
          :keep-alive-props="{ max: 20, include: keepAliveIncloude, key: keepAliveKey }",
          ref="content"
        )
          component(:is="component")
  layout-good-footer
</template>

<script lang="ts">
import { Component, Vue, Watch } from "nuxt-property-decorator";
import { TagNode } from "~/model/common";

@Component({
  name: "GoodLayoutsDefault",
})
export default class LayoutsDefault extends Vue {
  showPage = true;

  get collapse() {
    return this.$store.state.tags.collapse;
  }

  get tagsList() {
    return this.$store.state.tags.tagsList;
  }

  get keepAliveIncloude() {
    let tags: TagNode[] = this.$store.state.tags.tagsList;
    let keys = tags.filter((x) => x.keepalive).map((x) => x.routeName);
    return keys;
  }

  get keepAliveKey() {
    return this.$route.name;
  }

  @Watch("$store.state.tags.reloadCount")
  reloadEven() {
    // 移除组件
    this.showPage = false;
    // 在组件移除后，重新渲染组件
    // this.$nextTick可实现在DOM 状态更新后，执行传入的方法。
    this.$nextTick(() => {
      this.showPage = true;
    });
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  overflow-y: auto;
  background: radial-gradient(circle at top left,
    rgb(36, 29, 75) 0%,
    rgb(36, 29, 75) 10%,
    rgba(23, 22, 24, 1) 30%,
    rgba(23, 22, 24, 1) 100%);

  &::-webkit-scrollbar {
    width: 0;
  }
}
</style>
