
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [PayUrlQuery]请求参数
export interface PayUrlQuery {

  // account_id
  account_id?: string;
  // open_id
  open_id?: string;
  // 设备code（COMPUTER、H5、WX_MP）
  device_code?: string;
  // 回跳url
  return_url?: string;
}

// [PayUrlVo]响应参数
export interface PayUrlVo {

  // account_id
  account_id?: string;
  // open_id
  open_id?: string;
  // 设备code（COMPUTER、H5、WX_MP）
  device_code?: string;
  // 回跳url
  return_url?: string;
}

export interface PayResultVo {
  pay?: boolean;
}

export default {
  payUrl: `${config.baseUrl.apiUrl}/pay`,

  // method 修改数据
  // param id: ID
  // param query 请求Query参数
  pay: async (id: string, query: PayUrlQuery): Promise<PayUrlVo> => request.get(`${config.baseUrl.apiUrl}/pay/pay/${id}`, { params: query }),

  // 订单支付情况查询
  payOrdersResult: async (ordersId: string): Promise<PayResultVo> => request.get(`${config.baseUrl.apiUrl}/pay/pay/orders/${ordersId}/result`, {}),

}
