import { request } from '@/utils/request'

import { config } from '../../config/global.config'

export interface CmsContentListParam {
  _category_id?: string;
  _page?: number;
  _rows?: number;
}

export default {

  getListCount: async (subjectId: string, params: CmsContentListParam | undefined) => request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/content/count`, { params: params, token: true }),

  getList: async (subjectId: string, params: CmsContentListParam | undefined) => request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/content`, { params: params, token: true }),

  get: async (subjectId: string, id: string) => request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/content/${id}`, { token: true }),

  post: async (subjectId: string, body: any) => request.post(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/content`, body, { token: true }),

  put: async (subjectId: string, id: string, body: any) => request.put(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/content/${id}`, body, { token: true }),

  del: async (subjectId: string, id: string) => request.delete(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/content/${id}`, { token: true }),

}

