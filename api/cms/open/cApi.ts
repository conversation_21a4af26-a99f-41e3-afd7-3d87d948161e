
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [ContentLogVo]内容日志响应路径请求参数
 */
export interface ContentLogVo {

  /**
   * PV访问量
   */
  pv?: number;
}

export default {

  /**
   * 查询内容信息
   * @param subject_path: 主题地址
   * @param path: 内容地址
   * @returns 数据
   */
  getContent: async (subject_path: string, path: string): Promise<string> => await request.get(`${config.baseUrl.apiUrl}/cms/open/c/${subject_path}/${path}`, { token: false, pageRole: false }),


  /**
   * 内容Pv统计
   * @param subject_path: 主题地址
   * @param path: 内容地址
   * @returns 数据
   */
  contentPv: async (subject_path: string, path: string): Promise<ContentLogVo> => await request.get(`${config.baseUrl.apiUrl}/cms/open/c/${subject_path}/${path}/pv`, { token: false, pageRole: false }),

}
