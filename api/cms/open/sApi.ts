
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 查询内容信息路径请求参数
 */
export interface CmsOpenSGetContentQueryParam {

  /**
   * 每页数量
   */
  rows?: number;
}


/**
 * [CmsCategoryStaticVo]cms分类显示路径请求参数
 */
export interface CmsCategoryStaticVo {

  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 排序值, 必须字段
   */
  ord?: number;
  /**
   * 访问地址, 最小长度：0, 最大长度：64
   */
  path?: string;
}

export default {

  /**
   * 查询内容信息
   * @param subject_path: 主题地址
   * @param cid_page: 主题地址，由两部分组成，{category_path}_{page}，categoryId可以为空，为空时所有
   * @param query 请求参数
   * @returns 数据
   */
  getContent: async (subject_path: string, cid_page: string, query: CmsOpenSGetContentQueryParam): Promise<string> => await request.get(`${config.baseUrl.apiUrl}/cms/open/s/${subject_path}/${cid_page}`, { params: query, token: false, pageRole: false }),


  /**
   * 查询分类信息
   * @param subject_path: 主题地址
   * @returns 数据
   */
  getCategory: async (subject_path: string): Promise<Array<CmsCategoryStaticVo>> => await request.get(`${config.baseUrl.apiUrl}/cms/open/s/${subject_path}/cats`, { token: false, pageRole: false }),

}
