import { request } from '@/utils/request'

import { config } from '../../config/global.config'

interface CmsCategoryListParam { }

export default {

  getListCount: async (subjectId: string, params: CmsCategoryListParam | undefined) => request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/category/count`, { params: params, token: true }),

  getList: async (subjectId: string, params: CmsCategoryListParam | undefined) => request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/category`, { params: params, token: true }),

  get: async (subjectId: string, id: string) => request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/category/${id}`, { token: true }),

  post: async (subjectId: string, body: any) => request.post(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/category`, body, { token: true }),

  put: async (subjectId: string, id: string, body: any) => request.put(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/category/${id}`, body, { token: true }),

  del: async (subjectId: string, id: string) => request.delete(`${config.baseUrl.apiUrl}/cms/mg/subject/${subjectId}/category/${id}`, { token: true }),


}

