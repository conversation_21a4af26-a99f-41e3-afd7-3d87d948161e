import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取列表路径请求参数
 */
export interface CmsMgSubjectGetListQueryParam {

  /**
   * keyword
   */
  keyword?: string;
}


/**
 * 获取列表数量路径请求参数
 */
export interface CmsMgSubjectGetListCountQueryParam {

  /**
   * keyword
   */
  keyword?: string;
}


/**
 * [CmsSubjectContentStructRestrictCodeValueBo]cms主题内容Restrict路径请求参数
 */
export interface CmsSubjectContentStructRestrictCodeValueBo {

  /**
   * code
   */
  code?: string;
  /**
   * name
   */
  name?: string;
}


/**
 * [CmsSubjectContentStructRestrictBo]cms主题内容Restrict路径请求参数
 */
export interface CmsSubjectContentStructRestrictBo {

  /**
   * 极限
   */
  极限?: number;
  /**
   * 枚举列表
   */
  枚举列表?: Array<CmsSubjectContentStructRestrictCodeValueBo>;
}


/**
 * [CmsSubjectContentStructListBo]cms主题内容List结构路径请求参数
 */
export interface CmsSubjectContentStructListBo {

  /**
   * [DataType]数据结构类型, 必须字段, 值：INT, STRING, FLOAT, BOOL, DATE, ENUM, LIST, MAP
   */
  dataType?: string;
  /**
   * 字段, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 是否是必须字段
   */
  require?: boolean;
  /**
   * 最大长度
   */
  maxSize?: number;
  /**
   * 最小长度
   */
  minSize?: number;
  /**
   * 值范围
   */
  enumValues?: Array<string>;
  /**
   * 控件内容
   */
  控件类型?: string;
  /**
   * 约束信息
   */
  约束信息?: CmsSubjectContentStructRestrictBo;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [CmsSubjectContentStructBo]cms主题内容结构路径请求参数
 */
export interface CmsSubjectContentStructBo {

  /**
   * [DataType]数据结构类型, 必须字段, 值：INT, STRING, FLOAT, BOOL, DATE, ENUM, LIST, MAP
   */
  dataType?: string;
  /**
   * 字段, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 是否是必须字段
   */
  require?: boolean;
  /**
   * 最大长度
   */
  maxSize?: number;
  /**
   * 最小长度
   */
  minSize?: number;
  /**
   * 若类型为LIST，则附带此子字段定义
   */
  childrens?: Array<CmsSubjectContentStructListBo>;
  /**
   * 值范围
   */
  enumValues?: Array<string>;
  /**
   * 控件内容
   */
  控件类型?: string;
  /**
   * 约束信息
   */
  约束信息?: CmsSubjectContentStructRestrictBo;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 在列表展示
   */
  show?: boolean;
  /**
   * 是否允许检索
   */
  index?: boolean;
  /**
   * index字段
   */
  indexCode?: string;
}


/**
 * [CmsSubjectDetailVo]cms主题详情路径请求参数
 */
export interface CmsSubjectDetailVo {

  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 主键
   */
  id?: string;
  /**
   * 父一级id
   */
  idParent?: string;
  /**
   * 更新时间
   */
  modifyTime?: number;
  /**
   * 主题名称
   */
  name?: string;
  /**
   * 排序
   */
  ord?: number;
  /**
   * 地址，支持：英文数字下划线
   */
  path?: string;
  /**
   * 默认每页数量
   */
  rowsDef?: number;
  /**
   * 备注信息
   */
  remark?: string;
  /**
   * 内容结构
   */
  contentStructs?: Array<CmsSubjectContentStructBo>;
}


/**
 * [CmsSubjectModifyVo]cms主题更新路径请求参数
 */
export interface CmsSubjectModifyVo {

  /**
   * 父一级id，根节点传0, 必须字段, 最小长度：0, 最大长度：32
   */
  idParent?: string;
  /**
   * 主题名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 排序, 必须字段
   */
  ord?: number;
  /**
   * 默认每页数量
   */
  rowsDef?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 内容结构, 必须字段
   */
  contentStructs?: Array<CmsSubjectContentStructBo>;
}


/**
 * [CmsSubject]网站主题路径请求参数
 */
export interface CmsSubject {

  /**
   * 内容结构定义, 最小长度：0, 最大长度：65535
   */
  contentStructJson?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 父一级id, 最小长度：0, 最大长度：32
   */
  idParent?: string;
  /**
   * 更新时间, 必须字段
   */
  modifyTime?: number;
  /**
   * 主题名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 排序, 必须字段
   */
  ord?: number;
  /**
   * 地址，支持：英文数字下划线, 必须字段, 最小长度：1, 最大长度：64
   */
  path?: string;
  /**
   * 默认每页数量
   */
  rowsDef?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [CmsSubjectAddVo]cms主题添加路径请求参数
 */
export interface CmsSubjectAddVo {

  /**
   * 父一级id，根节点传0, 最小长度：0, 最大长度：32
   */
  idParent?: string;
  /**
   * 主题名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 排序, 必须字段
   */
  ord?: number;
  /**
   * 地址，支持：英文数字下划线, 必须字段, 最小长度：1, 最大长度：64
   */
  path?: string;
  /**
   * 默认每页数量
   */
  rowsDef?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 内容结构, 必须字段
   */
  contentStructs?: Array<CmsSubjectContentStructBo>;
}

export default {

  /**
   * 获取详情
   * @param id: 主键
   * @returns 数据
   */
  get: async (id: string): Promise<CmsSubjectDetailVo> => await request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/${id}`, { token: true, pageRole: false }),


  /**
   * 更新网站主题
   * @param id: Id
   * @param body 请求Body参数
   */
  update: async (id: string, body: CmsSubjectModifyVo): Promise<void> => await request.put(`${config.baseUrl.apiUrl}/cms/mg/subject/${id}`, body, { token: true, pageRole: false }),


  /**
   * 删除网站主题
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => await request.delete(`${config.baseUrl.apiUrl}/cms/mg/subject/${id}`, { token: true, pageRole: false }),


  /**
   * 获取列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: CmsMgSubjectGetListQueryParam): Promise<Array<CmsSubject>> => await request.get(`${config.baseUrl.apiUrl}/cms/mg/subject`, { params: query, token: true, pageRole: false }),


  /**
   * 添加网站主题
   * @param body 请求Body参数
   */
  insert: async (body: CmsSubjectAddVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/cms/mg/subject`, body, { token: true, pageRole: false }),


  /**
   * 获取列表数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: CmsMgSubjectGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/cms/mg/subject/count`, { params: query, token: true, pageRole: false }),

}
