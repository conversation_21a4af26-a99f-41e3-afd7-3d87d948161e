
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [MallAddress]商城地址路径请求参数
 */
export interface MallAddress {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  userId?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 默认, 必须字段
   */
  def?: boolean;
  /**
   * 地区, 必须字段, 最小长度：1, 最大长度：32
   */
  regionCode?: string;
  /**
   * 用户名, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 详细地址, 必须字段, 最小长度：1, 最大长度：256
   */
  address?: string;
  /**
   * 联系方式, 必须字段, 最小长度：1, 最大长度：32
   */
  phone?: string;
  /**
   * 邮箱, 最小长度：0, 最大长度：32
   */
  email?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [MallAddressAddVo]商城地址添加新建内容路径请求参数
 */
export interface MallAddressAddVo {

  /**
   * 详细地址, 必须字段, 最小长度：1, 最大长度：256
   */
  address?: string;
  /**
   * 默认, 必须字段
   */
  def?: boolean;
  /**
   * 邮箱, 最小长度：0, 最大长度：32
   */
  email?: string;
  /**
   * 用户名, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 联系方式, 必须字段, 最小长度：1, 最大长度：32
   */
  phone?: string;
  /**
   * 地区, 必须字段, 最小长度：1, 最大长度：32
   */
  regionCode?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}

export default {

  /**
   * 查询商城地址
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<MallAddress> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/address/${id}`, { token: true }),


  /**
   * 更新商城地址
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: MallAddressAddVo): Promise<MallAddress> => request.put(`${config.baseUrl.apiUrl}/mall/shopper/address/${id}`, body, { token: true }),


  /**
   * 删除商城地址
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/mall/shopper/address/${id}`, { token: true }),


  /**
   * 设置为默认地址
   * @param id: Id
   */
  putDefault: async (id: string): Promise<void> => request.put(`${config.baseUrl.apiUrl}/mall/shopper/address/${id}/def`, {}, { token: true }),


  /**
   * 获取当前用户商城地址
   * @returns 数据
   */
  getList: async (): Promise<Array<MallAddress>> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/address`, { token: true }),


  /**
   * 添加商城地址
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: MallAddressAddVo): Promise<MallAddress> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/address`, body, { token: true }),

}
