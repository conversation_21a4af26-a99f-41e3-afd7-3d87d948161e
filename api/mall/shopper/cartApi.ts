
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取购物车路径请求参数
 */
export interface MallShopperCartGetListQueryParam {

  /**
   * 选中
   */
  selected?: boolean;
}


/**
 * [MallCartAddVo]购物车路径请求参数
 */
export interface MallCartAddVo {

  /**
   * 商品规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 选中状态
   */
  selected?: boolean;
  /**
   * 数量, 必须字段
   */
  num?: number;
}


/**
 * [MallCart]购物车路径请求参数
 */
export interface MallCart {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 商品名称, 最小长度：0, 最大长度：64
   */
  productName?: string;
  /**
   * 商品图片, 最小长度：0, 最大长度：512
   */
  productPic?: string;
  /**
   * 商品规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  userId?: string;
  /**
   * 选中, 必须字段
   */
  selected?: boolean;
  /**
   * 最后修改时间, 必须字段
   */
  modifyTime?: number;
}

export default {

  /**
   * 批量添加修改购物车
   * @param body 请求Body参数
   */
  batchPut: async (body: Array<MallCartAddVo>): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/cart/batch/put`, body, { token: true }),


  /**
   * 批量从购物车删除
   * @param body 请求Body参数
   */
  batchDelete: async (body: Array<string>): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/cart/batch/delete`, body, { token: true }),


  /**
   * 获取购物车
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallShopperCartGetListQueryParam): Promise<Array<MallCart>> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/cart`, { params: query, token: true }),

}
