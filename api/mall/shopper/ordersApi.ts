
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取订单表路径请求参数
 */
export interface MallShopperOrdersGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 状态
   */
  state?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * 获取订单表路径请求参数
 */
export interface MallShopperOrdersGetListCountQueryParam {

  /**
   * 状态
   */
  state?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * [OrdersReceiveVo]订单签收信息路径请求参数
 */
export interface OrdersReceiveVo {


}


/**
 * [OrdersCancelVo]订单取消信息路径请求参数
 */
export interface OrdersCancelVo {

  /**
   * 取消备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [OrdersSubmitConsigneeParamVo]收货地址路径请求参数
 */
export interface OrdersSubmitConsigneeParamVo {

  /**
   * 地区, 必须字段, 最小长度：1, 最大长度：32
   */
  regionCode?: string;
  /**
   * 用户名, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 详细地址, 必须字段, 最小长度：1, 最大长度：256
   */
  address?: string;
  /**
   * 联系方式, 必须字段, 最小长度：1, 最大长度：32
   */
  phone?: string;
  /**
   * 邮箱, 最小长度：0, 最大长度：32
   */
  email?: string;
}


/**
 * [OrdersSubmitParamVo]订单提交参数路径请求参数
 */
export interface OrdersSubmitProductParamVo {

  /**
   * 商品编号, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 商品数量, 必须字段
   */
  num?: number;
}


/**
 * [OrdersSubmitParamVo]订单提交参数路径请求参数
 */
export interface OrdersSubmitParamVo {

  /**
   * 是否允许预定, 必须字段
   */
  allowNegative?: boolean;
  /**
   * consignee, 必须字段
   */
  consignee?: OrdersSubmitConsigneeParamVo;
  /**
   * 商品信息
   */
  products?: Array<OrdersSubmitProductParamVo>;
  /**
   * 商品下单价，当进行提交订单动作时，不能为空
   */
  priceOrders?: number;
  /**
   * 采购人备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [OrdersSubmitResultOrdersVo]订单提交响应路径请求参数
 */
export interface OrdersSubmitResultOrdersVo {

  /**
   * 订单号
   */
  id?: string;
  /**
   * 父订单号，根订单号为0
   */
  idParent?: string;
  /**
   * 根订单id
   */
  idRootParent?: string;
  /**
   * 订单提交ID
   */
  ordersSubjectId?: string;
}


/**
 * [OrdersSubmitResultVo]订单提交响应路径请求参数
 */
export interface OrdersSubmitResultVo {

  /**
   * 订单信息
   */
  orderses?: Array<OrdersSubmitResultOrdersVo>;
}


/**
 * [OrdersListProductVo]订单商品路径请求参数
 */
export interface OrdersListProductVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 订单id, 必须字段, 最小长度：1, 最大长度：32
   */
  ordersId?: string;
  /**
   * 根订单id, 必须字段, 最小长度：1, 最大长度：32
   */
  ordersRootId?: string;
  /**
   * 订单商品编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 类型（BASE：基础商品，GIFT：赠品）, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 商品规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 商品SKU, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 商品SPU, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  goodsId?: string;
  /**
   * 商品规格编号, 必须字段, 最小长度：1, 最大长度：32
   */
  goodsSpaceId?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 店铺名称, 必须字段, 最小长度：1, 最大长度：64
   */
  storeName?: string;
  /**
   * 商品名称, 最小长度：0, 最大长度：64
   */
  productName?: string;
  /**
   * 图片, 最小长度：0, 最大长度：256
   */
  productPic?: string;
  /**
   * 商品类别id, 必须字段, 最小长度：1, 最大长度：32
   */
  categoryId?: string;
  /**
   * 商品名称, 最小长度：0, 最大长度：512
   */
  categoryNames?: string;
  /**
   * 品牌id, 必须字段, 最小长度：1, 最大长度：32
   */
  brandId?: string;
  /**
   * 品牌名称, 最小长度：0, 最大长度：64
   */
  brandName?: string;
  /**
   * 原始金额, 必须字段
   */
  priceOriginal?: number;
  /**
   * 优惠总金额, 必须字段
   */
  priceDiscount?: number;
  /**
   * 店铺优惠金额, 必须字段
   */
  priceDiscountStore?: number;
  /**
   * 平台优惠金额, 必须字段
   */
  priceDiscountPlatform?: number;
  /**
   * 下单金额（实际下单额度）, 必须字段
   */
  priceOrders?: number;
  /**
   * 原始单价, 必须字段
   */
  unitPriceOriginal?: number;
  /**
   * 下单单价, 必须字段
   */
  unitPriceOrders?: number;
  /**
   * 商品数量, 必须字段
   */
  num?: number;
}


/**
 * [OrdersListVo]订单表路径请求参数
 */
export interface OrdersListVo {

  /**
   * 订单号, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 父订单号，根订单号为0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 根订单id, 必须字段, 最小长度：1, 最大长度：32
   */
  idRootParent?: string;
  /**
   * 订单提交ID, 必须字段, 最小长度：1, 最大长度：32
   */
  ordersSubjectId?: string;
  /**
   * 已被拆单，（无商品挂载）, 必须字段
   */
  split?: boolean;
  /**
   * 提交IP, 最小长度：0, 最大长度：32
   */
  deviceIp?: string;
  /**
   * 地理位置, 最小长度：0, 最大长度：64
   */
  deviceAddress?: string;
  /**
   * 商城编号, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 商城名称, 必须字段, 最小长度：1, 最大长度：64
   */
  mallName?: string;
  /**
   * 用户类型, 必须字段, 最小长度：1, 最大长度：32
   */
  shopperUserType?: string;
  /**
   * 采购人id, 必须字段, 最小长度：1, 最大长度：32
   */
  shopperUserId?: string;
  /**
   * 用户名称, 最小长度：0, 最大长度：64
   */
  shopperUserName?: string;
  /**
   * 采购人备注, 最小长度：0, 最大长度：512
   */
  shopperRemark?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 店铺名称, 必须字段, 最小长度：1, 最大长度：64
   */
  storeName?: string;
  /**
   * 收货人名称, 必须字段, 最小长度：1, 最大长度：64
   */
  consigneeName?: string;
  /**
   * 收货人联系方式, 必须字段, 最小长度：1, 最大长度：32
   */
  consigneePhone?: string;
  /**
   * 收货人邮箱, 最小长度：0, 最大长度：32
   */
  consigneeEmail?: string;
  /**
   * 收货人地址信息, 必须字段, 最小长度：1, 最大长度：32
   */
  consigneeRegionCode?: string;
  /**
   * 收货人详细地址, 必须字段, 最小长度：1, 最大长度：256
   */
  consigneeAddress?: string;
  /**
   * 收货人完整详细地址, 最小长度：0, 最大长度：512
   */
  consigneeAddressDetail?: string;
  /**
   * 采购人运输金额, 必须字段
   */
  priceFreight?: number;
  /**
   * 原始金额（原始商品加运费总额）, 必须字段
   */
  priceOriginal?: number;
  /**
   * 优惠总金额, 必须字段
   */
  priceDiscount?: number;
  /**
   * 店铺优惠金额, 必须字段
   */
  priceDiscountStore?: number;
  /**
   * 平台优惠金额, 必须字段
   */
  priceDiscountPlatform?: number;
  /**
   * 下单金额（实际下单额度）, 必须字段
   */
  priceOrders?: number;
  /**
   * 下单时间, 必须字段
   */
  createTime?: number;
  /**
   * 下单时间, 必须字段
   */
  submitTime?: number;
  /**
   * 订单状态（NEW：新订单、PAY：支付完成、DELIVERY：已发货、RECEIVE：已收货、FINISH：完成、CANCEL：取消）, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 支付完成, 必须字段
   */
  pay?: boolean;
  /**
   * 支付金额
   */
  payPrice?: number;
  /**
   * 支付时间
   */
  payTime?: number;
  /**
   * 支付单号, 最小长度：0, 最大长度：32
   */
  payOrdersId?: string;
  /**
   * 支付账单号, 最小长度：0, 最大长度：32
   */
  payBillId?: string;
  /**
   * 支付平台流水号, 最小长度：0, 最大长度：32
   */
  payPlatformNo?: string;
  /**
   * 发货标识, 必须字段
   */
  delivery?: boolean;
  /**
   * 发货时间
   */
  deliveryTime?: number;
  /**
   * 物流公司名称, 最小长度：0, 最大长度：64
   */
  deliveryLogistics?: string;
  /**
   * 物流单号, 最小长度：0, 最大长度：64
   */
  deliveryNo?: string;
  /**
   * 收货标识, 必须字段
   */
  receive?: boolean;
  /**
   * 收货时间
   */
  receiveTime?: number;
  /**
   * 完成标识, 必须字段
   */
  finish?: boolean;
  /**
   * 完成时间
   */
  finishTime?: number;
  /**
   * 取消标识, 必须字段
   */
  cancel?: boolean;
  /**
   * 取消时间
   */
  cancelTime?: number;
  /**
   * 子订单
   */
  childrenOrders?: Array<OrdersListVo>;
  /**
   * 商品信息
   */
  products?: Array<OrdersListProductVo>;
}


/**
 * [OrdersPayRequestResultVo]订单支付信息路径请求参数
 */
export interface OrdersPayRequestResultVo {

  /**
   * 订单号
   */
  id?: string;
  /**
   * 支付单号
   */
  payOrdersId?: string;
}

export default {

  /**
   * 订单签收信息
   * @param id: 订单id
   * @param body 请求Body参数
   */
  receive: async (id: string, body: OrdersReceiveVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/orders/${id}/receive`, body, { token: true }),


  /**
   * 订单取消信息
   * @param id: 订单id
   * @param body 请求Body参数
   */
  cancel: async (id: string, body: OrdersCancelVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/orders/${id}/cancel`, body, { token: true }),


  /**
   * 提交订单
   * @param body 请求Body参数
   * @returns 数据
   */
  submit: async (body: OrdersSubmitParamVo): Promise<OrdersSubmitResultVo> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/orders/submit`, body, { token: true }),


  /**
   * 准备提交订单
   * @param body 请求Body参数
   * @returns 数据
   */
  ready: async (body: OrdersSubmitParamVo): Promise<OrdersSubmitResultVo> => await request.post(`${config.baseUrl.apiUrl}/mall/shopper/orders/ready`, body, { token: true, pageRole: false }),


  /**
   * 获取订单表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallShopperOrdersGetListQueryParam): Promise<Array<OrdersListVo>> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/orders`, { params: query, token: true }),


  /**
   * 获取订单支付信息
   * @param id: 订单id
   * @returns 数据
   */
  payRequest: async (id: string): Promise<OrdersPayRequestResultVo> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/orders/${id}/pay-req`, { token: true }),


  /**
   * 获取订单表
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: MallShopperOrdersGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/orders/count`, { params: query, token: true }),

}
