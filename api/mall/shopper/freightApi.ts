
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取当前供应商运费信息路径请求参数
 */
export interface MallShopperFreightGetListQueryParam {

  /**
   * 供应商ID, 必须参数
   */
  storeId?: Array<string>;
}


/**
 * [MallFreightContentRangBo]运费阶梯内容路径请求参数
 */
export interface MallFreightContentBo$MallFreightContentRangBo {

  /**
   * 运费, 必须字段
   */
  price?: number;
  /**
   * 最低重量, 必须字段
   */
  weight?: number;
  /**
   * 最低体积（cm^3）, 必须字段
   */
  volume?: number;
}


/**
 * [MallFreightContentBo]运费内容路径请求参数
 */
export interface MallFreightContentBo {

  /**
   * 类型, 必须字段
   */
  type?: string;
  /**
   * 运费, 必须字段
   */
  price?: number;
  /**
   * 范围, 必须字段
   */
  ranges?: Array<MallFreightContentBo$MallFreightContentRangBo>;
}


/**
 * [MallFreightBo]运费信息路径请求参数
 */
export interface MallFreightBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 供应商id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 默认, 必须字段
   */
  def?: boolean;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * content, 必须字段
   */
  content?: MallFreightContentBo;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
}

export default {

  /**
   * 获取当前供应商运费信息
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallShopperFreightGetListQueryParam): Promise<Array<MallFreightBo>> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/freight`, { params: query, token: true }),

}
