
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [MallGoodsListSpaceVo]商品sku信息路径请求参数
 */
export interface MallGoodsListSpaceVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 默认商品图片, 最小长度：0, 最大长度：256
   */
  pic?: string;
  /**
   * 可销售, 必须字段
   */
  selling?: boolean;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * sku, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 排序编号
   */
  ord?: number;
  /**
   * 重量，单位：kg, 必须字段
   */
  baseWeight?: number;
  /**
   * 体积信息，单位：cm（10*30*100）, 必须字段, 最小长度：1, 最大长度：32
   */
  baseVolume?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：32
   */
  remark?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 物品id, 必须字段, 最小长度：1, 最大长度：32
   */
  goodsId?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 商品规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 店铺id, 最小长度：0, 最大长度：32
   */
  storeId?: string;
}


/**
 * [MallGoodsListVo]商品信息路径请求参数
 */
export interface MallGoodsListVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 商品类型, 最小长度：0, 最大长度：32
   */
  categoryId?: string;
  /**
   * 分类集合, 最小长度：0, 最大长度：512
   */
  categoryIds?: string;
  /**
   * 分类名称, 最小长度：0, 最大长度：512
   */
  categoryNames?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * spu, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 商品规格id，逗号,分隔, 最小长度：0, 最大长度：2048
   */
  spaceIds?: string;
  /**
   * 品牌id, 必须字段, 最小长度：1, 最大长度：32
   */
  brandId?: string;
  /**
   * 品牌名称, 必须字段, 最小长度：1, 最大长度：64
   */
  brandName?: string;
  /**
   * 商品上架状态, 必须字段
   */
  up?: boolean;
  /**
   * 上架时间
   */
  upTime?: number;
  /**
   * 不同规格数据
   */
  spaces?: Array<MallGoodsListSpaceVo>;
}


/**
 * [MallProductPropertyBo]商品规格路径请求参数
 */
export interface MallProductPropertyBo {

  /**
   * 属性code, 必须字段, 最小长度：0, 最大长度：32
   */
  code?: string;
  /**
   * 属性名, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 属性组名, 必须字段, 最小长度：1, 最大长度：64
   */
  groupName?: string;
  /**
   * 值, 必须字段, 最小长度：1, 最大长度：64
   */
  value?: string;
}


/**
 * [MallGoodsDetailSpaceVo]商品新增规格（SKU）路径请求参数
 */
export interface MallGoodsDetailSpaceVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 默认商品图片, 最小长度：0, 最大长度：256
   */
  pic?: string;
  /**
   * 可销售, 必须字段
   */
  selling?: boolean;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * sku, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 排序编号
   */
  ord?: number;
  /**
   * 重量，单位：kg, 必须字段
   */
  baseWeight?: number;
  /**
   * 体积信息，单位：cm（10*30*100）, 必须字段, 最小长度：1, 最大长度：32
   */
  baseVolume?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：32
   */
  remark?: string;
  /**
   * 商品规格属性
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 物品id, 必须字段, 最小长度：1, 最大长度：32
   */
  goodsId?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 商品规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 店铺id, 最小长度：0, 最大长度：32
   */
  storeId?: string;
}


/**
 * [MallGoodsDetailVo]商品信息路径请求参数
 */
export interface MallGoodsDetailVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 移动版详情, 最小长度：0, 最大长度：65535
   */
  contentMobile?: string;
  /**
   * PC版详情, 必须字段, 最小长度：1, 最大长度：65535
   */
  contentPc?: string;
  /**
   * 商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * 商品分类, 必须字段, 最小长度：1, 最大长度：32
   */
  categoryId?: string;
  /**
   * 商品规格id，逗号,分隔, 最小长度：0, 最大长度：2048
   */
  spaceIds?: string;
  /**
   * 品牌名称, 必须字段, 最小长度：1, 最大长度：64
   */
  brandName?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * spu, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 修改时间, 必须字段
   */
  modifyTime?: number;
  /**
   * 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 标签，逗号分隔, 最小长度：0, 最大长度：2048
   */
  tag?: string;
  /**
   * 商品上架状态, 必须字段
   */
  up?: boolean;
  /**
   * 上架时间
   */
  upTime?: number;
  /**
   * 不同规格数据
   */
  spaces?: Array<MallGoodsDetailSpaceVo>;
}


/**
 * [MallGoodsSimilarValueVo]相似商品信息路径请求参数
 */
export interface MallGoodsSimilarValueVo {

  /**
   * 属性值
   */
  value?: string;
  /**
   * 对应的spaceId列表
   */
  spaceIds?: Array<string>;
}


/**
 * [MallGoodsSimilarVo]相似商品信息路径请求参数
 */
export interface MallGoodsSimilarVo {

  /**
   * 属性编号
   */
  code?: string;
  /**
   * 属性分组
   */
  groupName?: string;
  /**
   * 属性名称
   */
  name?: string;
  /**
   * 属性值列表
   */
  values?: Array<MallGoodsSimilarValueVo>;
}

export default {

  /**
   * 通过SpaceId获取商品信息
   * @param body 请求Body参数
   * @returns 数据
   */
  getListBySpaceIds: async (body: Array<string>): Promise<Array<MallGoodsListVo>> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/goods/space/batch/list-by-ids`, body, { token: true }),


  /**
   * 通过SpaceId获取商品详情
   * @param spaceId: 规格id
   * @returns 数据
   */
  getDetailBySpaceId: async (spaceId: string): Promise<MallGoodsDetailVo> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/goods/space/${spaceId}`, { token: true }),


  /**
   * 通过SpaceId获取商品详情
   * @param spaceId: 规格id
   * @returns 数据
   */
  getSimilarBySpaceId: async (spaceId: string): Promise<Array<MallGoodsSimilarVo>> => request.get(`${config.baseUrl.apiUrl}/mall/shopper/goods/space/${spaceId}/similar`, { token: true }),

}
