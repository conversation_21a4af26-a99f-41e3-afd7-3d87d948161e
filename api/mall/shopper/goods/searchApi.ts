
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [GoodsSearchParamVo]商品搜索参数路径请求参数
 */
export interface GoodsSearchParamVo {

  /**
   * rows
   */
  rows?: number;
  /**
   * page
   */
  page?: number;
  /**
   * sort
   */
  sort?: string;
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 分类id
   */
  categoryId?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * [MallGoodsSearchListVo]商品信息路径请求参数
 */
export interface MallGoodsSearchListVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档建议, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档操作人id, 最小长度：0, 最大长度：32
   */
  archivedUserId?: string;
  /**
   * 商品类型, 最小长度：0, 最大长度：32
   */
  categoryId?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * spu, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 修改时间, 必须字段
   */
  modifyTime?: number;
  /**
   * 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 状态, NEW、UP、ARCHIVED, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 商品规格id，逗号,分隔, 最小长度：0, 最大长度：2048
   */
  spaceIds?: string;
  /**
   * 品牌名称, 必须字段, 最小长度：1, 最大长度：64
   */
  brandName?: string;
  /**
   * 商品上架状态, 必须字段
   */
  up?: boolean;
  /**
   * 上架意见, 最小长度：0, 最大长度：256
   */
  upRemark?: string;
  /**
   * 上架申请, 必须字段
   */
  upSubmit?: boolean;
  /**
   * 上架申请理由, 最小长度：0, 最大长度：512
   */
  upSubmitRemark?: string;
  /**
   * 上架申请
   */
  upSubmitTime?: number;
  /**
   * 上架申请人, 最小长度：0, 最大长度：32
   */
  upSubmitUserId?: string;
  /**
   * 上架时间
   */
  upTime?: number;
  /**
   * 上架操作人id, 最小长度：0, 最大长度：32
   */
  upUserId?: string;
  /**
   * 最低单价, 必须字段
   */
  unitPriceMin?: number;
  /**
   * 最低订单价格, 必须字段
   */
  unitPriceOrdersMin?: number;
  /**
   * 最低显示价格
   */
  unitPriceShowMin?: number;
}


/**
 * [GoodsSearchResultVo]商品搜索结果路径请求参数
 */
export interface GoodsSearchResultVo {

  /**
   * 总数
   */
  total?: number;
  /**
   * 商品信息
   */
  goods?: Array<MallGoodsSearchListVo>;
}

export default {

  /**
   * 搜索商品
   * @param body 请求Body参数
   * @returns 数据
   */
  search: async (body: GoodsSearchParamVo): Promise<GoodsSearchResultVo> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/goods/search`, body, { token: true }),

}
