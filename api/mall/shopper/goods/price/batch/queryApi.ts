
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [GoodsPriceParamVo]商品价格查询路径请求参数
 */
export interface GoodsPriceParamVo {

  /**
   * 商品规格id信息, 必须字段
   */
  spaceIds?: Array<string>;
}


/**
 * [GoodsPriceInfo]商品价格信息路径请求参数
 */
export interface GoodsPriceInfo {

  /**
   * 价格, 必须字段
   */
  unitPrice?: number;
  /**
   * 展示价格, 必须字段
   */
  unitPriceShow?: number;
  /**
   * 下单价格, 必须字段
   */
  unitPriceOrders?: number;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  spaceId?: string;
}

export default {

  /**
   * 通过SpaceId查询商品库存
   * @param body 请求Body参数
   * @returns 数据
   */
  getDetailBySpaceId: async (body: GoodsPriceParamVo): Promise<Array<GoodsPriceInfo>> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/goods/price/batch/query`, body, { token: true }),

}
