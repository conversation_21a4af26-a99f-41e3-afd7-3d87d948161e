
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [GoodsStockParamSpaceNumVo]商品库存商品数量信息查询路径请求参数
 */
export interface GoodsStockParamSpaceNumVo {

  /**
   * spaceId, 必须字段, 最小长度：1, 最大长度：32
   */
  spaceId?: string;
  /**
   * 库存, 必须字段
   */
  num?: number;
}


/**
 * [GoodsStockParamVo]商品库存查询路径请求参数
 */
export interface GoodsStockParamVo {

  /**
   * 商品数量信息, 必须字段
   */
  spaceNumInfos?: Array<GoodsStockParamSpaceNumVo>;
  /**
   * 地理位置
   */
  regionCode?: string;
}


/**
 * [GoodsStockInfo]商品库存信息路径请求参数
 */
export interface GoodsStockInfo {

  /**
   * 库存类型, NO_GOODS、OK、INCOMING、NO
   */
  stockType?: string;
  /**
   * 库存数，低于100为实际库存，大于100库存为null, 必须字段
   */
  quantity?: number;
  /**
   * 允许预定, 必须字段
   */
  allowNegative?: boolean;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  spaceId?: string;
  /**
   * 仓库，为空时默认仓库
   */
  depotId?: string;
  /**
   * 准备购买量, 必须字段
   */
  num?: number;
}

export default {

  /**
   * 通过SpaceId查询商品库存
   * @param body 请求Body参数
   * @returns 数据
   */
  getDetailBySpaceId: async (body: GoodsStockParamVo): Promise<Array<GoodsStockInfo>> => request.post(`${config.baseUrl.apiUrl}/mall/shopper/goods/stock/batch/query`, body, { token: true }),

}
