
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取商品列表路径请求参数
 */
export interface MallMgProductGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 排序字段
   */
  sort?: string;
  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 供应商Id
   */
  storeId?: string;
  /**
   * 商品状态字段
   */
  state?: Array<string>;
  /**
   * 商品分类id
   */
  categoryId?: string;
  /**
   * 品牌id
   */
  brandId?: string;
}


/**
 * 获取商品数量路径请求参数
 */
export interface MallMgProductGetListCountQueryParam {

  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 供应商Id
   */
  storeId?: string;
  /**
   * 商品状态字段
   */
  state?: Array<string>;
  /**
   * 商品分类id
   */
  categoryId?: string;
  /**
   * 品牌id
   */
  brandId?: string;
}


/**
 * [MallProductPropertyBo]商品规格路径请求参数
 */
export interface MallProductPropertyBo {

  /**
   * 属性code, 必须字段, 最小长度：0, 最大长度：32
   */
  code?: string;
  /**
   * 属性名, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 属性组名, 必须字段, 最小长度：1, 最大长度：64
   */
  groupName?: string;
  /**
   * 值, 必须字段, 最小长度：1, 最大长度：64
   */
  value?: string;
}


/**
 * [ProductDetailSpaceVo]商品新增规格（SKU）路径请求参数
 */
export interface MallProductDetailSpaceVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 默认商品图片, 最小长度：0, 最大长度：256
   */
  pic?: string;
  /**
   * 可销售, 必须字段
   */
  selling?: boolean;
  /**
   * 单价, 必须字段
   */
  unitPrice?: number;
  /**
   * 显示价格
   */
  unitPriceShow?: number;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * sku, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 重量，单位：kg, 必须字段
   */
  baseWeight?: number;
  /**
   * 体积信息，单位：cm（10*30*100）, 必须字段, 最小长度：1, 最大长度：32
   */
  baseVolume?: string;
  /**
   * 排序编号
   */
  ord?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：32
   */
  remark?: string;
  /**
   * 共享库存数量, 必须字段
   */
  quantity?: number;
  /**
   * 商品规格属性
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 店铺id, 最小长度：0, 最大长度：32
   */
  storeId?: string;
}


/**
 * [ProductDetailVo]商品信息路径请求参数
 */
export interface MallProductDetailVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 标签，逗号分隔, 最小长度：0, 最大长度：2048
   */
  tag?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 移动版详情, 最小长度：0, 最大长度：65535
   */
  contentMobile?: string;
  /**
   * PC版详情, 必须字段, 最小长度：1, 最大长度：65535
   */
  contentPc?: string;
  /**
   * 商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档建议, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档操作人id, 最小长度：0, 最大长度：32
   */
  archivedUserId?: string;
  /**
   * 商品分类, 必须字段, 最小长度：1, 最大长度：32
   */
  categoryId?: string;
  /**
   * 品牌Id, 必须字段, 最小长度：1, 最大长度：32
   */
  brandId?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * spu, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 状态, NEW、UP、ARCHIVED, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 修改时间, 必须字段
   */
  modifyTime?: number;
  /**
   * 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 商品上架状态, 必须字段
   */
  up?: boolean;
  /**
   * 上架意见, 最小长度：0, 最大长度：256
   */
  upRemark?: string;
  /**
   * 上架时间
   */
  upTime?: number;
  /**
   * 上架操作人id, 最小长度：0, 最大长度：32
   */
  upUserId?: string;
  /**
   * 不同规格数据
   */
  spaces?: Array<MallProductDetailSpaceVo>;
}


/**
 * [ProductModifySpaceVo]商品修改规格（SKU）路径请求参数
 */
export interface MallProductModifySpaceVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 默认商品图片, 最小长度：0, 最大长度：256
   */
  pic?: string;
  /**
   * 可销售, 必须字段
   */
  selling?: boolean;
  /**
   * 单价, 必须字段
   */
  unitPrice?: number;
  /**
   * 显示价格
   */
  unitPriceShow?: number;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * sku, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 重量，单位：kg, 必须字段
   */
  baseWeight?: number;
  /**
   * 体积信息，单位：cm（10*30*100）, 必须字段, 最小长度：1, 最大长度：32
   */
  baseVolume?: string;
  /**
   * 排序编号
   */
  ord?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：32
   */
  remark?: string;
  /**
   * 共享库存数量, 必须字段
   */
  quantity?: number;
  /**
   * 商品规格属性
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * Id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
}


/**
 * [ProductModifyVo]商品信息（SPU）路径请求参数
 */
export interface MallProductModifyVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 标记信息, 最小长度：0, 最大长度：1024
   */
  tag?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 移动版详情, 最小长度：0, 最大长度：65535
   */
  contentMobile?: string;
  /**
   * PC版详情, 必须字段, 最小长度：1, 最大长度：65535
   */
  contentPc?: string;
  /**
   * 商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * 不同规格数据, 必须字段
   */
  spaces?: Array<MallProductModifySpaceVo>;
}


/**
 * [ProductListSpaceVo]商品sku信息路径请求参数
 */
export interface MallProductListSpaceVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 默认商品图片, 最小长度：0, 最大长度：256
   */
  pic?: string;
  /**
   * 可销售, 必须字段
   */
  selling?: boolean;
  /**
   * 单价, 必须字段
   */
  unitPrice?: number;
  /**
   * 显示价格
   */
  unitPriceShow?: number;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * sku, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 重量，单位：kg, 必须字段
   */
  baseWeight?: number;
  /**
   * 体积信息，单位：cm（10*30*100）, 必须字段, 最小长度：1, 最大长度：32
   */
  baseVolume?: string;
  /**
   * 排序编号
   */
  ord?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：32
   */
  remark?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productId?: string;
  /**
   * 店铺id, 最小长度：0, 最大长度：32
   */
  storeId?: string;
  /**
   * 快照Id, 最小长度：0, 最大长度：32
   */
  auxSnapshotId?: string;
}


/**
 * [ProductListVo]商品信息路径请求参数
 */
export interface MallProductListVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 标记信息, 最小长度：0, 最大长度：1024
   */
  tag?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档建议, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档操作人id, 最小长度：0, 最大长度：32
   */
  archivedUserId?: string;
  /**
   * 商品类型, 最小长度：0, 最大长度：32
   */
  categoryId?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * spu, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 修改时间, 必须字段
   */
  modifyTime?: number;
  /**
   * 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 状态, NEW、UP、ARCHIVED, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 品牌Id, 必须字段, 最小长度：1, 最大长度：32
   */
  brandId?: string;
  /**
   * 商品上架状态, 必须字段
   */
  up?: boolean;
  /**
   * 上架意见, 最小长度：0, 最大长度：256
   */
  upRemark?: string;
  /**
   * 上架时间
   */
  upTime?: number;
  /**
   * 上架操作人id, 最小长度：0, 最大长度：32
   */
  upUserId?: string;
  /**
   * 不同规格数据
   */
  spaces?: Array<MallProductListSpaceVo>;
}


/**
 * [ProductAddSpaceVo]商品新增规格（SKU）路径请求参数
 */
export interface MallProductAddSpaceVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 默认商品图片, 最小长度：0, 最大长度：256
   */
  pic?: string;
  /**
   * 可销售, 必须字段
   */
  selling?: boolean;
  /**
   * 单价, 必须字段
   */
  unitPrice?: number;
  /**
   * 显示价格
   */
  unitPriceShow?: number;
  /**
   * 商品upc条码, 最小长度：0, 最大长度：32
   */
  upc?: string;
  /**
   * sku, 最小长度：0, 最大长度：32
   */
  sku?: string;
  /**
   * 重量，单位：kg, 必须字段
   */
  baseWeight?: number;
  /**
   * 体积信息，单位：cm（10*30*100）, 必须字段, 最小长度：1, 最大长度：32
   */
  baseVolume?: string;
  /**
   * 排序编号
   */
  ord?: number;
  /**
   * 备注信息, 最小长度：0, 最大长度：32
   */
  remark?: string;
  /**
   * 共享库存数量, 必须字段
   */
  quantity?: number;
  /**
   * 商品规格属性
   */
  properties?: Array<MallProductPropertyBo>;
}


/**
 * [ProductAddVo]商品信息（SPU）路径请求参数
 */
export interface MallProductAddVo {

  /**
   * SPU信息, 最小长度：0, 最大长度：32
   */
  spu?: string;
  /**
   * 名称, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 标记信息, 最小长度：0, 最大长度：1024
   */
  tag?: string;
  /**
   * 图片信息, 必须字段, 最小长度：0, 最大长度：1024
   */
  pic?: string;
  /**
   * 视频信息, 最小长度：0, 最大长度：1024
   */
  video?: string;
  /**
   * 移动版详情, 最小长度：0, 最大长度：65535
   */
  contentMobile?: string;
  /**
   * PC版详情, 必须字段, 最小长度：1, 最大长度：65535
   */
  contentPc?: string;
  /**
   * 商品通用属性,json格式：[{name:cpu,value:i7, code:cpu}]
   */
  properties?: Array<MallProductPropertyBo>;
  /**
   * 商品类型：BASE：基础商品, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 商品分类, 必须字段, 最小长度：1, 最大长度：32
   */
  categoryId?: string;
  /**
   * 品牌Id, 必须字段, 最小长度：1, 最大长度：32
   */
  brandId?: string;
  /**
   * 不同规格数据, 必须字段
   */
  spaces?: Array<MallProductAddSpaceVo>;
}


/**
 * [ProductUpSubmitVo]商品上下架申请路径请求参数
 */
export interface MallProductBatchUpVo {

  /**
   * 操作的商品id, 必须字段
   */
  ids?: Array<string>;
  /**
   * 上架时上架到的商城，上架时必传参数, 必须字段
   */
  mallIds?: Array<string>;
  /**
   * 操作, 必须字段
   */
  operate?: string;
  /**
   * 备注信息
   */
  remark?: string;
}


/**
 * [MallProductBatchArchivedVo]商品归档路径请求参数
 */
export interface MallProductBatchArchivedVo {

  /**
   * 操作的商品id, 必须字段
   */
  ids?: Array<string>;
  /**
   * 是否归档, 必须字段
   */
  archived?: boolean;
  /**
   * 备注信息
   */
  remark?: string;
}

export default {

  /**
   * 获取商品详情
   * @param id: id
   * @returns 数据
   */
  get: async (id: string): Promise<MallProductDetailVo> => request.get(`${config.baseUrl.apiUrl}/mall/mg/product/${id}`, { token: true }),


  /**
   * 修改商品信息
   * @param id: 商品Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: MallProductModifyVo): Promise<MallProductDetailVo> => request.put(`${config.baseUrl.apiUrl}/mall/mg/product/${id}`, body, { token: true }),


  /**
   * 获取商品列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallMgProductGetListQueryParam): Promise<Array<MallProductListVo>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/product`, { params: query, token: true }),


  /**
   * 添加商品信息
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: MallProductAddVo): Promise<MallProductDetailVo> => request.post(`${config.baseUrl.apiUrl}/mall/mg/product`, body, { token: true }),


  /**
   * 批量上下架审核
   * @param body 请求Body参数
   */
  batchUp: async (body: MallProductBatchUpVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/mg/product/batch/up`, body, { token: true }),


  /**
   * 批量商品归档
   * @param body 请求Body参数
   */
  batchArchived: async (body: MallProductBatchArchivedVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/mg/product/batch/archived`, body, { token: true }),


  /**
   * 获取商品数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: MallMgProductGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/mall/mg/product/count`, { params: query, token: true }),

}
