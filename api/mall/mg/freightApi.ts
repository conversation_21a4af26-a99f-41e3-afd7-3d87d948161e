
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取当前供应商运费信息路径请求参数
 */
export interface MallMgFreightGetListQueryParam {

  /**
   * 商城ID, 必须参数
   */
  mallId?: string;
}


/**
 * [MallFreightContentRangBo]运费阶梯内容路径请求参数
 */
export interface MallFreightContentBo$MallFreightContentRangBo {

  /**
   * 运费, 必须字段
   */
  price?: number;
  /**
   * 最低重量, 必须字段
   */
  weight?: number;
  /**
   * 最低体积（cm^3）, 必须字段
   */
  volume?: number;
}


/**
 * [MallFreightContentBo]运费内容路径请求参数
 */
export interface MallFreightContentBo {

  /**
   * 类型, 必须字段
   */
  type?: string;
  /**
   * 运费, 必须字段
   */
  price?: number;
  /**
   * 范围, 必须字段
   */
  ranges?: Array<MallFreightContentBo$MallFreightContentRangBo>;
}


/**
 * [MallFreightBo]运费信息路径请求参数
 */
export interface MallFreightBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 供应商id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 默认, 必须字段
   */
  def?: boolean;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * content, 必须字段
   */
  content?: MallFreightContentBo;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [MallFreightModifyVo]运费信息修改内容路径请求参数
 */
export interface MallFreightModifyVo {

  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 默认, 必须字段
   */
  def?: boolean;
  /**
   * content, 必须字段
   */
  content?: MallFreightContentBo;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [MallFreightAddVo]运费信息添加新建内容路径请求参数
 */
export interface MallFreightAddVo {

  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 默认, 必须字段
   */
  def?: boolean;
  /**
   * content, 必须字段
   */
  content?: MallFreightContentBo;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 商城id, 必须字段, 最小长度：1, 最大长度：32
   */
  mallId?: string;
}

export default {

  /**
   * 查询商城运费信息
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<MallFreightBo> => request.get(`${config.baseUrl.apiUrl}/mall/mg/freight/${id}`, { token: true }),


  /**
   * 更新商城运费信息
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: MallFreightModifyVo): Promise<MallFreightBo> => request.put(`${config.baseUrl.apiUrl}/mall/mg/freight/${id}`, body, { token: true }),


  /**
   * 删除运费信息
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/mall/mg/freight/${id}`, { token: true }),


  /**
   * 设置为默认运费信息
   * @param id: Id
   */
  putDefault: async (id: string): Promise<void> => request.put(`${config.baseUrl.apiUrl}/mall/mg/freight/${id}/def`, {}, { token: true }),


  /**
   * 获取当前供应商运费信息
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallMgFreightGetListQueryParam): Promise<Array<MallFreightBo>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/freight`, { params: query, token: true }),


  /**
   * 添加商城运费信息
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: MallFreightAddVo): Promise<MallFreightBo> => request.post(`${config.baseUrl.apiUrl}/mall/mg/freight`, body, { token: true }),

}
