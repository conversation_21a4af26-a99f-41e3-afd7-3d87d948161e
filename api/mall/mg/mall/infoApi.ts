
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [MallInfo]商城信息路径请求参数
 */
export interface MallInfo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [MallInfoAddVo]商城信息路径请求参数
 */
export interface MallInfoAddVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
}

export default {

  /**
   * 查询商城信息
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<MallInfo> => request.get(`${config.baseUrl.apiUrl}/mall/mg/mall/info/${id}`, { token: true }),


  /**
   * 更新商城信息
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: MallInfoAddVo): Promise<MallInfo> => request.put(`${config.baseUrl.apiUrl}/mall/mg/mall/info/${id}`, body, { token: true }),


  /**
   * 获取商城信息
   * @returns 数据
   */
  getAllList: async (): Promise<Array<MallInfo>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/mall/info`, { token: true }),


  /**
   * 添加商城信息
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: MallInfoAddVo): Promise<MallInfo> => request.post(`${config.baseUrl.apiUrl}/mall/mg/mall/info`, body, { token: true }),

}
