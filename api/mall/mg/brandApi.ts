
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取品牌管理路径请求参数
 */
export interface MallMgBrandGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 禁用
   */
  ban?: boolean;
  /**
   * id查询
   */
  id?: Array<string>;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * 获取品牌管理路径请求参数
 */
export interface MallMgBrandGetListCountQueryParam {

  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 禁用
   */
  ban?: boolean;
  /**
   * id查询
   */
  id?: Array<string>;
}


/**
 * [MallBrand]品牌管理路径请求参数
 */
export interface MallBrand {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 品牌名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 品牌别名，多个使用,分割
   */
  aliasName?: string;
  /**
   * Logo图片
   */
  logoImg?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 备注, 最小长度：0, 最大长度：4096
   */
  remark?: string;
}


/**
 * [MallBrandAddVo]品牌信息路径请求参数
 */
export interface MallBrandAddVo {

  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 品牌别名，多个使用,分割
   */
  aliasName?: string;
  /**
   * Logo图片
   */
  logoImg?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}

export default {

  /**
   * 查询品牌管理
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<MallBrand> => request.get(`${config.baseUrl.apiUrl}/mall/mg/brand/${id}`, { token: true }),


  /**
   * 更新品牌管理
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: MallBrandAddVo): Promise<MallBrand> => request.put(`${config.baseUrl.apiUrl}/mall/mg/brand/${id}`, body, { token: true }),


  /**
   * 获取品牌管理
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallMgBrandGetListQueryParam): Promise<Array<MallBrand>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/brand`, { params: query, token: true }),


  /**
   * 添加品牌管理
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: MallBrandAddVo): Promise<MallBrand> => request.post(`${config.baseUrl.apiUrl}/mall/mg/brand`, body, { token: true }),


  /**
   * 获取品牌管理
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: MallMgBrandGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/mall/mg/brand/count`, { params: query, token: true }),

}
