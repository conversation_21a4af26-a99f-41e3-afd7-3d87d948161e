
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [MallProductCategoryDetailVo]分类信息路径请求参数
 */
export interface MallCategoryDetailVo {

  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 父节点，根节点0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 属性定义信息
   */
  propertyStructs?: Array<MallProductCategoryPropertyStructBo>;
  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
}


/**
 * [MallProductCategoryAddVo]分类信息路径请求参数
 */
export interface MallCategoryAddVo {

  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 父节点，根节点0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 属性定义信息
   */
  propertyStructs?: Array<MallProductCategoryPropertyStructBo>;
}


/**
 * [MallCategoryListVo]分类信息路径请求参数
 */
export interface MallCategoryListVo {

  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 父节点，根节点0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
}


/**
 * [MallProductCategoryPropertyStructBo]商品内容结构路径请求参数
 */
export interface MallProductCategoryPropertyStructBo {

  /**
   * 分类id
   */
  categoryId?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  code?: string;
  /**
   * 数据类型, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 分组名称, 最小长度：0, 最大长度：256
   */
  groupName?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [MallProductCategoryBo]分类信息路径请求参数
 */
export interface MallProductCategoryBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 父节点，根节点0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * parentCategory
   */
  parentCategory?: MallProductCategoryBo;
  /**
   * 属性定义信息
   */
  propertyStructs?: Array<MallProductCategoryPropertyStructBo>;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}

export default {

  /**
   * 获取详情
   * @param id: id
   * @returns 数据
   */
  get: async (id: string): Promise<MallCategoryDetailVo> => request.get(`${config.baseUrl.apiUrl}/mall/mg/category/${id}`, { token: true }),


  /**
   * 修改
   * @param id: id
   * @param body 请求Body参数
   */
  modify: async (id: string, body: MallCategoryAddVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/mall/mg/category/${id}`, body, { token: true }),


  /**
   * 删除
   * @param id: id
   */
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/mall/mg/category/${id}`, { token: true }),


  /**
   * 获取列表信息
   * @returns 数据
   */
  getAllList: async (): Promise<Array<MallCategoryListVo>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/category`, { token: true }),


  /**
   * 添加
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: MallCategoryAddVo): Promise<MallCategoryDetailVo> => request.post(`${config.baseUrl.apiUrl}/mall/mg/category`, body, { token: true }),


  /**
   * 获取属性详情
   * @param id: id
   * @returns 数据
   */
  getProperty: async (id: string): Promise<Array<MallProductCategoryPropertyStructBo>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/category/${id}/property`, { token: true }),


  /**
   * 获取详情详情
   * @param id: id
   * @returns 数据
   */
  getDetail: async (id: string): Promise<MallProductCategoryBo> => request.get(`${config.baseUrl.apiUrl}/mall/mg/category/${id}/detail`, { token: true }),

}
