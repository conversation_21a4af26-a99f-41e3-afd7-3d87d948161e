
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取库存列表路径请求参数
 */
export interface MallMgProductStockGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 商品
   */
  space_id?: Array<string>;
  /**
   * 仓库
   */
  depot_id?: Array<string>;
  /**
   * storeId
   */
  store_id?: string;
}


/**
 * 获取库存数量路径请求参数
 */
export interface MallMgProductStockGetListCountQueryParam {

  /**
   * 商品
   */
  space_id?: Array<string>;
  /**
   * 仓库
   */
  depot_id?: Array<string>;
  /**
   * storeId
   */
  store_id?: string;
}


/**
 * [StockModifyAttrParamBo]调整库存接口路径请求参数
 */
export interface StockModifyAttrParamBo {

  /**
   * 商品编号, 必须字段
   */
  spaceId?: string;
  /**
   * 仓库id, 必须字段
   */
  depotId?: string;
  /**
   * 是否允许预定, 必须字段
   */
  allowNegative?: boolean;
  /**
   * 预定数, 必须字段
   */
  incoming?: number;
}


/**
 * [StockStockDeductParamVo]调整库存接口路径请求参数
 */
export interface StockDeductParamVo {

  /**
   * 商品编号, 必须字段
   */
  spaceId?: string;
  /**
   * 仓库地区, 必须字段
   */
  depotId?: string;
  /**
   * 操作类型，ADD：增加， SET：设置, 必须字段
   */
  type?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 是否允许预定, 必须字段
   */
  allowNegative?: boolean;
  /**
   * 备注
   */
  remark?: string;
}


/**
 * [MallProductStock]发布商品信息路径请求参数
 */
export interface MallProductStock {

  /**
   * 规格id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  spaceId?: string;
  /**
   * 店铺id, 必须字段, 最小长度：1, 最大长度：32
   */
  storeId?: string;
  /**
   * 仓库，ALL：所有地区, 必须字段, 最小长度：1, 最大长度：32
   */
  depotId?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 允许预定, 必须字段
   */
  allowNegative?: boolean;
  /**
   * 预定数, 必须字段
   */
  incoming?: number;
  /**
   * 库存数, 必须字段
   */
  quantity?: number;
}

export default {

  /**
   * 修改库存信息
   * @param body 请求Body参数
   */
  batchModify: async (body: Array<StockModifyAttrParamBo>): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/mg/product/stock/batch/modify-attr`, body, { token: true }),


  /**
   * 调整库存
   * @param body 请求Body参数
   */
  batchDeduct: async (body: Array<StockDeductParamVo>): Promise<void> => request.post(`${config.baseUrl.apiUrl}/mall/mg/product/stock/batch/deduct`, body, { token: true }),


  /**
   * 获取库存列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: MallMgProductStockGetListQueryParam): Promise<Array<MallProductStock>> => request.get(`${config.baseUrl.apiUrl}/mall/mg/product/stock`, { params: query, token: true }),


  /**
   * 获取库存数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: MallMgProductStockGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/mall/mg/product/stock/count`, { params: query, token: true }),

}
