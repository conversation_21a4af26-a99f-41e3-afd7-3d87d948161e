
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取系统菜单路径请求参数
 */
export interface BasicProjectAppMenuGetListQueryParam {

  /**
   * 应用APPID, 必须参数
   */
  project_app_id?: string;
}


/**
 * [ProjectAppMenu]系统菜单路径请求参数
 */
export interface ProjectAppMenu {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 唯一标识, 最小长度：0, 最大长度：32
   */
  code?: string;
  /**
   * 图标, 最小长度：0, 最大长度：32
   */
  icon?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * tag中显示名称, 最小长度：0, 最大长度：64
   */
  nameTag?: string;
  /**
   * url地址, 最小长度：0, 最大长度：128
   */
  url?: string;
  /**
   * URL的表达式（正则表达式），用于匹配当前菜单，json格式数组, 最小长度：0, 最大长度：1024
   */
  urlExp?: string;
  /**
   * 是否对当前启用缓存，默认：true, 必须字段
   */
  keepalive?: boolean;
  /**
   * 是否显示在菜单, 必须字段
   */
  show?: boolean;
  /**
   * 是否启用, 必须字段
   */
  valid?: boolean;
  /**
   * 需要当前用户具有此角色权限才显示，字符串数组, 最小长度：0, 最大长度：255
   */
  role?: string;
  /**
   * 需要当前页面角色是当角色才是现实，字符串数组, 最小长度：0, 最大长度：255
   */
  pageRole?: string;
  /**
   * 跳转指定页面时设置的页面角色, 最小长度：0, 最大长度：32
   */
  toPageRole?: string;
  /**
   * 指向父节点id，更节点为0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 项目APPid号, 必须字段, 最小长度：1, 最大长度：32
   */
  projectAppId?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * ord, 必须字段
   */
  ord?: number;
}


/**
 * [ProjectAppMenuAddVo]系统菜单添加新建内容路径请求参数
 */
export interface ProjectAppMenuAddVo {

  /**
   * 唯一标识, 最小长度：0, 最大长度：32
   */
  code?: string;
  /**
   * 图标, 最小长度：0, 最大长度：32
   */
  icon?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * tag中显示名称, 最小长度：0, 最大长度：64
   */
  nameTag?: string;
  /**
   * url地址, 最小长度：0, 最大长度：128
   */
  url?: string;
  /**
   * URL的表达式（正则表达式），用于匹配当前菜单，json格式数组, 最小长度：0, 最大长度：1024
   */
  urlExp?: string;
  /**
   * 是否对当前启用缓存，默认：true, 必须字段
   */
  keepalive?: boolean;
  /**
   * 是否显示在菜单, 必须字段
   */
  show?: boolean;
  /**
   * 是否启用, 必须字段
   */
  valid?: boolean;
  /**
   * 需要当前用户具有此角色权限才显示，字符串数组, 最小长度：0, 最大长度：255
   */
  role?: string;
  /**
   * 需要当前页面角色是当角色才是现实，字符串数组, 最小长度：0, 最大长度：255
   */
  pageRole?: string;
  /**
   * 跳转指定页面时设置的页面角色, 最小长度：0, 最大长度：32
   */
  toPageRole?: string;
  /**
   * 指向父节点id，更节点为0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 项目APPid号, 必须字段, 最小长度：1, 最大长度：32
   */
  projectAppId?: string;
  /**
   * ord, 必须字段
   */
  ord?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}

export default {

  /**
   * 查询系统菜单
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<ProjectAppMenu> => await request.get(`${config.baseUrl.apiUrl}/basic/project/app/menu/${id}`, { token: true, pageRole: false }),


  /**
   * 更新系统菜单
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: ProjectAppMenuAddVo): Promise<ProjectAppMenu> => await request.put(`${config.baseUrl.apiUrl}/basic/project/app/menu/${id}`, body, { token: true, pageRole: false }),


  /**
   * 删除系统菜单
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => await request.delete(`${config.baseUrl.apiUrl}/basic/project/app/menu/${id}`, { token: true, pageRole: false }),


  /**
   * 获取系统菜单
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: BasicProjectAppMenuGetListQueryParam): Promise<Array<ProjectAppMenu>> => await request.get(`${config.baseUrl.apiUrl}/basic/project/app/menu`, { params: query, token: true, pageRole: false }),


  /**
   * 添加系统菜单
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: ProjectAppMenuAddVo): Promise<ProjectAppMenu> => await request.post(`${config.baseUrl.apiUrl}/basic/project/app/menu`, body, { token: true, pageRole: false }),

}
