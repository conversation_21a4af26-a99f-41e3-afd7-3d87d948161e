
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [MapRegion]地图地理信息路径请求参数
export interface MapRegion {

  // 地址编号, 必须字段, 最小长度：1, 最大长度：32
  code?: string;
  // 父节点，0为根节点, 最小长度：0, 最大长度：32
  codeParent?: string;
  // 创建时间, 必须字段
  createTime?: number;
  // 级别, 必须字段
  level?: number;
  // 地址名称, 必须字段, 最小长度：1, 最大长度：64
  name?: string;
  // 备注, 最小长度：0, 最大长度：256
  remark?: string;
}

export default {

  // method 获取列表
  getAllList: async (): Promise<Array<MapRegion>> => request.get(`${config.baseUrl.apiUrl}/basic/map/region`, { token: true }),


  // method 公开列表
  getPubAllList: async (): Promise<Array<MapRegion>> => request.get(`${config.baseUrl.apiUrl}/basic/map/region/pub`, { token: false }),

}
