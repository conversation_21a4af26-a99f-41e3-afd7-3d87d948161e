
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取业务流任务列表路径请求参数
 */
export interface BasicBpmWorkflowGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 状态
   */
  states?: Array<string>;
  /**
   * 流程code
   */
  process_code?: string;
}


/**
 * 获取业务流任务数量路径请求参数
 */
export interface BasicBpmWorkflowGetListCountQueryParam {

  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 状态
   */
  states?: Array<string>;
  /**
   * 流程code
   */
  process_code?: string;
}


/**
 * [BpmWorkflowAuditParamBo]提交审核流程路径请求参数
 */
export interface BpmWorkflowAuditParamBo {

  /**
   * 审核信息, 必须字段, 值：PASS, REJECT
   */
  audit?: string;
  /**
   * 审核参数json格式, 最小长度：0, 最大长度：65535
   */
  paramJson?: string;
  /**
   * 审核备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [BpmUpdateProcess2newestBo]更新process到最新版本路径请求参数
 */
export interface BpmUpdateProcess2newestBo {

  /**
   * 流程Code
   */
  processCodes?: Array<string>;
}


/**
 * [BpmWorkflow]Bpm任务表路径请求参数
 */
export interface BpmWorkflow {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 流程code, 必须字段, 最小长度：1, 最大长度：32
   */
  processCode?: string;
  /**
   * 版本信息, 必须字段, 最小长度：1, 最大长度：32
   */
  processRecordId?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 任务类型, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 关联的任务id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态：INIT:初始化、RUNNING：进行中、REJECT：驳回，PASS：通过、CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 任务类型, 最小长度：0, 最大长度：32
   */
  auditSubjectType?: string;
  /**
   * 关联的任务id, 最小长度：0, 最大长度：32
   */
  auditSubjectId?: string;
  /**
   * 创建操作人类型, 最小长度：0, 最大长度：32
   */
  createOwnerType?: string;
  /**
   * 创建操作人Id, 最小长度：0, 最大长度：32
   */
  createOwnerId?: string;
  /**
   * 创建操作人编号, 最小长度：0, 最大长度：32
   */
  createOwnerNo?: string;
  /**
   * 创建操作人名称, 最小长度：0, 最大长度：32
   */
  createOwnerName?: string;
  /**
   * 参数json, 最小长度：0, 最大长度：65535
   */
  paramJson?: string;
  /**
   * 完成时间
   */
  finishTime?: number;
}

export default {

  /**
   * 强制取消任务
   * @param id: Id
   */
  cancel: async (id: string): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/basic/bpm/workflow/${id}/cancel`, {}, { token: true, pageRole: false }),


  /**
   * 强制继续任务
   * @param recordId: RecordId
   */
  proceed: async (recordId: string): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/basic/bpm/workflow/record/${recordId}/proceed`, {}, { token: true, pageRole: false }),


  /**
   * 强制审核任务
   * @param recordId: RecrodId
   * @param body 请求Body参数
   */
  audit: async (recordId: string, body: BpmWorkflowAuditParamBo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/basic/bpm/workflow/record/${recordId}/audit`, body, { token: true, pageRole: false }),


  /**
   * 批量更新BpmProcessRecordId到最新版
   * @param body 请求Body参数
   */
  batchUpdateBpmProcess2newest: async (body: BpmUpdateProcess2newestBo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/basic/bpm/workflow/batch-update-bpm-process2newest`, body, { token: true, pageRole: false }),


  /**
   * 获取业务流任务列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: BasicBpmWorkflowGetListQueryParam): Promise<Array<BpmWorkflow>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/workflow`, { params: query, token: true, pageRole: false }),


  /**
   * 查询业务流任务详情
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<BpmWorkflow> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/workflow/${id}`, { token: true, pageRole: false }),


  /**
   * 获取业务流任务数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: BasicBpmWorkflowGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/workflow/count`, { params: query, token: true, pageRole: false }),

}
