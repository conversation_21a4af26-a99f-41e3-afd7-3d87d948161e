
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取待办业务流任务列表路径请求参数
 */
export interface BasicBpmUserWorkflowGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 流程code
   */
  process_code?: string;
}


/**
 * 获取待办业务流任务数量路径请求参数
 */
export interface BasicBpmUserWorkflowGetListCountQueryParam {

  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 流程code
   */
  process_code?: string;
}


/**
 * [BpmWorkflowTodoListVo]流程代办信息路径请求参数
 */
export interface BpmWorkflowTodoListVo {

  /**
   * 记录id, 必须字段, 最小长度：1, 最大长度：32
   */
  recordId?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：32
   */
  recordName?: string;
  /**
   * 流程code, 必须字段, 最小长度：1, 最大长度：32
   */
  recordProcessCode?: string;
  /**
   * 业务流节点code, 必须字段, 最小长度：1, 最大长度：32
   */
  recordProcessNodeCode?: string;
  /**
   * 节点类型, 必须字段, 值：START, ORDINAR, AUDIT, PAUSE, END
   */
  recordProcessNodeType?: string;
  /**
   * 流程审核页面地址
   */
  recordTodoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  recordTodoDetailUrl?: string;
  /**
   * 任务id, 必须字段, 最小长度：1, 最大长度：32
   */
  workflowId?: string;
  /**
   * 任务类型, 必须字段
   */
  workflowSubjectType?: string;
  /**
   * 关联的任务id, 必须字段
   */
  workflowSubjectId?: string;
  /**
   * 任务名称
   */
  workflowName?: string;
  /**
   * 记录创建时间
   */
  recordCreateTime?: number;
  /**
   * 审核角色, 最小长度：0, 最大长度：32
   */
  recordAuditRoleCode?: string;
}


/**
 * [BpmProcessListVo]流程信息路径请求参数
 */
export interface BpmProcessListVo {

  /**
   * 业务编号, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 第一个流程节点code, 最小长度：0, 最大长度：32
   */
  nodeFirstCode?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 审核全部通过回调类名称, 最小长度：0, 最大长度：256
   */
  execPassClsName?: string;
  /**
   * 审核被驳回回调类名称, 最小长度：0, 最大长度：256
   */
  execRejectClsName?: string;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 描述信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 当前版本记录信息, 最小长度：0, 最大长度：32
   */
  processRecordId?: string;
}

export default {

  /**
   * 获取待办业务流任务列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: BasicBpmUserWorkflowGetListQueryParam): Promise<Array<BpmWorkflowTodoListVo>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/user/workflow/todo`, { params: query, token: true, pageRole: false }),


  /**
   * 获取待办业务流任务数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: BasicBpmUserWorkflowGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/user/workflow/todo/count`, { params: query, token: true, pageRole: false }),


  /**
   * 获取bpm流程
   * @returns 数据
   */
  getProcessList: async (): Promise<Array<BpmProcessListVo>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/user/workflow/process`, { token: true, pageRole: false }),

}
