
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [BpmProcessNodePropBo]业务流属性路径请求参数
 */
export interface BpmProcessNodePropBo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 值
   */
  value?: string;
}


/**
 * [BpmProcessNodeBo]Bpm业务流节点路径请求参数
 */
export interface BpmProcessNodeBo {

  /**
   * 流程节点code, 必须字段, 最小长度：1, 最大长度：255
   */
  code?: string;
  /**
   * 类型, 必须字段, 值：START, ORDINAR, AUDIT, PAUSE, END
   */
  type?: string;
  /**
   * 节点名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 执行器进入前调用类名称，为空是不回调, 最小长度：0, 最大长度：256
   */
  skipNodeClsNamePre?: string;
  /**
   * 跳过此节点的js表达式，返回true表示跳过，不配置则不跳过, 最小长度：0, 最大长度：65535
   */
  skipNodeExpn?: string;
  /**
   * 审核角色, 最小长度：0, 最大长度：32
   */
  auditRoleCode?: string;
  /**
   * 不可跳过强制审核, 必须字段
   */
  auditMust?: boolean;
  /**
   * 待办任务中不显示, 必须字段
   */
  hiddenTodo?: boolean;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 审核驳回类型, 值：PROCESS_REJECT, NODE_BACK
   */
  auditRejectType?: string;
  /**
   * 审核驳回回跳节点code
   */
  auditRejectBackNodeCode?: string;
  /**
   * 审核被驳回回调类名称, 最小长度：0, 最大长度：256
   */
  auditRejectExecClsName?: string;
  /**
   * 执行器进入前调用类名称，为空是不回调, 最小长度：0, 最大长度：256
   */
  execClsName?: string;
  /**
   * 属性信息
   */
  props?: Array<BpmProcessNodePropBo>;
}


/**
 * [BpmProcessNodeLinkBo]bpm业务流关联路径请求参数
 */
export interface BpmProcessNodeLinkBo {

  /**
   * 当前节点, 必须字段, 最小长度：1, 最大长度：32
   */
  processNodeCode?: string;
  /**
   * 下一个节点信息, 必须字段, 最小长度：1, 最大长度：32
   */
  nextProcessNodeCode?: string;
}


/**
 * [BpmProcessInfoBo]流程信息路径请求参数
 */
export interface BpmProcessInfoBo {

  /**
   * 业务编号, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 第一个流程节点code, 最小长度：0, 最大长度：32
   */
  nodeFirstCode?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 描述信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 当前版本记录信息, 最小长度：0, 最大长度：32
   */
  processRecordId?: string;
  /**
   * 审核全部通过回调类名称, 最小长度：0, 最大长度：256
   */
  execPassClsName?: string;
  /**
   * 审核被驳回回调类名称, 最小长度：0, 最大长度：256
   */
  execRejectClsName?: string;
  /**
   * 每次执行完一个node节点时回调, 最小长度：0, 最大长度：256
   */
  execNodeFinishClsName?: string;
  /**
   * 每次node节点执行前回调, 最小长度：0, 最大长度：256
   */
  execNodePreClsName?: string;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 节点信息
   */
  nodes?: Array<BpmProcessNodeBo>;
  /**
   * 节点连接信息
   */
  nodeLinks?: Array<BpmProcessNodeLinkBo>;
}


/**
 * [BpmProcessModifyVo]bpm业务流添加新建内容路径请求参数
 */
export interface BpmProcessModifyVo {

  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 审核全部通过回调类名称, 最小长度：0, 最大长度：256
   */
  execPassClsName?: string;
  /**
   * 审核被驳回回调类名称, 最小长度：0, 最大长度：256
   */
  execRejectClsName?: string;
  /**
   * 审核被取消回调类名称, 最小长度：0, 最大长度：256
   */
  execCancelClsName?: string;
  /**
   * 每次执行完一个node节点时回调, 最小长度：0, 最大长度：256
   */
  execNodeFinishClsName?: string;
  /**
   * 每次node节点执行前回调, 最小长度：0, 最大长度：256
   */
  execNodePreClsName?: string;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 描述信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 节点信息
   */
  nodes?: Array<BpmProcessNodeBo>;
  /**
   * 节点连接信息
   */
  nodeLinks?: Array<BpmProcessNodeLinkBo>;
}


/**
 * [BpmProcessListVo]流程信息路径请求参数
 */
export interface BpmProcessListVo {

  /**
   * 业务编号, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 第一个流程节点code, 最小长度：0, 最大长度：32
   */
  nodeFirstCode?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 审核全部通过回调类名称, 最小长度：0, 最大长度：256
   */
  execPassClsName?: string;
  /**
   * 审核被驳回回调类名称, 最小长度：0, 最大长度：256
   */
  execRejectClsName?: string;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 描述信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 当前版本记录信息, 最小长度：0, 最大长度：32
   */
  processRecordId?: string;
}


/**
 * [BpmProcessAddVo]bpm业务流添加新建内容路径请求参数
 */
export interface BpmProcessAddVo {

  /**
   * 业务编号, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 禁用, 必须字段
   */
  ban?: boolean;
  /**
   * 第一个流程节点code, 最小长度：1, 最大长度：32
   */
  nodeFirstCode?: string;
  /**
   * 审核全部通过回调类名称, 最小长度：0, 最大长度：256
   */
  execPassClsName?: string;
  /**
   * 审核被驳回回调类名称, 最小长度：0, 最大长度：256
   */
  execRejectClsName?: string;
  /**
   * 审核被取消回调类名称, 最小长度：0, 最大长度：256
   */
  execCancelClsName?: string;
  /**
   * 每次执行完一个node节点时回调, 最小长度：0, 最大长度：256
   */
  execNodeFinishClsName?: string;
  /**
   * 每次node节点执行前回调, 最小长度：0, 最大长度：256
   */
  execNodePreClsName?: string;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 描述信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 节点信息
   */
  nodes?: Array<BpmProcessNodeBo>;
  /**
   * 节点连接信息
   */
  nodeLinks?: Array<BpmProcessNodeLinkBo>;
}

export default {

  /**
   * 查询bpm业务流详情
   * @param code: code
   * @returns 数据
   */
  get: async (code: string): Promise<BpmProcessInfoBo> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/process/${code}`, { token: true, pageRole: false }),


  /**
   * 更新bpm业务流
   * @param code: code
   * @param body 请求Body参数
   */
  modify: async (code: string, body: BpmProcessModifyVo): Promise<void> => await request.put(`${config.baseUrl.apiUrl}/basic/bpm/process/${code}`, body, { token: true, pageRole: false }),


  /**
   * 删除bpm业务流
   * @param code: Code
   */
  delete: async (code: string): Promise<void> => await request.delete(`${config.baseUrl.apiUrl}/basic/bpm/process/${code}`, { token: true, pageRole: false }),


  /**
   * 获取bpm业务流
   * @returns 数据
   */
  getAllList: async (): Promise<Array<BpmProcessListVo>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/process`, { token: true, pageRole: false }),


  /**
   * 添加bpm业务流
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: BpmProcessAddVo): Promise<BpmProcessInfoBo> => await request.post(`${config.baseUrl.apiUrl}/basic/bpm/process`, body, { token: true, pageRole: false }),

}
