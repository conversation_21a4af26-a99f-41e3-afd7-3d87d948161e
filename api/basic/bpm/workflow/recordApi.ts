
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取业务流任务列表路径请求参数
 */
export interface BasicBpmWorkflowRecordGetALLListQueryParam {

  /**
   * 任务Id, 必须参数
   */
  workflowId?: string;
}


/**
 * 查询暂停点路径请求参数
 */
export interface BasicBpmWorkflowRecordGetPauseListQueryParam {

  /**
   * 任务Id, 必须参数
   */
  workflowId?: string;
}


/**
 * 查询审核点路径请求参数
 */
export interface BasicBpmWorkflowRecordGetAuditListQueryParam {

  /**
   * 任务Id, 必须参数
   */
  workflowId?: string;
}


/**
 * [BpmWorkflowRecord]Bpm任务记录路径请求参数
 */
export interface BpmWorkflowRecord {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：32
   */
  name?: string;
  /**
   * 流程code, 必须字段, 最小长度：1, 最大长度：32
   */
  processCode?: string;
  /**
   * 业务流节点code, 必须字段, 最小长度：1, 最大长度：32
   */
  processNodeCode?: string;
  /**
   * 节点类型, 必须字段, 值：START, ORDINAR, AUDIT, PAUSE, END
   */
  processNodeType?: string;
  /**
   * 节点属性
   */
  processNodeProps?: string;
  /**
   * 任务id, 必须字段, 最小长度：1, 最大长度：32
   */
  workflowId?: string;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 废弃, 必须字段
   */
  invalid?: boolean;
  /**
   * 待办任务中不显示, 必须字段
   */
  processNodeHiddenTodo?: boolean;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 审核主题类型, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 审核主题id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 执行参数json格式, 最小长度：0, 最大长度：65535
   */
  paramJson?: string;
  /**
   * 审核角色, 最小长度：0, 最大长度：32
   */
  auditRoleCode?: string;
  /**
   * 审核人id, 最小长度：0, 最大长度：32
   */
  auditOwnerId?: string;
  /**
   * 审核人编号, 最小长度：0, 最大长度：32
   */
  auditOwnerNo?: string;
  /**
   * 审核人名称, 最小长度：0, 最大长度：64
   */
  auditOwnerName?: string;
  /**
   * 审核人类型, 最小长度：0, 最大长度：32
   */
  auditOwnerType?: string;
  /**
   * 审核结果，AWAIT：等待，AUTO_PASS：自动通过、NOT_AUDIT：不需要审核，REJECT：驳回，PASS：通过, 必须字段, 最小长度：1, 最大长度：32
   */
  auditResult?: string;
  /**
   * 审核时间
   */
  auditTime?: number;
  /**
   * 审核备注, 最小长度：0, 最大长度：512
   */
  auditRemark?: string;
  /**
   * 审核参数, 最小长度：0, 最大长度：65535
   */
  auditParamJson?: string;
  /**
   * 状态：INIT:初始化、SKIP：跳过、PAUSE：暂停、AUDIT：等待审核，FINISH：完成、CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 完成时间
   */
  finishTime?: number;
  /**
   * 顺序号
   */
  ord?: number;
}

export default {

  /**
   * 获取业务流任务列表
   * @param query 请求参数
   * @returns 数据
   */
  getALLList: async (query: BasicBpmWorkflowRecordGetALLListQueryParam): Promise<Array<BpmWorkflowRecord>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/workflow/record`, { params: query, token: true, pageRole: false }),


  /**
   * 查询暂停点
   * @param query 请求参数
   * @returns 数据
   */
  getPauseList: async (query: BasicBpmWorkflowRecordGetPauseListQueryParam): Promise<Array<BpmWorkflowRecord>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/workflow/record/pause-record`, { params: query, token: true, pageRole: false }),


  /**
   * 查询审核点
   * @param query 请求参数
   * @returns 数据
   */
  getAuditList: async (query: BasicBpmWorkflowRecordGetAuditListQueryParam): Promise<Array<BpmWorkflowRecord>> => await request.get(`${config.baseUrl.apiUrl}/basic/bpm/workflow/record/audit-record`, { params: query, token: true, pageRole: false }),

}
