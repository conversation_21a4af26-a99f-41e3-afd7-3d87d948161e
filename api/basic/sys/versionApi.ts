
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [SysDicFieldI18nBo]系统数据字典字段国际化路径请求参数
 */
export interface SysDicFieldI18nBo {

  /**
   * language, 最小长度：1, 最大长度：32
   */
  language?: string;
  /**
   * 显示内容
   */
  label?: string;
}


/**
 * [SysDicFieldPropBo]系统数据字典字段Code属性路径请求参数
 */
export interface SysDicFieldPropBo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 值
   */
  value?: string;
}


/**
 * [SysDicFieldBo]系统数据字典字段路径请求参数
 */
export interface SysDicFieldBo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 显示内容
   */
  label?: string;
  /**
   * 字段信息
   */
  labels?: Array<SysDicFieldI18nBo>;
  /**
   * 字段属性信息
   */
  props?: Array<SysDicFieldPropBo>;
  /**
   * 备注
   */
  remark?: string;
}


/**
 * [SysDicBo]系统数据字典路径请求参数
 */
export interface SysDicBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 启用, 必须字段
   */
  valid?: boolean;
  /**
   * 字段信息
   */
  fields?: Array<SysDicFieldBo>;
  /**
   * 备注
   */
  remark?: string;
}


/**
 * [SysConfig]系统级别的配置表路径请求参数
 */
export interface SysConfig {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 字段编号, 必须字段
   */
  code?: string;
  /**
   * 启用
   */
  valid?: boolean;
  /**
   * 名称
   */
  value?: string;
  /**
   * 是否可公开, 必须字段
   */
  pub?: boolean;
  /**
   * 备注
   */
  remark?: string;
}


/**
 * [ProjectAppMenu]系统菜单路径请求参数
 */
export interface ProjectAppMenu {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 唯一标识, 最小长度：0, 最大长度：32
   */
  code?: string;
  /**
   * 图标, 最小长度：0, 最大长度：32
   */
  icon?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * tag中显示名称, 最大长度：64
   */
  nameTag?: string;
  /**
   * url地址, 最小长度：0, 最大长度：128
   */
  url?: string;
  /**
   * URL的表达式（正则表达式），用于匹配当前菜单，json格式数组, 最小长度：0, 最大长度：1024
   */
  urlExp?: string;
  /**
   * 是否对当前启用缓存，默认：true, 必须字段
   */
  keepalive?: boolean;
  /**
   * 是否显示在菜单, 必须字段
   */
  show?: boolean;
  /**
   * 是否启用, 必须字段
   */
  valid?: boolean;
  /**
   * 需要当前用户具有此角色权限才显示，字符串数组, 最小长度：0, 最大长度：255
   */
  role?: string;
  /**
   * 需要当前页面角色是当角色才是现实，字符串数组, 最小长度：0, 最大长度：255
   */
  pageRole?: string;
  /**
   * 跳转指定页面时设置的页面角色, 最小长度：0, 最大长度：32
   */
  toPageRole?: string;
  /**
   * 指向父节点id，更节点为0, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 项目APPid号, 必须字段, 最小长度：1, 最大长度：32
   */
  projectAppId?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * ord, 必须字段
   */
  ord?: number;
}


/**
 * [SysVersionPubVo]系统版本公开数据路径请求参数
 */
export interface SysVersionPubVo {

  /**
   * 数据字典
   */
  dics?: Array<SysDicBo>;
  /**
   * 配置文件
   */
  sysConfigs?: Array<SysConfig>;
  /**
   * 菜单数据
   */
  menus?: Array<ProjectAppMenu>;
}

export default {

  /**
   * 系统公开基础信息
   * @param sysAppId: 应用APPId,0
   * @returns 数据
   */
  getPubVersion: async (sysAppId: string): Promise<SysVersionPubVo> => await request.get(`${config.baseUrl.apiUrl}/basic/sys/version/${sysAppId}/pub`, { token: false, marginRequest: true, pageRole: false }),

}
