
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [SysCryptoPubkeyVo]系统加密公钥路径请求参数
 */
export interface SysCryptoPubkeyVo {

  /**
   * 公钥
   */
  publicKeyBase64?: string;
  /**
   * 版本
   */
  version?: string;
  /**
   * 算法
   */
  algorithm?: string;
}

export default {

  /**
   * 获取系统传输公钥信息
   * @param projectAppId: 项目应用APPId,0
   * @returns 数据
   */
  getTransactionPubKey: async (projectAppId: string): Promise<SysCryptoPubkeyVo> => await request.get(`${config.baseUrl.apiUrl}/basic/sys/crypto/${projectAppId}/transaction/pubkey`, { token: false, pageRole: false, marginRequest: true }),

}
