
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 通过code配置构建分享秘钥路径请求参数
 */
export interface BasicDatartShareBuildCodeByConfCodeQueryParam {

  /**
   * 参数值, 必须参数
   */
  code?: string;
}


/**
 * [DatartShareBuildCodeParamVo]构建分享Code参数路径请求参数
 */
export interface DatartShareBuildCodeParamVo {

  /**
   * 分享id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 分享key, 必须字段, 最小长度：1, 最大长度：64
   */
  key?: string;
}


/**
 * [DatartShareBuildCodeResultVo]构建分享Code结果路径请求参数
 */
export interface DatartShareBuildCodeResultVo {

  /**
   * 分享id
   */
  id?: string;
  /**
   * 分享Code
   */
  code?: string;
}

export default {

  /**
   * 构建分享秘钥
   * @param body 请求Body参数
   * @returns 数据
   */
  buildCode: async (body: DatartShareBuildCodeParamVo): Promise<DatartShareBuildCodeResultVo> => await request.post(`${config.baseUrl.apiUrl}/basic/datart/share/code`, body, { token: true, pageRole: false }),


  /**
   * 通过code配置构建分享秘钥
   * @param query 请求参数
   * @returns 数据
   */
  buildCodeByConfCode: async (query: BasicDatartShareBuildCodeByConfCodeQueryParam): Promise<DatartShareBuildCodeResultVo> => await request.get(`${config.baseUrl.apiUrl}/basic/datart/share/code-by-conf-code`, { params: query, token: true, pageRole: false }),

}
