
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 查询自动补全路径请求参数
 */
export interface BasicAuxyAutocompleteAutoGetAutocompletesQueryParam {

  /**
   * 业务编号
   */
  bs_code?: string;
  /**
   * Code信息
   */
  code?: string;
  /**
   * 属性信息
   */
  attr?: string;
  /**
   * 是否登录
   */
  login?: boolean;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * [AuxyAutocomplete]自动补全列表路径请求参数
 */
export interface AuxyAutocomplete {

  /**
   * 附带属性信息：prop.code(cpu), 最小长度：0, 最大长度：32
   */
  attr?: string;
  /**
   * 业务编号：mall.category.prop, 必须字段, 最小长度：1, 最大长度：32
   */
  bsCode?: string;
  /**
   * 主编号： categoryId(2001), 最小长度：0, 最大长度：32
   */
  code?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 备注, 最小长度：0, 最大长度：256
   */
  remark?: string;
  /**
   * 用户id, 最小长度：0, 最大长度：32
   */
  userId?: string;
  /**
   * 有效性, 必须字段
   */
  valid?: boolean;
  /**
   * 推荐字符串, 最小长度：0, 最大长度：4096
   */
  value?: string;
}

export default {

  /**
   * 查询自动补全
   * @param query 请求参数
   * @returns 数据
   */
  getAutocompletes: async (query: BasicAuxyAutocompleteAutoGetAutocompletesQueryParam): Promise<Array<AuxyAutocomplete>> => request.get(`${config.baseUrl.apiUrl}/basic/auxy/autocomplete/auto`, { params: query, token: true }),

}
