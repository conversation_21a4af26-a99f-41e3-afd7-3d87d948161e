
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * undefined路径请求参数
 */
export interface DamsAssetExtraImageVo {

  /**
   * 图片类型（ELEMENT:元素图、EXTEND:延展图、PRODUCT:产品图、SCENE:场景图、THREE_VIEW:三视图）, 必须字段, 最小长度：1
   */
  type?: string;
  /**
   * 图片ID, 必须字段, 最小长度：1
   */
  fileId?: string;
}


/**
 * undefined路径请求参数
 */
export interface DamsAssetInfoAddVo {

  /**
   * assetId, 最小长度：0, 最大长度：32
   */
  assetId?: string;
  /**
   * 资源库类型（市场灵感库/古德案例/设计提案/KV设计/3D模型）, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 项目编号, 必须字段, 最小长度：1, 最大长度：64
   */
  projectNo?: string;
  /**
   * 案例名称/标题, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 案例描述, 最小长度：0, 最大长度：2147483647
   */
  description?: string;
  /**
   * 上市年月/发布时间, 必须字段
   */
  releaseTime?: number;
  /**
   * 是否为古德原创, 必须字段
   */
  original?: boolean;
  /**
   * 案例类型（产品/报告/IP资源/其它）, 必须字段, 最小长度：0, 最大长度：32
   */
  caseType?: string;
  /**
   * 服务类型（做货/非做货）, 必须字段, 最小长度：0, 最大长度：32
   */
  serviceType?: string;
  /**
   * 交付文件ID
   */
  deliverableFileId?: string;
  /**
   * 主图(多图), 必须字段, 最小长度：0, 最大长度：4096
   */
  mainImage?: string;
  /**
   * 封面(一般只有一个文件), 最小长度：0, 最大长度：64
   */
  headImage?: string;
  /**
   * 源文件, 最小长度：0, 最大长度：4096
   */
  sourceFile?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：2048
   */
  remark?: string;
  /**
   * 扩展图片列表（元素图/延展图/产品图/场景图/三视图）
   */
  extraImages?: Array<DamsAssetExtraImageVo>;
  /**
   * 产品类型（服务类型为做货时使用，商品/赠品）, 最小长度：0, 最大长度：32
   */
  productType?: string;
  /**
   * 产品规格（服务类型为做货时使用）, 最小长度：0, 最大长度：255
   */
  productSpec?: string;
  /**
   * 产品价格（服务类型为做货时使用）
   */
  productPrice?: number;
  /**
   * 产品数量（服务类型为做货时使用）
   */
  productNum?: number;
  /**
   * 大货工期（服务类型为做货时使用）
   */
  dueTime?: number;
  /**
   * 客户/品牌 从标签组接口获取, 必须字段, 最小长度：1
   */
  customerId?: string;
  /**
   * 材质tagId，多个逗号分割
   */
  materialId?: string;
  /**
   * 普通标签id，多个逗号分割
   */
  tagId?: string;
}


/**
 * undefined路径请求参数
 */
export interface DamsAssetInfoDetailVo {

  /**
   * id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * assetId, 必须字段, 最小长度：1, 最大长度：32
   */
  assetId?: string;
  /**
   * 资源库类型, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * srcType, 最小长度：1, 最大长度：32
   */
  srcType?: string;
  /**
   * published, 必须字段
   */
  published?: boolean;
  /**
   * projectNo, 最小长度：1, 最大长度：64
   */
  projectNo?: string;
  /**
   * name, 必须字段, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * description, 最小长度：0, 最大长度：2147483647
   */
  description?: string;
  /**
   * createTime, 必须字段
   */
  createTime?: number;
  /**
   * createUserId, 必须字段, 最小长度：1, 最大长度：32
   */
  createUserId?: string;
  /**
   * updateUserId, 必须字段, 最小长度：1, 最大长度：32
   */
  updateUserId?: string;
  /**
   * 发布时间
   */
  releaseTime?: number;
  /**
   * 古德原创
   */
  original?: boolean;
  /**
   * caseType, 最小长度：0, 最大长度：32
   */
  caseType?: string;
  /**
   * audit, 必须字段
   */
  audit?: number;
  /**
   * auditTime
   */
  auditTime?: number;
  /**
   * auditUserId, 最小长度：0, 最大长度：32
   */
  auditUserId?: string;
  /**
   * auditRemark, 最小长度：0, 最大长度：4096
   */
  auditRemark?: string;
  /**
   * serviceType, 最小长度：0, 最大长度：32
   */
  serviceType?: string;
  /**
   * deliverableFileId, 必须字段, 最小长度：1, 最大长度：4096
   */
  deliverableFileId?: string;
  /**
   * mainImage, 最小长度：0, 最大长度：4096
   */
  mainImage?: string;
  /**
   * headImage, 最小长度：0, 最大长度：64
   */
  headImage?: string;
  /**
   * sourceFile, 最小长度：0, 最大长度：4096
   */
  sourceFile?: string;
  /**
   * remark, 最小长度：0, 最大长度：2048
   */
  remark?: string;
  /**
   * 产品类型, 最小长度：0, 最大长度：32
   */
  productType?: string;
  /**
   * 产品规格, 最小长度：0, 最大长度：255
   */
  productSpec?: string;
  /**
   * 产品价格
   */
  productPrice?: number;
  /**
   * 产品数量
   */
  productNum?: number;
  /**
   * 大货工期
   */
  dueTime?: number;
  /**
   * 客户/品牌 从标签组接口获取, 最小长度：0, 最大长度：128
   */
  customerId?: string;
  /**
   * 材质tagId，多个逗号分割
   */
  materialId?: string;
  /**
   * 普通标签id，多个逗号分割
   */
  tagId?: string;
  /**
   * 扩展图片列表（元素图/延展图/产品图/场景图/三视图）
   */
  extraImages?: Array<DamsAssetExtraImageVo>;
}

export default {

  /**
   * 上传案例库
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: DamsAssetInfoAddVo): Promise<DamsAssetInfoDetailVo> => await request.post(`${config.baseUrl.apiUrl}/dams/asset/info`, body, { token: true, pageRole: false }),


  /**
   * 查询案例库详情
   * @param id: ID
   * @returns 数据
   */
  getDetail: async (id: string): Promise<DamsAssetInfoDetailVo> => await request.get(`${config.baseUrl.apiUrl}/dams/asset/info/${id}`, { token: true, pageRole: false }),

}
