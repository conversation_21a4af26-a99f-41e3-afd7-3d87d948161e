
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取设计系统标签分组信息路径请求参数
 */
export interface DamsTagGroupGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
}


/**
 * 获取设计系统标签分组信息路径请求参数
 */
export interface DamsTagGroupGetGroupListQueryParam {

  /**
   * 标签组类型列表，支持多个类型过滤，为空则查询所有
 MATERIAL 材料 GENERAL 普通标签 brand品牌
   */
  tagGroupTypes?: Array<string>;
}


/**
 * [DamsTagGroup]设计系统标签分组信息路径请求参数
 */
export interface DamsTagGroup {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 类型（MATERIAL:材质、GENERAL：通用）, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 备注, 最小长度：0, 最大长度：2048
   */
  remark?: string;
}


/**
 * undefined路径请求参数
 */
export interface DamsTagGroupAddVo {

  /**
   * 标签组名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 标签组类型（MATERIAL:材质、GENERAL：通用）, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：2048
   */
  remark?: string;
}


/**
 * [DamsTagInfo]标签信息路径请求参数
 */
export interface DamsTagInfo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 所属分组id, 必须字段, 最小长度：1, 最大长度：32
   */
  groupId?: string;
  /**
   * 内容, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 依赖连接到的标签, 最小长度：0, 最大长度：4096
   */
  linkIds?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [DamsTagGroup]设计系统标签分组信息路径请求参数
 */
export interface DamsTagGroupVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 类型（MATERIAL:材质、GENERAL：通用）, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 备注, 最小长度：0, 最大长度：2048
   */
  remark?: string;
  /**
   * 旗下标签
   */
  tagInfos?: Array<DamsTagInfo>;
}

export default {

  /**
   * 查询设计系统标签分组信息
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<DamsTagGroup> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/group/${id}`, { token: false, pageRole: false }),


  /**
   * 更新设计系统标签分组信息
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: DamsTagGroupAddVo): Promise<DamsTagGroup> => await request.put(`${config.baseUrl.apiUrl}/dams/tag/group/${id}`, body, { token: false, pageRole: false }),


  /**
   * 删除设计系统标签分组信息
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => await request.delete(`${config.baseUrl.apiUrl}/dams/tag/group/${id}`, { token: false, pageRole: false }),


  /**
   * 获取设计系统标签分组信息
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: DamsTagGroupGetListQueryParam): Promise<Array<DamsTagGroup>> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/group`, { params: query, token: false, pageRole: false }),


  /**
   * 添加设计系统标签分组信息
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: DamsTagGroupAddVo): Promise<DamsTagGroup> => await request.post(`${config.baseUrl.apiUrl}/dams/tag/group`, body, { token: false, pageRole: false }),


  /**
   * 获取设计系统标签分组信息
   * @param query 请求参数
   * @returns 数据
   */
  getGroupList: async (query: DamsTagGroupGetGroupListQueryParam): Promise<Array<DamsTagGroupVo>> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/group/getGrouplist`, { params: query, token: true, pageRole: false }),


  /**
   * 获取设计系统标签分组信息
   * @returns 数据
   */
  getListCount: async (): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/group/count`, { token: false, pageRole: false }),

}
