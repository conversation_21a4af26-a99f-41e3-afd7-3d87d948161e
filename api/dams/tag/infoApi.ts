
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取标签信息路径请求参数
 */
export interface DamsTagInfoGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
}


/**
 * [DamsTagInfo]标签信息路径请求参数
 */
export interface DamsTagInfo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 所属分组id, 必须字段, 最小长度：1, 最大长度：32
   */
  groupId?: string;
  /**
   * 内容, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 依赖连接到的标签, 最小长度：0, 最大长度：4096
   */
  linkIds?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
}


/**
 * [DamsTagInfoAddVo]标签信息添加新建内容路径请求参数
 */
export interface DamsTagInfoAddVo {

  /**
   * 所属分组id, 必须字段, 最小长度：1, 最大长度：32
   */
  groupId?: string;
  /**
   * 内容, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 依赖连接到的标签, 最小长度：0, 最大长度：4096
   */
  linkIds?: string;
  /**
   * 备注信息, 最小长度：0, 最大长度：256
   */
  remark?: string;
}

export default {

  /**
   * 查询标签信息
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<DamsTagInfo> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/info/${id}`, { token: false, pageRole: false }),


  /**
   * 更新标签信息
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: DamsTagInfoAddVo): Promise<DamsTagInfo> => await request.put(`${config.baseUrl.apiUrl}/dams/tag/info/${id}`, body, { token: false, pageRole: false }),


  /**
   * 删除标签信息
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => await request.delete(`${config.baseUrl.apiUrl}/dams/tag/info/${id}`, { token: false, pageRole: false }),


  /**
   * 获取标签信息
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: DamsTagInfoGetListQueryParam): Promise<Array<DamsTagInfo>> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/info`, { params: query, token: false, pageRole: false }),


  /**
   * 添加标签信息
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: DamsTagInfoAddVo): Promise<DamsTagInfo> => await request.post(`${config.baseUrl.apiUrl}/dams/tag/info`, body, { token: false, pageRole: false }),


  /**
   * 获取标签信息
   * @returns 数据
   */
  getListCount: async (): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/dams/tag/info/count`, { token: false, pageRole: false }),

}
