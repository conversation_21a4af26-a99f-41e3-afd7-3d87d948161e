
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 文件上传接口路径请求参数
 */
export interface SysFsUploadQueryParam {

  /**
   * 文件名称
   */
  filename?: string;
  /**
   * UUID
   */
  uuid?: string;
  /**
   * 保密文件，不公开
   */
  secrecy?: boolean;
  /**
   * 水印
   */
  watermark?: boolean;
  /**
   * 过期时长，单位：秒（S）
   */
  exp?: number;
}


/**
 * 文本上传接口路径请求参数
 */
export interface SysFsTextQueryParam {

  /**
   * UUID
   */
  uuid?: string;
}


/**
 * 批量文件信息查询路径请求参数
 */
export interface SysFsInfosQueryParam {

  /**
   * uuid, 必须参数
   */
  uuid?: string;
}


/**
 * 文件下载接口路径请求参数
 */
export interface SysFsDownloadQueryParam {

  /**
   * 文件名称
   */
  filename?: string;
  /**
   * 尺寸，1-8
   */
  s?: number;
  /**
   * 是否为直接下载，控制浏览器直接下载，默认为false
   */
  d?: boolean;
  /**
   * uuid，用户授权可访问的文件
   */
  uuid?: string;
}


/**
 * 文件信息查询接口路径请求参数
 */
export interface SysFsInfoQueryParam {

  /**
   * uuid
   */
  uuid?: string;
}


/**
 * [ResDoc]资源索引表路径请求参数
 */
export interface ResDoc {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 版本号, 必须字段, 最小长度：1, 最大长度：32
   */
  versionNo?: string;
  /**
   * 文件类型, 最小长度：0, 最大长度：128
   */
  mimeType?: string;
  /**
   * 文件后缀, 最小长度：0, 最大长度：32
   */
  suffix?: string;
  /**
   * 文件名, 最小长度：0, 最大长度：128
   */
  name?: string;
  /**
   * 文件大小
   */
  size?: number;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 描述, 最小长度：0, 最大长度：1024
   */
  description?: string;
  /**
   * 存储路径, 最小长度：0, 最大长度：64
   */
  path?: string;
  /**
   * 文件hash值, 最小长度：0, 最大长度：128
   */
  hash?: string;
  /**
   * 用户编号, 必须字段, 最小长度：1, 最大长度：32
   */
  userId?: string;
  /**
   * uuid, 最小长度：0, 最大长度：32
   */
  uuid?: string;
  /**
   * 被禁用
   */
  ban?: boolean;
  /**
   * 保密
   */
  secrecy?: boolean;
  /**
   * 水印
   */
  watermark?: boolean;
  /**
   * 上传中
   */
  uploading?: boolean;
  /**
   * 过期时间
   */
  expireTime?: number;
}


/**
 * [FsUpdateTextVo]上传文本路径请求参数
 */
export interface FsUpdateTextVo {

  /**
   * 文本内容, 必须字段, 最小长度：1
   */
  content?: string;
  /**
   * 文件名称
   */
  filename?: string;
  /**
   * 文件类型
   */
  contentType?: string;
  /**
   * 过期时间
   */
  exp?: number;
  /**
   * 保密文件，不公开
   */
  secrecy?: boolean;
}


/**
 * [FsQueryListByHashParamVo]文件上传前数据查询路径请求参数
 */
export interface FsQueryListByHashParamVo {

  /**
   * 文件hash值, 必须字段, 最小长度：0, 最大长度：64
   */
  hash?: string;
  /**
   * 文件尺寸，用于进行秒传检测，需要同时传递hash, 必须字段
   */
  size?: number;
}


/**
 * [FileInfoParamVo]文件信息查询路径请求参数
 */
export interface FileInfoParamVo {

  /**
   * 文件id, 必须字段
   */
  ids?: Array<string>;
}

export default {

  // method 文件上传接口
  // param query 请求参数
  upload: async (query: SysFsUploadQueryParam): Promise<ResDoc> => request.post(`${config.baseUrl.apiUrl}/sys/fs`, {}, { params: query, token: true }),


  // method 文本上传接口
  // param query 请求参数
  // param body 请求Body参数
  text: async (query: SysFsTextQueryParam, body: FsUpdateTextVo): Promise<ResDoc> => request.post(`${config.baseUrl.apiUrl}/sys/fs/text`, body, { params: query, token: true }),


  // method 通过Hash获取文件信息
  // param body 请求Body参数
  getListByHash: async (body: FsQueryListByHashParamVo, cf: { ticket?: string } = {}): Promise<Array<ResDoc>> => request.post(`${config.baseUrl.apiUrl}/sys/fs/query_by_hash`, body, { token: false, ticket: cf.ticket }),


  // method 批量文件信息查询
  // param query 请求参数
  // param body 请求Body参数
  infos: async (query: SysFsInfosQueryParam, body: FileInfoParamVo): Promise<Array<ResDoc>> => request.post(`${config.baseUrl.apiUrl}/sys/fs/batch/query/info`, body, { params: query, token: false, marginRequest: true }),


  // method 文件下载接口
  // param id: 文件Id
  // param query 请求参数
  download: async (id: string, query: SysFsDownloadQueryParam): Promise<void> => request.get(`${config.baseUrl.apiUrl}/sys/fs/${id}`, { params: query, token: false }),


  // method 文件信息查询接口
  // param id: undefined
  // param query 请求参数
  info: async (id: string, query: SysFsInfoQueryParam): Promise<ResDoc> => request.get(`${config.baseUrl.apiUrl}/sys/fs/${id}/info`, { params: query, token: false, marginRequest: true }),

}
