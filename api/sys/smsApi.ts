
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [SmsVerifySendVo]短信发送参数路径请求参数
export interface SmsVerifySendVo {

    // 电话号码
    phone?: string;
    // 短信模板
    tmplCode?: string;
    // 短信签名
    signName?: string;
}


// [SmsRespVo]消息发送回执路径请求参数
export interface SmsRespVo {

    // Id
    id?: string;
}


// [SmsSendVo]短信发送参数路径请求参数
export interface SmsSendVo {

    // 电话号码
    phone?: string;
    // 短信签名
    signName?: string;
    // 短信模板
    tmplCode?: string;
    // 参数，json格式
    param?: string;
    // 重试次数
    againNum?: number;
}


// [SmsBatchSendVo]短信批量发送参数路径请求参数
export interface SmsBatchSendVo {

    // 短信消息体
    bodies?: Array<SmsSendVo>;
}


// [SmsBatchRespVo]消息批量发送回执路径请求参数
export interface SmsBatchRespVo {

    // resps
    resps?: Array<SmsRespVo>;
}


// [MsgVerifyCodeBo]验证码返回值路径请求参数
export interface MsgVerifyCodeBo {

    // 账户
    account?: string;
    // code
    code?: string;
    // 创建时间
    createTime?: string;
}

export default {

    // method 发送短信验证码
    // param body 请求Body参数
    sendVerify: async (body: SmsVerifySendVo): Promise<SmsRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/sms/verify`, body, { token: false }),


    // method 发送短信
    // param body 请求Body参数
    send: async (body: SmsSendVo): Promise<SmsRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/sms/send`, body, { token: false }),


    // method 批量发送短信
    // param body 请求Body参数
    sendBatch: async (body: SmsBatchSendVo): Promise<SmsBatchRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/sms/batch/send`, body, { token: false }),


    // method 获取手机验证码
    // param id: undefined
    getMsgVerifyCode: async (id: string): Promise<MsgVerifyCodeBo> => request.get(`${config.baseUrl.apiUrl}/sys/sms/verify/${id}`, { token: false }),


    // method 删除手机验证码
    // param id: undefined
    delMsgVerifyCode: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/sys/sms/verify/${id}`, { token: false }),

}
