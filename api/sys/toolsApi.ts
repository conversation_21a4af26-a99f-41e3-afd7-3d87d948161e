
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 生成二维码路径请求参数
export interface ToolsBuildQRCodeQueryParam {

    // 内容
    content?: string;
    // 尺寸
    size?: number;
    // 前置颜色
    onc?: string;
    // 背景色
    bgc?: string;
    // 类型：base64
    type?: string;
}


// 生成条形码路径请求参数
export interface ToolsBuildCodeQueryParam {

    // 内容
    content?: string;
    // 宽度
    width?: number;
    // 高度
    height?: number;
    // 前置颜色
    onc?: string;
    // 背景色
    bgc?: string;
    // 类型：base64
    type?: string;
}


// [ToolsRespVo]工具响应类路径请求参数
export interface ToolsRespVo {

    // 内容
    content?: string;
}

export default {

    // method 生成二维码
    // param query 请求参数
    buildQRCode: async (query: ToolsBuildQRCodeQueryParam): Promise<ToolsRespVo> => request.get(`${config.baseUrl.apiUrl}/sys/tools/qrcode`, { params: query, token: false }),


    // method 生成条形码
    // param query 请求参数
    buildCode: async (query: ToolsBuildCodeQueryParam): Promise<ToolsRespVo> => request.get(`${config.baseUrl.apiUrl}/sys/tools/code`, { params: query, token: false }),

}
