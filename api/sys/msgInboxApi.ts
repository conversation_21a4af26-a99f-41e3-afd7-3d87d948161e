
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取列表路径请求参数
 */
export interface SysMsgInboxGetListQueryParam {

  /**
   * 用户id
   */
  userId?: string;
  /**
   * 状态（NEW，READED，DEL）
   */
  state?: string;
  /**
   * 业务编号
   */
  business_code?: string;
  /**
   * 类型
   */
  type?: string;
  /**
   * 页面
   */
  page?: number;
  /**
   * 每页多少条
   */
  rows?: number;
}


/**
 * 获取数量路径请求参数
 */
export interface SysMsgInboxGetListCountQueryParam {

  /**
   * 用户id
   */
  userId?: string;
  /**
   * 状态（NEW，READED，DEL）
   */
  state?: string;
  /**
   * 业务编号
   */
  business_code?: string;
  /**
   * 类型
   */
  type?: string;
}


/**
 * 获取当前用户列表路径请求参数
 */
export interface SysMsgInboxGetListByMeQueryParam {

  /**
   * 状态（NEW，READED，DEL）
   */
  state?: string;
  /**
   * 业务编号
   */
  business_code?: string;
  /**
   * 类型
   */
  type?: string;
  /**
   * 页面
   */
  page?: number;
  /**
   * 每页多少条
   */
  rows?: number;
}


/**
 * 获取当前用户数量路径请求参数
 */
export interface SysMsgInboxGetListCountByMeQueryParam {

  /**
   * 状态（NEW，READED，DEL）
   */
  state?: string;
  /**
   * 业务编号
   */
  business_code?: string;
  /**
   * 类型
   */
  type?: string;
}


/**
 * [MsgInboxPutNotifyVo]阅读设置路径请求参数
 */
export interface MsgInboxPutNotifyVo {

  /**
   * notify, 必须字段
   */
  notify?: boolean;
}


/**
 * [MsgInboxPutDelVo]删除设置路径请求参数
 */
export interface MsgInboxPutDelVo {

  /**
   * del, 必须字段
   */
  del?: boolean;
}


/**
 * [MsgInboxSendBo]发送参数路径请求参数
 */
export interface MsgInboxSendBo {

  /**
   * 业务编号, 必须字段, 最小长度：1, 最大长度：32
   */
  businessCode?: string;
  /**
   * 内容, 最小长度：0, 最大长度：4096
   */
  msg?: string;
  /**
   * 标题, 必须字段, 最小长度：1, 最大长度：256
   */
  title?: string;
  /**
   * 跳转数据集合, 最小长度：0, 最大长度：1024
   */
  toData?: string;
  /**
   * 跳转目标编号, 最小长度：0, 最大长度：512
   */
  toPage?: string;
  /**
   * 类型, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 消息类型, 必须字段, 最小长度：1, 最大长度：32
   */
  msgType?: string;
  /**
   * 用户id，指定用户，ALL：代表所有人, 必须字段, 最小长度：1, 最大长度：32
   */
  userId?: string;
}


/**
 * [MsgInboxBatchSendVo]批量发送参数路径请求参数
 */
export interface MsgInboxBatchSendVo {

  /**
   * 消息体
   */
  bodies?: Array<MsgInboxSendBo>;
}


/**
 * [MsgInbox]站内信息路径请求参数
 */
export interface MsgInbox {

  /**
   * 业务编号, 必须字段, 最小长度：1, 最大长度：32
   */
  businessCode?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 删除, 必须字段
   */
  del?: boolean;
  /**
   * 删除时间
   */
  delTime?: number;
  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 内容, 最小长度：0, 最大长度：512
   */
  msg?: string;
  /**
   * 已读, 必须字段
   */
  notify?: boolean;
  /**
   * 已读时间
   */
  notifyTime?: number;
  /**
   * 标题, 必须字段, 最小长度：1, 最大长度：256
   */
  title?: string;
  /**
   * 跳转数据集合, 最小长度：0, 最大长度：1024
   */
  toData?: string;
  /**
   * 跳转目标编号, 最小长度：0, 最大长度：512
   */
  toPage?: string;
  /**
   * 类型, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 消息类型, 必须字段, 最小长度：1, 最大长度：32
   */
  msgType?: string;
  /**
   * 用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  userId?: string;
  /**
   * 状态（NEW，READED，DEL）, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
}


/**
 * [MsgInboxDeleteAllVo]删除设置路径请求参数
 */
export interface MsgInboxDeleteAllVo {

  /**
   * state
   */
  state?: string;
}

export default {

  /**
   * 已读标记
   * @param id: 主键
   * @param body 请求Body参数
   */
  putNotify: async (id: string, body: MsgInboxPutNotifyVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/sys/msg-inbox/${id}/notify`, body, { token: true }),


  /**
   * 删除标记
   * @param id: 主键
   * @param body 请求Body参数
   */
  putDel: async (id: string, body: MsgInboxPutDelVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/sys/msg-inbox/${id}/del`, body, { token: true }),


  /**
   * 已读标记
   * @param id: 主键
   * @param body 请求Body参数
   */
  putNotifyByMe: async (id: string, body: MsgInboxPutNotifyVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/${id}/notify`, body, { token: true }),


  /**
   * 删除标记
   * @param id: 主键
   * @param body 请求Body参数
   */
  putDelByMe: async (id: string, body: MsgInboxPutDelVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/${id}/del`, body, { token: true }),


  /**
   * 全部标记为已读
   * @param body 请求Body参数
   */
  putAllNotifyByMe: async (body: MsgInboxPutNotifyVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/all/notify`, body, { token: true }),


  /**
   * 全部标记为删除
   * @param body 请求Body参数
   */
  putAllDelByMe: async (body: MsgInboxPutDelVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/all/del`, body, { token: true }),


  /**
   * 发送站内
   * @param body 请求Body参数
   */
  send: async (body: MsgInboxSendBo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/sys/msg-inbox/send`, body, { token: true }),


  /**
   * 批量发送站内
   * @param body 请求Body参数
   */
  sendBatch: async (body: MsgInboxBatchSendVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/sys/msg-inbox/batch/send`, body, { token: true }),


  /**
   * 获取列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: SysMsgInboxGetListQueryParam): Promise<Array<MsgInbox>> => request.get(`${config.baseUrl.apiUrl}/sys/msg-inbox`, { params: query, token: true }),


  /**
   * 获取详情
   * @param id: 主键
   * @returns 数据
   */
  get: async (id: string): Promise<MsgInbox> => request.get(`${config.baseUrl.apiUrl}/sys/msg-inbox/${id}`, { token: true }),


  /**
   * 删除数据
   * @param id: 主键
   */
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/sys/msg-inbox/${id}`, { token: true }),


  /**
   * 获取数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: SysMsgInboxGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/sys/msg-inbox/count`, { params: query, token: true }),


  /**
   * 获取当前用户列表
   * @param query 请求参数
   * @returns 数据
   */
  getListByMe: async (query: SysMsgInboxGetListByMeQueryParam): Promise<Array<MsgInbox>> => request.get(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me`, { params: query, token: true }),


  /**
   * 获取当前用户详情
   * @param id: 主键
   * @returns 数据
   */
  getByMe: async (id: string): Promise<MsgInbox> => request.get(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/${id}`, { token: true }),


  /**
   * 删除数据
   * @param id: 主键
   */
  delteByMe: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/${id}`, { token: true }),


  /**
   * 获取当前用户数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCountByMe: async (query: SysMsgInboxGetListCountByMeQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/count`, { params: query, token: true }),


  /**
   * 删除全部数据
   * @param body 请求Body参数
   */
  deleteAllByMe: async (body: MsgInboxDeleteAllVo): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/sys/msg-inbox/by-me/all`, { data: body, token: true }),

}
