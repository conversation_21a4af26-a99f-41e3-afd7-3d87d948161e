
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [TmplConvertParamVo]转换参数路径请求参数
export interface TmplConvertParamVo {

    // 掩层图片，suc.img
    watermarkImg?: string;
    // 目标类型，pdf, xlsx
    type?: string;
    // 数据内容, json格式
    jxlsData?: string;
    // 模板名称：scu.xlsx
    jxlsTmplName?: string;
    // 水印缩放比例
    watermarkPercent?: number;
}

export default {

    // method 模板服务，数据组装，响应文件
    // param body 请求Body参数
    convert: async (body: TmplConvertParamVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/sys/tmpl/convert`, body, { token: false }),

}
