
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [MaintainUpgradeParamVo]监控升级信息参数路径请求参数
 */
export interface MaintainUpgradeParamVo {

  /**
   * 数据内容
   */
  sql?: string;
}


/**
 * [MaintainUpgradeRespVo]监控升级信息响应路径请求参数
 */
export interface MaintainUpgradeRespVo {

  /**
   * 数量
   */
  count?: number;
}


/**
 * [MaintainUpgradeQueryRespFieldBo]监控升级信息字段路径请求参数
 */
export interface MaintainUpgradeQueryRespFieldBo {

  /**
   * Code
   */
  code?: string;
  /**
   * 类型
   */
  type?: string;
}


/**
 * [MaintainUpgradeQueryRespBo]监控升级信息路径请求参数
 */
export interface MaintainUpgradeQueryRespBo {

  /**
   * 数据库类型
   */
  dbType?: string;
  /**
   * 数据内容
   */
  data?: Array<any>;
  /**
   * 字段信息
   */
  fields?: Array<MaintainUpgradeQueryRespFieldBo>;
}

export default {

  /**
   * 执行更新数据
   * @param body 请求Body参数
   * @returns 数据
   */
  update: async (body: MaintainUpgradeParamVo): Promise<MaintainUpgradeRespVo> => await request.post(`${config.baseUrl.apiUrl}/sys/maintain/upgrade/exec/update`, body, { token: true, pageRole: false }),


  /**
   * 执行查询数据
   * @param body 请求Body参数
   * @returns 数据
   */
  getQuery: async (body: MaintainUpgradeParamVo): Promise<MaintainUpgradeQueryRespBo> => await request.post(`${config.baseUrl.apiUrl}/sys/maintain/upgrade/exec/query`, body, { token: true, pageRole: false }),


  /**
   * 执行插入数据
   * @param body 请求Body参数
   * @returns 数据
   */
  insert: async (body: MaintainUpgradeParamVo): Promise<MaintainUpgradeRespVo> => await request.post(`${config.baseUrl.apiUrl}/sys/maintain/upgrade/exec/insert`, body, { token: true, pageRole: false }),

  /**
   * 执行删除数据
   * @param body 请求Body参数
   * @returns 数据
   */
  delete: async (body: MaintainUpgradeParamVo): Promise<MaintainUpgradeRespVo> => await request.post(`${config.baseUrl.apiUrl}/sys/maintain/upgrade/exec/delete`, body, { token: true, pageRole: false }),

}
