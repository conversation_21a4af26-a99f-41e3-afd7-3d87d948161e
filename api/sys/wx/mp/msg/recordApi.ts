
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [MsgRecord]消息日志路径请求参数
export interface MsgRecord {

    // 主键
    id?: string;
    // 消息类型(SMS：短信、EMAIL：邮件、MP：小程序)
    type?: string;
    // 创建时间
    createTime?: string;
    // 模板编号
    tmplCode?: string;
    // 标题
    subject?: string;
    // 模板内容
    contentTmpl?: string;
    // 对象内容
    contentData?: string;
    // 详细内容
    contentDetail?: string;
    // 备注信息
    remark?: string;
    // 目标账户
    toCode?: string;
    // 抄送账户
    ccCode?: string;
    // 隐蔽抄送账户
    bccCode?: string;
    // 附件url
    attachment?: string;
    // cids
    cids?: string;
    // 签名
    signName?: string;
    // 目标地址
    toPage?: string;
    // 已发送
    sent?: boolean;
    // 发送时间
    sendTime?: string;
    // 平台业务id号
    platformBizId?: string;
    // 上传编号
    platformUpExtendCode?: string;
    // 平台模板编号
    platformTmplCode?: string;
    // 发送结果（WAIT：等待，SUCCESS：成功，FAIL：失败）
    sendResult?: string;
    // 发送结果回执时间
    sendResultTime?: string;
    // 发送结果信息
    sendResultMsg?: string;
    // 已重试
    again?: boolean;
    // 重试时间
    againTime?: string;
    // 重试次数
    againNum?: number;
    // 重试id
    againId?: string;
    // 上一级id
    parentId?: string;
}

export default {

    // method 获取小程序记录
    // param id: undefined
    get: async (id: string): Promise<MsgRecord> => request.get(`${config.baseUrl.apiUrl}/sys/wx/mp/msg/record/${id}`, { token: false }),

}
