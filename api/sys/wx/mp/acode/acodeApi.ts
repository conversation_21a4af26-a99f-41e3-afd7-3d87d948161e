
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 生成小程序码路径请求参数
export interface WxMpAcodeAcodeBuildACodeQueryParam {

    sys_app_id?: string;
    scene?: string;
    page?: string;
    // 尺寸
    size?: number;
    // 类型：base64
    type?: string;
    auto_color?: boolean;
    // 颜色rgb: 255,255,255
    rgb?: string;
    // 内容
    content?: null;
    // 自动颜色
    autoColor?: null;
}


// [ToolsRespVo]工具响应类路径请求参数
export interface ToolsRespVo {

    // 内容
    content?: string;
}

export default {

    // method 生成小程序码
    // param query 请求参数
    buildACode: async (query: WxMpAcodeAcodeBuildACodeQueryParam): Promise<ToolsRespVo> => request.get(`${config.baseUrl.apiUrl}/sys/wx/mp/acode/acode`, { params: query, token: false }),

}
