
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [WxMpVerifySendVo]发送验证码参数路径请求参数
export interface WxMpVerifySendVo {

    // openId
    openId?: string;
    // 应用id
    sysAppId?: string;
    // 短信模板
    tmplCode?: string;
    // 目标地址
    toPage?: string;
}


// [WxMpRespVo]消息发送回执路径请求参数
export interface WxMpRespVo {

    // Id
    id?: string;
}


// [WxMpSendVo]发送参数路径请求参数
export interface WxMpSendVo {

    // openId
    openId?: string;
    // 应用id
    sysAppId?: string;
    // 目标地址
    toPage?: string;
    // 短信模板
    tmplCode?: string;
    // 参数，json格式
    param?: string;
}


// [WxMpBatchSendVo]批量发送参数路径请求参数
export interface WxMpBatchSendVo {

    // 消息体
    bodies?: Array<WxMpSendVo>;
}


// [WxMpBatchRespVo]消息批量发送回执路径请求参数
export interface WxMpBatchRespVo {

    // resps
    resps?: Array<WxMpRespVo>;
}


// [MsgVerifyCodeBo]验证码返回值路径请求参数
export interface MsgVerifyCodeBo {

    // 账户
    account?: string;
    // code
    code?: string;
    // 创建时间
    createTime?: string;
}

export default {

    // method 发送验证码
    // param body 请求Body参数
    sendVerify: async (body: WxMpVerifySendVo): Promise<WxMpRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/wx/mp/msg/verify`, body, { token: false }),


    // method 发送信息
    // param body 请求Body参数
    send: async (body: WxMpSendVo): Promise<WxMpRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/wx/mp/msg/send`, body, { token: false }),


    // method 批量发送
    // param body 请求Body参数
    sendBatch: async (body: WxMpBatchSendVo): Promise<WxMpBatchRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/wx/mp/msg/batch/send`, body, { token: false }),


    // method 获取验证码
    // param id: undefined
    getMsgVerifyCode: async (id: string): Promise<MsgVerifyCodeBo> => request.get(`${config.baseUrl.apiUrl}/sys/wx/mp/msg/verify/${id}`, { token: false }),


    // method 删除验证码
    // param id: undefined
    delMsgVerifyCode: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/sys/wx/mp/msg/verify/${id}`, { token: false }),

}
