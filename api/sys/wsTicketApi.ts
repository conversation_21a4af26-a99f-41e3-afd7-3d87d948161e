
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [WsRevokeTicketBo]吊销Ticket信息路径请求参数
 */
export interface WsRevokeTicketBo {

  /**
   * 临时令牌
   */
  ticket?: string;
}


/**
 * [WsTicketMsgBo]ws的Ticket信息路径请求参数
 */
export interface WsTicketMsgBo {

  /**
   * 临时令牌
   */
  ticket?: string;
}

export default {

  /**
   * 吊销ticket
   * @param body 请求Body参数
   */
  revoke: async (body: WsRevokeTicketBo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/sys/ws-ticket/revoke`, body, { token: false, pageRole: false }),


  /**
   * 通过token构建ticket
   * @returns 数据
   */
  buildByToken: async (): Promise<WsTicketMsgBo> => await request.post(`${config.baseUrl.apiUrl}/sys/ws-ticket/build-by-token`, {}, { token: true, pageRole: false }),

}
