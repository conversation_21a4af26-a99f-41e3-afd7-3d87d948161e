
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 获取验证码图片路径请求参数
export interface CaptchaImgQueryParam {

    // uuid，必须32位
    uuid?: string;
    // 宽度
    w?: number;
    // 高度
    h?: number;
    // 验证码类型：SC:普通，GC:GIF图片，CC:中文，CGC:中文GIF，AC:算数,RV:旋转
    type?: string;
    // 响应数据类型, base64
    rtype?: string;
}


// [CaptchaMsgRo]结果数据路径请求参数
export interface CaptchaMsgRo {

    // uuid
    uuid?: string;
    // 验证码值
    code?: string;
    // 验证码类型安全级别
    level?: number;
    // 验证码类型，SC:普通，GC:GIF图片，CC:中文，CGC:中文GIF，AC:算数
    type?: string;
}


// [CaptchaRespVo]验证码响应类路径请求参数
export interface CaptchaRespVo {

    // 内容
    content?: string;
}

export default {

    // method 获取code
    // param uuid: uuid
    getCode: async (uuid: string): Promise<CaptchaMsgRo> => request.get(`${config.baseUrl.apiUrl}/sys/captcha/${uuid}`, { token: false }),


    // method 获取验证码图片
    // param query 请求参数
    img: async (query: CaptchaImgQueryParam): Promise<CaptchaRespVo> => request.get(`${config.baseUrl.apiUrl}/sys/captcha/img`, { params: query, token: false }),

}
