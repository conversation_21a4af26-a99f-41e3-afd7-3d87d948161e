
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [WsMsgSendBodyVo]长连接批量参数路径请求参数
export interface WsMsgSendBodyVo {

    // 业务code
    bc?: string;
    // 功能code
    fc?: string;
    // 事件code
    ec?: string;
    // 数据体
    d?: string;
}


// [WsMsgSendVo]长连接发送数据路径请求参数
export interface WsMsgSendVo {

    // 所有用户
    all?: boolean;
    // 登录用户
    login?: boolean;
    msg?: WsMsgSendBodyVo;
    uids?: Array<string>;
}


// [WsMsgBatchSendVo]批量发送参数路径请求参数
export interface WsMsgBatchSendVo {

    // 消息体
    msgs?: Array<WsMsgSendVo>;
}

export default {

    // method 发送
    // param body 请求Body参数
    send: async (body: WsMsgSendVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/sys/ws-msg/send`, body, { token: false }),


    // method 批量发送
    // param body 请求Body参数
    sendBatch: async (body: WsMsgBatchSendVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/sys/ws-msg/batch/send`, body, { token: false }),

}
