
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [MsgReceipt]消息回执路径请求参数
export interface MsgReceipt {

    // 主键
    id?: string;
    // 类型(SMS:短信,EMAIL:邮件,MP:小程序)
    type?: string;
    // 找到关联的record_id
    recordId?: string;
    // 接收到的时间
    receiveTime?: string;
    // 远端账户
    remoteCode?: string;
    // 目标编号
    destCode?: string;
    // 签名
    signName?: string;
    // 内容
    content?: string;
}

export default {

    // method 获取短信回复记录
    // param id: undefined
    get: async (id: string): Promise<MsgReceipt> => request.get(`${config.baseUrl.apiUrl}/sys/sms/receipt/${id}`, { token: false }),

}
