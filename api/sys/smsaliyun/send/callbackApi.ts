
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// undefined路径请求参数
export interface SmsAliyunController$SmsReportBo {

    phone_number?: string;
    success?: boolean;
    biz_id?: string;
    out_id?: string;
    send_time?: string;
    report_time?: string;
    err_code?: string;
    err_msg?: string;
    sms_size?: string;
}


// [AliSmsReportVo]消息发送回执路径请求参数
export interface AliSmsReportVo {

    // code
    code?: number;
    // msg
    msg?: string;
}

export default {

    // method 获取短信记录
    // param body 请求Body参数
    callback: async (body: Array<SmsAliyunController$SmsReportBo>): Promise<AliSmsReportVo> => request.post(`${config.baseUrl.apiUrl}/sys/sms-aliyun/send/callback`, body, { token: false }),

}
