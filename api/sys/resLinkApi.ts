
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [ResLinkAddVo]连接映射信息路径请求参数
export interface ResLinkAddVo {

    // Id类型
    idType?: string;
    // 过期时间
    expireTime?: string;
    // 业务编号
    businessCode?: string;
    // 类型
    type?: string;
    // 跳转目标编号
    toPage?: string;
    // 数据集合
    data?: string;
}


// [ResLinkRespVo]连接映射结果信息路径请求参数
export interface ResLinkRespVo {

    // Id
    id?: string;
}


// [ResLink]连接映射路径请求参数
export interface ResLink {

    // 主键
    id?: string;
    // 创建时间
    createTime?: string;
    // 过期时间
    expireTime?: string;
    // 业务编号
    businessCode?: string;
    // 类型
    type?: string;
    // 跳转目标编号
    toPage?: string;
    // 数据集合
    data?: string;
}

export default {

    // method 创建连接
    // param body 请求Body参数
    post: async (body: ResLinkAddVo): Promise<ResLinkRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/res-link`, body, { token: false }),


    // method 查询详情
    // param id: undefined
    get: async (id: string): Promise<ResLink> => request.get(`${config.baseUrl.apiUrl}/sys/res-link/${id}`, { token: false }),


    // method 查询详情，未找到时返回空
    // param id: undefined
    find: async (id: string): Promise<ResLink> => request.get(`${config.baseUrl.apiUrl}/sys/res-link/find/${id}`, { token: false }),

}
