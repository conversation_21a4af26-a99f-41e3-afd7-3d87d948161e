
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [TaskProgressBo]任务进度信息路径请求参数
export interface TaskProgressBo {

  // ID
  id?: string;
  // 用户id
  uid?: string;
  // 类型
  type?: string;
  // 开始时间
  beginTime?: number;
  // 总数
  total?: number;
  // 进度
  progress?: number;
  // 完成
  finished?: boolean;
  // 成功
  success?: boolean;
  // 记录信息
  msgs?: Array<string>;
  // 成功信息
  data?: string;
  // 失败信息
  errorData?: string;
}


// [ResDoc]资源索引表路径请求参数
export interface ResDoc {

  // 主键, 必须字段, 最小长度：1, 最大长度：32
  id?: string;
  // 版本号, 必须字段, 最小长度：1, 最大长度：32
  versionNo?: string;
  // 文件类型, 最小长度：0, 最大长度：128
  mimeType?: string;
  // 文件后缀, 最小长度：0, 最大长度：32
  suffix?: string;
  // 文件名, 最小长度：0, 最大长度：128
  name?: string;
  // 文件大小
  size?: number;
  // 创建时间, 必须字段
  createTime?: number;
  // 描述, 最小长度：0, 最大长度：1024
  description?: string;
  // 存储路径, 最小长度：0, 最大长度：64
  path?: string;
  // 文件hash值, 最小长度：0, 最大长度：64
  hash?: string;
  // 用户编号, 必须字段, 最小长度：1, 最大长度：32
  userId?: string;
  // 被禁用
  ban?: boolean;
  // 上传中
  uploading?: boolean;
}

export default {

  // method 提交任务查询任务
  // param code: Code信息
  submit: async (code: string): Promise<TaskProgressBo> => request.post(`${config.baseUrl.apiUrl}/sys/report/exec/query/${code}/submit`, {}, { token: true }),

  // method 任务进度查询
  // param pid: 任务id
  progress: async (pid: string): Promise<TaskProgressBo> => request.post(`${config.baseUrl.apiUrl}/sys/report/exec/query/progress/${pid}`, {}, { token: true }),

}
