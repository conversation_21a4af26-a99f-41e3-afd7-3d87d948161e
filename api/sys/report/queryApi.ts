
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 查询列表信息路径请求参数
export interface SysReportQueryGetListQueryParam {

  // 每页条数
  rows?: number;
  // 页码，从0开始
  page?: number;
  // Code信息
  code?: string;
}


// 查询列表数量信息路径请求参数
export interface SysReportQueryGetListCountQueryParam {

  // Code信息
  code?: string;
}


// [ReportQueryVo]报告查询信息路径请求参数
export interface ReportQueryVo {

  // 主键, 必须字段, 最小长度：1, 最大长度：32
  id?: string;
  // 生效, 必须字段
  valid?: boolean;
  // 执行统计查询, 必须字段
  execTotalQuery?: boolean;
  // 配置信息code，URL路径, 最小长度：0, 最大长度：64
  code?: string;
  // 名称, 必须字段, 最小长度：1, 最大长度：64
  name?: string;
  // 查询指令, 必须字段
  queryCmd?: string;
  // 字段信息
  columns?: Array<ReportQueryColumnBo>;
  // 创建时间, 必须字段
  createTime?: number;
  // 备注, 最小长度：0, 最大长度：512
  remark?: string;
}


// [ReportQueryAddVo]报告查询添加信息路径请求参数
export interface ReportQueryAddVo {

  // 生效, 必须字段
  valid?: boolean;
  // 执行统计查询, 必须字段
  execTotalQuery?: boolean;
  // 配置信息code，URL路径, 最小长度：0, 最大长度：64
  code?: string;
  // 名称, 必须字段, 最小长度：1, 最大长度：64
  name?: string;
  // 查询指令, 必须字段
  queryCmd?: string;
  // 备注, 最小长度：0, 最大长度：512
  remark?: string;
}


// [ReportQueryModifyColumnVo]报告查询字段信息路径请求参数
export interface ReportQueryModifyColumnVo {

  // 字段信息, 必须字段
  columns?: Array<ReportQueryColumnBo>;
}


// [ReportQuery]报告查询信息路径请求参数
export interface ReportQuery {

  // 主键, 必须字段, 最小长度：1, 最大长度：32
  id?: string;
  // 生效, 必须字段
  valid?: boolean;
  // 执行统计查询, 必须字段
  execTotalQuery?: boolean;
  // 配置信息code，URL路径, 最小长度：0, 最大长度：64
  code?: string;
  // 名称, 必须字段, 最小长度：1, 最大长度：64
  name?: string;
  // 查询指令, 必须字段
  queryCmd?: string;
  // 字段信息的JSON格式
  columnsJson?: string;
  // 创建时间, 必须字段
  createTime?: number;
  // 备注, 最小长度：0, 最大长度：512
  remark?: string;
}


// undefined路径请求参数
export interface ExcelExportColumnHandler {


}


// [ReportQueryColumnBo]报告字段路径请求参数
export interface ReportQueryColumnBo {

  // 字段名称
  code?: string;
  // 展示名称
  name?: string;
  // 类型
  type?: string;
  // 数据字典Code
  dicCode?: string;
  // 时间格式化方案
  dateFormat?: string;
  // 为真时的值
  trueValue?: string;
  // 为假时的值
  falseValue?: string;
  // fun
  fun?: ExcelExportColumnHandler;
}

export default {

  // method 查询信息
  // param id: ID
  get: async (id: string): Promise<ReportQueryVo> => request.get(`${config.baseUrl.apiUrl}/sys/report/query/${id}`, { token: true }),


  // method 修改数据
  // param id: ID
  // param body 请求Body参数
  modify: async (id: string, body: ReportQueryAddVo): Promise<ReportQueryVo> => request.put(`${config.baseUrl.apiUrl}/sys/report/query/${id}`, body, { token: true }),


  // method 删除查询数据
  // param id: ID
  delete: async (id: string): Promise<number> => request.delete(`${config.baseUrl.apiUrl}/sys/report/query/${id}`, { token: true }),


  // method 修改字段数据
  // param id: ID
  // param body 请求Body参数
  modifyColumns: async (id: string, body: ReportQueryModifyColumnVo): Promise<ReportQueryVo> => request.put(`${config.baseUrl.apiUrl}/sys/report/query/${id}/columns`, body, { token: true }),


  // method 查询列表信息
  // param query 请求参数
  getList: async (query: SysReportQueryGetListQueryParam): Promise<Array<ReportQuery>> => request.get(`${config.baseUrl.apiUrl}/sys/report/query`, { params: query, token: true }),


  // method 创建数据
  // param body 请求Body参数
  add: async (body: ReportQueryAddVo): Promise<ReportQueryVo> => request.post(`${config.baseUrl.apiUrl}/sys/report/query`, body, { token: true }),


  // method 查询信息
  // param id: ID
  getDefFields: async (id: string): Promise<Array<ReportQueryColumnBo>> => request.get(`${config.baseUrl.apiUrl}/sys/report/query/${id}/def-columns`, { token: true }),


  // method 查询列表数量信息
  // param query 请求参数
  getListCount: async (query: SysReportQueryGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/sys/report/query/count`, { params: query, token: true }),

}
