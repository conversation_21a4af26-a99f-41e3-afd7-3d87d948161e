
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [FsUpSyncRenewParamVo]上传同步对象参数路径请求参数
 */
export interface FsUpSyncRenewParamVo {

  /**
   * 内容信息, 最小长度：0, 最大长度：10000
   */
  content?: string;
}


/**
 * [FsUpSyncRenewResultVo]上传同步对象参数路径请求参数
 */
export interface FsUpSyncRenewResultVo {

  /**
   * 值
   */
  value?: string[];
  /**
   * 有效
   */
  valid?: boolean;

  /**
   * 已被扫描
   */
  scaned?: boolean;
}


/**
 * [FsUpSyncMsgParamVo]上传同步对象参数路径请求参数
 */
export interface FsUpSyncMsgParamVo {

  /**
   * 内容信息, 最小长度：0, 最大长度：10000
   */
  value?: string;
}


/**
 * [FsUpSyncMsgResultVo]上传同步对象参数路径请求参数
 */
export interface FsUpSyncMsgResultVo {

  /**
   * 令牌
   */
  ticket?: string;
  /**
   * 基础信息
   */
  baseMsg?: string;
  /**
   * 内容
   */
  content?: string[];
  /**
   * 有效
   */
  valid?: boolean;
}


/**
 * [FsUpSyncTicketParamVo]上传同步对象参数路径请求参数
 */
export interface FsUpSyncTicketParamVo {
  /**
   * 基础信息
   */
  baseMsg?: string;
}


/**
 * [FsUpSyncTicketResultVo]上传同步对象路径请求参数
 */
export interface FsUpSyncTicketResultVo {

  /**
   * 内容id
   */
  id?: string;
}

export default {
  /**
   * 吊销
   * @param id: id
   */
  revoke: async (id: string): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/sys/fs/up-sync/${id}/revoke`, {}, { token: true, pageRole: false }),

  /**
   * 续期并设置content
   * @param id: id
   * @param body 请求Body参数
   * @returns 数据
   */
  renew: async (id: string, body: FsUpSyncRenewParamVo): Promise<FsUpSyncRenewResultVo> => await request.post(`${config.baseUrl.apiUrl}/sys/fs/up-sync/${id}/renew`, body, { token: true, pageRole: false }),


  /**
   * 消息
   * @param id: id
   * @param body 请求Body参数
   * @returns 数据
   */
  putValue: async (id: string, body: FsUpSyncMsgParamVo): Promise<FsUpSyncMsgResultVo> => await request.post(`${config.baseUrl.apiUrl}/sys/fs/up-sync/${id}/put-value`, body, { token: false, pageRole: false }),


  /**
   * 构建文件同步许可
   * @param body 请求Body参数
   * @returns 数据
   */
  buildTicket: async (body: FsUpSyncTicketParamVo): Promise<FsUpSyncTicketResultVo> => await request.post(`${config.baseUrl.apiUrl}/sys/fs/up-sync/build-ticket`, body, { token: true, pageRole: false }),

}
