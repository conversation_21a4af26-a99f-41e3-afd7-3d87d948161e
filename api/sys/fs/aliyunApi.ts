
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 上传文件请求路径请求参数
 */
export interface SysFsAliyunUploadRequestQueryParam {

  /**
   * UUID
   */
  uuid?: string;
}


/**
 * [FsUploadPreParamVo]文件上传与上传数据路径请求参数
 */
export interface FsUploadPreParamVo {

  /**
   * 文件hash值, 最小长度：0, 最大长度：64
   */
  hash?: string;
  /**
   * 文件尺寸，用于进行秒传检测，需要同时传递hash, 必须字段
   */
  size?: number;
  /**
   * 文件名称, 必须字段, 最小长度：0, 最大长度：128
   */
  name?: string;
  /**
   * 文件类型, 必须字段, 最小长度：0, 最大长度：128
   */
  mimeType?: string;
  /**
   * 过期时间
   */
  exp?: number;
  /**
   * 保密文件，不公开
   */
  secrecy?: boolean;
  /**
   * 水印
   */
  watermark?: boolean;
}


/**
 * [FsAliyunUploadAuthPreVo]阿里云文件认证信息路径请求参数
 */
export interface FsAliyunUploadAuthPreVo {

  /**
   * 认证id
   */
  accessKeyId?: string;
  /**
   * 认证Secret
   */
  accessKeySecret?: string;
  /**
   * 过期时间
   */
  expiration?: string;
  /**
   * 安全token
   */
  securityToken?: string;
  /**
   * 上传路径
   */
  filepath?: string;
  /**
   * 桶信息
   */
  bucket?: string;
  /**
   * 地址信息
   */
  region?: string;
}


/**
 * [ResDoc]资源索引表路径请求参数
 */
export interface ResDoc {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 版本号, 必须字段, 最小长度：1, 最大长度：32
   */
  versionNo?: string;
  /**
   * 文件类型, 最小长度：0, 最大长度：128
   */
  mimeType?: string;
  /**
   * 文件后缀, 最小长度：0, 最大长度：32
   */
  suffix?: string;
  /**
   * 文件名, 最小长度：0, 最大长度：128
   */
  name?: string;
  /**
   * 文件大小
   */
  size?: number;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 描述, 最小长度：0, 最大长度：1024
   */
  description?: string;
  /**
   * 存储路径, 最小长度：0, 最大长度：64
   */
  path?: string;
  /**
   * 文件hash值, 最小长度：0, 最大长度：128
   */
  hash?: string;
  /**
   * 用户编号, 必须字段, 最小长度：1, 最大长度：32
   */
  userId?: string;
  /**
   * uuid, 最小长度：0, 最大长度：32
   */
  uuid?: string;
  /**
   * 被禁用
   */
  ban?: boolean;
  /**
   * 保密
   */
  secrecy?: boolean;
  /**
   * 水印
   */
  watermark?: boolean;
  /**
   * 上传中
   */
  uploading?: boolean;
  /**
   * 过期时间
   */
  expireTime?: number;
}


/**
 * [FsAliyunUploadPreVo]阿里云文件预上传对象路径请求参数
 */
export interface FsAliyunUploadPreVo {

  /**
   * 文件已存在
   */
  exist?: boolean;
  /**
   * 认证信息
   */
  auth?: FsAliyunUploadAuthPreVo;
  /**
   * 数据对象
   */
  doc?: ResDoc;
}

export default {

  // method 上传文件请求
  // param query 请求参数
  // param body 请求Body参数
  uploadRequest: async (query: SysFsAliyunUploadRequestQueryParam, body: FsUploadPreParamVo, cf: { ticket?: string } = {}): Promise<FsAliyunUploadPreVo> => request.post(`${config.baseUrl.apiUrl}/sys/fs/aliyun`, body, { params: query, token: true, ticket: cf.ticket }),


  // method 上传完成标识符
  // param id: 文件id
  uploaded: async (id: string, cf: { ticket?: string } = {}): Promise<void> => request.post(`${config.baseUrl.apiUrl}/sys/fs/aliyun/${id}/uploaded`, {}, { token: true, ticket: cf.ticket }),

}
