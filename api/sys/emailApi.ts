
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [EmailAccountVo]邮件账户路径请求参数
export interface EmailAccountVo {

    // 目标email
    email?: string;
    // 目标用户
    name?: string;
}


// [EmailVerifySendVo]发送验证码参数路径请求参数
export interface EmailVerifySendVo {

    // 主题
    subject?: string;
    // 短信签名
    signName?: string;
    toEmail?: EmailAccountVo;
    // 短信模板
    tmplCode?: string;
}


// [EmailRespVo]消息发送回执路径请求参数
export interface EmailRespVo {

    // Id
    id?: string;
}


// [EmailCidVo]邮件cid路径请求参数
export interface EmailCidVo {

    // cid
    cid?: string;
    // 名称
    name?: string;
    // url
    url?: string;
}


// [EmailAttachmentVo]邮件附件路径请求参数
export interface EmailAttachmentVo {

    // 名称
    name?: string;
    // url
    url?: string;
}


// [EmailSendVo]发送参数路径请求参数
export interface EmailSendVo {

    // 目标用户
    toEmails?: Array<EmailAccountVo>;
    // 抄送用户
    ccEmails?: Array<EmailAccountVo>;
    // 隐蔽抄送用户
    bccEmails?: Array<EmailAccountVo>;
    // 发送人签名
    signName?: string;
    // 主题
    subject?: string;
    // 短信模板
    tmplCode?: string;
    // 参数，json格式
    param?: string;
    // cid资源
    cids?: Array<EmailCidVo>;
    // 附件信息
    attachments?: Array<EmailAttachmentVo>;
}


// [MsgVerifyCodeBo]验证码返回值路径请求参数
export interface MsgVerifyCodeBo {

    // 账户
    account?: string;
    // code
    code?: string;
    // 创建时间
    createTime?: string;
}

export default {

    // method 发送验证码
    // param body 请求Body参数
    sendVerify: async (body: EmailVerifySendVo): Promise<EmailRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/email/verify`, body, { token: false }),


    // method 发送信息
    // param body 请求Body参数
    send: async (body: EmailSendVo): Promise<EmailRespVo> => request.post(`${config.baseUrl.apiUrl}/sys/email/send`, body, { token: false }),


    // method 获取验证码
    // param id: undefined
    getMsgVerifyCode: async (id: string): Promise<MsgVerifyCodeBo> => request.get(`${config.baseUrl.apiUrl}/sys/email/verify/${id}`, { token: false }),


    // method 删除验证码
    // param id: undefined
    delMsgVerifyCode: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/sys/email/verify/${id}`, { token: false }),

}
