
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [SysDicFieldI18nBo]系统数据字典字段国际化路径请求参数
 */
export interface SysDicFieldI18nBo {

  /**
   * language, 最小长度：1, 最大长度：32
   */
  language?: string;
  /**
   * 显示内容
   */
  label?: string;
}


/**
 * [SysDicFieldPropBo]系统数据字典字段Code属性路径请求参数
 */
export interface SysDicFieldPropBo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 值
   */
  value?: string;
}


/**
 * [SysDicFieldBo]系统数据字典字段路径请求参数
 */
export interface SysDicFieldBo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 显示内容
   */
  label?: string;
  /**
   * 字段信息
   */
  labels?: Array<SysDicFieldI18nBo>;
  /**
   * 字段属性信息
   */
  props?: Array<SysDicFieldPropBo>;
  /**
   * 备注
   */
  remark?: string;
}


/**
 * [SysDicBo]系统数据字典路径请求参数
 */
export interface SysDicBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 启用, 必须字段
   */
  valid?: boolean;
  /**
   * 字段信息
   */
  fields?: Array<SysDicFieldBo>;
  /**
   * 备注
   */
  remark?: string;
}


/**
 * [SysDicAddVo]系统数据字典路径请求参数
 */
export interface SysDicAddVo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 启用, 必须字段
   */
  valid?: boolean;
  /**
   * 字段信息
   */
  fields?: Array<SysDicFieldBo>;
  /**
   * 备注
   */
  remark?: string;
}

export default {

  // method 系统数据字典详情
  // param id: undefined
  get: async (id: string): Promise<SysDicBo> => request.get(`${config.baseUrl.apiUrl}/auth/dic/dic/${id}`, { token: true }),


  // method 修改
  // param id: undefined
  // param body 请求Body参数
  modify: async (id: string, body: SysDicAddVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/auth/dic/dic/${id}`, body, { token: true }),


  // method 删除
  // param id: undefined
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/dic/dic/${id}`, { token: true }),


  // method 系统数据字典列表
  getList: async (): Promise<Array<SysDicBo>> => request.get(`${config.baseUrl.apiUrl}/auth/dic/dic`, { token: true }),

  // method 添加
  // param body 请求Body参数
  add: async (body: SysDicAddVo): Promise<SysDicBo> => request.post(`${config.baseUrl.apiUrl}/auth/dic/dic`, body, { token: true }),

}
