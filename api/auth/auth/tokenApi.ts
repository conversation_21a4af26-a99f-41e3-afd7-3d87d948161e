
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [TokenRevokeParamVo]Token吊销参数路径请求参数
 */
export interface TokenRevokeParamVo {

  /**
   * 刷新token
   */
  rtoken?: string;
  /**
   * token
   */
  token?: string;
}


/**
 * [TokenRefreshParamVo]Token签发参数路径请求参数
 */
export interface TokenRefreshParamVo {

  /**
   * 刷新token, 必须字段
   */
  rtoken?: string;
}


/**
 * [TokenMessageVo]token信息路径请求参数
 */
export interface TokenMessageVo {

  /**
   * id号
   */
  id?: string;
  /**
   * token
   */
  token?: string;
  /**
   * token有效期（单位：秒）
   */
  expries?: number;
  /**
   * 刷新Token
   */
  rtoken?: string;
  /**
   * 刷新token有效期（单位：秒）
   */
  rexpries?: number;
}


export default {

  /**
   * 吊销token
   * @param body 请求Body参数
   */
  revoke: async (body: TokenRevokeParamVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/auth/auth/token/revoke`, body, { token: false, skipToken: true }),


  /**
   * 获取token
   * @param body 请求Body参数
   * @returns 数据
   */
  refresh: async (body: TokenRefreshParamVo): Promise<TokenMessageVo> => request.post(`${config.baseUrl.apiUrl}/auth/auth/token/refresh`, body, { token: false, skipToken: true, cryptoBody: true, cryptoResult: true }),

}
