
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

export type AuthBridgeState =
  /**
   * 开始等待授权
   */
  'BEGIN' |
  /**
   * 等待确认
   */
  'PENDING' |
  /**
   * 授权完成（过期）
   */
  'END'


/**
 * 通过id获取Token路径请求参数
 */
export interface AuthAuthBridgeGetTokenByIdQueryParam {

  /**
   * id
   */
  id?: string;
  /**
   * uuid
   */
  uuid?: string;
}


/**
 * 通过uuid获取当前状态路径请求参数
 */
export interface AuthAuthBridgeGetStateByIdQueryParam {

  /**
   * id号
   */
  id?: string;
  /**
   * uuid
   */
  uuid?: string;
}


/**
 * [TokenMessageVo]token信息路径请求参数
 */
export interface TokenMessageVo {

  /**
   * id号
   */
  id?: string;
  /**
   * token
   */
  token?: string;
  /**
   * token有效期（单位：秒）
   */
  expries?: number;
  /**
   * 刷新Token
   */
  rtoken?: string;
  /**
   * 刷新token有效期（单位：秒）
   */
  rexpries?: number;
}


/**
 * [PutTokenBodyVo]设置token路径请求参数
 */
export interface PutTokenBodyVo {

  /**
   * id, 必须字段
   */
  id?: string;
}


/**
 * [AuthBridgeStateBo]授权桥状态信息路径请求参数
 */
export interface AuthBridgeStateBo {

  /**
   * 授权桥类型, 值：SEND_TOKEN, ACCEPT_TOKEN
   */
  type?: string;
  /**
   * [AuthBridgeState]授权桥状态信息, 值：BEGIN, PENDING, END
   */
  state?: AuthBridgeState;
}

export default {

  /**
   * 通过id获取Token
   * @param query 请求参数
   * @returns 数据
   */
  getTokenById: async (query: AuthAuthBridgeGetTokenByIdQueryParam): Promise<TokenMessageVo> => await request.get(`${config.baseUrl.apiUrl}/auth/auth/bridge/token`, { params: query, token: false, pageRole: false }),


  /**
   * 设置token
   * @param body 请求Body参数
   */
  putToken: async (body: PutTokenBodyVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/auth/auth/bridge/token`, body, { token: true, pageRole: false }),


  /**
   * 通过uuid获取当前状态
   * @param query 请求参数
   * @returns 数据
   */
  getStateById: async (query: AuthAuthBridgeGetStateByIdQueryParam): Promise<AuthBridgeStateBo> => await request.get(`${config.baseUrl.apiUrl}/auth/auth/bridge/state`, { params: query, token: false, pageRole: false }),

}
