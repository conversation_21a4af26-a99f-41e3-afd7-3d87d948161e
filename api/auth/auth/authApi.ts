
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取用户权限信息(token)路径请求参数
 */
export interface AuthAuthAuthAuthTokenUserQueryParam {

  /**
   * token, 必须参数
   */
  token?: string;
}


/**
 * 获取角色权限信息路径请求参数
 */
export interface AuthAuthAuthAuthRoleQueryParam {

  /**
   * 角色权限信息获取：ROLE_OPEN, 必须参数
   */
  role_code?: string;
}


/**
 * undefined路径请求参数
 */
export interface Switch2userBodyBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 具有的角色
   */
  roleCodes?: Array<string>;
  /**
   * 数据
   */
  data?: string;
}


/**
 * [TokenMessageVo]token信息路径请求参数
 */
export interface TokenMessageVo {

  /**
   * id号
   */
  id?: string;
  /**
   * token
   */
  token?: string;
  /**
   * token有效期（单位：秒）
   */
  expries?: number;
  /**
   * 刷新Token
   */
  rtoken?: string;
  /**
   * 刷新token有效期（单位：秒）
   */
  rexpries?: number;
}


/**
 * [AuthUser]授权用户信息路径请求参数
 */
export interface AuthUser {

  /**
   * 主账户Id
   */
  id?: string;
  /**
   * 终端类型
   */
  deviceType?: string;
  /**
   * 类型：RREFRESH_TOKEN: 刷新token
   */
  type?: string;
  /**
   * 具有的角色
   */
  roleCodes?: Array<string>;
  /**
   * 用户携带的信息
   */
  data?: string;
  /**
   * 代理用户Json数据
   */
  proxyUsersJson?: string;
}


/**
 * [TokenModelBo]token相关信息路径请求参数
 */
export interface TokenModelBo {

  /**
   * authUser
   */
  authUser?: AuthUser;
  /**
   * id
   */
  id?: string;
  /**
   * token
   */
  token?: string;
  /**
   * createTime
   */
  createTime?: number;
  /**
   * exp
   */
  exp?: number;
  /**
   * roles
   */
  roles?: Array<TokenModelRoleBo>;
  /**
   * tokenExpiresTimeLong
   */
  tokenExpiresTimeLong?: number;
}


/**
 * [TokenModelPowerBo]token权限信息路径请求参数
 */
export interface TokenModelPowerBo {

  /**
   * 主键
   */
  code?: string;
  /**
   * 应用编号(spring.application.name)
   */
  appCode?: string;
  /**
   * 类名称
   */
  clsName?: string;
  /**
   * 方法名称
   */
  clsMethod?: string;
  /**
   * 请求方法
   */
  httpMethod?: string;
  /**
   * url地址
   */
  url?: string;
}


/**
 * [TokenModelRoleBo]token角色信息路径请求参数
 */
export interface TokenModelRoleBo {

  /**
   * 角色code
   */
  code?: string;
  /**
   * 权限信息
   */
  powers?: Array<TokenModelPowerBo>;
}

export default {


  /**
   * 代理用户，切换用户
   * @param body 请求Body参数
   * @returns 数据
   */
  proxySwitch2user: async (body: Switch2userBodyBo): Promise<TokenMessageVo> => await request.post(`${config.baseUrl.apiUrl}/auth/auth/auth/proxy/switch2user`, body, { token: true, pageRole: false, cryptoBody: true, cryptoResult: true }),


  /**
   * 代理用户，回退用户
   * @returns 数据
   */
  proxyBack: async (): Promise<TokenMessageVo> => await request.post(`${config.baseUrl.apiUrl}/auth/auth/auth/proxy/back`, {}, { token: true, pageRole: false, cryptoBody: true, cryptoResult: true }),


  /**
   * 获取用户权限信息(authUser)
   * @param body 请求Body参数
   * @returns 数据
   */
  authTokenUserByAuthUser: async (body: AuthUser): Promise<TokenModelBo> => await request.post(`${config.baseUrl.apiUrl}/auth/auth/auth/get-user-by-auth-user`, body, { token: true, pageRole: false }),


  /**
   * 获取用户权限信息(token)
   * @param query 请求参数
   * @returns 数据
   */
  authTokenUser: async (query: AuthAuthAuthAuthTokenUserQueryParam): Promise<TokenModelBo> => await request.get(`${config.baseUrl.apiUrl}/auth/auth/auth/user`, { params: query, token: true, pageRole: false }),


  /**
   * 获取角色权限信息
   * @param query 请求参数
   * @returns 数据
   */
  authRole: async (query: AuthAuthAuthAuthRoleQueryParam): Promise<Array<TokenModelRoleBo>> => await request.get(`${config.baseUrl.apiUrl}/auth/auth/auth/role`, { params: query, token: true, pageRole: false }),

}
