
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [LoginParamVo]用户登录参数信息路径请求参数
 */
export interface LoginParamVo {

  /**
   * 用户名, 必须字段, 最小长度：1, 最大长度：32
   */
  username?: string;
  /**
   * 密码，预处理后结果, 必须字段
   */
  password?: string;
  /**
   * 验证码uuid
   */
  captchaUuid?: string;
  /**
   * 验证码
   */
  captchaCode?: string;
}


/**
 * [TokenMessageVo]token信息路径请求参数
 */
export interface TokenMessageVo {

  /**
   * id号
   */
  id?: string;
  /**
   * token
   */
  token?: string;
  /**
   * token有效期（单位：秒）
   */
  expries?: number;
  /**
   * 刷新Token
   */
  rtoken?: string;
  /**
   * 刷新token有效期（单位：秒）
   */
  rexpries?: number;
}

export default {

  /**
   * 用户登录，错误码：MAN_MATCHINE_FAIL表示验证码校验不通过。
   * @param body 请求Body参数
   * @returns 数据
   */
  login: async (body: LoginParamVo): Promise<TokenMessageVo> => await request.post(`${config.baseUrl.apiUrl}/auth/auth/login`, body, { token: false, skipToken: true, cryptoBody: true, cryptoResult: true }),

}

