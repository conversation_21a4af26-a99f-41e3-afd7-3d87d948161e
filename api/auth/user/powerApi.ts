
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [TokenModelPowerBo]token权限信息路径请求参数
export interface TokenModelPowerBo {

    // 主键
    code?: string;
    // 应用编号(spring.application.name)
    appCode?: string;
    // 类名称
    clsName?: string;
    // 方法名称
    clsMethod?: string;
    // 请求方法
    httpMethod?: string;
    // url地址
    url?: string;
}


// [TokenModelRoleBo]token角色信息路径请求参数
export interface TokenModelRoleBo {

    // 角色code
    code?: string;
    // 权限信息
    powers?: Array<TokenModelPowerBo>;
}


// [UserPowerModelVo]用户权限信息路径请求参数
export interface UserPowerModelVo {

    roles?: Array<TokenModelRoleBo>;
}

export default {

    // method 获取用户权限信息
    power: async (): Promise<UserPowerModelVo> => request.get(`${config.baseUrl.apiUrl}/auth/user/power`, { token: true }),

}
