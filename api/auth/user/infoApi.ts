
import { request } from '@/utils/request'
import { config } from '@/config/global.config'


/**
 * 获取数据列表路径请求参数
 */
export interface AuthUserInfoGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * 获取业务数据数量路径请求参数
 */
export interface AuthUserInfoGetListCountQueryParam {

  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * [UserInfoChangePasswordIdParamVo]通过用户修改密码路径请求参数
 */
export interface UserInfoChangePasswordIdParamVo {

  /**
   * 用户id, 必须字段
   */
  userId?: string;
  /**
   * 新密码
   */
  password?: string;
}


/**
 * [UserInfoChangePasswordParamVo]用户修改自身密码路径请求参数
 */
export interface UserInfoChangePasswordMeParamVo {

  /**
   * 原始密码
   */
  oldPassword?: string;
  /**
   * 新密码, 必须字段
   */
  password?: string;
}


/**
 * [UserListVo]用户信息表路径请求参数
 */
export interface UserListVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 用户名, 最小长度：0, 最大长度：32
   */
  username?: string;
  /**
   * 账户禁用
   */
  ban?: boolean;
  /**
   * 账户生效时间
   */
  startTime?: number;
  /**
   * 账户禁用时间
   */
  endTime?: number;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 角色信息
   */
  roleCode?: string;
  /**
   * 上次登录地址
   */
  loginLastAddr?: string;
  /**
   * 上次登录IP
   */
  loginLastIp?: string;
  /**
   * 上次登录时间
   */
  loginLastTime?: number;
  /**
   * 这次登录地址
   */
  loginThisAddr?: string;
  /**
   * 这次登录IP
   */
  loginThisIp?: string;
  /**
   * 这次登录时间
   */
  loginThisTime?: number;
}

export default {

  /**
 * 通过id修改登录密码
 * @param body 请求Body参数
 */
  changePasswordById: async (body: UserInfoChangePasswordIdParamVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/auth/user/info/change-password`, body, { token: true, pageRole: false, cryptoBody: true, cryptoResult: true }),

  /**
   * 修改密码
   * @param body 请求Body参数
   */
  changePasswordByMe: async (body: UserInfoChangePasswordMeParamVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/auth/user/info/change-password/by-me`, body, { token: true, cryptoBody: true, cryptoResult: true }),


  /**
   * 获取数据列表
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: AuthUserInfoGetListQueryParam): Promise<Array<UserListVo>> => request.get(`${config.baseUrl.apiUrl}/auth/user/info`, { params: query, token: true, pageRole: true }),


  /**
   * 获取业务数据数量
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: AuthUserInfoGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/auth/user/info/count`, { params: query, token: true, pageRole: true }),

}
