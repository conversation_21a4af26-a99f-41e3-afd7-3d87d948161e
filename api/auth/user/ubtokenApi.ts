
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 通过用户token获取绑定信息路径请求参数
export interface UserUbtokenGetByUbtokenQueryParam {

    // 用户绑定token，不能与user_id同时为空
    ubtoken?: string;
}


// [UbTokenMsgRo]用户绑定表路径请求参数
export interface UbTokenMsgRo {

    // 系统应用id
    sysAppId?: string;
    // 绑定该应用的用户id
    openId?: string;
    // 用户标识id
    unionId?: string;
    // 绑定时间
    createTime?: string;
    // 过期时间
    过期时间?: string;
    expries?: number;
}


// [UbtokenAddVo]用户绑定表路径请求参数
export interface UbtokenAddVo {

    // 系统应用id
    sysAppId?: string;
    // 绑定该应用的用户id
    openId?: string;
    // 用户标识id
    unionId?: string;
}


// [UbtokenVo]用户绑定表路径请求参数
export interface UbtokenVo {

    // ubtoken
    ubtoken?: string;
    // ubtoken有效期（单位：秒）
    ubexpries?: number;
}

export default {

    // method 通过用户token获取绑定信息
    // param query 请求参数
    getByUbtoken: async (query: UserUbtokenGetByUbtokenQueryParam): Promise<UbTokenMsgRo> => request.get(`${config.baseUrl.apiUrl}/auth/user/ubtoken`, { params: query, token: false }),


    // method 签发ubtoken
    // param body 请求Body参数
    createUbtoken: async (body: UbtokenAddVo): Promise<UbtokenVo> => request.post(`${config.baseUrl.apiUrl}/auth/user/ubtoken`, body, { token: false }),

}
