
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 获得绑定信息路径请求参数
export interface UserUserbondGetListQueryParam {

  // 用户绑定token，不能与user_id同时为空
  ubtoken?: string;
  // 当前用户id，不能与ubtoken同时为空
  user_id?: string;
}


// [UserBondVo]用户绑定表路径请求参数
export interface UserBondVo {

  // 用户id
  userId?: string;
  // 系统应用id
  sysAppId?: string;
  // 绑定该应用的用户id
  openId?: string;
  // 用户标识id
  unionId?: string;
  // 绑定时间
  createTime?: string;
}



// 获得绑定，用于开放查询信息路径请求参数
export interface UserUserBondGetListByMeQueryParam {

  // 用户绑定token，不能与user_id同时为空
  ubtoken?: string;
  // 当前用户id，不能与ubtoken同时为空
  user_id?: string;
}

// [UserBondAddVo]用户绑定表路径请求参数
export interface UserBondAddVo {

  // 用户绑定token
  ubtoken?: string;
  // 当前用户可传0
  userId?: string;
}


// [UserBondDeleteVo]用户绑定表路径请求参数
export interface UserBondDeleteVo {

  // appId
  appId?: string;
  // openId
  openId?: string;
  // 用户id
  userId?: string;
}


// [UserBondTokenParamVo]用户绑定表路径请求参数
export interface UserBondTokenParamVo {

  // 用户绑定token
  ubtoken?: string;
  // 用户标识id
  userId?: string;
}


// [TokenMessageVo]token信息路径请求参数
export interface TokenMessageVo {

  // token
  token?: string;
  // token有效期（单位：秒）
  expries?: number;
  // 刷新Token
  rtoken?: string;
  // 刷新token有效期（单位：秒）
  rexpries?: number;
}


// [UserBondAddByMeVo]用户绑定自身路径请求参数
export interface UserBondAddByMeVo {

  // 用户绑定token
  ubtoken?: string;
}


// [UserBondDeleteByMeVo]解绑用户自己路径请求参数
export interface UserBondDeleteByMeVo {

  // appId
  appId?: string;
  // openId
  openId?: string;
}

export default {

  // method 获得绑定信息
  // param query 请求参数
  getList: async (query: UserUserbondGetListQueryParam): Promise<Array<UserBondVo>> => request.get(`${config.baseUrl.apiUrl}/auth/user/user-bond`, { params: query, token: false }),


  // method 绑定用户
  // param body 请求Body参数
  bond: async (body: UserBondAddVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/auth/user/user-bond`, body, { token: false }),


  // method 解除绑定
  // param body 请求Body参数
  unbond: async (body: UserBondDeleteVo): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/user/user-bond`, { data: body, token: false }),


  // method 签发token
  // param body 请求Body参数
  token: async (body: UserBondTokenParamVo): Promise<TokenMessageVo> => request.post(`${config.baseUrl.apiUrl}/auth/user/user-bond/token`, body, { token: false }),


  // method 获得绑定，用于开放查询信息
  // param query 请求参数
  getListByMe: async (query: UserUserBondGetListByMeQueryParam): Promise<Array<UserBondVo>> => request.get(`${config.baseUrl.apiUrl}/auth/user/user-bond/by-me`, { params: query, token: false }),

  // method 绑定当前用户
  // param body 请求Body参数
  bondByMe: async (body: UserBondAddByMeVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/auth/user/user-bond/by-me`, body, { token: false }),


  // method 解除当前用户绑定
  // param body 请求Body参数
  unbondByMe: async (body: UserBondDeleteByMeVo): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/user/user-bond/by-me`, { data: body, token: false }),

}
