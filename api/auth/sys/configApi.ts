
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [SysConfig]配置信息表路径请求参数
 */
export interface SysConfig {

  /**
   * Code信息, 必须字段, 最小长度：1, 最大长度：256
   */
  code?: string;
  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 备注, 最小长度：0, 最大长度：65535
   */
  remark?: string;
  /**
   * 启用, 必须字段
   */
  valid?: boolean;
  /**
   * 是否可公开, 必须字段
   */
  pub?: boolean;
  /**
   * 值信息, 最小长度：0, 最大长度：65535
   */
  value?: string;
}


/**
 * [SysConfigAddVo]系统系统配置信息路径请求参数
 */
export interface SysConfigAddVo {

  /**
   * Code信息, 必须字段, 最小长度：1, 最大长度：256
   */
  code?: string;
  /**
   * 启用, 必须字段
   */
  valid?: boolean;
  /**
   * 是否可公开, 必须字段
   */
  pub?: boolean;
  /**
   * 值信息, 最小长度：0, 最大长度：65535
   */
  value?: string;
  /**
   * 备注, 最小长度：0, 最大长度：65535
   */
  remark?: string;
}

export default {

  /**
   * 系统配置详情
   * @param id: id
   * @returns 数据
   */
  get: async (id: string): Promise<SysConfig> => request.get(`${config.baseUrl.apiUrl}/auth/sys/config/${id}`, { token: true }),

  /**
   * 修改
   * @param id: id
   * @param body 请求Body参数
   */
  modify: async (id: string, body: SysConfigAddVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/auth/sys/config/${id}`, body, { token: true }),


  /**
   * 删除
   * @param id: id
   */
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/sys/config/${id}`, { token: true }),


  /**
   * 系统配置列表
   * @returns 数据
   */
  getList: async (): Promise<Array<SysConfig>> => request.get(`${config.baseUrl.apiUrl}/auth/sys/config`, { token: true }),


  /**
   * 添加
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: SysConfigAddVo): Promise<SysConfig> => request.post(`${config.baseUrl.apiUrl}/auth/sys/config`, body, { token: true }),

}
