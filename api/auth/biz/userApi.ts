
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [Users]用户信息表路径请求参数
 */
export interface Users {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 用户名, 最小长度：0, 最大长度：32
   */
  username?: string;
  /**
   * 用户密码md5(md5(pwd).toLowerCase()+pwd_salt), 最小长度：0, 最大长度：32
   */
  password?: string;
  /**
   * 密码盐, 最小长度：0, 最大长度：32
   */
  pwdSalt?: string;
  /**
   * 账户禁用
   */
  ban?: boolean;
  /**
   * 账户生效时间
   */
  startTime?: number;
  /**
   * 账户禁用时间
   */
  endTime?: number;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 角色信息
   */
  roleCode?: string;
  /**
   * 上次登录地址
   */
  loginLastAddr?: string;
  /**
   * 上次登录IP
   */
  loginLastIp?: string;
  /**
   * 上次登录时间
   */
  loginLastTime?: number;
  /**
   * 这次登录地址
   */
  loginThisAddr?: string;
  /**
   * 这次登录IP
   */
  loginThisIp?: string;
  /**
   * 这次登录时间
   */
  loginThisTime?: number;
}


/**
 * [UserByMeInfoVo]用户信息路径请求参数
 */
export interface UserByMeInfoVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 最小长度：0, 最大长度：32
   */
  name?: string;
  /**
   * 用户名, 最小长度：0, 最大长度：32
   */
  username?: string;
  /**
   * 用户密码md5(md5(pwd).toLowerCase()+pwd_salt), 最小长度：0, 最大长度：32
   */
  password?: string;
  /**
   * 密码盐, 最小长度：0, 最大长度：32
   */
  pwdSalt?: string;
  /**
   * 账户禁用
   */
  ban?: boolean;
  /**
   * 账户生效时间
   */
  startTime?: number;
  /**
   * 账户禁用时间
   */
  endTime?: number;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 角色信息
   */
  roleCode?: string;
  /**
   * 上次登录地址
   */
  loginLastAddr?: string;
  /**
   * 上次登录IP
   */
  loginLastIp?: string;
  /**
   * 上次登录时间
   */
  loginLastTime?: number;
  /**
   * 这次登录地址
   */
  loginThisAddr?: string;
  /**
   * 这次登录IP
   */
  loginThisIp?: string;
  /**
   * 这次登录时间
   */
  loginThisTime?: number;
  /**
   * 是否是代理用户
   */
  authProxyUser?: boolean;
  /**
   * authProxyUserInfo
   */
  authProxyUserInfo?: UserByMeInfoVo;

}

export default {

  /**
   * 获取用户信息
   * @param id: 用户id
   * @returns 数据
   */
  get: async (id: string): Promise<Users> => await request.get(`${config.baseUrl.apiUrl}/auth/biz/user/${id}`, { token: true, pageRole: false }),


  /**
   * 获取用户自身信息
   * @returns 数据
   */
  getByMe: async (): Promise<UserByMeInfoVo> => await request.get(`${config.baseUrl.apiUrl}/auth/biz/user/by-me`, { token: true, pageRole: false }),

}
