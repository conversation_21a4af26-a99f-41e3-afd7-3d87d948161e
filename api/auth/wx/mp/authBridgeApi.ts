
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 签发发送Token图片，扫码绑定路径请求参数
export interface WxMpAuthbridgeSendTokenQueryParam {

    // sysApiId
    sys_app_id?: string;
    // 图片类型:默认为小程序码, WX_MP, JSON
    type?: string;
    // 随机uuid，长度必须32
    uuid?: string;
}


// 签发接受Token图片，扫码登录路径请求参数
export interface WxMpAuthbridgeAcceptTokenQueryParam {

    // sysApiId
    sys_app_id?: string;
    // 图片类型:默认为小程序码, WX_MP, JSON
    type?: string;
    // 随机uuid，长度必须32
    uuid?: string;
}


// [WxMpAuthBridgeRespVo]桥接响应信息路径请求参数
export interface WxMpAuthBridgeRespVo {

    // id
    id?: string;
}

export default {

    // method 签发发送Token图片，扫码绑定
    // param query 请求参数
    sendToken: async (query: WxMpAuthbridgeSendTokenQueryParam): Promise<WxMpAuthBridgeRespVo> => request.get(`${config.baseUrl.apiUrl}/auth/wx/mp/auth-bridge/send-token`, { params: query, token: true }),


    // method 签发接受Token图片，扫码登录
    // param query 请求参数
    acceptToken: async (query: WxMpAuthbridgeAcceptTokenQueryParam): Promise<WxMpAuthBridgeRespVo> => request.get(`${config.baseUrl.apiUrl}/auth/wx/mp/auth-bridge/accept-token`, { params: query, token: false, skipToken: true }),

}
