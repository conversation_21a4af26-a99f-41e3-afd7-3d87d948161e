
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [WxMpRegisterParamVo]微信小程序注册参数路径请求参数
export interface WxMpRegisterParamVo {

    // ubtoken数据
    ubtoken?: string;
    // 头像
    headImg?: string;
    // 昵称
    nickname?: string;
    // 手机号加密iv
    phoneNumberIv?: string;
    // 手机号加密后密文
    phoneNumberEncryptedData?: string;
    // 附带参数
    json?: string;
}


// [TokenMessageVo]token信息路径请求参数
export interface TokenMessageVo {

    // token
    token?: string;
    // token有效期（单位：秒）
    expries?: number;
    // 刷新Token
    rtoken?: string;
    // 刷新token有效期（单位：秒）
    rexpries?: number;
}


// [WxMpSeesionParamVo]请求参数路径请求参数
export interface WxMpSeesionParamVo {

    // code
    code?: string;
    // 当前系统sysAppId，非微信的appId
    sysAppId?: string;
}


// [WxMpSessionVo]微信小程序token响应数据路径请求参数
export interface WxMpSessionVo {

    // 用户在当前应用唯一标识openId
    openId?: string;
    // 用户唯一标识unionId
    unionId?: string;
    // 当前用户绑定token
    ubtoken?: string;
    // ubtoken有效期（单位：秒）
    ubexpries?: number;
}

export default {

    // method 注册数据
    // param body 请求Body参数
    register: async (body: WxMpRegisterParamVo): Promise<TokenMessageVo> => request.post(`${config.baseUrl.apiUrl}/auth/wx/mp/auth/token`, body, { token: false, skipToken: true }),


    // method 使用code会换session
    // param body 请求Body参数
    token: async (body: WxMpSeesionParamVo): Promise<WxMpSessionVo> => request.post(`${config.baseUrl.apiUrl}/auth/wx/mp/auth/session`, body, { token: false }),

}
