
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 获取微信token路径请求参数
export interface WxMpAccesstokenTokenQueryParam {

    // sys_app_id
    sys_app_id?: string;
}


// [WxMpAccessTokenVo]微信小程序token响应数据路径请求参数
export interface WxMpAccessTokenVo {

    // token
    token?: string;
    // token有效期（单位：秒）
    expries?: number;
}

export default {

    // method 获取微信token
    // param query 请求参数
    token: async (query: WxMpAccesstokenTokenQueryParam): Promise<WxMpAccessTokenVo> => request.get(`${config.baseUrl.apiUrl}/auth/wx/mp/access-token`, { params: query, token: false, skipToken: true }),

}
