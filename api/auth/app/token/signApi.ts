
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [TokenSignParamVo]Token签发参数路径请求参数
export interface TokenSignParamVo {

    // 客户端id
    clientId?: string;
    // 客户端随机uuid
    none?: string;
    // 时间戳，单位：毫秒
    timestemp?: number;
    // 签名：SHA256(clientId + timestemp + none + clientSecret)
    sign?: string;
}


// [TokenMessageVo]token信息路径请求参数
export interface TokenMessageVo {

    // token
    token?: string;
    // token有效期（单位：秒）
    expries?: number;
    // 刷新Token
    rtoken?: string;
    // 刷新token有效期（单位：秒）
    rexpries?: number;
}

export default {

    // method 获取token
    // param body 请求Body参数
    sign: async (body: TokenSignParamVo): Promise<TokenMessageVo> => request.post(`${config.baseUrl.apiUrl}/auth/app/token/sign`, body, { token: false, skipToken: true }),

}
