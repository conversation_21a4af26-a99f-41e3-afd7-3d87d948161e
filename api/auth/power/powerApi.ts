
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [SysPower]系统权限表路径请求参数
export interface SysPower {

  // 主键
  code?: string;
  // 名称
  name?: string;
  // 父code
  parentCode?: string;
  // 依赖code
  depCode?: string;
  // 应用编号(spring.application.name)
  appCode?: string;
  // 类名称
  clsName?: string;
  // 方法名称
  clsMethod?: string;
  // 请求方法
  httpMethod?: string;
  // url地址
  url?: string;
  // 隐藏
  hidden?: boolean;
  // 备注信息
  remark?: string;
}


// [SysPowerModifyVo]系统权限管理内容路径请求参数
export interface SysPowerModifyVo {

  // 主键
  code?: string;
  // 名称
  name?: string;
  // 父code
  parentCode?: string;
  // 依赖code
  depCode?: string;
  // 应用编号(spring.application.name)
  appCode?: string;
  // 类名称
  clsName?: string;
  // 方法名称
  clsMethod?: string;
  // 请求方法
  httpMethod?: string;
  // url地址
  url?: string;
  // 隐藏
  hidden?: boolean;
  // 备注信息
  remark?: string;
}


// [SysPowerAddVo]新增系统权限路径请求参数
export interface SysPowerAddVo {

  // 主键
  code?: string;
  // 名称
  name?: string;
  // 父code
  parentCode?: string;
  // 依赖code
  depCode?: string;
  // 应用编号(spring.application.name)
  appCode?: string;
  // 类名称
  clsName?: string;
  // 方法名称
  clsMethod?: string;
  // 请求方法
  httpMethod?: string;
  // url地址
  url?: string;
  // 隐藏
  hidden?: boolean;
  // 备注信息
  remark?: string;
}


// [SysPowerModifyParentCodeVo]系统权限修改系统父Code路径请求参数
export interface SysPowerModifyParentCodeVo {

  // 主键
  code?: string;
  // 父code
  parentCode?: string;
}

export default {

  // method 获取权限
  // param code: 权限code
  get: async (code: string): Promise<SysPower> => request.get(`${config.baseUrl.apiUrl}/auth/power/power/${code}`, { token: true }),


  // method 更新权限信息
  // param code: 权限code
  // param body 请求Body参数
  modify: async (code: string, body: SysPowerModifyVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/auth/power/power/${code}`, body, { token: true }),


  // method 删除权限
  // param code: 权限code
  delete: async (code: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/power/power/${code}`, { token: true }),


  // method 获取权限列表
  getList: async (): Promise<Array<SysPower>> => request.get(`${config.baseUrl.apiUrl}/auth/power/power`, { token: true }),


  // method 新增权限信息
  // param body 请求Body参数
  add: async (body: SysPowerAddVo): Promise<SysPower> => request.post(`${config.baseUrl.apiUrl}/auth/power/power`, body, { token: true }),


  // method 批量修改父Code
  // param body 请求Body参数
  putParentCode: async (body: Array<SysPowerModifyParentCodeVo>): Promise<void> => request.post(`${config.baseUrl.apiUrl}/auth/power/power/batch/put/parent-code`, body, { token: true }),

}
