
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// [SysRole]角色信息路径请求参数
export interface SysRole {

  // 角色code(ROLE_前缀)
  code?: string;
  // 角色名称
  name?: string;
  // 角色隐藏
  hidden?: boolean;
  // 备注信息
  remark?: string;
}


// [SysRoleModifyVo]修改系统角色路径请求参数
export interface SysRoleModifyVo {

  // 角色code(ROLE_前缀)
  code?: string;
  // 角色名称
  name?: string;
  // 备注信息
  remark?: string;
}


// [SysRoleAddVo]新增系统角色路径请求参数
export interface SysRoleAddVo {

  // 角色code(ROLE_前缀)
  code?: string;
  // 角色名称
  name?: string;
  // 备注信息
  remark?: string;
}

export default {

  // method 获取角色
  // param code: 角色code
  get: async (code: string): Promise<SysRole> => request.get(`${config.baseUrl.apiUrl}/auth/power/role/${code}`, { token: true }),


  // method 更新角色信息
  // param code: 权限code
  // param body 请求Body参数
  modify: async (code: string, body: SysRoleModifyVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/auth/power/role/${code}`, body, { token: true }),


  // method 删除角色
  // param code: 权限code
  delete: async (code: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/power/role/${code}`, { token: true }),


  // method 获取角色列表
  getList: async (): Promise<Array<SysRole>> => request.get(`${config.baseUrl.apiUrl}/auth/power/role`, { token: true }),


  // method 新增角色信息
  // param body 请求Body参数
  add: async (body: SysRoleAddVo): Promise<SysRole> => request.post(`${config.baseUrl.apiUrl}/auth/power/role`, body, { token: true }),

}
