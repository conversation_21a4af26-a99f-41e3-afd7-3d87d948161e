
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [SysPrincipalVo]系统授权主体管理路径请求参数
 */
export interface SysPrincipalVo {

  /**
   * 类型（USER、APP、STORE）, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
}

export default {

  /**
   * 获取全部数据列表
   * @returns 数据
   */
  getAllList: async (): Promise<Array<SysPrincipalVo>> => await request.get(`${config.baseUrl.apiUrl}/auth/power/principal`, { token: true }),

}
