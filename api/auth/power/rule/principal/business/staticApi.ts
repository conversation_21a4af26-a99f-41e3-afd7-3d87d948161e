
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取统计列表路径请求参数
 */
export interface AuthPowerRulePrincipalBusinessStaticGetStaticListQueryParam {

  /**
   * 统计类型：SUBJECT，ROLE，OWNER, 必须参数
   */
  type?: string;
  /**
   * 用户Type, 必须参数
   */
  owner_type?: string;
  /**
   * 职权范围Type, 必须参数
   */
  subject_type?: string;
  /**
   * 角色Code, 必须参数
   */
  role_code?: Array<string>;
  /**
   * 用户Id, 必须参数
   */
  owner_id?: Array<string>;
  /**
   * 职权范围Id, 必须参数
   */
  subject_id?: Array<string>;
}


/**
 * [SysRuleStaticVo]授权统计管理路径请求参数
 */
export interface SysRuleStaticVo {

  /**
   * Id
   */
  id?: string;
  /**
   * 数量
   */
  num?: number;
}

export default {

  /**
   * 获取统计列表
   * @param query 请求参数
   * @returns 数据
   */
  getStaticList: async (query: AuthPowerRulePrincipalBusinessStaticGetStaticListQueryParam): Promise<Array<SysRuleStaticVo>> => request.get(`${config.baseUrl.apiUrl}/auth/power/rule/principal/business/static`, { params: query, token: true }),

}
