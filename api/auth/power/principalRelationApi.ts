
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [SysPrincipalRelation]路径请求参数
 */
export interface SysPrincipalRelation {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 父节点类型, 必须字段, 最小长度：1, 最大长度：32
   */
  codeParent?: string;
  /**
   * 备注, 最小长度：0, 最大长度：65535
   */
  remark?: string;
  /**
   * 查询数据的SQL，响应字段（id, id_parent）。, 最小长度：0, 最大长度：65535
   */
  sql?: string;
}

export default {

  /**
   * 获取全部数据列表
   * @returns 数据
   */
  getAllList: async (): Promise<Array<SysPrincipalRelation>> => await request.get(`${config.baseUrl.apiUrl}/auth/power/principal-relation`, { token: true, pageRole: false }),

}
