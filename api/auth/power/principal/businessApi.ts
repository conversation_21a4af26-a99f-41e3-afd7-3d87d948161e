
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取数据列表路径请求参数
 */
export interface AuthPowerPrincipalBusinessGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 排序字段
   */
  sort?: string;
  /**
   * ids，多个中间使用逗号（,）分隔
   */
  id?: Array<string>;
  /**
   * 字段信息，附加额外查询，例如：childrenNum
   */
  fields?: string;
  /**
   * 父标签，根目录写0
   */
  id_parent?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * 获取业务数据数量路径请求参数
 */
export interface AuthPowerPrincipalBusinessGetListCountQueryParam {

  /**
   * ids，多个中间使用逗号（,）分隔
   */
  id?: Array<string>;
  /**
   * 父标签，根目录写0
   */
  id_parent?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * [SysPrincipalBusinessBo]系统主体业务数据路径请求参数
 */
export interface SysPrincipalBusinessDetailBo {

  /**
   * Id
   */
  id?: string;
  /**
   * 编号
   */
  no?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 父节点id，0为根节点
   */
  idParent?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 子节点数量
   */
  childrenNum?: number;
}

export default {

  /**
   * 获取数据列表
   * @param code: code
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (code: string, query: AuthPowerPrincipalBusinessGetListQueryParam): Promise<Array<SysPrincipalBusinessDetailBo>> => request.get(`${config.baseUrl.apiUrl}/auth/power/principal/business/${code}`, { params: query, token: true }),


  /**
   * 获取业务数据数量
   * @param code: code
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (code: string, query: AuthPowerPrincipalBusinessGetListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/auth/power/principal/business/${code}/count`, { params: query, token: true }),

}
