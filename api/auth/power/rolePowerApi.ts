
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

// 获取角色授权信息列表路径请求参数
export interface PowerRolepowerGetListQueryParam {

    // 角色Code
    role_code?: string;
    // 权限Code
    power_code?: string;
}


// [SysRolePower]角色对权限授权路径请求参数
export interface SysRolePower {

    // 角色code
    roleCode?: string;
    // 权限code
    powerCode?: string;
}


// [RolePowerModifyPowerVo]修改角色权限路径请求参数
export interface RolePowerModifyPowerVo {

    // roleCode
    roleCode?: string;
    // 新的权限Code
    powerCodes?: Array<string>;
}


// [RolePowerGroupVo]角色分组信息路径请求参数
export interface RolePowerGroupVo {

    // code
    code?: string;
    // 数量
    count?: number;
}

export default {

    // method 获取角色授权信息列表
    // param query 请求参数
    getList: async (query: PowerRolepowerGetListQueryParam): Promise<Array<SysRolePower>> => request.get(`${config.baseUrl.apiUrl}/auth/power/role-power`, { params: query, token: true }),


    // method 增加了授权信息
    // param body 请求Body参数
    add: async (body: SysRolePower): Promise<SysRolePower> => request.post(`${config.baseUrl.apiUrl}/auth/power/role-power`, body, { token: true }),


    // method 删除角色授权信息
    // param body 请求Body参数
    delete: async (body: SysRolePower): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/power/role-power`, { data: body, token: true }),


    // method 修改角色的权限信息
    // param body 请求Body参数
    modifyRolePower: async (body: RolePowerModifyPowerVo): Promise<void> => request.post(`${config.baseUrl.apiUrl}/auth/power/role-power/role/modify/power`, body, { token: true }),


    // method 获取角色分组信息
    getRoleGroupList: async (): Promise<Array<RolePowerGroupVo>> => request.get(`${config.baseUrl.apiUrl}/auth/power/role-power/role-group`, { token: true }),


    // method 获取权限分组信息
    getPowerGroupList: async (): Promise<Array<RolePowerGroupVo>> => request.get(`${config.baseUrl.apiUrl}/auth/power/role-power/power-group`, { token: true }),

}
