
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取列表路径请求参数
 */
export interface AuthPowerRuleGetVoListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 排序字段
   */
  sort?: string;
  /**
   * 用户Type, 必须参数
   */
  owner_type?: string;
  /**
   * 职权范围Type, 必须参数
   */
  subject_type?: string;
  /**
   * 角色Code, 必须参数
   */
  role_code?: string;
  /**
   * 用户Id, 必须参数
   */
  owner_id?: string;
  /**
   * 职权范围Id, 必须参数
   */
  subject_id?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * 获数据数量路径请求参数
 */
export interface AuthPowerRuleGetVoListCountQueryParam {

  /**
   * 用户Type, 必须参数
   */
  owner_type?: string;
  /**
   * 职权范围Type, 必须参数
   */
  subject_type?: string;
  /**
   * 角色Code, 必须参数
   */
  role_code?: string;
  /**
   * 用户Id, 必须参数
   */
  owner_id?: string;
  /**
   * 职权范围Id, 必须参数
   */
  subject_id?: string;
  /**
   * 关键词
   */
  keyword?: string;
}


/**
 * [SysRuleBatchAddVo]批量添加授权管理路径请求参数
 */
export interface SysRuleBatchAddVo {

  /**
   * 用户类型，USER, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 用户id, 必须字段
   */
  ownerIds?: Array<string>;
  /**
   * 角色code, 必须字段
   */
  roleCodes?: Array<string>;
  /**
   * 关联职权范围类型，STORE, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 职权范围id, 必须字段
   */
  subjectIds?: Array<string>;
}


/**
 * [SysRule]授权管理路径请求参数
 */
export interface SysRule {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 用户类型，USER, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 角色code, 必须字段, 最小长度：1, 最大长度：32
   */
  roleCode?: string;
  /**
   * 关联的id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 关联职权范围类型，STORE, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 授权时间, 必须字段
   */
  createTime?: number;
}


/**
 * [SysRuleVo]授权管理路径请求参数
 */
export interface SysRuleVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 用户类型，USER, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 角色code, 必须字段, 最小长度：1, 最大长度：32
   */
  roleCode?: string;
  /**
   * 关联的id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 关联职权范围类型，STORE, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 授权时间, 必须字段
   */
  createTime?: number;
  /**
   * 用户编号
   */
  ownerNo?: string;
  /**
   * 用户名称
   */
  ownerName?: string;
  /**
   * 职权范围编号
   */
  subjectNo?: string;
  /**
   * 职权范围名称
   */
  subjectName?: string;
  /**
   * 角色名称
   */
  roleName?: string;
}

export default {

  /**
   * 批量添加权限信息
   * @param body 请求Body参数
   * @returns 数据
   */
  batchAdd: async (body: SysRuleBatchAddVo): Promise<Array<SysRule>> => request.post(`${config.baseUrl.apiUrl}/auth/power/rule/batch/add`, body, { token: true }),


  /**
   * 获取列表
   * @param query 请求参数
   * @returns 数据
   */
  getVoList: async (query: AuthPowerRuleGetVoListQueryParam): Promise<Array<SysRuleVo>> => request.get(`${config.baseUrl.apiUrl}/auth/power/rule/vo`, { params: query, token: true }),


  /**
   * 获数据数量
   * @param query 请求参数
   * @returns 数据
   */
  getVoListCount: async (query: AuthPowerRuleGetVoListCountQueryParam): Promise<number> => request.get(`${config.baseUrl.apiUrl}/auth/power/rule/vo/count`, { params: query, token: true }),


  /**
   * 移除数据
   * @param id: Id
   */
  delete: async (id: string): Promise<void> => request.delete(`${config.baseUrl.apiUrl}/auth/power/rule/${id}`, { token: true }),

}
