
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 更新盘点数据路径请求参数
 */
export interface WmsMgInventoryProductPutProductMsgQueryParam {

  /**
   * 盘点id, 必须参数
   */
  inventory_id?: string;
}


/**
 * 获取盘点单路径请求参数
 */
export interface WmsMgInventoryProductGetListQueryParam {

  /**
   * 盘点id, 必须参数
   */
  inventory_id?: string;
  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 状态
   */
  state?: Array<string>;
  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 仓库id
   */
  containerId?: string;
}


/**
 * 获取出盘点单路径请求参数
 */
export interface WmsMgInventoryProductGetListCountQueryParam {

  /**
   * 盘点id, 必须参数
   */
  inventory_id?: string;
  /**
   * 状态
   */
  state?: Array<string>;
  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 仓库id
   */
  containerId?: string;
}


/**
 * [WmsInventoryProductUpdateProductVo]盘点单商品数据更新商品详情路径请求参数
 */
export interface WmsInventoryProductUpdateProductVo {

  /**
   * 主键, 必须字段
   */
  id?: number;
  /**
   * 真实数量, 必须字段
   */
  numReal?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsInventoryProductUpdateVo]盘点单商品数据更新路径请求参数
 */
export interface WmsInventoryProductUpdateVo {

  /**
   * 商品信息, 必须字段
   */
  products?: Array<WmsInventoryProductUpdateProductVo>;
}


/**
 * [WmsInventoryProduct]WMS盘点商品清单路径请求参数
 */
export interface WmsInventoryProduct {

  /**
   * 主键, 必须字段
   */
  id?: number;
  /**
   * 盘点Id, 必须字段, 最小长度：1, 最大长度：32
   */
  inventoryId?: string;
  /**
   * 仓库盒子id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseBoxId?: string;
  /**
   * 商品数量, 必须字段
   */
  warehouseNumStorage?: number;
  /**
   * 状态：WAITE：等待确认、OK：正常、SURPLUS：盘盈、SHORTAGE：盘亏, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 真实数量, 必须字段
   */
  numReal?: number;
  /**
   * 盘点备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}

export default {

  /**
   * 更新盘点数据
   * @param query 请求参数
   * @param body 请求Body参数
   */
  putProductMsg: async (query: WmsMgInventoryProductPutProductMsgQueryParam, body: WmsInventoryProductUpdateVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/inventory/product/put-product-msg`, body, { params: query, token: true, pageRole: false }),


  /**
   * 获取盘点单
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: WmsMgInventoryProductGetListQueryParam): Promise<Array<WmsInventoryProduct>> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/inventory/product`, { params: query, token: true, pageRole: false }),


  /**
   * 获取出盘点单
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: WmsMgInventoryProductGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/inventory/product/count`, { params: query, token: true, pageRole: false }),

}
