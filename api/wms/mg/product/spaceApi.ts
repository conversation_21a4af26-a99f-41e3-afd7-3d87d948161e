
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取主商品详细信息路径请求参数
 */
export interface WmsMgProductSpaceGetListQueryParam {

  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 类型
   */
  state?: Array<string>;
  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * id
   */
  id?: Array<string>;
}


/**
 * 获取主商品详细信息路径请求参数
 */
export interface WmsMgProductSpaceGetListCountQueryParam {

  /**
   * 关键词
   */
  keyword?: string;
  /**
   * 类型
   */
  state?: Array<string>;
  /**
   * id
   */
  id?: Array<string>;
}


/**
 * [WmsProductSpace]主商品详细信息路径请求参数
 */
export interface WmsProductSpace {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 编号, 必须字段, 最小长度：1, 最大长度：32
   */
  sku?: string;
  /**
   * 储存条件(,)逗号分割, 最小长度：0, 最大长度：256
   */
  environment?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态：OK：正常、ARCHIVED：归档, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档备注, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 归档人id, 最小长度：0, 最大长度：32
   */
  archivedUserId?: string;
}


/**
 * [WmsProductSpaceAddVo]主商品详细信息添加新建内容路径请求参数
 */
export interface WmsProductSpaceAddVo {

  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 货物编号, 必须字段, 最小长度：1, 最大长度：32
   */
  sku?: string;
  /**
   * 储存条件(,)逗号分割, 最小长度：0, 最大长度：256
   */
  environment?: string;
}


/**
 * [WmsProductBatchArchivedVo]商品归档路径请求参数
 */
export interface WmsProductBatchArchivedVo {

  /**
   * 操作的商品id, 必须字段
   */
  ids?: Array<string>;
  /**
   * 是否归档, 必须字段
   */
  archived?: boolean;
  /**
   * 备注信息
   */
  remark?: string;
}

export default {

  /**
   * 查询主商品详细信息
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<WmsProductSpace> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/product/space/${id}`, { token: true, pageRole: false }),


  /**
   * 更新主商品详细信息
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: WmsProductSpaceAddVo): Promise<WmsProductSpace> => await request.put(`${config.baseUrl.apiUrl}/wms/mg/product/space/${id}`, body, { token: true, pageRole: false }),


  /**
   * 获取主商品详细信息
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: WmsMgProductSpaceGetListQueryParam): Promise<Array<WmsProductSpace>> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/product/space`, { params: query, token: true, pageRole: false }),


  /**
   * 添加主商品详细信息
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: WmsProductSpaceAddVo): Promise<WmsProductSpace> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/product/space`, body, { token: true, pageRole: false }),


  /**
   * 主商品归档
   * @param body 请求Body参数
   */
  batchArchived: async (body: WmsProductBatchArchivedVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/product/space/batch/archived`, body, { token: true, pageRole: false }),


  /**
   * 获取主商品详细信息
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: WmsMgProductSpaceGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/product/space/count`, { params: query, token: true, pageRole: false }),

}
