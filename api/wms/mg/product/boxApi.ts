
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取商品盒子标识同种切隔离数据相同的商品路径请求参数
 */
export interface WmsMgProductBoxGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * id
   */
  id?: Array<string>;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 是否展示可用
   */
  hasUnLock?: boolean;
}


/**
 * 获取商品盒子标识同种切隔离数据相同的商品路径请求参数
 */
export interface WmsMgProductBoxGetListCountQueryParam {

  /**
   * id
   */
  id?: Array<string>;
  /**
   * 关键字
   */
  keyword?: string;
  /**
   * 是否展示可用
   */
  hasUnLock?: boolean;
}


/**
 * [WmsProductBox]商品盒子标识同种切隔离数据相同的商品路径请求参数
 */
export interface WmsProductBox {

  /**
   * id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 扩展标识，用于确定是否需要单独建立商品隔离, 必须字段, 最小长度：1, 最大长度：32
   */
  productExtendId?: string;
  /**
   * 数量, 必须字段
   */
  numStorage?: number;
  /**
   * 锁定的商品数量, 必须字段
   */
  numLock?: number;
  /**
   * 状态：OK：正常、LOCK：锁定、ARCHIVED：归档, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档备注, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 所属人类型, 最小长度：0, 最大长度：32
   */
  ownerType?: string;
  /**
   * 所有者id, 最小长度：0, 最大长度：32
   */
  ownerId?: string;
  /**
   * 批次号, 必须字段, 最小长度：1, 最大长度：32
   */
  lotNum?: string;
}

export default {

  /**
   * 获取商品盒子标识同种切隔离数据相同的商品
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: WmsMgProductBoxGetListQueryParam): Promise<Array<WmsProductBox>> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/product/box`, { params: query, token: true, pageRole: false }),


  /**
   * 查询商品盒子标识同种切隔离数据相同的商品
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<WmsProductBox> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/product/box/${id}`, { token: true, pageRole: false }),


  /**
   * 获取商品盒子标识同种切隔离数据相同的商品
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: WmsMgProductBoxGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/product/box/count`, { params: query, token: true, pageRole: false }),

}
