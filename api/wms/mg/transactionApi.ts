
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取出入库单路径请求参数
 */
export interface WmsMgTransactionGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
}


/**
 * [WmsTransactionAuditVo]审核路径请求参数
 */
export interface WmsTransactionAuditVo {

  /**
   * true:同意，false:拒绝, 必须字段
   */
  audit?: boolean;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransaction]出入库单路径请求参数
 */
export interface WmsTransaction {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 所属人类型, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 所属人用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 类型：INBOUND:入库、OUTBOUND：出库、MOVE：移库, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 业务流程编号, 最小长度：0, 最大长度：32
   */
  bpmNo?: string;
  /**
   * 原因类型,NORMAL: 正常, 必须字段, 最小长度：1, 最大长度：32
   */
  reasonType?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 主体类型（USER_CHANGE：用户操作）, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 主体id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * box信息完整, 必须字段
   */
  boxMsgComplete?: boolean;
  /**
   * 商品加锁, 必须字段
   */
  productLock?: boolean;
  /**
   * 盒子加锁, 必须字段
   */
  boxLock?: boolean;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransactionBox]库存单商品路径请求参数
 */
export interface WmsTransactionBox {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 库存单id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionId?: string;
  /**
   * 商品盒子id, 最小长度：0, 最大长度：32
   */
  productBoxId?: string;
  /**
   * 申请单商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionProductId?: string;
  /**
   * 类型：IN：存入、OUT：取出, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 厂库id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseId?: string;
  /**
   * 厂库货架id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseContainerId?: string;
  /**
   * 仓库盒子, 最小长度：0, 最大长度：32
   */
  warehouseBoxId?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransactionProduct]库存单商品路径请求参数
 */
export interface WmsTransactionProduct {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 库存单id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionId?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 商品扩展信息, 必须字段, 最小长度：1, 最大长度：32
   */
  productExtendId?: string;
  /**
   * 商品盒子id, 最小长度：0, 最大长度：32
   */
  productBoxId?: string;
  /**
   * 批次号, 必须字段, 最小长度：1, 最大长度：32
   */
  lotNum?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [BpmWorkflowRecord]Bpm任务记录路径请求参数
 */
export interface BpmWorkflowRecord {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：32
   */
  name?: string;
  /**
   * 流程code, 必须字段, 最小长度：1, 最大长度：32
   */
  processCode?: string;
  /**
   * 业务流节点code, 必须字段, 最小长度：1, 最大长度：32
   */
  processNodeCode?: string;
  /**
   * 节点类型, 必须字段, 值：START, ORDINAR, AUDIT, PAUSE, END
   */
  processNodeType?: string;
  /**
   * 节点属性
   */
  processNodeProps?: string;
  /**
   * 任务id, 必须字段, 最小长度：1, 最大长度：32
   */
  workflowId?: string;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 废弃, 必须字段
   */
  invalid?: boolean;
  /**
   * 待办任务中不显示, 必须字段
   */
  processNodeHiddenTodo?: boolean;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 审核主题类型, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 审核主题id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 执行参数json格式, 最小长度：0, 最大长度：65535
   */
  paramJson?: string;
  /**
   * 审核角色, 最小长度：0, 最大长度：32
   */
  auditRoleCode?: string;
  /**
   * 审核人id, 最小长度：0, 最大长度：32
   */
  auditOwnerId?: string;
  /**
   * 审核人编号, 最小长度：0, 最大长度：32
   */
  auditOwnerNo?: string;
  /**
   * 审核人名称, 最小长度：0, 最大长度：64
   */
  auditOwnerName?: string;
  /**
   * 审核人类型, 最小长度：0, 最大长度：32
   */
  auditOwnerType?: string;
  /**
   * 审核结果，AWAIT：等待，AUTO_PASS：自动通过、NOT_AUDIT：不需要审核，REJECT：驳回，PASS：通过, 必须字段, 最小长度：1, 最大长度：32
   */
  auditResult?: string;
  /**
   * 审核时间
   */
  auditTime?: number;
  /**
   * 审核备注, 最小长度：0, 最大长度：512
   */
  auditRemark?: string;
  /**
   * 审核参数, 最小长度：0, 最大长度：65535
   */
  auditParamJson?: string;
  /**
   * 状态：INIT:初始化、SKIP：跳过、PAUSE：暂停、AUDIT：等待审核，FINISH：完成、CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 完成时间
   */
  finishTime?: number;
  /**
   * 顺序号
   */
  ord?: number;
}


/**
 * [WmsTransactionInfoBo]出入库单路径请求参数
 */
export interface WmsTransactionInfoBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 所属人类型, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 所属人用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 类型：INBOUND:入库、OUTBOUND：出库、MOVE：移库, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 业务流编号, 最小长度：0, 最大长度：32
   */
  bpmNo?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态： NEW：新建，SUBMIT：已提交，REJECT：驳回，ACCEPT：同意，FINISH：完成，CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 主体类型（USER_CHANGE：用户操作）, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 主体id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 盒子信息完整, 必须字段
   */
  boxMsgComplete?: boolean;
  /**
   * 盒子加锁, 必须字段
   */
  boxLock?: boolean;
  /**
   * 商品加锁, 必须字段
   */
  productLock?: boolean;
  /**
   * 原因类型,NORMAL: 正常, 必须字段, 最小长度：1, 最大长度：32
   */
  reasonType?: string;
  /**
   * 商品盒子
   */
  boxs?: Array<WmsTransactionBox>;
  /**
   * 商品信息
   */
  products?: Array<WmsTransactionProduct>;
  /**
   * 任务流记录
   */
  bpmWorkflowRecords?: Array<BpmWorkflowRecord>;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}

export default {

  /**
   * 审核信息
   * @param id: Id
   * @param body 请求Body参数
   */
  audit: async (id: string, body: WmsTransactionAuditVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/transaction/audit/${id}`, body, { token: true, pageRole: false }),


  /**
   * 获取出入库单
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: WmsMgTransactionGetListQueryParam): Promise<Array<WmsTransaction>> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/transaction`, { params: query, token: true, pageRole: false }),


  /**
   * 查询出入库单
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<WmsTransactionInfoBo> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/transaction/${id}`, { token: true, pageRole: false }),


  /**
   * 获取出入库单
   * @returns 数据
   */
  getListCount: async (): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/transaction/count`, { token: true, pageRole: false }),

}
