
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * [WmsTransactionOutboundAddProductVo]库存单商品路径请求参数
 */
export interface WmsTransactionOutboundAddProductVo {

  /**
   * 商品盒子id, 必须字段, 最小长度：1, 最大长度：32
   */
  productBoxId?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransactionOutboundAddVo]出入库单添加新建内容路径请求参数
 */
export interface WmsTransactionOutboundAddVo {

  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 商品信息, 必须字段
   */
  products?: Array<WmsTransactionOutboundAddProductVo>;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 原因类型,NORMAL: 正常, 必须字段, 最小长度：1, 最大长度：32
   */
  reasonType?: string;
}


/**
 * [WmsTransactionBox]库存单商品路径请求参数
 */
export interface WmsTransactionBox {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 库存单id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionId?: string;
  /**
   * 商品盒子id, 最小长度：0, 最大长度：32
   */
  productBoxId?: string;
  /**
   * 申请单商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionProductId?: string;
  /**
   * 类型：IN：存入、OUT：取出, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 厂库id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseId?: string;
  /**
   * 厂库货架id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseContainerId?: string;
  /**
   * 仓库盒子, 最小长度：0, 最大长度：32
   */
  warehouseBoxId?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransactionProduct]库存单商品路径请求参数
 */
export interface WmsTransactionProduct {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 库存单id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionId?: string;
  /**
   * 商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  productSpaceId?: string;
  /**
   * 商品扩展信息, 必须字段, 最小长度：1, 最大长度：32
   */
  productExtendId?: string;
  /**
   * 商品盒子id, 最小长度：0, 最大长度：32
   */
  productBoxId?: string;
  /**
   * 批次号, 必须字段, 最小长度：1, 最大长度：32
   */
  lotNum?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [BpmWorkflowRecord]Bpm任务记录路径请求参数
 */
export interface BpmWorkflowRecord {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：32
   */
  name?: string;
  /**
   * 流程code, 必须字段, 最小长度：1, 最大长度：32
   */
  processCode?: string;
  /**
   * 业务流节点code, 必须字段, 最小长度：1, 最大长度：32
   */
  processNodeCode?: string;
  /**
   * 节点类型, 必须字段, 值：START, ORDINAR, AUDIT, PAUSE, END
   */
  processNodeType?: string;
  /**
   * 节点属性
   */
  processNodeProps?: string;
  /**
   * 任务id, 必须字段, 最小长度：1, 最大长度：32
   */
  workflowId?: string;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 废弃, 必须字段
   */
  invalid?: boolean;
  /**
   * 待办任务中不显示, 必须字段
   */
  processNodeHiddenTodo?: boolean;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 审核主题类型, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 审核主题id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 执行参数json格式, 最小长度：0, 最大长度：65535
   */
  paramJson?: string;
  /**
   * 审核角色, 最小长度：0, 最大长度：32
   */
  auditRoleCode?: string;
  /**
   * 审核人id, 最小长度：0, 最大长度：32
   */
  auditOwnerId?: string;
  /**
   * 审核人编号, 最小长度：0, 最大长度：32
   */
  auditOwnerNo?: string;
  /**
   * 审核人名称, 最小长度：0, 最大长度：64
   */
  auditOwnerName?: string;
  /**
   * 审核人类型, 最小长度：0, 最大长度：32
   */
  auditOwnerType?: string;
  /**
   * 审核结果，AWAIT：等待，AUTO_PASS：自动通过、NOT_AUDIT：不需要审核，REJECT：驳回，PASS：通过, 必须字段, 最小长度：1, 最大长度：32
   */
  auditResult?: string;
  /**
   * 审核时间
   */
  auditTime?: number;
  /**
   * 审核备注, 最小长度：0, 最大长度：512
   */
  auditRemark?: string;
  /**
   * 审核参数, 最小长度：0, 最大长度：65535
   */
  auditParamJson?: string;
  /**
   * 状态：INIT:初始化、SKIP：跳过、PAUSE：暂停、AUDIT：等待审核，FINISH：完成、CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 完成时间
   */
  finishTime?: number;
  /**
   * 顺序号
   */
  ord?: number;
}


/**
 * [WmsTransactionInfoBo]出入库单路径请求参数
 */
export interface WmsTransactionInfoBo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 所属人类型, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 所属人用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 类型：INBOUND:入库、OUTBOUND：出库、MOVE：移库, 必须字段, 最小长度：1, 最大长度：32
   */
  type?: string;
  /**
   * 编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 业务流编号, 最小长度：0, 最大长度：32
   */
  bpmNo?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态： NEW：新建，SUBMIT：已提交，REJECT：驳回，ACCEPT：同意，FINISH：完成，CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 主体类型（USER_CHANGE：用户操作）, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 主体id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 盒子信息完整, 必须字段
   */
  boxMsgComplete?: boolean;
  /**
   * 盒子加锁, 必须字段
   */
  boxLock?: boolean;
  /**
   * 商品加锁, 必须字段
   */
  productLock?: boolean;
  /**
   * 原因类型,NORMAL: 正常, 必须字段, 最小长度：1, 最大长度：32
   */
  reasonType?: string;
  /**
   * 商品盒子
   */
  boxs?: Array<WmsTransactionBox>;
  /**
   * 商品信息
   */
  products?: Array<WmsTransactionProduct>;
  /**
   * 任务流记录
   */
  bpmWorkflowRecords?: Array<BpmWorkflowRecord>;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransactionOutboundPutBoxBoxVo]库存单商品库位路径请求参数
 */
export interface WmsTransactionOutboundPutBoxBoxVo {

  /**
   * 申请单商品id, 必须字段, 最小长度：1, 最大长度：32
   */
  transactionProductId?: string;
  /**
   * 仓库盒子, 最小长度：0, 最大长度：32
   */
  warehouseBoxId?: string;
  /**
   * 数量, 必须字段
   */
  num?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsTransactionOutboundPutBoxMsgVo]盒子信息补充vo路径请求参数
 */
export interface WmsTransactionOutboundPutBoxMsgVo {

  /**
   * 盒子信息
   */
  boxs?: Array<WmsTransactionOutboundPutBoxBoxVo>;
}

export default {

  /**
   * 创建出库单
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: WmsTransactionOutboundAddVo): Promise<WmsTransactionInfoBo> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/transaction/outbound`, body, { token: true, pageRole: false }),


  /**
   * 追加盒子信息
   * @param id: Id
   * @param body 请求Body参数
   */
  putBoxMsg: async (id: string, body: WmsTransactionOutboundPutBoxMsgVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/transaction/outbound/put-box-msg/${id}`, body, { token: true, pageRole: false }),

}
