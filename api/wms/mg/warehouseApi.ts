
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取供应商仓库路径请求参数
 */
export interface WmsMgWarehouseGetAllListQueryParam {

  /**
   * 类型
   */
  state?: Array<string>;
}


/**
 * [WmsWarehouse]供应商仓库路径请求参数
 */
export interface WmsWarehouse {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 仓库名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态：OK：正常、ARCHIVED：归档, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档备注, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsWarehouseAddVo]供应商仓库添加新建内容路径请求参数
 */
export interface WmsWarehouseAddVo {

  /**
   * 仓库名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsWarehouseArchivedUpdateVo]归档申请路径请求参数
 */
export interface WmsWarehouseArchivedUpdateVo {

  /**
   * 备注信息
   */
  remark?: string;
}

export default {

  /**
   * 查询供应商仓库
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<WmsWarehouse> => request.get(`${config.baseUrl.apiUrl}/wms/mg/warehouse/${id}`, { token: true, pageRole: true }),


  /**
   * 更新供应商仓库
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: WmsWarehouseAddVo): Promise<WmsWarehouse> => request.put(`${config.baseUrl.apiUrl}/wms/mg/warehouse/${id}`, body, { token: true, pageRole: true }),


  /**
   * 归档仓库
   * @param id: Id
   * @param body 请求Body参数
   */
  archived: async (id: string, body: WmsWarehouseArchivedUpdateVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/wms/mg/warehouse/${id}/archived`, body, { token: true, pageRole: true }),


  /**
   * 获取供应商仓库
   * @param query 请求参数
   * @returns 数据
   */
  getAllList: async (query: WmsMgWarehouseGetAllListQueryParam): Promise<Array<WmsWarehouse>> => request.get(`${config.baseUrl.apiUrl}/wms/mg/warehouse`, { params: query, token: true, pageRole: true }),


  /**
   * 添加供应商仓库
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: WmsWarehouseAddVo): Promise<WmsWarehouse> => request.post(`${config.baseUrl.apiUrl}/wms/mg/warehouse`, body, { token: true, pageRole: true }),

}
