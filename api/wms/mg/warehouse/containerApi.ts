
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取仓储货柜货架路径请求参数
 */
export interface WmsMgWarehouseContainerGetAllListQueryParam {

  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 类型
   */
  state?: Array<string>;
}


/**
 * [WmsWarehouseContainer]仓储货柜货架路径请求参数
 */
export interface WmsWarehouseContainer {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 货架编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 所属仓库id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseId?: string;
  /**
   * 所属上一级货架（0：根货架）, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 是否可存储商品(0:区域，1:货架), 必须字段
   */
  storage?: boolean;
  /**
   * 最大容量，storage为true时有效, 必须字段
   */
  numMax?: number;
  /**
   * 使用容量，storage为true时有效, 必须字段
   */
  numUsed?: number;
  /**
   * 锁定容量，storage为true时有效, 必须字段
   */
  numLock?: number;
  /**
   * 状态：OK：正常、ARCHIVED：归档, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 锁定状态计数, 必须字段
   */
  stateLockNum?: number;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 储存条件(,)逗号分割, 最小长度：0, 最大长度：256
   */
  environment?: string;
  /**
   * 归档备注, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsWarehouseContainerModifyVo]仓储货柜货架添加新建内容路径请求参数
 */
export interface WmsWarehouseContainerModifyVo {

  /**
   * 货架编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 所属仓库, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseId?: string;
  /**
   * 所属上一级货架（0：根货架）, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 储存条件(,)逗号分割, 最小长度：0, 最大长度：256
   */
  environment?: string;
  /**
   * 最大容量，storage为true时有效, 必须字段
   */
  numMax?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsWarehouseContainerArchivedUpdateVo]归档申请路径请求参数
 */
export interface WmsWarehouseContainerArchivedUpdateVo {

  /**
   * 备注信息
   */
  remark?: string;
}


/**
 * [WmsWarehouseContainerAddVo]仓储货柜货架添加新建内容路径请求参数
 */
export interface WmsWarehouseContainerAddVo {

  /**
   * 货架编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：64
   */
  name?: string;
  /**
   * 所属仓库, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseId?: string;
  /**
   * 所属上一级货架（0：根货架）, 必须字段, 最小长度：1, 最大长度：32
   */
  idParent?: string;
  /**
   * 储存条件(,)逗号分割, 最小长度：0, 最大长度：256
   */
  environment?: string;
  /**
   * 最大容量，storage为true时有效, 必须字段
   */
  numMax?: number;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
  /**
   * 是否可存储商品(0:区域，1:货架), 必须字段
   */
  storage?: boolean;
}

export default {

  /**
   * 查询仓储货柜货架
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<WmsWarehouseContainer> => request.get(`${config.baseUrl.apiUrl}/wms/mg/warehouse/container/${id}`, { token: true }),


  /**
   * 更新仓储货柜货架
   * @param id: Id
   * @param body 请求Body参数
   * @returns 数据
   */
  modify: async (id: string, body: WmsWarehouseContainerAddVo): Promise<WmsWarehouseContainer> => request.put(`${config.baseUrl.apiUrl}/wms/mg/warehouse/container/${id}`, body, { token: true }),


  /**
   * 归档仓库
   * @param id: Id
   * @param body 请求Body参数
   */
  archived: async (id: string, body: WmsWarehouseContainerArchivedUpdateVo): Promise<void> => request.put(`${config.baseUrl.apiUrl}/wms/mg/warehouse/container/${id}/archived`, body, { token: true }),


  /**
   * 获取仓储货柜货架
   * @param query 请求参数
   * @returns 数据
   */
  getAllList: async (query: WmsMgWarehouseContainerGetAllListQueryParam): Promise<Array<WmsWarehouseContainer>> => request.get(`${config.baseUrl.apiUrl}/wms/mg/warehouse/container`, { params: query, token: true }),


  /**
   * 添加仓储货柜货架
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: WmsWarehouseContainerAddVo): Promise<WmsWarehouseContainer> => request.post(`${config.baseUrl.apiUrl}/wms/mg/warehouse/container`, body, { token: true }),

}
