
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取仓库盒子，同盒子只能是同一个商品路径请求参数
 */
export interface WmsMgWarehouseBoxGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
  /**
   * 仓库盒子id
   */
  id?: Array<string>;
  /**
   * 商品盒子id
   */
  product_box_id?: Array<string>;
}


/**
 * 获取仓库盒子，同盒子只能是同一个商品路径请求参数
 */
export interface WmsMgWarehouseBoxGetListCountQueryParam {

  /**
   * 商品盒子id
   */
  product_box_id?: Array<string>;
}


/**
 * [WmsWarehouseBox]仓库盒子，同盒子只能是同一个商品路径请求参数
 */
export interface WmsWarehouseBox {

  /**
   * id, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 存放仓库id, 必须字段, 最小长度：1, 最大长度：32
   */
  warehouseId?: string;
  /**
   * 货柜id, 必须字段, 最小长度：1, 最大长度：32
   */
  containerId?: string;
  /**
   * 商品盒子id, 必须字段, 最小长度：1, 最大长度：32
   */
  productBoxId?: string;
  /**
   * 数量, 必须字段
   */
  numStorage?: number;
  /**
   * 锁定的商品数量, 必须字段
   */
  numLock?: number;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态：OK：正常、LOCK：锁定、ARCHIVED：归档, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 归档, 必须字段
   */
  archived?: boolean;
  /**
   * 归档时间
   */
  archivedTime?: number;
  /**
   * 归档备注, 最小长度：0, 最大长度：512
   */
  archivedRemark?: string;
}

export default {

  /**
   * 获取仓库盒子，同盒子只能是同一个商品
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: WmsMgWarehouseBoxGetListQueryParam): Promise<Array<WmsWarehouseBox>> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/warehouse/box`, { params: query, token: true, pageRole: false }),


  /**
   * 获取仓库盒子，同盒子只能是同一个商品
   * @param query 请求参数
   * @returns 数据
   */
  getListCount: async (query: WmsMgWarehouseBoxGetListCountQueryParam): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/warehouse/box/count`, { params: query, token: true, pageRole: false }),

}
