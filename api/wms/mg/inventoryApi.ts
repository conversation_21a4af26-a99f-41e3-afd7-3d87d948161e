
import { request } from '@/utils/request'
import { config } from '@/config/global.config'

/**
 * 获取盘点单路径请求参数
 */
export interface WmsMgInventoryGetListQueryParam {

  /**
   * 每页多少行
   */
  rows?: number;
  /**
   * 页码，从0开始
   */
  page?: number;
}


/**
 * [WmsInventory]盘点任务路径请求参数
 */
export interface WmsInventory {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态：NEW：新建、SUBMIT：进行中、FINISH：完成、CANCEL: 取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 流程编号, 最小长度：0, 最大长度：32
   */
  bpmNo?: string;
  /**
   * 审核状态, 最小长度：0, 最大长度：32
   */
  auditState?: string;
  /**
   * 审核备注, 最小长度：0, 最大长度：255
   */
  auditRemark?: string;
  /**
   * 所属人类型, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 所属人用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 盘点仓库id, 必须字段, 最小长度：1, 最大长度：2048
   */
  warehouseContainerId?: string;
}


/**
 * [WmsInventoryAddVo]盘点任务添加新建内容路径请求参数
 */
export interface WmsInventoryAddVo {

  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 盘点仓库ids, 必须字段
   */
  warehouseContainerIds?: Array<string>;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [WmsInventoryAuditVo]审核路径请求参数
 */
export interface WmsInventoryAuditVo {

  /**
   * true:同意，false:拒绝, 必须字段
   */
  audit?: boolean;
  /**
   * 备注, 最小长度：0, 最大长度：512
   */
  remark?: string;
}


/**
 * [BpmWorkflowRecord]Bpm任务记录路径请求参数
 */
export interface BpmWorkflowRecord {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 名称, 必须字段, 最小长度：1, 最大长度：32
   */
  name?: string;
  /**
   * 流程code, 必须字段, 最小长度：1, 最大长度：32
   */
  processCode?: string;
  /**
   * 业务流节点code, 必须字段, 最小长度：1, 最大长度：32
   */
  processNodeCode?: string;
  /**
   * 节点类型, 必须字段, 值：START, ORDINAR, AUDIT, PAUSE, END
   */
  processNodeType?: string;
  /**
   * 节点属性
   */
  processNodeProps?: string;
  /**
   * 任务id, 必须字段, 最小长度：1, 最大长度：32
   */
  workflowId?: string;
  /**
   * 创建时间
   */
  createTime?: number;
  /**
   * 废弃, 必须字段
   */
  invalid?: boolean;
  /**
   * 待办任务中不显示, 必须字段
   */
  processNodeHiddenTodo?: boolean;
  /**
   * 流程审核页面地址, 最小长度：0, 最大长度：512
   */
  todoAuditUrl?: string;
  /**
   * 流程详情页面地址, 最小长度：0, 最大长度：512
   */
  todoDetailUrl?: string;
  /**
   * 审核主题类型, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectType?: string;
  /**
   * 审核主题id, 必须字段, 最小长度：1, 最大长度：32
   */
  subjectId?: string;
  /**
   * 执行参数json格式, 最小长度：0, 最大长度：65535
   */
  paramJson?: string;
  /**
   * 审核角色, 最小长度：0, 最大长度：32
   */
  auditRoleCode?: string;
  /**
   * 审核人id, 最小长度：0, 最大长度：32
   */
  auditOwnerId?: string;
  /**
   * 审核人编号, 最小长度：0, 最大长度：32
   */
  auditOwnerNo?: string;
  /**
   * 审核人名称, 最小长度：0, 最大长度：64
   */
  auditOwnerName?: string;
  /**
   * 审核人类型, 最小长度：0, 最大长度：32
   */
  auditOwnerType?: string;
  /**
   * 审核结果，AWAIT：等待，AUTO_PASS：自动通过、NOT_AUDIT：不需要审核，REJECT：驳回，PASS：通过, 必须字段, 最小长度：1, 最大长度：32
   */
  auditResult?: string;
  /**
   * 审核时间
   */
  auditTime?: number;
  /**
   * 审核备注, 最小长度：0, 最大长度：512
   */
  auditRemark?: string;
  /**
   * 审核参数, 最小长度：0, 最大长度：65535
   */
  auditParamJson?: string;
  /**
   * 状态：INIT:初始化、SKIP：跳过、PAUSE：暂停、AUDIT：等待审核，FINISH：完成、CANCEL：取消, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 完成时间
   */
  finishTime?: number;
  /**
   * 顺序号
   */
  ord?: number;
}


/**
 * [WmsInventoryVo]盘点任务路径请求参数
 */
export interface WmsInventoryVo {

  /**
   * 主键, 必须字段, 最小长度：1, 最大长度：32
   */
  id?: string;
  /**
   * 编号, 必须字段, 最小长度：1, 最大长度：32
   */
  no?: string;
  /**
   * 名称, 最小长度：0, 最大长度：64
   */
  name?: string;
  /**
   * 创建时间, 必须字段
   */
  createTime?: number;
  /**
   * 状态, 必须字段, 最小长度：1, 最大长度：32
   */
  state?: string;
  /**
   * 流程编号, 最小长度：0, 最大长度：32
   */
  bpmNo?: string;
  /**
   * 结果流程编号, 最小长度：0, 最大长度：32
   */
  resultBpmNo?: string;
  /**
   * 审核状态, 最小长度：0, 最大长度：32
   */
  auditState?: string;
  /**
   * 审核备注, 最小长度：0, 最大长度：255
   */
  auditRemark?: string;
  /**
   * 所属人类型, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerType?: string;
  /**
   * 所属人用户id, 必须字段, 最小长度：1, 最大长度：32
   */
  ownerId?: string;
  /**
   * 盘点仓库id, 必须字段
   */
  warehouseContainerIds?: Array<string>;
  /**
   * 任务流记录
   */
  bpmWorkflowRecords?: Array<BpmWorkflowRecord>;
}

export default {

  /**
   * 获取盘点单
   * @param query 请求参数
   * @returns 数据
   */
  getList: async (query: WmsMgInventoryGetListQueryParam): Promise<Array<WmsInventory>> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/inventory`, { params: query, token: true, pageRole: false }),


  /**
   * 生成盘点单
   * @param body 请求Body参数
   * @returns 数据
   */
  add: async (body: WmsInventoryAddVo): Promise<WmsInventory> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/inventory`, body, { token: true, pageRole: false }),


  /**
   * 取消盘点
   * @param id: Id
   */
  cancel: async (id: string): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/inventory/cancel/${id}`, {}, { token: true, pageRole: false }),


  /**
   * 开始盘点
   * @param id: Id
   */
  begin: async (id: string): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/inventory/begin/${id}`, {}, { token: true, pageRole: false }),


  /**
   * 审核信息
   * @param id: Id
   * @param body 请求Body参数
   */
  audit: async (id: string, body: WmsInventoryAuditVo): Promise<void> => await request.post(`${config.baseUrl.apiUrl}/wms/mg/inventory/audit/${id}`, body, { token: true, pageRole: false }),


  /**
   * 查询盘点单
   * @param id: Id
   * @returns 数据
   */
  get: async (id: string): Promise<WmsInventory> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/inventory/${id}`, { token: true, pageRole: false }),


  /**
   * 获取出盘点单
   * @returns 数据
   */
  getListCount: async (): Promise<number> => await request.get(`${config.baseUrl.apiUrl}/wms/mg/inventory/count`, { token: true, pageRole: false }),

}
