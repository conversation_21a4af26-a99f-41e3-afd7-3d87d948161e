/**
 * 判断是否为空返回默认值
 * @param data 数据
 * @param def 默认值
 * @returns 值
 */
const getValueDef = (data: any, def: any) => {
  return data == undefined || data == null ? def : data;
}

/**
 * 将字段转换映射为数组
 * @param field 字段信息
 * @returns 数据字段信息
 */
const field2array = (field: string | Array<string | number>): Array<string | number> => {
  if (!(field instanceof Array)) {
    //字段转换
    // 处理.分割
    let fields = field.split('.');
    let fds: Array<string | number> = [];
    fields.forEach(x => {
      if (!x) {
        return;
      }
      // 处理[]数组
      let tfs: Array<string> = x.split('[');
      if (tfs.length <= 1) {
        fds.push(x);
      } else {
        tfs.forEach(y => {
          if (!y) {
            return;
          }
          fds.push(y.endsWith(']') ? +(y.substr(0, y.length - 1)) : y);
        });
      }
    });
    field = fds;
  }
  return field;
}

/**
 * 对象中提取字段，未找到可返回默认字段
 * @param data 数据对象
 * @param field 字段信息
 * @param def 默认值
 * @returns 数据值
 */
const getFieldValue = <T>(data: T, field: keyof T | string | Array<string | number>, def: any) => {
  // def = getValueDef(def, '');
  if (!data || !field) {
    return def;
  }
  let fields = field2array(<any>field);

  let value: any = data;
  for (let idx in fields) {
    let fd = fields[idx];
    value = value[fd];
    if (value == undefined || value == null) {
      return def;
    }
  }
  return value;
}

/**
 * 设置数据对象值
 * @param data 数据对象
 * @param field 字段信息
 * @param value 值
 */
const setFieldValue = (data: any, field: string | Array<string | number>, value: any) => {
  if (!data) {
    return;
  }
  let fields = field2array(field);

  let node = data;
  for (let i = 0; i < fields.length; i++) {
    let fd = fields[i];

    if (i + 1 == fields.length) {
      // 最后阶段
      node[fd] = value;
      return;
    }

    let nfd = fields[i + 1];
    // 中间变量
    if (typeof (node[fd]) === 'object') {
      if (typeof (nfd) === 'number') {
        if (!(node[fd] instanceof Array)) {
          node[fd] = [];
        }
      } else {
        if (node[fd] instanceof Array) {
          node[fd] = {};
        }
      }
    } else {
      node[fd] = typeof (nfd) === 'number' ? [] : {};
    }
    node = node[fd];
  }
}

/**
 * 使用默认值初始化字段，若字段子已存在则不进行处理
 * @param data 数据对象
 * @param field 字段
 * @param def 默认值
 */
const initFieldValue = (data: any, field: string | Array<string | number>, def: any) => {
  let val = getFieldValue(data, field, def);
  setFieldValue(data, field, val);
}

/**
 * 扫描对象的字段信息，将数据值装换为数组
 * @param data 扫描对象
 * @returns 字段以及数据值
 */
const data2field = (data: any): Array<{ field: '', data: null }> => {
  let fields: Array<{ field: '', data: null }> = [];
  const isLeafNode = (node: any) => {
    if (node == null || node == undefined) {
      return true;
    }
    if (typeof (node) != 'object') {
      return true;
    }
    let count = 0;
    for (let key in node) {
      count++;
    }
    return count == 0;
  }
  const scanData = (data: any, fields: Array<{ field: string, data: any }>, parentField: string) => {
    for (let key in data) {
      let node = data[key];
      let nfield = '';
      if (data instanceof Array) {
        nfield = parentField ? parentField + '[' + key + ']' : key;
      } else {
        nfield = parentField ? parentField + '.' + key : key;
      }
      if (isLeafNode(node)) {
        fields.push({ field: nfield, data: node });
      } else {
        scanData(node, fields, nfield);
      }
    }
  }
  scanData(data, fields, '');
  return fields;
}

/**
 * 通过默认对象对指定对象进行初始化
 * @param data 被初始化对象
 * @param defData 默认对象
 */
const initField = (data: any, defData: any) => {
  if (!data || !defData) {
    return;
  }
  let fields: Array<{ field: '', data: null }> = data2field(defData);
  for (let i in fields) {
    let field = fields[i];
    setFieldValue(data, field.field, field.data);
  }
}

export default {
  getValueDef,
  getFieldValue,
  field2array,
  setFieldValue,
  initFieldValue,
  data2field,
  initField,
};
