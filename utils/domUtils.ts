
export class DomSizeListener {
    dom: any | null = null;
    widthName: string = 'clientWidth';
    heightName: string = 'clientHeight';

    callback: ()=>void = ()=>{};
    timeout: number = 0;
    timer: any = null;
    runTimer: any = null;

    size = { w: 0, h: 0 }

    /**
     * 监听dom组件宽高变化
     * @param dom 
     * @param callback 
     * @param timeout 监听到变化后多少毫秒后再进行回调，默认10毫秒
     */
    constructor(dom: any, callback: ()=>void | null, timeout: number = 10) {
        this.dom = dom;
        this.callback = callback;
        this.timeout = timeout;
    }

    start() {
        let that = this;
        if (!that.dom) {
            return;
        }
        that.size.w = that.dom![that.widthName];
        that.size.h = that.dom![that.heightName];
        that.__timer();
    }

    __timer() {
        let that = this;
        that.timer = setTimeout(()=>{
            that.__timer();
            if (!that.dom) {
                return;
            }
            if (that.size.w == that.dom![that.widthName] && that.size.h == that.dom![that.heightName]) {
                return;
            }
            that.size.w = that.dom![that.widthName];
            that.size.h = that.dom![that.heightName];
            // 执行
            if (this.timeout <= 0) {
                this.run();
            }
            if (that.runTimer) {
                clearTimeout(that.runTimer);
                that.runTimer = null;
            }
            that.runTimer = setTimeout(() => {
                this.run();
            }, that.timeout);
        }, 100);
    }

    stop() {
        let that = this;
        if (that.timer) {
            clearTimeout(that.timer);
            that.timer = null;
        }
        if (that.runTimer) {
            clearTimeout(that.runTimer);
            that.runTimer = null;
        }
    }

    run() {
        let that = this;
        that.size.w = that.dom![that.widthName];
        that.size.h = that.dom![that.heightName];
        if (this.callback) {
            this.callback();
        }
    }

}



export default {}

