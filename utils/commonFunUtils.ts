
/**
 * 匹配为boolean
 */
const parseBoolean = (obj: any, def: boolean = false): boolean => {
  if (undefined == obj || null == obj || obj === '') {
    return def;
  }
  if (typeof obj == 'boolean') {
    return obj;
  }
  if (obj instanceof Boolean) {
    return <boolean>obj;
  }
  let b = obj.toString().toLowerCase();
  if (b == 'yes' || b == 'y' || b == 'true' || b == 't') {
    return true;
  }
  if (b == '0' || b == 'false' || b == 'f' || b == 'no' || b == 'n') {
    return false;
  }
  try {
    return +b != 0;
  } catch (error) {
  }
  return true;
}

export default { parseBoolean }
