import commonFunUtils from './commonFunUtils';
import routerUtils from './routerUtils'
/**
 * 表单发送EmitInput事件，并针对element做事件通知
 * @param that 当前Vu对象
 * @param value 值
 */
function formEmitInput(that: Vue, value: any) {
  that.$emit("input", value);
  that.$emit("change", value);

  let componentName = "el-form-item";
  let eventName = "el.form.change";
  var parent = that.$parent || that.$root;
  if (!parent || !parent.$options) {
    return;
  }
  var name = (<any>parent.$options)._componentTag;

  while (parent && (!name || name !== componentName)) {
    parent = parent.$parent;

    if (parent && parent.$options) {
      name = (<any>parent.$options)._componentTag;
    }
  }
  if (parent) {
    parent.$emit.apply(parent, [eventName, value]);
  }
}

const isProp = (that: Vue) => {
  if (that.$props) {
    let prop = that.$props[VALUE_TAG_PROP];
    return commonFunUtils.parseBoolean(prop, false);
  } else {
    return false;
  }
}

function getQueryValue<T extends string[] | string>(that: Vue, key: string, def: T): T {
  if (isProp(that)) {
    return that.$props[key];
  } else {
    let param = routerUtils.getParamValue(that, key, '');
    if (param) {
      return param;
    }
    param = routerUtils.getQueryValue(that, key, def);
    return param;
  }
}

const putQueryValue = (that: Vue, obj: any) => {
  routerUtils.putQueryValue(that, obj);
}

const toPage = (that: Vue, action: string, url: string, param: any) => {
  if (isProp(that)) {
    that.$emit(action, param);
  } else {
    that.$router.push(url);
  }
}

const VALUE_TAG_PROP = "_prop";

export default {
  VALUE_TAG_PROP,
  formEmitInput,
  getQueryValue,
  putQueryValue,
  toPage,
}