
const timeUtils = {
  timeOffset: 0,
  get serverTime() {
    return new Date(new Date().getTime() + this.timeOffset);
  },
  set serverTime(value: Date) {
    if (!value) {
      return;
    }
    let nt = new Date().getTime();
    this.timeOffset = nt - value.getTime();
  },
  timeout(time: number): Promise<any> {
    return new Promise((r: any, j: any) => {
      setTimeout(() => r(), time);
    });
  }
}

export default timeUtils;

