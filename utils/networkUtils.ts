export class RequestBuffer<T> {
  private requestList: Array<{
    r: (value: T) => void,
    j: (err: any) => void
  }> = [];
  private responseCb: ((success: boolean) => void) | null = null;

  request = (cb: () => Promise<T>): Promise<T> => {
    let that = this;
    return new Promise((r, j) => {
      that.requestList.push({ r, j });
      if (that.requestList.length > 1) {
        return;
      }
      cb().then(res => {
        let ns = that.requestList;
        that.requestList = [];
        for (let i in ns) {
          ns[i].r(res);
        }
        if (this.responseCb) {
          this.responseCb(true);
        }
      }).catch(err => {
        let ns = that.requestList;
        that.requestList = [];
        for (let i in ns) {
          ns[i].j(err);
        }
        if (this.responseCb) {
          this.responseCb(false);
        }
      });
    });
  }

  responseListener(responseCb: (success: boolean) => void) {
    this.responseCb = responseCb;
  }
}

const buildRequestBuffer = <T>() => {
  return new RequestBuffer<T>();
}

export default {
  buildRequestBuffer,
}
