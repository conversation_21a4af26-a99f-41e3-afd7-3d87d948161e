const formCheck = async (that: Vue, ref: string): Promise<boolean> => {
  return await new Promise((r, j) => {
    (<any>that.$refs[ref]).validate((success: boolean, object: any) => {
      console.log('校验不通过原因：', object)
      r(success);
    });
  });
}

const formCheckField = async (that: Vue, ref: string, props: Array<string> | string): Promise<boolean> => {
  return await new Promise((r, j) => {
    (<any>that.$refs[ref]).validateField(props, (success: any) => r(!success));
  });
}

const isEmpty = (value: any) => {
  if (value == null || value == undefined) {
    return true;
  } else {
    if (typeof value == 'string') {
      if (!value) {
        return true;
      } else {
        return false;
      }
    } else if (Array.isArray(value)) {
      if ((<Array<any>>value).length > 0) {
        return false;
      } else {
        return true;
      }
    } else {
      return false;
    }
  }
}

/**
 * 封装校验器
 * @param fun 回调
 * @returns 校验器
 */
const valid = (fun: (rule: any, value: any) => Promise<string | null | undefined> | string | null | undefined) => {
  return {
    trigger: ["blur", "change"],
    validator: (rule: any, value: any, cb: any) => {
      let r = fun(rule, value);
      if (!r) {
        cb();
        return;
      }
      if (r instanceof Promise) {
        r.then((res) => {
          if (res) {
            cb(new Error(res));
          } else {
            cb();
          }
        }).catch(err => {
          cb(err);
        });
      } else {
        if (r) {
          cb(new Error(r));
        } else {
          cb();
        }
      }
    },
  };
}

const required = (prop: any, msg: string = '') => {
  msg = msg ? msg : "该字段不能为空";
  return valid(async (rule: any, value: any) => {
    return isEmpty(value) ? msg : null;
  });
}

const email = (prop: any, msg: string = '') => {
  msg = msg ? msg : "Email格式不正确";
  return valid(async (rule: any, value: any) => {
    if (isEmpty(value)) {
      return null;
    }
    let rgx = /^([A-Za-z0-9_\-\.\u4e00-\u9fa5])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,8})$/;
    if (!rgx.test(value)) {
      return msg;
    }
    return null;
  });
}

const phone = (prop: any, msg: string = '') => {
  msg = msg ? msg : "联系方式格式不正确";
  return {
    trigger: ["blur", "change"],
    validator: (rule: any, value: any, cb: any) => {
      if (isEmpty(value)) {
        cb();
      } else {
        let rgx = /^\+?\d{10,15}$/;
        if (!rgx.test(value)) {
          cb(new Error(msg));
        } else {
          cb();
        }
      }
    },
  };
}

// [min, max]
const lenRange = (range: [number, number], msg: string = '') => {
  if (range[0] == range[1]) {
    msg = msg ? msg : `长度必须等于${range[0]}`;
  } else {
    msg = msg ? msg : `长度必须介于${range[0]}到${range[1]}之间`;
  }
  return {
    trigger: ["blur", "change"],
    validator: (rule: any, value: any, cb: any) => {
      if (isEmpty(value)) {
        cb();
      } else {
        let vn = (<string>value).length;
        if (!(range[0] <= vn && vn <= range[1])) {
          cb(new Error(msg));
        } else {
          cb();
        }
      }
    },
  };
}

const number = (prop: any, msg: string = '') => {
  msg = msg ? msg : `必须为数字格式`;
  return {
    trigger: ["blur", "change"],
    validator: (rule: any, value: any, cb: any) => {
      if (isEmpty(value)) {
        cb();
      } else {
        let rgx = /^-?\d+(\.\d+)?$/
        if (!rgx.test(value)) {
          cb(new Error(msg));
        } else {
          cb();
        }
      }
    },
  };
}

// [min, max]
const numRange = (range: [number, number], msg: string = '') => {
  msg = msg ? msg : `必须介于${range[0]}到${range[1]}之间`;
  return {
    trigger: ["blur", "change"],
    validator: (rule: any, value: any, cb: any) => {
      if (isEmpty(value)) {
        cb();
      } else {
        let rgx = /[^\d.]/g;
        if (!rgx.test(value)) {
          cb();
          return;
        }
        let vn = +value;
        if (!(range[0] <= vn && vn <= range[1])) {
          cb(new Error(msg));
        } else {
          cb();
        }
      }
    },
  };
}

export default { formCheck, formCheckField, valid, required, email, phone, lenRange, number, numRange }

