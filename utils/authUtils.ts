// TODO 权限注解Start
const hasPower = (probj: any, power: Array<string> | string | undefined | null) => {
  if (!power || power.length <= 0) {
    return true;
  }
  if (!probj || !probj.roles) {
    return false;
  }
  power = power instanceof Array ? power : [power];
  let pr = probj.roles;
  for (let i in power) {
    let code = power[i];
    for (let j in pr) {
      let p = pr[j];
      if (p.code == code) {
        return true;
      }
      if (p.powers && p.powers.length > 0) {
        for (let k in p.powers) {
          if (p.powers[k].code == code) {
            return true;
          }
        }
      }
    }
  }
  return false;
}

export default {
  hasPower
}