import fieldUtils from "./fieldUtils";
import sysVersionPubApi from '~/api/basic/sys/versionApi';
import dicApi from "@/api/auth/dic/dicApi";
import { config } from "~/config/global.config";

/**
 * [SysDicFieldI18nBo]系统数据字典字段国际化路径请求参数
 */
export interface SysDicFieldI18nBo {

  // language, 最小长度：1, 最大长度：32
  language?: string;
  // 显示内容
  label?: string;
}

/**
 * [SysDicFieldPropBo]系统数据字典字段Code属性路径请求参数
 */
export interface SysDicFieldPropBo {

  /**
   * code, 最小长度：1, 最大长度：32
   */
  code?: string;
  /**
   * 值
   */
  value?: string;
}


// [SysDicFieldBo]系统数据字典字段路径请求参数
export interface SysDicFieldBo {

  // code, 最小长度：1, 最大长度：32
  code?: string;
  // 显示内容
  label?: string;
  // 字段信息
  labels?: Array<SysDicFieldI18nBo>;
  // 属性信息
  props?: Array<SysDicFieldPropBo>;
  // 备注
  remark?: string;
}


// [SysDicBo]系统数据字典路径请求参数
export interface SysDicBo {

  // 主键, 必须字段, 最小长度：1, 最大长度：32
  id?: string;
  // code, 最小长度：1, 最大长度：32
  code?: string;
  // 名称, 必须字段, 最小长度：1, 最大长度：64
  name?: string;
  // 启用, 必须字段
  valid?: boolean;
  // 字段信息
  fields?: Array<SysDicFieldBo>;
  // 备注
  remark?: string;
}

interface Tkey2value<T> {
  [key: string]: T;
}
interface TCode2fields2value {
  [key: string]: Array<SysDicFieldBo>;
}
interface TCode2fields {
  [key: string]: Array<SysDicFieldBo>;
}


let i18n = '';
let dicData: Array<SysDicBo> = [];
// code -> obj
let code2obj: Tkey2value<SysDicBo> = {};
// code -> field -> fObj
let code2field2value: Tkey2value<Tkey2value<SysDicFieldBo>> = {};
// code -> field -> i18n -> label
let code2field2value2i18n: Tkey2value<Tkey2value<Tkey2value<SysDicFieldI18nBo>>> = {};
// code -> field -> prop -> label
let code2field2value2prop: Tkey2value<Tkey2value<Tkey2value<SysDicFieldPropBo>>> = {};
// code -> value -> fObj
let code2value2value: Tkey2value<Tkey2value<SysDicFieldBo>> = {};

const setI18n = (lang: string) => {
  i18n = lang;
}

const getI18n = (): string => {
  return i18n ? i18n : 'zh';
}

// 获取原始数据集
const getData = (): any => fieldUtils.getValueDef(dicData, {});
// 获取所有字典列表数据集
const getList = (): Array<SysDicBo> => fieldUtils.getValueDef(getData(), []);

/**
 * 装载数据字典数据
 * @param pub 公开数据
 */
const reload = async (pub: boolean = true) => {
  let data: Array<SysDicBo> = <any>(await (pub ? (await sysVersionPubApi.getPubVersion(config.sysInfo.projectAppId)).dics : dicApi.getList()));
  dicData = data ? data : [];
  let list = getList();
  {
    let _code2obj: Tkey2value<SysDicBo> = {};
    list.forEach((x: SysDicBo) => _code2obj[x.code!] = x);
    code2obj = _code2obj;
  }
  {
    let _code2field2value: Tkey2value<Tkey2value<SysDicFieldBo>> = {};
    let _code2value2value: Tkey2value<Tkey2value<SysDicFieldBo>> = {};
    let _code2field2value2i18n: Tkey2value<Tkey2value<Tkey2value<SysDicFieldI18nBo>>> = {};
    let _code2field2value2prop: Tkey2value<Tkey2value<Tkey2value<SysDicFieldPropBo>>> = {};
    list.forEach(x => {
      let field2value = _code2field2value[x.code!];
      field2value = field2value ? field2value : {};

      let value2value = _code2value2value[x.code!];
      value2value = value2value ? value2value : {};

      let field2value2i18n = _code2field2value2i18n[x.code!];
      field2value2i18n = field2value2i18n ? field2value2i18n : {};

      let field2value2prop = _code2field2value2prop[x.code!];
      field2value2prop = field2value2prop ? field2value2prop : {};

      if (x.fields) {
        x.fields.forEach((y) => {
          field2value[y.code!] = y;
          value2value[y.label!] = y;

          let value2i18n = field2value2i18n[y.code!];
          value2i18n = value2i18n ? value2i18n : {};

          if (y.labels) {
            y.labels.forEach((lb: SysDicFieldI18nBo) => {
              value2i18n[lb.language!] = lb;
              value2value[lb.label!] = y;
            });
          }
          field2value2i18n[y.code!] = value2i18n;

          let value2prop = field2value2prop[y.code!];
          value2prop = value2prop ? value2prop : {};
          if (y.props) {
            y.props.forEach((lb: SysDicFieldPropBo) => {
              value2prop[lb.code!] = lb;
            });
          }
          field2value2prop[y.code!] = value2prop;
        });
      }

      _code2field2value[x.code!] = field2value;
      _code2value2value[x.code!] = value2value;
      _code2field2value2i18n[x.code!] = field2value2i18n;
      _code2field2value2prop[x.code!] = field2value2prop;
    });
    code2field2value = _code2field2value;
    code2value2value = _code2value2value;
    code2field2value2prop = _code2field2value2prop;
    code2field2value2i18n = _code2field2value2i18n;
  }
}

// 通过code获取指定数据字典数据
const getDic = (code: string): SysDicBo => {
  return fieldUtils.getFieldValue(code2obj, [code], {});
}

// 通过code获取指定数据字典数据项列表
const getFields = (code: string): Array<SysDicFieldBo> => {
  return fieldUtils.getFieldValue(code2obj, [code, 'fields'], []);
}

// 通过code与field字段获取指定数据字典数据项对象
const getField = (code: string, field: string): SysDicFieldBo => {
  return fieldUtils.getFieldValue(code2field2value, [code, field], {});
}

// 通过code与field字段获取指定数据字典数据项对象中的value值
const getFieldValue = (code: string, field: string, def: any = null): string => {
  def = def ? def : field;
  let fvObj = getField(code, field);
  if (!fvObj.label) {
    return def;
  }
  let i18n = getI18n();
  let label = fieldUtils.getFieldValue(code2field2value2i18n, [code, field, i18n, 'label'], undefined);
  if (label == undefined) {
    label = fieldUtils.getFieldValue(code2field2value, [code, field, 'label'], def);
  }
  return label;
}

// 通过code获取prop属性
const getFieldPropValue = (code: string, field: string, propCode: string, def: any = null): string => {
  def = def ? def : field;
  let fvObj = getField(code, field);
  if (!fvObj.label) {
    return def;
  }
  let prop: SysDicFieldPropBo | undefined = fieldUtils.getFieldValue(code2field2value2prop, [code, field, propCode], undefined);
  if (prop == undefined || prop == null) {
    return def;
  }
  return prop.value ?? def;
}


// 通过code字段获取指定数据字典针对Select中options结构的数据列表
const getOptions = (code: string, fields: Array<string> | undefined = undefined): Array<{ value: string, label: string }> => {
  fields = fields ? fields : [];
  let res = getFields(code);

  if (fields.length > 0) {
    return fields.map(x => ({ value: x, label: getFieldValue(code, x) }));
  } else {
    return res.map(x => ({ value: x.code!, label: getFieldValue(code, x.code!) }));
  }
}

/**
 * 通过code与现实value值，查询指定数据字典指定项对象
 * @param code code
 * @param value 值
 * @returns  指定数据字典指定项对象
 */
const getFieldByValue = (code: string, value: string) => {
  return fieldUtils.getFieldValue(code2value2value, [code, value], {});
}

export default {
  setI18n,
  getI18n,
  getData,
  getList,
  reload,
  getDic,
  getFields,
  getField,
  getFieldValue,
  getFieldPropValue,
  getOptions,
  getFieldByValue,
}

