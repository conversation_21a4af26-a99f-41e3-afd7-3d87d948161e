import Sorttable from "sortablejs";


type SorttableMoveEvent = {
  // 可能有多个
  type?: 'move' | string;
  // 移动对象
  dragged?: HTMLElement;
  // 目标
  related?: HTMLElement;
}

const buildOnMoveStop = (className: string) => {
  return (e: SorttableMoveEvent) => {
    if (e.type != "move") {
      return true;
    }
    if (!e.related || !e.related.classList) {
      return false;
    }
    return e.related.classList.contains(className);
  }
}

/**
 * 绑定拖拽排序
  tbody .sorthandle {
    cursor: grab;
  }

  el-table-column(width="35", label="拖动")
    template(#default="scope")
      .sorthandle
        i.el-icon-rank

  let el: any = this.$refs.filedstable;
  let tbody = el.querySelector(".el-table__body-wrapper tbody");
  sortableUtils.sortable(tbody, this, "reportQuery.columns", {
    handle: ".sorthandle",
  });
 * @param el 绑定拖拽对象
 * @param that 当前Vue
 * @param field 拖拽调整的字段
 * @param config 配平
 * @returns 退拽对象
 */
const sortable = (
  el: any,
  that: Vue,
  field: string | Array<string>,
  config: {
    handle?: string,
    getData?: () => Array<any>,
    onMove?: (e: SorttableMoveEvent) => boolean,
    end?: (data: Array<any>) => void,
    offset?: number,
  } | undefined = undefined
) => {
  config = config ? config : {};
  config.onMove = config.onMove ? config.onMove : () => true;
  if (typeof field == "string") {
    field = field.split(".");
  }
  return new Sorttable(el, {
    disabled: false,
    animation: 150,
    handle: config?.handle,
    onMove: config.onMove,
    onEnd: function (e: { newIndex: any; oldIndex: any }) {
      let offset: number = config!.offset == undefined || config!.offset == null ? 0 : config!.offset!;
      let { newIndex, oldIndex } = e;
      newIndex = newIndex - offset;
      oldIndex = oldIndex - offset;

      let lastFieldCode = field[field.length - 1];

      let data: Array<any> = <any>null;
      if (config!.getData) {
        data = config!.getData();
      } else {
        let od: any = that;
        for (let i = 0; i < field.length - 1; i++) {
          od = od[field[i]];
        }
        data = <any>od[lastFieldCode];
      }
      // 交换
      let obj = data.splice(oldIndex, 1)[0];
      data?.splice(newIndex, 0, obj);

      // 结束处理
      if (config!.end) {
        config!.end(data);
      } else {
        let od: any = that;
        for (let i = 0; i < field.length - 1; i++) {
          od = od[field[i]];
        }
        od[lastFieldCode!] = [];
        that.$nextTick(() => {
          let od: any = that;
          for (let i = 0; i < field.length - 1; i++) {
            od = od[field[i]];
          }
          od[lastFieldCode!] = data;
        });
      }
    },
  });
}

export default { sortable, buildOnMoveStop }
