import sm from "sm-crypto";
import stringUtils from "./stringUtils";

const generateData = (): string => {
  const chars = "0123456789abcdef";
  let key = "";
  for (let i = 0; i < 32; i++) {
    key += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return key;
}
const sm4GenerateKey = (): string => {
  const key = generateData();
  return stringUtils.hexToBase64(key);
}
const sm4GenerateIv = (): string => {
  return generateData();
}

const sm4Encrypt = (content: string, keyBase64: string): string => {
  const iv = sm4GenerateIv();
  const key = stringUtils.base64ToHex(keyBase64);
  const cipherHex = sm.sm4.encrypt(content, key, {
    padding: "pkcs#7",
    mode: "cbc",
    iv,
  });
  const ivBase64 = stringUtils.hexToBase64(iv);
  const cipherBase64 = stringUtils.hexToBase64(cipherHex);
  return ivBase64 + ":" + cipherBase64;
}

const sm4Decrypt = (content: string, keyBase64: string): string => {
  if (!content) {
    return content;
  }
  const key = stringUtils.base64ToHex(keyBase64);
  const [ivBase64, cipherBase64] = content.split(":");
  const cipherHex = stringUtils.base64ToHex(cipherBase64);
  const iv = stringUtils.base64ToHex(ivBase64);
  return sm.sm4.decrypt(cipherHex, key, {
    padding: "pkcs#7",
    mode: "cbc",
    iv,
  });
}

const sm3Hash = (content: string): string => {
  return sm.sm3(content);
}

type KeyPair = {
  keyBase64?: string;
  publicKeyBase64?: string | null;
}
const sm2GenerateKey = (): KeyPair => {
  const key = sm.sm2.generateKeyPairHex();
  return {
    publicKeyBase64: stringUtils.hexToBase64(key.publicKey),
    keyBase64: stringUtils.hexToBase64(key.privateKey),
  };
}


const sm2Encode = (content: string, pubKeyBase64: string): string => {
  const pubKey = stringUtils.base64ToHex(pubKeyBase64);
  const cipherHex = sm.sm2.doEncrypt(content, pubKey, 1);
  const cipherBase64 = stringUtils.hexToBase64("04" + cipherHex); // 04为约定的头信息
  return cipherBase64;
}

const sm2Decode = (content: string, priKeyBase64: string): string => {
  const priKey = stringUtils.base64ToHex(priKeyBase64);
  let hexTxt = stringUtils.base64ToHex(content);
  if (!hexTxt.startsWith("04")) {
    throw new Error("请使用04开头的密文");
  }
  hexTxt = hexTxt.substring(2);
  return sm.sm2.doDecrypt(hexTxt, priKey, 1);
}

const sm2Sign = (content: string, userId: string, priKeyBase64: string): string => {
  const priKey = stringUtils.base64ToHex(priKeyBase64);
  const cc = sm.sm2.doSignature(content, priKey, {
    hash: true,
    userId,
    der: true,
  });
  return stringUtils.hexToBase64(cc);
}

const sm2Verify = (content: string, value: string, userId: string, pubKeyBase64: string): boolean => {
  const v = stringUtils.base64ToHex(value);
  const pubKey = stringUtils.base64ToHex(pubKeyBase64);
  return sm.sm2.doVerifySignature(content, v, pubKey, {
    hash: true,
    userId,
    der: true,
  });
}



export default { sm4GenerateKey, sm4Encrypt, sm4Decrypt, sm3Hash, sm2GenerateKey, sm2Encode, sm2Decode, sm2Sign, sm2Verify };

