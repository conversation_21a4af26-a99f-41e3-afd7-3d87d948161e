// 示例操作
// fileexportUtils.buildFileExport(
//   {
//     submitCb: () => reportExecQueryApi.submit(val.code),
//     progressCb: reportExecQueryApi.progress,
//   },
//   {},
//   (pg) => {}
// );

import reportExecQueryApi, { TaskProgressBo } from '@/api/sys/report/exec/queryApi'
import urlUtils from "~/utils/urlUtils";

export class FileExportError extends Error {
  result: TaskProgressBo | null = null;
}

export class FileExport {
  code?: string | {
    submitCb?: () => Promise<TaskProgressBo>,
    progressCb?: (id: string) => Promise<TaskProgressBo>
  };
  body?: any;
  prograss?: (pg: TaskProgressBo) => void;
  id?: string;

  result?: TaskProgressBo | null = null;

  submited: boolean = false;
  finished: boolean = false;
  stoped: boolean = false;

  finishCbs: Array<any> = [];

  async submit(): Promise<TaskProgressBo | undefined | null> {
    if (this.submited) {
      return this.result;
    }
    this.submited = true;
    let res: TaskProgressBo = <any>{};
    if (typeof (this.code) != 'string') {
      res = await this.code!.submitCb!();
    } else {
      res = await reportExecQueryApi.submit(this.code!);
    }
    this.result = res;
    this.id = res.id;
    this.__requestPg();
    return res;
  }

  __requestPg() {
    let that = this;
    setTimeout(() => {
      if (that.stoped || that.finished) {
        return;
      }
      (async () => {
        that.__requestPg();
        let pg: TaskProgressBo = <any>{};
        if (typeof (this.code) != 'string') {
          pg = await this.code!.progressCb!(that.id!);
        } else {
          pg = await reportExecQueryApi.progress(that.id!);
        }
        that.result = pg;
        try {
          that.prograss ? that.prograss(that.result) : null;
        } catch (error) {
          console.error('执行进度出现异常', error);
        }
        if (that.result.finished) {
          that.finished = true;
          for (let finishCb of that.finishCbs) {
            try {
              finishCb ? finishCb(that.result) : null;
            } catch (error) {
              console.error('执行完成进度出现异常', error);
            }
          }
          return;
        }
      })();
    }, 1000);
  }

  stop() {
    this.stoped = true;
  }

  getPrograss(): TaskProgressBo | null | undefined {
    return this.result;
  }

  async submitAndWait(): Promise<TaskProgressBo | undefined | null> {
    let that = this;
    return new Promise((r, j) => {
      (async () => {
        that.finishCbs.push(() => {
          let result = that.result;
          if (!result) {
            j(new Error('导出数据报告时，响应出现问题，数据：' + that.code + ', body: ' + JSON.stringify(that.body)));
            return;
          }
          if (result.success) {
            r(result);
            return;
          } else {
            let err = new FileExportError('导出异常');
            err.result = result;
            j(err);
          }
        });
        await that.submit();
      })();
    });
  }

  async submitAndGetUrl(): Promise<string> {
    let res = await this.submitAndWait();
    let resDoc = JSON.parse(res?.data!);
    let url = urlUtils.file2url(resDoc.id);
    return url!;
  }

}

const buildFileExport = (code: string | {
  submitCb?: () => Promise<TaskProgressBo>,
  progressCb?: (id: string) => Promise<TaskProgressBo>
}, body: any, prograss: (pg: TaskProgressBo | undefined | null) => void): FileExport => {
  let fe = new FileExport();
  fe.code = code;
  fe.body = body;
  fe.prograss = prograss;
  return fe;
}


export default { buildFileExport }
