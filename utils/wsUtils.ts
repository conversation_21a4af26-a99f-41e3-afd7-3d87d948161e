import { BadRequestException, ForbiddenException, NotFoundException, ServerException, UnauthorizedException } from "./request";

export interface WsMsgRequest {
  /**
   * 消息id，id为空表示汇报消息
   */
  id?: string;
  /**
   * type类型, _开头为系统消息
   */
  t?: string;
  /**
   * data: 消息体
   */
  d?: string;
}

const WsTypeSys = {
  TYPE_LOGIN: "USER_LOGIN",
  TYPE_JOIN: "_JOIN",
  TYPE_PUTSH: "_P",
}

export interface WsMsgResponse {
  /**
   * id: 消息编号，存在id则为响应内容，不存在则为推送内容
   */
  id?: string;

  /**
   * 系统类型，
   */
  t?: string;

  /**
   * code: 信息编号，与HTTPCode一直
   */
  c?: number;

  /**
   * data: 业务数据
   */
  d?: string;
}

class TimeoutException extends ServerException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}

class MsgSendException extends ServerException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}


type MsgCb = {
  r: (res: any) => void;
  j: (err: any) => void;
  rid: string;
  end: boolean;
  timer: any;
}

type MsgSendConfig = {
  // 不考虑是否连接建立是否完成，强制发送
  must: boolean,
  // 立刻马上发送，不写入等待队列
  instantly: boolean,
}

const excpDeal = (body: WsMsgResponse) => {
  const status = body.c;
  if (status && status < 400) {
    try {
      return body.d ? JSON.parse(body.d) : body.d;
    } catch (error) {
      return body.d;
    }
  }
  let data: any = body.d;
  if (data) {
    try {
      data = JSON.parse(data);
    } catch (error) {
    }
  }
  if (status != null && data && data.errorCode && 'YHERT_ERROR_TAG' === data.errorCode) {
    // 错误响应
    if (status >= 500) {
      throw new ServerException(data.code, data.message, data.data);
    }
    if (status == 401) {
      throw new UnauthorizedException(data.code, data.message, data.data);
    }
    if (status == 403) {
      throw new ForbiddenException(data.code, data.message, data.data);
    }
    if (status == 404) {
      throw new NotFoundException(data.code, data.message, data.data);
    }
    throw new BadRequestException(data.code, data.message, data.data);
  } else {
    throw new Error(JSON.stringify(body));
  }
}

export class Ws {
  requestMsgs = new Map<string, MsgCb>();
  sendQueue: Array<string> = [];

  url = '';
  sendByte = false;
  ws: WebSocket | null = null;
  ticketCb: null | (() => Promise<string | null>) = null;
  running = true;
  opened = false;
  reqTimeout = 3000;
  lastErrorTime = 0;
  msgCount = 0;
  connectedCb: null | (() => void) = null;
  disconnectCb: null | (() => void) = null;
  msgCallback: (event: MessageEvent<any>) => void;
  constructor(url: string, ticketCb: () => Promise<string | null>, msgCallback: (event: MessageEvent<any>) => void) {
    this.url = url;
    this.ticketCb = ticketCb;
    this.msgCallback = msgCallback;
  }

  open() {
    console.log('发起webSocket请求，连接WS服务器。[' + this.url + ']');
    this._open();
    //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
      close();
    }
  }
  _runHeart() {
    let that = this;
    setTimeout(() => {
      if (that.running && that.ws) {
        try {
          that.ws?.send("{}");
        } catch (error) {
        }
        that._runHeart();
      }
    }, 29000);
  }
  _connectedCb() {
    let that = this;
    this.opened = true;
    if (that.connectedCb) {
      that.connectedCb();
    }
    that.__openSend();
    that._runHeart();
  }
  __openSend() {
    if (this.sendQueue.length > 0) {
      for (let body of this.sendQueue) {
        try {
          this.__ws_send(body);
        } catch (error) {
          console.error('发送消息失败', error);
        }
      }
      this.sendQueue = [];
    }
  }
  async _open() {
    if (this.ws) {
      return;
    }
    if (!this.url) {
      return;
    }
    if (!this.ticketCb) {
      return;
    }
    const ticket = await this.ticketCb();
    if (!ticket) {
      return;
    }
    try {
      const url = this.url + '?ticket=' + ticket;
      this.ws = new WebSocket(url);
    } catch (error) {
      console.error('服务器连接出错', error);
      return;
    }
    const that = this;
    this.ws.onopen = (event) => {
      if (that.ws) {
        that.lastErrorTime = 0;
        that.ws?.send("{}");
      }
    }
    let errorCallback = () => {
      if (that.running) {
        that.opened = false;
        if (that.ws) {
          if (!that.ws.CLOSED) {
            that.ws.close();
          }
          that.ws = null;
          let time = new Date().getTime();
          if (that.lastErrorTime <= 0) {
            // 初次报错
            if (that.disconnectCb) {
              that.disconnectCb();
            }
            that.lastErrorTime = time;
            that._open();
          } else {
            // 连接出错， 延时重试
            let timeout = 0;
            if (time - that.lastErrorTime >= 1000 * 60) {
              timeout = 1000 * 60;
            } else {
              timeout = 1000 * 10;
            }
            setTimeout(() => {
              that._open();
            }, timeout);
          }
        }
      }
    }
    this.ws.onerror = function (event) {
      console.log('webSocket连接服务器出错。[' + this.url + ']');
      errorCallback();
    }
    this.ws.onclose = function (event) {
      console.log('webSocket连接服务器已断开。[' + this.url + ']');
      errorCallback();
    }
    this.ws.onmessage = (event) => {
      if (!that.opened) {
        console.info('ws连接成功。');
        that._connectedCb();
      }
      // console.log('webSocket连接服务器成功。[' + this.url + ']');
      if (event && event.data) {
        let edata = event.data;
        (async () => {
          let json: string = '';
          if (typeof (edata) == 'string') {
            json = edata;
          } else if (edata instanceof ArrayBuffer) {
            json = this.__ws_recive(edata);
          } else if (edata instanceof Blob) {
            let arrayBuffer = await edata.arrayBuffer();
            json = this.__ws_recive(arrayBuffer);
          } else {
            json = edata;
          }
          if (!json) {
            return;
          }

          // 消息处理
          let data: WsMsgResponse = JSON.parse(json);
          let id = data.id;
          if (id) {
            console.log("收到消息：" + json);
            let msg = this.requestMsgs.get(id);
            if (msg) {
              that.__clearMsg(msg);
              try {
                let r = excpDeal(data);
                msg.r(r);
              } catch (error) {
                msg.j(error);
              }
            }
          } else {
            if (WsTypeSys.TYPE_PUTSH == data.t && data.d) {
              console.log("收到消息：" + json);
              let msg = JSON.parse(data.d);
              this.msgCallback(msg);
            }
          }
        })();
      }
    }
    return this.ws;
  }

  __clearMsg(msg: MsgCb) {
    if (msg) {
      this.requestMsgs.delete(msg.rid);
      msg.end = true;
      clearTimeout(msg.timer);
    }
  }

  __send(body: string, cf: MsgSendConfig, rmsg: MsgCb | null = null) {
    try {
      if (this.opened || cf.must) {
        // 系统正常连接中，或者要求必须强制发送
        this.__ws_send(body);
        return;
      }
      // 连接建立未完成
      if (cf.instantly) {
        // 要求马上发送
        throw new MsgSendException('发送消息异常。');
      } else {
        this.sendQueue.push(body);
      }
    } catch (error) {
      console.error('发送数据异常。', error);
      if (rmsg) {
        if (error instanceof MsgSendException) {
          rmsg.j(error);
        } else {
          rmsg.j(new MsgSendException('发送消息异常。'));
        }
      }
    }
  }

  __ws_recive(arrayBuffer: ArrayBuffer): string {
    const uint8Array = new Uint8Array(arrayBuffer);
    // 截掉第一个字符
    const contentArray = uint8Array.slice(1);
    // 将 Uint8Array 转换为字符串
    const decoder = new TextDecoder();
    return decoder.decode(contentArray);
  }

  __ws_send(body: string) {
    if (this.sendByte) {
      const str = body;
      const encoder = new TextEncoder();
      const uint8Array = encoder.encode(str); // Uint8Array 是 ArrayBufferView 的一种
      const bs = new Uint8Array(1 + uint8Array.length);
      bs[0] = 0x00;
      bs.set(uint8Array, 1);
      this.ws?.send(bs);
    } else {
      this.ws?.send(body);
    }
  }

  send(type: string, data: string, cf: MsgSendConfig | undefined = undefined): Promise<any> {
    cf = cf ? cf : { must: false, instantly: false };
    let rid = '' + (++this.msgCount);
    let body: WsMsgRequest = {
      id: rid,
      t: type,
      d: data,
    }
    let that = this;
    return new Promise((r, j) => {
      let rmsg: MsgCb = { r, j, rid, end: false, timer: null };
      that.requestMsgs.set(rid, rmsg);
      rmsg.timer = setTimeout(() => {
        let end = rmsg.end;
        that.__clearMsg(rmsg);
        if (!end) {
          j(new TimeoutException("timeout"));
        }
      }, that.reqTimeout)

      try {
        that.__send(JSON.stringify(body), cf!);
      } catch (error) {
        console.error('发送ws信息异常数据', error);
        that.requestMsgs.delete(rid);
        j(new Error('发送数据失败'));
      }
    });
  }
  push(type: string, data: string, cf: MsgSendConfig | undefined = undefined): Promise<any> {
    cf = cf ? cf : { must: false, instantly: false };
    let that = this;
    let body: WsMsgRequest = {
      t: type,
      d: data,
    }
    return new Promise((r, j) => {
      that.__send(JSON.stringify(body), cf!);
    });
  }
  waitOpen() {
    return new Promise((r, j) => {
      if (!this.ws) {
        j(new Error('未发起连接'));
      }
      if (!this.running) {
        j(new Error('已关闭连接'));
      }

    });
  }
  close() {
    console.log('断开webSocket连接。[' + this.url + ']');
    this.running = false;
    if (this.ws) {
      this.ws.close();
    }
  }
}

export default { WsTypeSys }
