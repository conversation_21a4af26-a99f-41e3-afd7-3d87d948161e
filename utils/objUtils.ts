function deepClone<T>(obj: T, map = new WeakMap()): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 如果对象已经被复制过，直接返回之前的副本
  if (map.has(<any>obj)) {
    return map.get(<any>obj);
  }

  // Handle Date
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  // Handle Array
  if (Array.isArray(obj)) {
    const clonedArray = obj.map(item => deepClone(item, map)) as unknown as T;
    map.set(obj, clonedArray);
    return clonedArray;
  }

  // Handle Object
  const clonedObj: { [key: string]: any } = {};
  map.set(<any>obj, clonedObj);

  for (const key in obj) {
    if ((<any>obj).hasOwnProperty(key)) {
      clonedObj[key] = deepClone((obj as { [key: string]: any })[key], map);
    }
  }

  return clonedObj as T;
}

export default { deepClone }
