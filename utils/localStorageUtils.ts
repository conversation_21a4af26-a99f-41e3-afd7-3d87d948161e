

let SUFFIX = "_____timeout"
export default {
  //设置数据
  set: function (cname: string, cvalue: string, expries: number | undefined = -1) {
    // debugger
    localStorage.setItem(cname, cvalue);
    if (expries && expries >= 0) {
      if (expries == 0) {
        this.clear(cname);
      } else {
        localStorage.setItem(cname + SUFFIX, '' + (new Date().getTime() + expries));
      }
    }
  },

  //获取cookie
  get: function (cname: string) {
    // debugger
    let value = localStorage.getItem(cname);
    if (!value) {
      return value;
    }
    let exp = localStorage.getItem(cname + SUFFIX);
    if (exp) {
      if (new Date().getTime() < +exp) {
        return value;
      } else {
        this.clear(cname);
        return null;
      }
    } else {
      return value;
    }
  },

  //清除cookie
  clear: function (cname: string) {
    localStorage.removeItem(cname + SUFFIX);
    localStorage.removeItem(cname);
  }
}
