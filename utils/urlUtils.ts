import { config } from "@/config/global.config";

/**
 * 将url和参数拼接成完整地址
 * @param {string} url url地址
 * @param {Json} data json对象
 * @returns {string}
 */
const getUrl = (url: string, data: any = null) => {
  //看原始url地址中开头是否带?，然后拼接处理好的参数
  if (data) {
    let paramStr = getParam(data);
    if (paramStr) {
      url = url += (url.indexOf('?') < 0 ? '?' : '') + paramStr;
    }
  }
  return url;
}

/**
 * 解析出路径信息
 * @param url URL地址
 * @returns uri
 */
const getUri = (url: string): string => {
  if (!url) {
    return url;
  }
  let urls = url.split('?');
  return urls[0];
}

/**
 * 传入对象返回url参数
 * @param {Object} data {a:1}
 * @returns {string}
 */
const getParam = (data: any) => {
  let url = '';
  for (var k in data) {
    let value = data[k] !== undefined && data[k] !== null ? data[k] : '';
    // url += `&${k}=${encodeURIComponent(value)}`
    if (value instanceof Array) {
      for (let j in value) {
        let val = value[j];
        url += `&${k}=${encodeURIComponent(val)}`
      }
    } else {
      url += `&${k}=${encodeURIComponent(value)}`
    }
  }
  return url ? url.substring(1) : ''
}

const url2query = (url: string): any => {
  if (!url) {
    return {};
  }
  let urls = url.split('?');
  if (urls.length <= 1) {
    return {};
  }
  let param = urls[1];
  let params = param.split('&');
  let r: any = {};
  for (let key in params) {
    let p = params[key];
    if (p) {
      let ps = p.split('=');
      let k = ps[0];
      let v = ps.length > 1 ? ps[1] : '';
      v = decodeURIComponent(v);
      let oldVal = r[k];
      if (oldVal) {
        if (oldVal instanceof Array) {
          oldVal.push(v);
        } else {
          r[k] = [oldVal, v];
        }
      } else {
        r[k] = v;
      }
    }
  }
  return r;
}

/**
 * 替换url参数
 * this.$router.replace(urlUtils.putParam(this.$route.fullPath, { type: "a" }));
 * @param url 原始url内容
 * @param param 参数内容
 * @returns 响应url
 */
const putParam = (url: string, param: any) => {
  if (!param) {
    return url;
  }
  let data = url2query(url);
  data = { ...data, ...param };
  for (let key in data) {
    let v = data[key];
    if (v == null || v == undefined || v == '') {
      delete data[key];
    }
  }
  let uri = getUri(url);
  let nurl = getUrl(uri, data);
  return nurl;
}

export type FileParam = {
  /**
   * 尺寸
   * 1-8
   */
  s?: number | null,
  /**
   * 类型
   */
  t?: string | null,
  /**
   * uid
   */
  uid?: string | null,
  /**
   * token
   */
  token?: string | null,
  /**
   * 票据
   */
  ticket?: string | null,
  /**
   * 去除水印
   */
  nwt?: boolean | null,
}

const file2array = (id: string, param: FileParam = {}): Array<string> => {
  if (!id) {
    return [];
  }
  let ids = id.split(',');
  let urls: Array<string> = [];
  for (let idx in ids) {
    let x = ids[idx];
    if (!x) {
      continue;
    }
    if (x.startsWith('http://') || x.startsWith('https://')) {
      urls.push(x);
    } else {
      let url = '';
      if (config?.fs?.downtype == 'server') {
        url = config.baseUrl.fileApiUrl ? `${config.baseUrl.fileApiUrl}/${x}` : `${config.baseUrl.apiUrl}/sys/fs/${x}`;
        let up: any = {};
        if (param.s) {
          up.s = param.s;
        }
        if (param.t) {
          up.t = param.t;
        }
        if (param.uid) {
          up.uuid = param.uid;
        }
        if (param.token) {
          up._access_token = param.token;
        }
        if (param.ticket) {
          up._ticket = param.ticket;
        }
        if (param.nwt != null && param.nwt != undefined) {
          up.nwt = param.nwt;
        }
        url = getUrl(url, up);
      } else {
        if (config.baseUrl.fileApiUrl) {
          // 存在独立域名
          let path = '';
          path = path + x.substring(0, 1) + '/';
          path = path + x.substring(1, 4) + '/';
          path = path + x;
          url = `${config.baseUrl.fileApiUrl}/${path}`;
          if (param.s) {
            if (config.fs && config.fs.downtype) {
              if (config.fs.downtype == 'alioss') {
                // 阿里云oss系统
                let sizeIdx = [50, 100, 150, 200, 400, 800, 1200, 1920];
                if (param.s > 0 && param.s <= sizeIdx.length) {
                  let size = sizeIdx[param.s - 1];
                  let ossParam = `image/resize,m_lfit,w_${size},h_${size}`;
                  url = url + `?x-oss-process=${ossParam}`;
                }
              } else {
                url = url + `?s=${param.s}`;
              }
            } else {
              url = url + `?s=${param.s}`;
            }
          }
        } else {
          url = `${config.baseUrl.apiUrl}/sys/fs/${x}`;
          let up: any = {};
          if (param.s) {
            up.s = param.s;
          }
          if (param.t) {
            up.t = param.t;
          }
          if (param.uid) {
            up.uuid = param.uid;
          }
          if (param.token) {
            up.access_token = param.token;
          }
          url = getUrl(url, up);
        }
      }
      urls.push(url);
    }
  }
  return urls;
}

const file2url = (id: string, param: FileParam = {}): string | null => {
  let urls = file2array(id, param);
  if (urls.length <= 0) {
    return null;
  } else {
    return urls[0];
  }
}

const buildQrcodeUrl = (content: string) => {
  let url = getUrl('/sys/tools/qrcode', { content })
  return `${config.baseUrl.apiUrl}${url}`;
}

export default { getUrl, getUri, getParam, putParam, url2query, file2array, file2url, buildQrcodeUrl }
