type RemoveUndefined<T> = {
  [K in keyof T]-?: Exclude<T[K], undefined>;
};
type KeysWithValueTypePrivate<T, VT> = {
  [K in keyof T]: T[K] extends VT ? K : never;
}[keyof T];
type KeysWithValueType<T, VT> = KeysWithValueTypePrivate<RemoveUndefined<T>, VT> extends never ? keyof T : KeysWithValueTypePrivate<RemoveUndefined<T>, VT>

/**
 * 拼接字符串
 * @param ls 数组对象
 * @param split 分隔符
 * @returns 结果
 */
const join = (ls: Array<any>, split?: string): string | null => {
  if (!ls) {
    return null;
  }
  let out = '';
  for (const key in ls) {
    if (+key > 0) {
      out += split;
    }
    out += ls[key];
  }
  return out;
}

export type Array2Tree<T, K extends string> = {
  // [K1: string]: any;
  [K2 in K]?: Array<Array2Tree<T, K>>;
};

/**
 * 将数组装换为树形结构
 * @param ls 数组数据
 * @param key 数组组件
 * @param parentField 指向父一级的字段
 * @param childrenField 结果保存到的字段
 * @returns 数组结构
 */
const array2tree = <T, K extends string, R extends T & Array2Tree<T, K>>(arr: Array<T>, key: keyof T, parentField: keyof T, childrenField: K): Array<R> => {
  let root: Array<R> = []
  let key2obj: any = {};
  arr.forEach(x => key2obj[x[key]] = x);
  for (let obj of arr) {
    let pobj: any = key2obj[obj[parentField]];
    if (pobj) {
      pobj[childrenField] = pobj[childrenField] ? pobj[childrenField] : [];
      pobj[childrenField].push(obj);
    } else {
      root.push(<any>obj);
    }
  }
  return root;
}

/**
 * 将树形结构拍平为数组
 * @param tree 数组
 * @param childrenField 子节点字段
 * @returns 结果
 */
const tree2array = <T, K extends keyof T>(tree: Array<T> | T, childrenField: K): Array<T> => {
  const root: Array<T> = [];
  const visited = new Set<T>();

  const _get = (t: Array<T> | T | null | undefined) => {
    if (!t || visited.has(t as T)) {
      return;
    }
    if (Array.isArray(t)) {
      for (const item of t) {
        _get(item);
      }
    } else {
      visited.add(t);
      root.push(t);
      const children: any = t[childrenField];
      _get(children);
    }
  }

  _get(tree);
  return root;
}

const __marginArrayMap = <T1, T2>(a1: T1, a2: T2): T1 | T2 => ({ ...a1, ...a2 });

function marginArray<T1, T1K extends keyof T1 & KeysWithValueType<T1, T2[T2K]>, T2, T2K extends keyof T2 & KeysWithValueType<T2, T1[T1K]>>(
  arr1: Array<T1>,
  arr1key: T1K,
  arr2: Array<T2>,
  arr2key: T2K
): Array<T1 & T2>;

function marginArray<T1, T1K extends keyof T1 & KeysWithValueType<T1, T2[T2K]>, T2, T2K extends keyof T2 & KeysWithValueType<T2, T1[T1K]>, R>(arr1: T1[], arr1key: T1K, arr2: T2[], arr2key: T2K, map: (a1: T1, a2: T2 | undefined) => R): R[];

/**
 * 合并参数
 * @param arr1 数组1 
 * @param arr1key 数组1key
 * @param arr2 数组2
 * @param arr2key 数组2key
 * @param map 联合函数
 * @returns 结果
 */
function marginArray<T1, T1K extends keyof T1 & KeysWithValueType<T1, T2[T2K]>, T2, T2K extends keyof T2 & KeysWithValueType<T2, T1[T1K]>, R>(arr1: T1[], arr1key: T1K, arr2: T2[], arr2key: T2K, map?: (a1: T1, a2: T2 | undefined) => R): R[] {
  let _map = map ? map : __marginArrayMap;
  let arr2key2arr: Map<any, T2> = new Map();
  arr2.forEach(x => arr2key2arr.set(x[arr2key], x))
  let result: Array<any> = []
  for (let i = 0; i < arr1.length; i++) {
    let a1 = arr1[i];
    let a2 = arr2key2arr.get(a1[arr1key]);
    let r = _map(a1, a2)
    result.push(r);
  }
  return <any>result;
}

/**
 * 从Map深度搜索
 * @param map map映射关系
 * @param parentKey 指向父节点的字段
 * @param val 代搜索的值
 * @returns 搜索到的数据
 */
function deepFindByMap<T, TK extends keyof T, V extends T[TK]>(map: Map<T[TK], T>, parentKey: TK, val: V): T[] {
  if (!val) {
    return [];
  }
  if (!map) {
    return [];
  }
  let vals: T[] = []
  while (val) {
    let v = map.get(val);
    if (!v) {
      break;
    }
    vals.push(v);
    val = <any>v[parentKey];
  }
  return vals;
}

/**
 * 数组映射为Map
 * @param arr 数组
 * @param key 键
 * @returns Map关系
 */
function array2map<T, TK extends keyof T, V extends Exclude<T[TK], undefined>>(arr: T[], key: TK): Map<V, T> {
  let map: Map<V, T> = new Map();
  arr.forEach(x => {
    let v = x[key];
    if (v != undefined) {
      map.set(<any>v, x);
    }
  });
  return map;
}

/**
 * 从数组深度搜索
 * @param arr 数组数据
 * @param key 对应获取字段
 * @param parentKey 指向父节点的字段
 * @param val 代搜索的值
 * @returns 搜索到的数据
 */
function deepFindByArray<T, TK1 extends keyof T & KeysWithValueType<T, T[TK2]>, TK2 extends keyof T & KeysWithValueType<T, T[TK1]>>(arr: T[], key: TK1, parentKey: TK2, val: T[TK2]): T[] {
  let map = array2map(arr, key);
  return deepFindByMap(<any>map, parentKey, val);
}


function groupBy<T, TK extends keyof T>(arr: T[], key: TK): Map<T[TK], T[]>;
function groupBy<T, TK extends keyof T, R extends T[TK]>(arr: T[], key: (v: T) => R): Map<R, T[]>;

/**
 * 将数组合并为Map数组
 * @param arr 数组数据
 * @param key 对应字段
 * @returns Map结构
 */
function groupBy<T, TK extends keyof T>(arr: T[], key: (v: T) => T[TK] | TK): Map<T[TK], T[]> {
  let key2list: Map<T[TK], T[]> = new Map<T[TK], T[]>();
  arr.forEach(x => {
    let v: T[TK] = typeof key == 'function' ? key(x) : (<any>x)[(<any>key)];
    let list = key2list.get(v);
    if (!list) {
      list = [];
      key2list.set(v, list);
    }
    list.push(x);
  });
  return key2list;
}

export default { join, array2tree, tree2array, marginArray, array2map, deepFindByMap, deepFindByArray, groupBy }
