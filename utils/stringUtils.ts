

type CamelToSnake<S extends string> =
  S extends `${infer T}${infer U}` ?
  T extends Capitalize<T> ?
  `_${Lowercase<T>}${CamelToSnake<U>}` :
  `${T}${CamelToSnake<U>}` : S;


export type SnakeCaseObject<T> = {
  [K in keyof T as CamelToSnake<K & string>]: T[K] extends object ? SnakeCaseObject<T[K]> : T[K];
};

/**
 * 驼峰转换下划线
 * @param value 数据
 * @returns 值
 */
const toSnake = (value: string) => value.replace(/([A-Z])/g, "_$1").toLowerCase()

/**
 * 下划线转换驼峰
 * @param value 数据
 * @returns 值
 */
const toCamel = (value: string) => value.replace(/\_(\w)/g, (all, letter) => letter.toUpperCase())

/**
 * 参数对象中的每个属性进行驼峰转下划线命名法
 * @param obj 待处理的对象
 * @returns 处理后对象
 */
const objectKeyToSnake = <T>(obj: T | null | undefined): SnakeCaseObject<T> | null | undefined => {
  if (obj == null || obj == undefined) {
    return <any>obj;
  }
  let res: any = {};
  for (let key in obj) res[toSnake(key)] = obj[key];
  return res;
}


type SnakeToCamel<S extends string> =
  S extends `${infer T}_${infer U}` ?
  `${T}${Capitalize<SnakeToCamel<U>>}` :
  S;

export type CamelCaseObject<T> = {
  [K in keyof T as SnakeToCamel<K & string>]: T[K] extends object ? CamelCaseObject<T[K]> : T[K];
};

/**
 * 参数对象中的每个属性进行驼峰转下划线命名法
 * @param obj 待处理的对象
 * @returns 处理后对象
 */
const objectKeyToCamel = <T>(obj: T | null | undefined): CamelCaseObject<T> | null | undefined => {
  if (obj == null || obj == undefined) {
    return <any>obj;
  }
  let res: any = {};
  for (let key in obj) res[toSnake(key)] = obj[key];
  return res;
}

/**
 * 接取某个字符之前的字符
 * @param str 字符
 * @param a 查找字符
 * @returns 接取
 */
const subBefore = (str: string, a: string) => {
  if (!str) {
    return str;
  }
  let idx = str.indexOf(a);
  if (idx < 0) {
    return str;
  } else {
    return str.substring(0, idx);
  }
};

// base64 解码函数（仅限英文字符 base64）
const atobPolyfill = (input: string): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  let str = input.replace(/=+$/, '');
  let output = '';

  for (let bc = 0, bs = 0, buffer: any, i = 0; (buffer = str.charAt(i++));) {
    buffer = chars.indexOf(buffer);
    if (buffer === -1) continue;
    bs = (bs << 6) | buffer;
    bc += 6;
    if (bc >= 8) {
      bc -= 8;
      output += String.fromCharCode((bs >> bc) & 0xff);
    }
  }

  return output;
}


/**
 * 将 Base64 编码字符串转换为十六进制字符串
 * @param base64 编码后字符串
 * @returns 内容
 */
const base64ToHex = (base64: string): string => {
  const binary = typeof atob !== 'undefined' ? atob(base64) : atobPolyfill(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return Array.from(bytes)
    .map(byte => byte.toString(16).padStart(2, '0'))
    .join('');
};


// 手动实现的 btoa Polyfill（仅支持英文和字节数据）
const btoaPolyfill = (binary: string): string => {
  const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let base64 = '';
  let i = 0;

  while (i < binary.length) {
    const byte1 = binary.charCodeAt(i++) & 0xff;
    const byte2 = i < binary.length ? binary.charCodeAt(i++) & 0xff : NaN;
    const byte3 = i < binary.length ? binary.charCodeAt(i++) & 0xff : NaN;

    const triplet = (byte1 << 16) | ((byte2 || 0) << 8) | (byte3 || 0);

    base64 += base64Chars[(triplet >> 18) & 0x3f];
    base64 += base64Chars[(triplet >> 12) & 0x3f];
    base64 += isNaN(byte2) ? '=' : base64Chars[(triplet >> 6) & 0x3f];
    base64 += isNaN(byte3) ? '=' : base64Chars[triplet & 0x3f];
  }

  return base64;
}

/**
 * 将十六进制字符串转换为 Base64 编码
 * @param hex 十六进制字符串
 * @returns Base64 编码
 */
const hexToBase64 = (hex: string): string => {
  if (hex.length % 2 !== 0) {
    throw new Error('Invalid hex string');
  }
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.slice(i, i + 2), 16);
  }
  let binary = '';
  for (let i = 0; i < bytes.length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return (typeof btoa !== 'undefined') ? btoa(binary) : btoaPolyfill(binary);
};

export default { toSnake, toCamel, objectKeyToSnake, objectKeyToCamel, subBefore, base64ToHex, hexToBase64 }
