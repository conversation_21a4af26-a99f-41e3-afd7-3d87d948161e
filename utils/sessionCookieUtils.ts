
import { config } from '~/config/global.config'

let SUFFIX = "_____timeout"

type CookieClearOption = {
  path?: string | null;
}

type CookieSetOption = CookieClearOption & {
  sameSite?: 'Strict' | 'Lax' | 'None';
  domain?: string;
};

const CookieUtils = {
  //设置cookie
  set: (cname: string, cvalue: any, expries: number | undefined = -1, option: CookieSetOption = {}) => {
    CookieUtils._set(cname, cvalue, undefined, option);
    if (expries && expries >= 0) {
      if (expries == 0) {
        CookieUtils._clear(cname, option);
      } else {
        CookieUtils._set(cname + SUFFIX, new Date().getTime() + expries, undefined, option);
      }
    }
  },

  //设置cookie
  _set: (cname: string, cvalue: any, expries: number | undefined, option: CookieSetOption = {}) => {
    option = option ? option : {};

    let ck = cname + "=" + cvalue;
    if (expries != undefined) {
      var d = new Date();
      d.setTime(d.getTime() + expries);
      var expires = "expires=" + d.toUTCString();
      ck = ck + ";" + expires;
    }
    let path = option.path ? option.path : config.cookie.path;
    if (path) {
      ck = ck + ";path=" + path;
    }
    let sameSite = option.sameSite ? option.sameSite : 'Strict';
    if (sameSite) {
      ck = ck + ";SameSite=" + sameSite;
    }
    if (option.domain) {
      ck = ck + ";Domain=" + option.domain;
    }
    document.cookie = ck;
  },

  get: (cname: string) => {
    let value = CookieUtils._get(cname);
    if (!value) {
      return value;
    }
    let exp = CookieUtils._get(cname + SUFFIX);
    if (exp) {
      if (new Date().getTime() < +exp) {
        return value;
      } else {
        CookieUtils._clear(cname, {});
        return null;
      }
    } else {
      return value;
    }
  },

  //获取cookie
  _get: (cname: string) => {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
      var c = ca[i];
      // console.log(c)
      while (c.charAt(0) == ' ') c = c.substring(1);
      if (c.startsWith(name)) {
        return c.substring(name.length, c.length);
      }
    }
    return "";
  },

  //清除cookie
  clear: (cname: string, option: CookieClearOption = {}) => {
    option = option ? option : {};
    CookieUtils._set(cname + SUFFIX, "", -1, option);
    CookieUtils._set(cname, "", -1, option);
  },

  //清除cookie
  _clear: (cname: string, option: CookieClearOption = {}) => {
    CookieUtils._set(cname, "", -1, option);
  }
};

export default CookieUtils;

