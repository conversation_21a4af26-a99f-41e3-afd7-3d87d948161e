
function templatedeal(content: string, param: object): string;
function templatedeal(content: string, callback: (key: string) => string): string;

/**
   * 替换内容，带回调方式
   *
   * @param content 模板内容
   * @param callback 回调函数，用于提供替换值
   * @returns 替换后的结果字符串
   */
function templatedeal(content: string, callback: object | ((key: string) => string)): string {
  if (!content || typeof content !== "string") {
    return content;
  }
  const getByKey = (key: string): any => {
    if (typeof callback === 'function') {
      return (<Function>callback)(key);
    } else {
      let ks = key.split('.');
      let v: any = callback;
      for (let k of ks) {
        if (!k) {
          continue;
        }
        if (v === null || v === undefined) {
          return '';
        }
        if (v instanceof Map) {
          v = v.get(k);
        } else {
          v = v[k];
        }
      }
      return v;
    }
  }

  const pattern = /(@\[[A-Za-z0-9\$\^\:_\.\(\)\-]+\])/g;
  const result: string[] = [];
  let lastEnd = 0;

  content.replace(pattern, (match, _, offset) => {
    // 添加未匹配部分
    result.push(content.slice(lastEnd, offset));
    lastEnd = offset + match.length;

    // 去掉 `@[` 和 `]`
    const key = match.slice(2, -1);
    const value = getByKey(key);
    result.push(value !== null && value !== undefined ? value.toString() : "");
    return ""; // 必须返回一个字符串，避免 TypeScript 报错
  });

  // 添加剩余部分
  result.push(content.slice(lastEnd));
  return result.join("");
}


export default {
  templatedeal
}

