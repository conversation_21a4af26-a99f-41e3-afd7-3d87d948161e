import sysVersionPubApi, { ProjectAppMenu } from '~/api/basic/sys/versionApi';
import { config } from "~/config/global.config";
import { MenuItem } from "~/model/common";
import arrayUtils, { Array2Tree } from './arrayUtils';

type MenuItemSort = {
  menu: MenuItem;
  score: number;
}

function __findMenu(menus: MenuItem[], ms: MenuItemSort[], cb: (m: MenuItem) => number) {
  if (!menus || menus.length <= 0) {
    return;
  }
  for (let menu of menus) {
    if (menu) {
      let score = cb(menu);
      if (score > 0) {
        ms.push({
          menu,
          score
        });
      }
      if (menu.subs) {
        __findMenu(menu.subs, ms, cb);
      }
    }
  }
}

function _findMenu(menus: MenuItem[], cb: (m: MenuItem) => number): MenuItem | null {
  let ms: MenuItemSort[] = [];
  __findMenu(menus, ms, cb);
  if (ms.length <= 0) {
    return null;
  }
  ms = ms.sort((x, y) => y.score - x.score)
  return ms[0].menu;
}


function findMenuReg(path: string): MenuItem | null {
  if (!path) {
    return null;
  }
  if (path != '') {
    path = path.endsWith('/') ? path.substring(0, path.length - 1) : path;
  }

  let menus: MenuItem[] = config.menu.items;
  let m = _findMenu(menus, (m) => m.url == path ? 1 : 0);
  if (m) {
    return m;
  }

  m = _findMenu(menus, (m) => {
    if (!m.urlExp) {
      return 0;
    }
    for (let rxg of m.urlExp) {
      if (new RegExp(rxg).test(path)) {
        if (m.url) {
          return m.url.split('/').length;
        }
        return 1;
      }
    }
    return 0;
  });
  if (m) {
    return m;
  }

  m = _findMenu(menus, (m) => {
    if (m.urlExp) {
      return 0;
    }
    if (m.url == '/') {
      return 0;
    }
    if (new RegExp('^' + m.url + '.*').test(path)) {
      if (m.url) {
        return m.url.split('/').length;
      }
      return 1;
    }
    return 0;
  });
  return m;
}

function menuInit(items: MenuItem[], pre: string) {
  if (!items || items.length <= 0) {
    return;
  }
  for (let i = 0; i < items.length; i++) {
    let item = items[i];
    item.keepalive = item.keepalive == null || item.keepalive == undefined ? true : item.keepalive;
    item.show = item.show == null || item.show == undefined ? true : item.show;
    item._mid = pre + i;
    if (item.subs) {
      menuInit(item.subs, pre + i + '-');
    }
  }
}


const __sysMenu2configMenu = (ms: (ProjectAppMenu & Array2Tree<ProjectAppMenu, "subs">)[] | null, result: MenuItem[]) => {
  if (!ms || ms.length <= 0) {
    return;
  }
  for (let m of ms) {
    let r: MenuItem = <any>{ ...m, };
    r.name = m.code;
    r.pageRole = m.pageRole ? JSON.parse(m.pageRole!) : [];
    r.role = m.role ? JSON.parse(m.role!) : [];
    r.urlExp = m.urlExp ? JSON.parse(m.urlExp!) : [];

    if (m.code && m.name) {
      for (let k in config.i18n.locales) {
        let v = config.i18n.locales[k];
        if (v && v.msg && v.msg['menu']) {
          v.msg['menu'][m.code] = m.name;
          if (m.nameTag) {
            v.msg['menu'][`tag-${m.code}`] = m.nameTag;
          }
        }
      }
    }

    let subs: MenuItem[] = [];
    __sysMenu2configMenu(m.subs ? m.subs : null, subs);
    r.subs = subs.length > 0 ? subs : undefined;

    result.push(r);
  }
}

const reload = async (pub: boolean = true) => {
  let data: Array<ProjectAppMenu> = <any>(await sysVersionPubApi.getPubVersion(config.sysInfo.projectAppId)).menus;
  let menus: ProjectAppMenu[] = data ? data : [];

  if (menus && menus.length > 0) {
    let items: MenuItem[] = [];
    let ms = arrayUtils.array2tree(menus, 'id', 'idParent', 'subs');
    __sysMenu2configMenu(ms, items);
    config.menu.items = items;
  }
  menuInit(config.menu.items, 'm-');
}

export default {
  findMenuReg,
  reload,
}
