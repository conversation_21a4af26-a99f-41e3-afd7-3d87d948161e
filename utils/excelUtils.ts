/**
 * 完成Excel的导入导出
 *  https://www.jianshu.com/p/31534691ed53
 *  https://github.com/SheetJS/sheetjs#supported-output-formats
 */
import { request } from "./request";
import { WorkBook } from 'xlsx'

// 参数类型
export interface ExcelExportByRequestColumn {
  code?: string;
  name?: string;
  type?: string;
  format?: string;
  truename?: string;
  falsename?: string;
  deal?: (data: any, column: ExcelExportByRequestColumn, idx: number, ctx: ExcelExportByContext) => any | Promise<any>;
}

export interface ExcelExportByRequestSheetOption {
  requestCount: () => number | Promise<number>;
  requestData: (rows: number, page: number) => Array<any> | Promise<Array<any>>;
  rows?: number;
  sheetName?: string;
  columns?: Array<ExcelExportByRequestColumn>;
}

export interface ExcelExportByRequestOption {
  sheets: Array<ExcelExportByRequestSheetOption>;
  processCallback: (ctx: ExcelExportByContext, option: ExcelExportByRequestOption) => any | Promise<any>;
  filename?: string;
}

export interface ExcelExportByRequestOneSheetOption {
  requestCount: () => number | Promise<number>;
  requestData: (rows: number, page: number) => Array<any> | Promise<Array<any>>;
  rows?: number;
  columns?: Array<ExcelExportByRequestColumn>;
  processCallback: (ctx: ExcelExportByContext, option: ExcelExportByRequestOption) => any | Promise<any>;
  filename?: string;
  sheetname?: string;
}

export interface ExcelExportByUrlOption {
  rows?: number;
  columns?: Array<ExcelExportByRequestColumn>;
  processCallback: (ctx: ExcelExportByContext, option: ExcelExportByRequestOption) => any | Promise<any>;
  filename?: string;
  sheetname?: string;

  reqCountUrl?: string;
  reqDataUrl?: string;
  reqMethod?: string;
  reqParams?: any;
  reqData?: any;
  reqHeaders?: any;
  // 不携带token
  reqToken?: boolean;
  // 跳过token检查
  reqSkipToken?: boolean;
}

export interface ExcelExportByListOneSheetOption {
  data: any;
  columns?: Array<ExcelExportByRequestColumn>;
  processCallback: (ctx: ExcelExportByContext, option: ExcelExportByRequestOption) => any | Promise<any>;
  filename?: string;
  sheetname?: string;
}

// Context内容
export interface ExcelExportProcess {
  all?: number;
  count?: number;
  page?: number;
  maxPage?: number;
  rows?: number;
  sheetname?: string;
}

export interface ExcelExportByContext {
  filename?: string;
  wb?: WorkBook;
  process?: Array<ExcelExportProcess>;
  option?: ExcelExportByRequestOption;
  all?: number;
  count?: number;
}

export type ExcelAnalysisSheetMsg = {
  sheetname: string;
  data: string[][];
}

export default {
  async _requestUrl(option: ExcelExportByRequestSheetOption, proc: ExcelExportProcess, i: number): Promise<Array<any>> {
    let sdata: Array<any> = [];
    try {
      let pd = option.requestData(proc.rows!, i);
      sdata = pd instanceof Promise ? await pd : pd;
    } catch (error) {
      console.log("请求服务器数据出错，将重试。", error);
      try {
        let pd = option.requestData(proc.rows!, i);
        sdata = pd instanceof Promise ? await pd : pd;
      } catch (error) {
        console.log("请求服务器数据出错，将重试。", error);
        try {
          let pd = option.requestData(proc.rows!, i);
          sdata = pd instanceof Promise ? await pd : pd;
        } catch (error) {
          console.log("请求服务器数据出错，请检查您的网络。", error);
          throw error;
        }
      }
    }
    return sdata;
  },
  async _analysisData(data: any, outdata: Array<Array<any>>, option: ExcelExportByRequestSheetOption, proc: ExcelExportProcess, idx: number, ctx: ExcelExportByContext) {
    let columns = option.columns!;
    let cd: Array<any> = [];
    for (let i = 0; i < columns.length; i++) {
      let column = columns[i];
      let pr = column.deal!(data, column, idx, ctx);
      let r = pr instanceof Promise ? await pr : pr;
      cd.push(r);
    }
    outdata.push(cd);
  },

  // 导出工作薄
  async _exportWs(option: ExcelExportByRequestSheetOption, proc: ExcelExportProcess, ctx: ExcelExportByContext) {
    let headerStrs: Array<any> = [];
    let columns = option.columns!;
    for (let i = 0; i < columns.length; i++) {
      let column = columns[i];
      headerStrs.push(column.name);
    }

    /* Initial row */
    let XLSX = await import("xlsx");
    var ws = XLSX.utils.json_to_sheet([headerStrs], {
      skipHeader: true,
    });

    // 请求数据
    let idx = 0;
    for (let i = 0; i < proc.maxPage!; i++) {
      let sdata: Array<any> = await this._requestUrl(option, proc, i);

      // 处理数据
      let outdata: Array<Array<any>> = [];
      for (let j = 0; j < sdata.length; j++) {
        await this._analysisData(sdata[j], outdata, option, proc, idx, ctx);
        idx = idx + 1;
      }

      // 写入工作薄
      XLSX.utils.sheet_add_json(ws, outdata, {
        skipHeader: true,
        origin: -1,
      });

      // 进度更新
      proc.count = proc.count! + sdata.length;
      ctx.count = ctx.count! + sdata.length;
      let pr = ctx.option!.processCallback(ctx, ctx.option!);
      pr instanceof Promise ? await pr : pr;
    }

    // 处理工作薄名称
    XLSX.utils.book_append_sheet(ctx.wb!, ws, proc.sheetname);
  },

  // 默认字段处理逻辑
  async _defaultColumnsDeal(data: any, column: ExcelExportByRequestColumn, idx: number, ctx: ExcelExportByContext) {
    let d = data[column.code!]
    let result: any = {}
    if (column.type) {
      if (column.type == 'datetime') {
        let moment: any = await import("moment");
        result.v = moment(new Date(+d)).format(column.format);
      } else {
        if (typeof d == 'boolean') {
          result.v = d ? column.truename : column.falsename;
        } else {
          result.v = d;
        }
      }
    } else {
      if (typeof d == 'boolean') {
        result.v = d ? column.truename : column.falsename;
      } else {
        result.v = d;
      }
    }
    return result;
  },

  async _initColumns(ctx: ExcelExportByContext) {
    for (let i = 0; i < ctx.option!.sheets.length; i++) {
      let sheet = ctx.option?.sheets![i]!;
      for (let j = 0; j < sheet.columns!.length; j++) {
        let column = sheet.columns![j]!;
        if (column.deal != undefined && column.deal != null) {
          continue;
        }
        // TODO 字段处理逻辑
        column.deal = this._defaultColumnsDeal;
      }
    }
  },

  // 初始化上下文
  async _initContext(option: ExcelExportByRequestOption, ctx: ExcelExportByContext) {
    ctx.option = option;

    // 文件名
    let moment: any = await import("moment");
    let filename = option.filename ? option.filename : 'Excel导出' + moment().format("yyyyMMDDHHmmss") + '.xlsx';
    filename = filename.endsWith('.xlsx') ? filename : filename + '.xlsx';
    ctx.filename = filename;

    // 初始化字段信息
    this._initColumns(ctx);

    // 初始化进度数据
    let procCountProcs: Array<Promise<number>> = [];
    for (let i = 0; i < option.sheets.length; i++) {
      let op = option.sheets[i];
      let pcount = op.requestCount();
      if (pcount instanceof Promise) {
        procCountProcs.push(pcount);
      } else {
        procCountProcs.push(new Promise((r, j) => {
          r(pcount);
        }));
      }
    }

    // 发起请求
    let pcounts = await Promise.all(procCountProcs)

    // 组装数据
    ctx.all = 0;
    ctx.count = 0;
    ctx.process = [];
    for (let i = 0; i < option.sheets.length; i++) {
      let proc: ExcelExportProcess = {};

      let op = option.sheets[i];
      let sheetname = op.sheetName;
      sheetname = sheetname ? sheetname : '工作薄' + (i + 1);
      proc.sheetname = sheetname;

      proc.all = pcounts[i];
      proc.count = 0;
      ctx.all = ctx.all + proc.all;

      let rows = op.rows;
      rows = rows ? rows : 100;
      proc.rows = rows;
      proc.page = 0;
      proc.maxPage = Math.ceil(proc.all / proc.rows);

      ctx.process.push(proc);
    }
  },

  // 初始数据检查
  _initCheck(option: ExcelExportByRequestOption): ExcelExportByRequestOption {
    if (!option.sheets || option.sheets.length <= 0) {
      throw Error('参数异常sheets不能为空，请联系管理员');
    }
    option.processCallback = option.processCallback ? option.processCallback : () => { };
    for (let i = 0; i < option.sheets.length; i++) {
      let sheet = option.sheets[i];
      if (!sheet) {
        throw Error('参数异常sheets不能为空，请联系管理员');
      }
      if (!sheet.requestCount) {
        throw Error('参数异常requestCount不能为空，请联系管理员');
      }
      if (!sheet.requestData) {
        throw Error('参数异常requestData不能为空，请联系管理员');
      }
      if (!sheet.columns || sheet.columns.length <= 0) {
        throw Error('参数异常columns不能为空，请联系管理员');
      }
      for (let j = 0; j < sheet.columns.length; j++) {
        let column = sheet.columns[j];
        if (!column.code) {
          throw Error('参数异常column[].code不能为空，请联系管理员');
        }
        if (column.type == 'datetime') {
          column.format = column.format ? column.format : 'yyyyMMDDHHmmss';
        }
        column.truename = column.truename ? column.truename : '是';
        column.falsename = column.falsename ? column.falsename : '否';
        column.name = column.name ? column.name : column.code;
      }
    }
    return option;
  },

  // 数据格式进行转换
  _exporttype2option(option: any): ExcelExportByRequestOption {
    if (option.sheets && !option.columns) {
      return <ExcelExportByRequestOption>option;
    }
    let op: ExcelExportByRequestOneSheetOption = option;
    let r = <ExcelExportByRequestOption>{};
    r.filename = op.filename;
    r.processCallback = op.processCallback;
    r.sheets = [];
    let sheet = <ExcelExportByRequestSheetOption>{};
    sheet.requestData = op.requestData;
    sheet.requestCount = op.requestCount;
    sheet.rows = op.rows;
    sheet.columns = op.columns;
    sheet.sheetName = op.sheetname;
    r.sheets.push(sheet);
    return r;
  },

  // 通过远端请求导出Excel
  async exportByRequest(option: ExcelExportByRequestOption | ExcelExportByRequestOneSheetOption) {
    let ctx: ExcelExportByContext = {};
    option = this._exporttype2option(option);
    option = this._initCheck(option);
    await this._initContext(option, ctx);

    /* create new workbook */
    let XLSX = await import("xlsx");
    var wb = XLSX.utils.book_new();
    ctx.wb = wb;

    for (let i = 0; i < option.sheets.length; i++) {
      let op = option.sheets[i];
      let proc = ctx.process![i];
      await this._exportWs(op, proc, ctx);
    }

    XLSX.writeFile(wb, ctx.filename!);
  },

  // 将数据导出为Excel
  async exportData(option: ExcelExportByListOneSheetOption) {
    let op = <ExcelExportByRequestOneSheetOption>{};
    op.columns = option.columns;
    op.filename = option.filename;
    op.processCallback = option.processCallback;

    let data = option.data ? option.data : [];
    op.requestCount = () => data.length;
    op.requestData = () => data;
    op.rows = data.length;

    await this.exportByRequest(op);
  },

  async exportByUrl(option: ExcelExportByUrlOption) {
    let op = <ExcelExportByRequestOneSheetOption>{};
    op.columns = option.columns;
    op.filename = option.filename;
    op.sheetname = option.sheetname;
    op.processCallback = option.processCallback;
    op.rows = option.rows;

    let reqData = option.reqData ? JSON.parse(JSON.stringify(option.reqData)) : {};
    let reqHeaders = option.reqHeaders ? JSON.parse(JSON.stringify(option.reqHeaders)) : {};
    let reqParams = option.reqParams ? JSON.parse(JSON.stringify(option.reqParams)) : {};
    let reqMethod = option.reqMethod ? option.reqMethod : 'GET';
    op.requestCount = () => request.request({ method: reqMethod, url: option.reqCountUrl, params: reqParams, data: reqData, headers: reqHeaders, token: option.reqToken, skipToken: option.reqSkipToken });
    op.requestData = (rows, page) => {
      reqData.rows = rows;
      reqData.page = page;
      return request.request({ method: reqMethod, url: option.reqData, params: reqParams, data: reqData, headers: reqHeaders, token: option.reqToken, skipToken: option.reqSkipToken });
    };

    await this.exportByRequest(op);
  },

  /****************** 导入解析 **************** */
  _readWs(file: any): Promise<WorkBook> {
    return new Promise((r, j) => {
      import("xlsx").then(XLSX => {
        if (!file) {
          j(null);
        }
        let f =
          file.files && file.files.length > 0 ? file.files[0] : null;
        if (!file) {
          j(null);
        }
        let reader = new FileReader();
        reader.onload = (e) => {
          let data = e.target?.result;
          let wb = XLSX.read(data, { type: "binary" });
          r(wb);
        };
        reader.onerror = (e) => {
          j(null);
        }
        reader.readAsBinaryString(f);
      });
    });
  },
  _csvToArray(data: string, delimeter: string = ','): string[][] {
    // Retrieve the delimeter
    if (delimeter == undefined)
      delimeter = ',';
    if (delimeter && delimeter.length > 1)
      delimeter = ',';

    // initialize variables
    var newline = '\n';
    var eof = '';
    var i = 0;
    var c = data.charAt(i);
    var row = 0;
    var col = 0;
    var array = <string[][]>[];

    while (c != eof) {
      // skip whitespaces
      while (c == ' ' || c == '\t' || c == '\r') {
        c = data.charAt(++i); // read next char
      }

      // get value
      var value = "";
      if (c == '\"') {
        // value enclosed by double-quotes
        c = data.charAt(++i);

        do {
          if (c != '\"') {
            // read a regular character and go to the next character
            value += c;
            c = data.charAt(++i);
          }

          if (c == '\"') {
            // check for escaped double-quote
            var cnext = data.charAt(i + 1);
            if (cnext == '\"') {
              // this is an escaped double-quote. 
              // Add a double-quote to the value, and move two characters ahead.
              value += '\"';
              i += 2;
              c = data.charAt(i);
            }
          }
        }
        while (c != eof && c != '\"');

        if (c == eof) {
          throw "Unexpected end of data, double-quote expected";
        }
        c = data.charAt(++i);
      } else {
        // value without quotes
        while (c != eof && c != delimeter && c != newline && c != ' ' && c != '\t' && c != '\r') {
          value += c;
          c = data.charAt(++i);
        }
      }

      // add the value to the array
      if (array.length <= row)
        array.push(new Array());
      array[row].push(value);

      // skip whitespaces
      while (c == ' ' || c == '\t' || c == '\r') {
        c = data.charAt(++i);
      }

      // go to the next row or column
      if (c == delimeter) {
        // to the next column
        col++;
      }
      else if (c == newline) {
        // to the next row
        col = 0;
        row++;
      }
      // else if (c != eof) {
      //     // unexpected character
      //     throw "Delimiter expected after character " + i;
      // }

      // go to the next character
      c = data.charAt(++i);
    }
    return array;
  },


  async readExcel(file: any): Promise<ExcelAnalysisSheetMsg[]> {
    let XLSX = await import("xlsx");
    let wb = await this._readWs(file);
    if (!wb) {
      throw new Error('导入文件异常')
    }
    let res = <ExcelAnalysisSheetMsg[]>[];
    for (let i = 0; i < wb.SheetNames.length; i++) {
      let sheetname = wb.SheetNames[i];
      let data = XLSX.utils.sheet_to_csv(wb.Sheets[sheetname]);
      let list = this._csvToArray(data);
      res.push({ sheetname: sheetname, data: list });
    }
    return res;
  }


}

