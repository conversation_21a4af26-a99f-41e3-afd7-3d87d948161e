type DelayBufferProps<T> = { timeout?: number, count?: number, delay?: boolean, cb: (list: Array<T>) => void }

export class DelayBuffer<T> {
  props: DelayBufferProps<T> | null = null;
  list: Array<T> = [];
  timer: any = null;

  // {timeout: 3000, count: 100, cb:(list)=>void}
  constructor(props: DelayBufferProps<T> | null = null) {
    this.setProps(props);
    this.list = [];
    this.timer = null;
  }

  setProps(props: DelayBufferProps<T> | null = null) {
    props = props ? props : <DelayBufferProps<T>>{};
    props.timeout = props.timeout ?? 3000;
    props.count = props.count ?? 100;
    props.delay == undefined || props.delay == null ? false : props.delay;
    props.cb ? props.cb : (list: Array<T>) => { };
    this.props = props;
  }

  setCb(cb: (list: Array<T>) => void | null | undefined) {
    cb = !!cb ? cb : (list: Array<T>) => { };
    this.props!.cb = <any>cb;
  }

  put(obj: T) {
    let that = this;
    that.list.push(obj);

    if (that.props!.count! > 0) {
      if (that.list.length >= that.props!.count!) {
        that.props!.cb(that.list);
        that.list = [];
        return;
      }
    }

    if (that.timer) {
      if (that.props!.delay) {
        clearTimeout(that.timer);
        that.timer = null;
      } else {
        return;
      }
    }

    that.timer = setTimeout(() => {
      that.timer = null;
      if (that.list.length <= 0) {
        return;
      }
      that.props!.cb(that.list);
      that.list = [];
    }, that.props!.timeout);
  }

}

type DelayOperateProp = {
  timeout: number
}

export class DelayOperate {
  list = <{ cb: (value: boolean) => void }[]>[];
  timer: any | null = null;
  prop: DelayOperateProp | null = null;

  constructor(prop: DelayOperateProp = { timeout: 500 }) {
    prop = prop ?? { timeout: 500 };
    prop.timeout = prop.timeout || 500;
    this.prop = prop;
  }

  delay(): Promise<boolean> {
    let that = this;
    return new Promise((r, j) => {
      this.list.push({ cb: r });
      if (that.timer) {
        clearTimeout(that.timer);
        that.timer = null;
      }
      that.timer = setTimeout(() => {
        that.__timeout();
      }, that.prop!.timeout);
    });
  }

  private __timeout() {
    let list = this.list;
    this.list = [];
    if (list.length <= 0) {
      return;
    }
    for (let i = 0; i < list.length - 1; i++) {
      list[i].cb(false);
    }
    list[list.length - 1].cb(true);
  }

}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T & { cancel: () => void } {
  let timeout: any = null;

  const debounced = function(this: any, ...args: any[]) {
    const context = this;

    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      timeout = null;
      func.apply(context, args);
    }, wait);
  } as T & { cancel: () => void };

  debounced.cancel = function() {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };

  return debounced;
}

export default { DelayBuffer, DelayOperate, debounce }
