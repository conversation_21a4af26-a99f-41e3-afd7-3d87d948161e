const Fingerprint2 = require('fingerprintjs2');
const uuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  }).replaceAll('-', '');
}

const getFp = async (): Promise<string> => {
  const getF = () => {
    return new Promise<string>((resolve, reject) => {
      const fingerprint = Fingerprint2.get((components: any) => { // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
        // debugger
        // console.log('fp:', JSON.stringify(components));
        // 将components转换为map,内部结构：
        //     [
        // {
        //     "key": "userAgent",
        //     "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
        // }]
        const kvMap: any = {};
        components.forEach((x: any) => kvMap[x.key] = x.value);

        const values: any[] = [];
        values.push(kvMap.userAgent);
        values.push(kvMap.webdriver);
        values.push(kvMap.language);
        values.push(kvMap.timezoneOffset);
        values.push(kvMap.timezone);
        values.push(kvMap.cpuClass);
        values.push(kvMap.platform);
        values.push(kvMap.plugins);
        values.push(kvMap.canvas);
        values.push(kvMap.webgl);
        values.push(kvMap.webglVendorAndRenderer);
        values.push(kvMap.touchSupport);
        values.push(kvMap.fonts);

        // const values = components.filter(x => { }).map((component: any) => component.value); // 配置的值的数组
        const murmur = Fingerprint2.x64hash128(values.join(''), 31); // 生成浏览器指纹
        resolve(murmur);
      });
    });
  }
  const id = await getF();
  return id;
}

export default { uuid, getFp }
