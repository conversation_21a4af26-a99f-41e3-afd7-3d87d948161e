import fieldUtils from "./fieldUtils";
import sysVersionPubApi from '~/api/basic/sys/versionApi';
import dicApi from "@/api/auth/dic/dicApi";
import commonFunUtils from "./commonFunUtils";
import { config } from "~/config/global.config";

// [SysConfig]系统级别的配置表路径请求参数
export interface SysConfig {

  // 主键, 必须字段, 最小长度：1, 最大长度：32
  id?: string;
  // 字段编号, 必须字段
  code?: string;
  // 启用
  valid?: boolean;
  // 名称
  value?: string;
  // 是否可公开, 必须字段
  pub?: boolean;
  // 备注
  remark?: string;
}

export class SysconfigException extends Error {
  constructor(msg: string) {
    super(msg)
  }
}

interface Tkey2value<T> {
  [key: string]: T;
}

let sysconfigs: Array<SysConfig> = [];
// code -> obj
let code2obj: Tkey2value<SysConfig> = {};

// 获取原始数据集
const getData = (): any => fieldUtils.getValueDef(sysconfigs, {});
// 获取所有字典列表数据集
const getList = (): Array<SysConfig> => fieldUtils.getValueDef(getData(), []);

/**
 * 装载配置数据
 * @param pub 加载开放接口配置
 */
const reload = async (pub: boolean = true) => {
  let data: Array<SysConfig> = <any>(await sysVersionPubApi.getPubVersion(config.sysInfo.projectAppId)).sysConfigs;
  sysconfigs = data ? data : [];
  let list = getList();
  {
    let _code2obj: Tkey2value<SysConfig> = {};
    list.filter(x => x.valid).forEach((x: SysConfig) => _code2obj[x.code!] = x);
    code2obj = _code2obj;
  }
}

/**
 * 通过code获取配置项
 * @param code code
 * @param nullThrow 未找到时是否直接抛出异常
 * @returns 配置项
 */
const getConfig = (code: string, nullThrow = true): SysConfig | null => {
  const config = fieldUtils.getFieldValue(code2obj, [code], null);
  if (!config) {
    if (nullThrow) {
      throw new SysconfigException(`配置"${code}"值不存在，请联系管理员.`);
    } else {
      return null;
    }
  } else {
    return config;
  }
}

/**
 * 通过code获取值
 * @param code code
 * @param nullThrow 未找到时是否直接抛出异常
 * @returns 值
 */
const getValue = <T>(code: string, nullThrow = true): string | null => {
  const value = getConfig(code, nullThrow)?.value;
  if (value) {
    return value;
  } else {
    if (nullThrow) {
      throw new SysconfigException(`配置"${code}"值不存在，请联系管理员.`);
    } else {
      return null;
    }
  }
}

/**
 * 通过code获取指定数据
 * @param code 配置Code
 * @param def 默认值
 * @returns 值
 */
const getValueDef = (code: string, def: string): string => {
  const value = getValue(code, false);
  return value ? value : def;
}

/**
 * 获取配置值
 * @param code 配置code
 * @returns 值
 */
const getNumber = (code: string): number => {
  return +(getValue(code, true)!);
}

/**
 * 获取配置值
 * @param code 配置code
 * @param def 默认值
 * @returns 值
 */
const getNumberDef = (code: string, def: number): number => {
  const value = getValue(code, false);
  return value ? + value : def;
}

/**
 * 获取配置值
 * @param code 配置code
 * @returns 值
 */
const getBoolean = (code: string): boolean => {
  return commonFunUtils.parseBoolean(getValue(code, true)!);
}

/**
 * 获取配置值
 * @param code 配置code
 * @param def 默认值
 * @returns 值
 */
const getBooleanDef = (code: string, def: boolean): boolean => {
  const value = getValue(code, false);
  return value ? commonFunUtils.parseBoolean(value) : def;
}

/**
 * 获取配置值
 * @param code 配置code
 * @returns 值
 */
const getJson = (code: string): any => {
  return JSON.parse(getValue(code, true)!);
}

/**
 * 获取配置值
 * @param code 配置code
 * @param def 默认值
 * @returns 值
 */
const getJsonDef = <T>(code: string, def: T): T => {
  const value = getValue(code, false);
  return value ? JSON.parse(value) : def;
}

export default {
  getData,
  getList,
  reload,

  getConfig,
  getValue,
  getValueDef,
  getNumber,
  getNumberDef,
  getBoolean,
  getBooleanDef,
  getJson,
  getJsonDef
}

