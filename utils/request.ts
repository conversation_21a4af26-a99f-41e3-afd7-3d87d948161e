import { Store } from 'vuex';
import cryptoUtils, { TransactionCryptoOperator } from './cryptoUtils';
import { NuxtAxiosInstance } from '@nuxtjs/axios'
import urlUtils from '@/utils/urlUtils'
import networkUtils, { RequestBuffer } from './networkUtils';
import { SysCryptoPubkeyVo } from '~/api/basic/sys/cryptoApi';

let $axios: NuxtAxiosInstance
let $store: Store<any>;

export function initializeAxios(axiosInstance: NuxtAxiosInstance, store: Store<any>) {
  $axios = axiosInstance;
  $store = store;
}

class CommonException extends Error {
  code: string;
  data: any;

  constructor(code: string, msg: string = '', data: any = null) {
    super(msg)
    this.code = code;
    this.data = data;
  }
}

class BadRequestException extends CommonException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}

class NotFoundException extends CommonException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}

class UnauthorizedException extends CommonException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}

class ForbiddenException extends CommonException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}

class ServerException extends CommonException {

  constructor(code: string, msg: string = '', data: any = null) {
    super(code, msg, data);
  }

}

export interface NetResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: any;
  request?: any;
}

export interface NetRequestParam {
  // 请求地址
  url?: string
  // 请求方式
  method?: string
  // 票据
  ticket?: string
  // 参数
  params?: any
  // 请求体
  data?: any
  // 请求头
  headers?: any
  // 不携带token
  token?: boolean
  // 跳过token检查
  skipToken?: boolean
  // 是否携带pageRole
  pageRole?: boolean
  // 是否合并请求，默认为false，为true时，当参数一致的多个请求同期调用时，会自动合并为只发送一个请求，响应后根据响应进行结果再分发，注意：该参数不能与file参数同时为true
  marginRequest?: boolean
  // 文件类型传输FormData格式，注意：该参数不能与marginRequest参数同时为true
  file?: boolean
  // 递归深度
  deep?: number;
  //响应原始数据
  responseAll?: boolean;
  onUploadProgress?: (e: ProgressEvent) => void;
  // 传输加密对象，通过cryptoHelepr.buildTransactionCryptoOperator函数创建
  transactionCryptoOperator?: TransactionCryptoOperator;
  // 加密对象
  cryptoBody?: boolean;
  // 加密参数
  cryptoParam?: boolean;
  // 响应加密
  cryptoResult?: boolean;
}

let request = {
  __margin: new Map<string, RequestBuffer<any>>(),
  get<T = any>(url: string, cf?: NetRequestParam): Promise<T> {
    let pa: NetRequestParam = cf ? cf : {};
    pa.url = url;
    pa.method = 'GET';
    return request.request(pa);
  },
  post<T = any>(url: string, data?: any, cf?: NetRequestParam): Promise<T> {
    let pa: NetRequestParam = cf ? cf : {};
    pa.url = url;
    pa.method = 'POST';
    pa.data = data;
    return request.request(pa);
  },
  put<T = any>(url: string, data?: any, cf?: NetRequestParam): Promise<T> {
    let pa: NetRequestParam = cf ? cf : {};
    pa.url = url;
    pa.method = 'PUT';
    pa.data = data;
    return request.request(pa);
  },
  delete<T = any>(url: string, cf?: NetRequestParam): Promise<T> {
    let pa: NetRequestParam = cf ? cf : {};
    pa.url = url;
    pa.method = 'DELETE';
    return request.request(pa);
  },
  async request<T = any>(cf: NetRequestParam): Promise<T> {
    let rp: any = {};
    rp.url = cf.url;
    rp.method = cf.method ? cf.method : 'GET';
    rp.headers = cf.headers ? cf.headers : {};
    rp.data = cf.data ? cf.data : {};
    rp.params = cf.params ? cf.params : {};
    rp.ticket = cf.ticket ? cf.ticket : null;
    rp.token = (cf.token != null && cf.token != undefined) ? cf.token : false;
    rp.pageRole = (cf.pageRole != null && cf.pageRole != undefined) ? cf.pageRole : false;
    rp.skipToken = (cf.skipToken != null && cf.skipToken != undefined) ? cf.skipToken : false;
    rp.marginRequest = (cf.marginRequest != null && cf.marginRequest != undefined) ? cf.marginRequest : false;
    rp.file = (cf.file != null && cf.file != undefined) ? cf.file : false;
    rp.responseAll = (cf.responseAll != null && cf.responseAll != undefined) ? cf.responseAll : false;
    rp.transactionCryptoOperator = cf.transactionCryptoOperator;
    rp.cryptoBody = (cf.cryptoBody != null && cf.cryptoBody != undefined) ? cf.cryptoBody : false;
    rp.cryptoParam = (cf.cryptoParam != null && cf.cryptoParam != undefined) ? cf.cryptoParam : false;
    rp.cryptoResult = (cf.cryptoResult != null && cf.cryptoResult != undefined) ? cf.cryptoResult : false;

    rp.deep = cf.deep ? cf.deep : 1;
    if (rp.file && rp.marginRequest) {
      throw new CommonException('请求参数file与marginRequest不能同时为true')
    }

    // 判断是否需要加密
    if (rp.cryptoBody || rp.cryptoParam || rp.cryptoResult || rp.transactionCryptoOperator) {
      if (rp.file) {
        throw new CommonException('加密传输不允许使用file参数')
      }
      rp.transactionCryptoOperator = rp.transactionCryptoOperator ? rp.transactionCryptoOperator :
        cryptoUtils.buildTransactionCryptoOperator();
      const transactionCryptoOperator: TransactionCryptoOperator = rp.transactionCryptoOperator;

      // 参数准备
      if (rp.cryptoParam) {
        rp.cryptoParam = false;
        let nparams: any = {};
        for (const key in rp.params) {
          const value = rp.params[key];
          if (value == undefined || value == null) {
            continue;
          }
          if (value instanceof Array) {
            const vals: any[] = [];
            for (let i = 0; i < value.length; i++) {
              const v = value[i];
              if (v !== undefined && v !== null) {
                vals.push(transactionCryptoOperator.encrypt('' + v));
              }
            }
          } else {
            nparams[key] = transactionCryptoOperator.encrypt('' + value);
          }
        }
        rp.params = nparams;
        rp.headers['X-CRYPTO-PARAM'] = 'true';
      }
      if (rp.cryptoBody) {
        rp.cryptoBody = false;
        const dataJson = JSON.stringify(rp.data);
        const cdata = transactionCryptoOperator.encrypt(dataJson);
        rp.data = {
          content: cdata,
        };
        rp.headers['X-CRYPTO-BODY'] = 'true';
      }

      if (rp.cryptoResult) {
        rp.headers['X-CRYPTO-RESULT'] = 'true';
      }

      // 封装异步请求
      const transactionResult: SysCryptoPubkeyVo = await $store.dispatch("app/getTransactionPublicKey");
      const symKey = {
        key: transactionCryptoOperator.getKey()?.keyBase64,
        algorithm: transactionCryptoOperator.getAlgorithm(),
      };
      const cryptoKey = cryptoUtils.encrypt(transactionResult.algorithm!, JSON.stringify(symKey), transactionResult.publicKeyBase64!);
      rp.headers = { ...rp.headers };
      rp.headers['X-CRYPTO-KEY'] = cryptoKey;
      rp.headers['X-CRYPTO-VERSION'] = transactionResult.version;
    }

    rp.url = urlUtils.getUrl(rp.url, rp.params);
    rp.params = {};
    rp.onUploadProgress = cf.onUploadProgress;

    let respBody = null;
    if (rp.marginRequest && rp.deep <= 1) {
      let key = (rp.url + '|||' + JSON.stringify(rp.data));
      let buffer: RequestBuffer<any> | undefined = this.__margin.get(key);
      if (!buffer) {
        buffer = networkUtils.buildRequestBuffer();
        this.__margin.set(key, buffer);
        buffer.responseListener(() => {
          this.__margin.delete(key)
        });
      }
      respBody = await buffer.request(() => this.__request(rp));
    } else {
      respBody = await this.__request(rp);
    }
    return respBody;
  },
  __request<T = any>(rp: any): Promise<T> {
    if (rp.file) {
      let data = rp.data;
      if (!(data instanceof FormData)) {
        let fd = new FormData();
        if (data) {
          for (let key in data) {
            fd.append(key, data[key]);
          }
        }
        rp.data = fd;
      }
    }
    return $axios.request(<any>rp);
  }
}

export { request, CommonException, BadRequestException, NotFoundException, UnauthorizedException, ForbiddenException, ServerException, $axios }
