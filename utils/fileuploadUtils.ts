
import fsAliyunApi, { FsAliyunUploadPreVo, FsUploadPreParamVo, SysFsAliyunUploadRequestQueryParam } from '~/api/sys/fs/aliyunApi';
import { config } from '@/config/global.config';
import fsApi, { FsQueryListByHashParamVo, ResDoc } from '~/api/sys/fsApi';
import { request } from '@/utils/request';
import idUtils from './idUtils';
import storeManager from '~/utils/localStorageUtils';

const SparkMd5 = require('spark-md5')
const OSS = require('ali-oss');

// const OSS = require('ali-oss')

const pageSize = 1024 * 1024 * 10
const getUuidKey = () => {
  return 'fs.uuid';
}

const getUuid = (): string => {
  let uuidKey = getUuidKey();
  let uid = storeManager.get(uuidKey);
  if (uid) {
    return uid;
  } else {
    uid = idUtils.uuid();
    storeManager.set(uuidKey, uid);
    return uid;
  }
}

const filemd5 = (file: File, onProgress: (e: { total: number, loaded: number, isTrusted: boolean }) => void): Promise<string> => {
  return new Promise((r, j) => {
    let spark = new SparkMd5.ArrayBuffer();
    let total = file.size;
    const readFile = (file: File, start: number, page: number, cb: () => void) => {
      let end = start + page;
      let endState = end >= total;
      end = endState ? total : end;
      let reader = new FileReader();
      let bs = file.slice(start, end);
      reader.onload = (e: any) => {
        let result = e.target.result;
        spark.append(result);
        if (endState) {
          onProgress({ total, loaded: end, isTrusted: true });
          cb();
        } else {
          onProgress({ total, loaded: end, isTrusted: false });
          readFile(file, end, page, cb);
        }
      }
      reader.readAsArrayBuffer(bs);
    }
    readFile(file, 0, pageSize, () => {
      r(spark.end());
    });
  });
}

const uploadFile = (file: File, resp: FsAliyunUploadPreVo, onProgress: (e: { total: number, loaded: number, isTrusted: boolean }) => void): Promise<void> => {
  return new Promise((r, j) => {
    const client = new OSS({
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
      region: resp.auth?.region,
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: resp.auth?.accessKeyId,
      accessKeySecret: resp.auth?.accessKeySecret,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: resp.auth?.securityToken,
      // 填写Bucket名称。
      bucket: resp.auth?.bucket
    });
    let filepath = resp.auth?.filepath;
    // debugger
    // object表示上传到OSS的文件名称。
    // file表示浏览器中需要上传的文件，支持HTML5 file和Blob类型。
    // client.put(filepath, file).then(function (r1: any) {
    //     console.log('put success: %j', r1);
    //     // return client.get('object');
    //     debugger
    // }).then(function (r2: any) {
    //     console.log('get success: %j', r2);
    // }).catch(function (err: any) {
    //     console.error('error: %j', err);
    // });


    // 设置中断点。
    let abortCheckpoint = null;

    // 中断分片上传。
    // client.abortMultipartUpload(abortCheckpoint.name, abortCheckpoint.uploadId)

    const options = {
      // 获取分片上传进度、断点和返回值。
      progress: (p: any, cpt: any, res: any) => {
        let total = file.size;
        let loaded = Math.floor(total * p);
        onProgress({ total, loaded, isTrusted: false });
      },
      // 设置并发上传的分片数量。
      parallel: 4,
      // 设置分片大小。默认值为1 MB，最小值为100 KB。
      partSize: 1024 * 1024,
      // headers,
      // 自定义元数据，通过HeadObject接口可以获取Object的元数据。
      // meta: { year: 2020, people: "test" },
      // mime: "text/plain",
    };
    client.multipartUpload(filepath, file, {
      ...options
    }).then(function (r2: any) {
      r();
    }).catch(function (err: any) {
      j(err);
    });
  });
}

const readFile = async (file: File, type: 'dataurl' | 'arraybuffer' = 'dataurl'): Promise<string | ArrayBuffer | null | undefined> => {
  return new Promise((r, j) => {
    const read = new FileReader()
    read.onload = (e) => {
      r(e.target?.result);
    }
    if (type == 'arraybuffer') {
      read.readAsArrayBuffer(file);
    } else if (type = 'dataurl') {
      read.readAsDataURL(file);
    } else {
      const exhaustiveCheck: never = type;
      throw new Error(`Unhandled result: ${exhaustiveCheck}`);
    }

  });
}

const upload = (params: {
  file: File, secrecy?: boolean | undefined | null, exp?: number | undefined | null, uuid?: string | undefined | null, ticket?: string | undefined | null, watermark?: boolean | undefined | null
  onProgress: (e: { total: number, loaded: number, isTrusted: boolean }) => void
}): Promise<ResDoc> => {
  params.secrecy = params.secrecy == undefined || params.secrecy == null ? false : params.secrecy;
  params.watermark = params.watermark == undefined || params.watermark == null ? false : params.watermark;
  params.exp = params.exp == undefined || params.exp == null ? null : params.exp;
  params.onProgress = params.onProgress ? params.onProgress : (e) => { };
  let uuid: string = params.uuid ? params.uuid : getUuid();
  let file = params.file;
  let total = file.size;
  return (async () => {
    let md5 = await filemd5(file, (e) => {
      params.onProgress({ loaded: e.loaded, total: e.total * 20, isTrusted: false });
    });

    if (md5) {
      let rqParam: FsQueryListByHashParamVo = {};
      rqParam.hash = md5;
      rqParam.size = file.size;
      let docs = await fsApi.getListByHash(rqParam, { ticket: params.ticket ?? undefined });
      if (docs && docs.length > 0) {
        return docs[0];
      }
    }
    if (config.fs && config.fs.uptype && config.fs.uptype == 'server') {
      let fileParam: any = { 'file': file };
      if (params.secrecy) {
        fileParam.secrecy = true;
        fileParam.uuid = uuid;
      }
      if (params.exp) {
        fileParam.exp = params.exp;
      }
      if (params.watermark) {
        fileParam.watermark = params.watermark;
      }
      const onUploadProgress = (e: ProgressEvent) => {
        params.onProgress({ loaded: e.loaded * 19 + e.total, total: e.total * 20, isTrusted: e.isTrusted });
      }
      let doc = await request.post(`${config.baseUrl.apiUrl}/sys/fs`, fileParam, {
        token: true, file: true, onUploadProgress, ticket: params.ticket ?? undefined
      });
      params.onProgress({ loaded: total * 20, total: total * 20, isTrusted: true });
      return doc;
    } else {
      let rqBody: FsUploadPreParamVo = {};
      rqBody.size = file.size;
      rqBody.hash = md5;
      rqBody.mimeType = file.type;
      rqBody.name = file.name;
      rqBody.secrecy = <boolean>params.secrecy;
      rqBody.watermark = <boolean>params.watermark;
      rqBody.exp = params.exp ?? undefined;
      let rpParam: SysFsAliyunUploadRequestQueryParam = {};
      if (params.secrecy) {
        rpParam.uuid = uuid;
      }
      let resp = await fsAliyunApi.uploadRequest(rpParam, rqBody, { ticket: params.ticket ?? undefined });
      let doc = resp.doc;
      if (resp.exist) {
        return doc;
      }
      await uploadFile(file, resp, (e) => {
        params.onProgress({ loaded: e.loaded * 19 + e.total, total: e.total * 20, isTrusted: e.isTrusted });
      });
      await fsAliyunApi.uploaded(doc?.id!, { ticket: params.ticket ?? undefined });
      params.onProgress({ loaded: total * 20, total: total * 20, isTrusted: true });
      return resp.doc;
    }
  })();
}

export default { upload, getUuid, readFile }

