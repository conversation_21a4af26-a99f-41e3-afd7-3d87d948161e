import cryptoSmUtils from "./cryptoSmUtils";

type KeyPair = {
  keyBase64?: string;
  publicKeyBase64?: string | null;
}
/**
 * 生成非对称秘钥
 * @param algorithm 算法
 * @returns 秘钥对
 */
const generateKey = (algorithm: string): KeyPair => {
  if (algorithm === 'SM2_256') {
    return cryptoSmUtils.sm2GenerateKey();
  } else if (algorithm === 'SM4_128') {
    const keyBase64 = cryptoSmUtils.sm4GenerateKey();
    return {
      keyBase64,
      publicKeyBase64: null,
    }
  } else {
    throw new Error('不支持的算法');
  }
}

/**
 * 加密
 * @param algorithm 算法
 * @param content 内容
 * @param keyBase64 秘钥
 * @returns 密文
 */
const encrypt = (algorithm: string, content: string, keyBase64: string): string => {
  if (algorithm === 'SM2_256') {
    return cryptoSmUtils.sm2Encode(content, keyBase64);
  } else if (algorithm === 'SM4_128') {
    return cryptoSmUtils.sm4Encrypt(content, keyBase64);
  } else {
    throw new Error('不支持的算法');
  }
}

/**
 * 解密
 * @param algorithm 算法
 * @param content 密文
 * @param keyBase64 秘钥
 * @returns 解密后内容
 */
const decrypt = (algorithm: string, content: string, keyBase64: string): string => {
  if (algorithm === 'SM2_256') {
    return cryptoSmUtils.sm2Decode(content, keyBase64);
  } else if (algorithm === 'SM4_128') {
    return cryptoSmUtils.sm4Decrypt(content, keyBase64);
  } else {
    throw new Error('不支持的算法');
  }
}

/**
 * 摘要计算
 * @param algorithm 算法
 * @param content 内容
 * @returns 摘要
 */
const hash = (algorithm: string, content: string): string => {
  return cryptoSmUtils.sm3Hash(content);
}

/**
 * 签名
 * @param algorithm 算法
 * @param content 内容
 * @param userId 用户id
 * @param priKeyBase64 私钥
 * @returns 签名
 */
const sign = (algorithm: string, content: string, userId: string, keyBase64: string): string => {
  return cryptoSmUtils.sm2Sign(content, userId, keyBase64);
}
/**
 * 验签
 * @param algorithm 算法
 * @param content 内容
 * @param value 待比对的签名
 * @param userId 用户id
 * @param pubKeyBase64 公钥
 * @returns 验签结果
 */
const verify = (algorithm: string, content: string, value: string, userId: string, pubKeyBase64: string): boolean => {
  return cryptoSmUtils.sm2Verify(content, value, userId, pubKeyBase64);
}

/**
 * 传输加密对象
 */
export class TransactionCryptoOperator {
  private key: KeyPair | null = null;
  private algorithm = 'SM4_128';
  constructor() {
    this.key = generateKey(this.algorithm);
  }

  getKey(): KeyPair | null {
    return this.key;
  }
  setKey(key: KeyPair | null) {
    this.key = key;
  }
  getAlgorithm(): string {
    return this.algorithm;
  }
  setAlgorithm(algorithm: string) {
    this.algorithm = algorithm;
  }


  encrypt(content: string): string {
    return encrypt(this.algorithm, content, this.key!.keyBase64!);
  }

  decrypt(content: string): string {
    return decrypt(this.algorithm, content, this.key!.keyBase64!);
  }

}

const buildTransactionCryptoOperator = (): TransactionCryptoOperator => {
  return new TransactionCryptoOperator();
}


export default { generateKey, encrypt, decrypt, hash, sign, verify, buildTransactionCryptoOperator };

