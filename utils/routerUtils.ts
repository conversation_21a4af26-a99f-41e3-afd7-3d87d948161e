import fieldUtils from "./fieldUtils";
import urlUtils from "./urlUtils"
import { config } from "@/config/global.config";

const getQueryByVue = (that: Vue) => {
  return urlUtils.url2query(that.$route.fullPath);
}

function getQueryValue<T extends string[] | string>(that: Vue, key: string, def: T): T {
  let query = urlUtils.url2query(that.$route.fullPath);
  let val = <string | string[] | undefined>fieldUtils.getFieldValue(query, key, def);
  if (Array.isArray(def)) {
    if (val) {
      if (Array.isArray(val)) {
        return <any>val;
      } else {
        return <any>[val]
      }
    } else {
      return def;
    }
  } else {
    if (val) {
      if (Array.isArray(val)) {
        return <any>val[0];
      } else {
        return <any>val;
      }
    } else {
      return def;
    }
  }
}

const putQueryValue = (that: Vue, obj: any) => {
  let nurl = urlUtils.putParam(that.$route.fullPath, obj)
  if (nurl != that.$route.fullPath) {
    that.$router.replace(nurl);
  }
}

const getParamByVue = (that: Vue) => {
  return that.$route.params;
}

const getParamValue = (that: Vue, key: string, def: string) => {
  let params = getParamByVue(that);
  return fieldUtils.getFieldValue(params, key, def);
}

/**
 * 获取当前系统基础路径页面
 * @param that 当前页面
 * @returns 基础路径
 */
const getLocalUrl = (that: Vue) => {
  let hrefUrl = window.location.href;
  let appUrl = that && that.$route ? that.$route.path : null;
  if (!appUrl || !hrefUrl) {
    return config.baseUrl.wwwUrl;
  }
  {
    let startIdx = hrefUrl.indexOf('?');
    hrefUrl = startIdx >= 0 ? hrefUrl.substring(0, startIdx) : hrefUrl;
  }
  if (!hrefUrl.endsWith(appUrl)) {
    return config.baseUrl.wwwUrl;
  }
  return hrefUrl.substring(0, hrefUrl.length - appUrl.length);
}

/**
 * 获取当前系统路径
 * @param that 当前页面
 * @returns 路径
 */
const getRoutePath = (that: Vue) => {
  let route = that.$route;
  let path = route.path;
  if (path == '/') {
    return path;
  }
  let routePath = path.endsWith("/")
    ? path.substring(0, path.length - 1)
    : path;
  return routePath;
}

export default { getQueryByVue, getQueryValue, putQueryValue, getLocalUrl, getParamByVue, getParamValue, getRoutePath }

