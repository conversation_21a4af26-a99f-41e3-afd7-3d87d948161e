import Vue from "vue";
// @ts-ignore
import YSvgIcon from "~/components/y/y-svg-icon.vue";
// @ts-ignore
import YSvgSprite from "~/components/y/y-svg-sprite.vue";

// 注册全局组件
Vue.component("y-svg-icon", YSvgIcon);
Vue.component("y-svg-sprite", YSvgSprite);

export default () => {
    // 在应用启动时加载SVG Sprite
    if (process.client) {
        const svgSprite = new Vue({
            render: h => h(YSvgSprite)
        }).$mount();

        document.body.appendChild(svgSprite.$el);
    }
};
