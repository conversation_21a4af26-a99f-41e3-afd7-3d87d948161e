import { Plugin } from '@nuxt/types'
import ax, { AxiosResponse, AxiosRequestConfig, AxiosError } from 'axios'
import { TransactionCryptoOperator } from '~/utils/cryptoUtils';
import { BadRequestException, UnauthorizedException, ForbiddenException, ServerException, initializeAxios, NotFoundException, request, NetRequestParam } from '~/utils/request'
import timeUtils from '~/utils/timeUtils';

export type ResponseBody = AxiosError & {
  response: Omit<AxiosResponse<any>, 'config'> & {
    config: NetRequestParam & AxiosRequestConfig;
  }
}
const accessor: Plugin = ({ env, $axios, store, error, app }) => {
  $axios.setBaseURL('');

  initializeAxios($axios, store);

  $axios.onRequest(async (config: AxiosRequestConfig) => {
    config.withCredentials = false;
    let cf = <any>config;
    // console.log('update>>', JSON.stringify(config));
    // token处理
    if (!cf.skipToken) {
      if (cf.token != undefined && cf.token) {
        let token = await store.dispatch('auth/getToken');
        if (token) {
          config.headers['Authorization'] = 'Bearer ' + token;
        }
      } else {
        store.dispatch('auth/getToken');
      }
    }
    if (cf.ticket) {
      cf.headers['Authorization'] = 'Ticket ' + cf.ticket;
    }

    let driverId = await store.dispatch('app/getDriverId');
    config.headers['X-Driver-Id'] = driverId;

    if (cf.pageRole) {
      let pageRole = await store.dispatch('auth/getPageRole');
      if (pageRole) {
        config.headers['X-Page-Role'] = pageRole;
      }
    }
    return config;
  })

  let responseInterceptor = async (body: ResponseBody) => {
    if (!body) {
      throw new Error('请求异常');
    }
    if (!body.response && body instanceof Error) {
      throw body;
    }
    const response = body.response;
    if (response.config && response.config.responseAll) {
      return body;
    }
    let { status, data } = response;
    const rp = response.config;
    if (rp.cryptoResult) {
      const transactionCryptoOperator: TransactionCryptoOperator = rp.transactionCryptoOperator!;
      const bodyStr = transactionCryptoOperator.decrypt(data['content']);
      if (bodyStr) {
        data = JSON.parse(bodyStr);
      } else {
        data = bodyStr;
      }
      response.data = data;
    }
    if (status < 400) {
      // 分析设置服务器时间
      if (response && response.headers && response.headers.date) {
        try {
          let serverTime: Date = new Date(Date.parse(response.headers.date));
          timeUtils.serverTime = serverTime;
        } catch (error) {
          console.error('时间格式化出错: ' + response.headers.date, error);
        }
      }
      // 正常响应
      return data;
    } else {
      // 错误响应
      if (data.errorCode && 'YHERT_ERROR_TAG' === data.errorCode) {
        if (status >= 500) {
          throw new ServerException(data.code, data.message, data.data);
        }
        if (status == 401) {
          let unauthorizedException = new UnauthorizedException(data.code, data.message, data.data);
          if ((data.code == 'UNAUTHORIZED' || data.code == 'TOKEN_FAIL') && response.config.deep! <= 1) {
            if (response.config.ticket) {
              throw unauthorizedException;
            }
            let token = null;
            try {
              token = await store.dispatch('auth/refreshToken');
            } catch (error) {
              if (error instanceof BadRequestException) {
                if (error.code == 'SIGN_FAIL' || error.code == 'UNAUTHORIZED' || error.code == 'TOKEN_FAIL') {
                  token = null;
                } else {
                  throw unauthorizedException;
                }
              } else {
                throw unauthorizedException;
              }
            }
            if (token) {
              let rq: any = {};
              rq.url = response.config.url;
              rq.method = response.config.method;
              rq.headers = response.config.headers;
              rq.params = response.config.params;
              rq.data = response.config.data;
              rq.token = response.config.token;
              rq.skipToken = response.config.skipToken;
              rq.deep = response.config.deep! + 1;
              rq.marginRequest = response.config.marginRequest;
              rq.transactionCryptoOperator = response.config.transactionCryptoOperator;
              rq.cryptoBody = response.config.cryptoBody;
              rq.cryptoParam = response.config.cryptoParam;
              rq.cryptoResult = response.config.cryptoResult;
              delete rq.headers.Authorization;
              return await request.request(rq);
            } else {
              app.router?.push("/");
              throw unauthorizedException;
            }
          } else {
            throw unauthorizedException;
          }
        }
        if (status == 403) {
          throw new ForbiddenException(data.code, data.message, data.data);
        }
        if (status == 404) {
          throw new NotFoundException(data.code, data.message, data.data);
        }
        throw new BadRequestException(data.code, data.message, data.data);
      } else {
        console.error('服务器异常：', body)
        throw new Error(...<any>['服务器异常，请检查网络连接，或联系系统管理员', { cause: body }]);
      }
    }
  };

  ax.defaults.withCredentials = false;
  $axios.defaults.withCredentials = false;
  $axios.onResponse(async (data) => await responseInterceptor(<ResponseBody>{ response: data }));
  $axios.onError(async (resp) => await responseInterceptor(<ResponseBody>resp));
}

export default accessor
