import { Plugin } from '@nuxt/types'
import dicUtils from '~/utils/dicUtils'
import menuUtils from '~/utils/menuUtils';
import sysconfigUtils from '~/utils/sysconfigUtils';

if (window && (<any>window).PageSpy) {
  try {
    let title = document.title;
    let project = (<any>window.location).hostname;
    (<any>window).$pageSpy = new (<any>window).PageSpy({ autoRender: false, title, project });
    console.log(`init pagespy project: ${project}, title: ${title}, address: ${(<any>window).$pageSpy.address}`);
  } catch (error) {
    console.log('初始化pagespy异常：', error)
  }
}

const sysbaseInitPlugin: Plugin = async ({ app, store }) => {
  // 获取默认配置
  try {
    await Promise.all([dicUtils.reload(), sysconfigUtils.reload()]);
    await menuUtils.reload();
  } catch (error) {
    window.alert('服务器连接出现异常，请稍后重试。');
    throw error;
  }
}

export default sysbaseInitPlugin;
