import Vue from 'vue'
import VueI18n from 'vue-i18n'
import { Plugin } from '@nuxt/types'
import { config as globalConfig } from '~/config/global.config'
import dicUtils from '~/utils/dicUtils';

Vue.use(VueI18n);

const i18nPlugin: Plugin = async ({ app, store }) => {
  store.commit('tags/initCollapse');
  // 获取默认配置
  let config = globalConfig.i18n;
  let locale = await store.dispatch('i18n/getI18nLocal');
  if (!locale) {
    locale = config.locale;
    store.commit('i18n/setI18nLocal', locale);
  }
  dicUtils.setI18n(locale);

  // 准备数据配置
  let lls = [];
  let msgs = <any>{};
  for (const key in config.locales) {
    lls.push(key);
    msgs[key] = (<any>config.locales)[key].msg;
  }

  // 初始化
  const locales = lls;
  let i18n = <any>new VueI18n({
    locale: locale,
    fallbackLocale: locales,
    messages: msgs
  });
  app.i18n = i18n;

  // 绑定切换方法
  i18n.switchLocale = (locale: string) => {
    if (locales.indexOf(locale) !== -1) {
      i18n.locale = locale;
      store.commit('i18n/setI18nLocal', locale);
    }
  }

}

export default i18nPlugin
