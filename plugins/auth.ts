import { Plugin } from '@nuxt/types'

const auth: Plugin = ({ env, store, error, app }) => {
  store.watch((state) => state.ws.msg, async (msg) => {
    if (msg.bc != 'auth') {
      return;
    }
    if (msg.fc != 'logout') {
      return;
    }

    let logout = false;
    let bmsg = '';
    {
      const mdJson = msg.d;
      if (!mdJson) {
        logout = true;
      } else {
        const mb = JSON.parse(mdJson);
        bmsg = mb.msg;
        const excludeRids = mb.excludeRids;
        const rid = await store.dispatch('auth/getTokenRid');
        if (rid) {
          if (!excludeRids) {
            logout = true;
          } else {
            if (excludeRids.indexOf(rid) >= 0) {
              logout = true;
            }
          }
        }
      }
    }
    if (logout) {
      return;
    }
    console.log('被强制退出。', bmsg);
    await store.dispatch('auth/logout');
    try {
      bmsg = bmsg ? bmsg : '被强制登出';
      app.$notify.error({ title: '提示信息', message: bmsg });
    } catch (error) {
    }
    app.router?.push('/');
  })
}

export default auth
