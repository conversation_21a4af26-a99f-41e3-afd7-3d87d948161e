import Vue from 'vue'
import moment from 'moment'
import { Plugin } from '@nuxt/types'
import dicUtils from '~/utils/dicUtils';
import { VueAuth, VueMsg, VueWs } from '~/model/vue';
import ruleUtils from '~/utils/ruleUtils';
import fieldUtils from '~/utils/fieldUtils';
import sysconfigUtils from '~/utils/sysconfigUtils';


const vueBasePlugin: Plugin = (content) => {
  Vue.prototype.$dic = dicUtils;
  Vue.prototype.$sysconfig = sysconfigUtils;
  Vue.prototype.$moment = moment
  Vue.prototype.$fieldUtils = fieldUtils;
  Vue.prototype.$msg = new VueMsg({ $store: content.store });
  Vue.prototype.$auth = new VueAuth({ $store: content.store });
  Vue.prototype.$ws = new VueWs({ $store: content.store });
  Vue.prototype.$rule = ruleUtils;
}

export default vueBasePlugin
