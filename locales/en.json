{"menu": {"msg-inbox": "Message", "index": "Dashboard", "mall": "Mall", "mall-mg": "Mall manage", "mall-store": "Mall store", "mall-mallinfo": "Mall info", "mall-brand": "Mall brand", "mall-category": "Mall category", "mall-product": "Mall product", "mall-orders": "Mall orders", "mall-freight": "Mall freight", "wms": "Warehouse", "wms-warehouse": "Warehouse manage", "wms-container": "Container manage", "wms-product": "Product manage", "role_power": "Role and power", "power-role": "Role manage", "power-power": "Power manage", "power-rule-USER-MALL_CATEGORY": "Category rule", "power-rule-USER-WMS_WAREHOUSE": "Wms warehouse rule", "sysbase": "System config", "sysbase-dic": "Dictionary", "sysbase-sysconfig": "Sysconfig", "sysbase-report-query": "Report query", "sysbase-log": "Log", "sysbase-msg-inbox-sender": "Msg inbox sender", "sysbase-maintain-upgrade": "Maintain upgrade", "cms-manage": "CMS manage", "cms-subject": "CMS Subject", "cms-content": "CMS Content", "adiary": "Test", "user": "User", "user-mg": "User info", "table": "Table", "form_group": "Form group", "form": "Form", "upload": "Upload", "icon": "Icon", "charts": "Charts", "i18n": "I18n", "error": "Error", "permission": "Power", "error404": "404", "demo": "Demo", "def-msg-msg-inbox": "Msg inbox"}, "common": {"base": {"save": "Save", "add": "Add", "modify": "Modify", "export": "Export", "edit": "Edit", "detail": "Detail", "delete": "Delete", "q_delete": "Are you sure you want to delete this data?", "submit": "Submit", "confirm": "Confirm", "ok": "Ok", "cancel": "Cancel", "upload": "Upload", "search": "Search"}, "success": {"save": "Success to save. ", "modify": "Success to modify. ", "delete": "Success to delete. "}, "error": {"select": "No data selected. ", "reload": "Error loading data, please try again later. ", "save": "Failed to save data. Please try again later. ", "modify": "Failed to modify data. Please try again later. ", "delete": "Failed to delete data. Please try again later. "}}, "cmpl": {"layout": {"header": {"title": "Admin", "home": "Home", "message_num": "There are {message} unread messages", "message": "Message", "language": "language", "userinfo": "User Info", "logout": "Logout", "changePassword": "Change password", "proxyswtich2user": "Switch to user", "proxyback": "Back", "setting": "Setting"}, "tags": {"operate": "Operate", "closeOther": "Close Other", "closeAll": "Close All"}}, "y": {"upload": {"btn_upload": "Upload"}}}, "layouts": {"empty": {"language": "language"}, "error": {"fail_404_msg": "Page not found"}}, "pages": {"common": {"login": {"index": {"title": "Admin System", "welcome": "Welcome", "login": "<PERSON><PERSON>", "login_form_username_label": "Username", "login_form_username_placeholder": "username", "login_form_username_tip": "Please input the username", "login_form_username_fail": "Please input the username", "login_form_password_label": "Password", "login_form_password_placeholder": "password", "login_form_password_tip": "Please input the password", "login_form_password_fail": "Please input the password", "login_form_fail_msg": "Username or password is wrong"}}}, "msg-inboxs": {"unread_msg_label": "Unread message({count})", "delete": "Delete"}, "msg-inbox": {"title": "Message", "unread_msg_label": "Unread message({count})", "mark_read": "<PERSON> read", "mark_read_all": "<PERSON> read all", "read_msg_label": "Read message({count})", "delete": "Delete", "delete_all": "Delete all", "delete_msg_label": "Recycle bin({count})", "reduction": "Reduction", "clear_all": "Clear all"}, "cms": {"subject": {"index": {"title": "CMS Subject", "document": "Document", "swagger": "Swagger"}, "edit": {"title_edit": "Edit", "title_add": "Add"}, "content": {"index": {"title": "CMS Document"}}}}, "power": {"power": {"index": {"title": "Power Manage"}, "edit": {"title_edit": "Edit", "title_add": "Add"}}, "role": {"index": {"title": "Role Manage", "edit_power": "Rule"}, "edit": {"title_edit": "Edit", "title_add": "Add"}}}, "adiary": {"index": {"btn_create": "Create"}}}}